/**
 * Rounds to next power of two.
 * @function nextPow2
 * @param {number} v - input value
 * @returns {number} - next rounded power of two
 * @category maths
 * @advanced
 */
export declare function nextPow2(v: number): number;
/**
 * Checks if a number is a power of two.
 * @function isPow2
 * @param {number} v - input value
 * @returns {boolean} `true` if value is power of two
 * @category maths
 * @advanced
 */
export declare function isPow2(v: number): boolean;
/**
 * Computes ceil of log base 2
 * @function log2
 * @param {number} v - input value
 * @returns {number} logarithm base 2
 * @category maths
 * @advanced
 */
export declare function log2(v: number): number;
