/**
 * Calculates the squared distance from a point to a line segment defined by two endpoints.
 * @param x - x coordinate of the point
 * @param y - y coordinate of the point
 * @param x1 - x coordinate of the first endpoint of the line segment
 * @param y1 - y coordinate of the first endpoint of the line segment
 * @param x2 - x coordinate of the second endpoint of the line segment
 * @param y2 - y coordinate of the second endpoint of the line segment
 * @returns The squared distance from the point to the line segment
 * @category maths
 * @internal
 */
export declare function squaredDistanceToLineSegment(x: number, y: number, x1: number, y1: number, x2: number, y2: number): number;
