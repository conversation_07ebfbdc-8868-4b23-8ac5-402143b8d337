/**
 * Check if a point is inside a triangle.
 * @param px - x coordinate of the point
 * @param py - y coordinate of the point
 * @param x1 - x coordinate of the first vertex of the triangle
 * @param y1 - y coordinate of the first vertex of the triangle
 * @param x2 - x coordinate of the second vertex of the triangle
 * @param y2 - y coordinate of the second vertex of the triangle
 * @param x3 - x coordinate of the third vertex of the triangle
 * @param y3 - y coordinate of the third vertex of the triangle
 * @returns `true` if the point is inside the triangle, `false` otherwise
 * @category maths
 * @internal
 */
export declare function pointInTriangle(px: number, py: number, x1: number, y1: number, x2: number, y2: number, x3: number, y3: number): boolean;
