/**
 * This file is auto generated by scripts/utils/autoGenerateUnsafeEvalFunctions.ts
 * Do not edit manually - or you will be sad.
 */
import type { UNIFORM_TYPES } from '../../rendering/renderers/shared/shader/types';
/** @internal */
export type UniformUploadFunction = (name: string, cu: any, cv: any, v: any, ud: any, uv: any, gl: any) => void;
/** @internal */
export declare const uniformSingleParserFunctions: Record<UNIFORM_TYPES | string, UniformUploadFunction>;
/** @internal */
export declare const uniformArrayParserFunctions: Record<UNIFORM_TYPES | string, UniformUploadFunction>;
/** @internal */
export declare const uniformParserFunctions: UniformUploadFunction[];
