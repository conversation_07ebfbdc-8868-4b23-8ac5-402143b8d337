/**
 * This file is auto generated by scripts/utils/autoGenerateUnsafeEvalFunctions.ts
 * Do not edit manually - or you will be sad.
 */
import type { UNIFORM_TYPES } from '../../rendering/renderers/shared/shader/types';
/** @internal */
export type UboUploadFunction = (name: string, data: Float32Array, offset: number, uv: any, v: any) => void;
/** @internal */
export declare const uboParserFunctions: UboUploadFunction[];
/** @internal */
export declare const uboSingleFunctionsWGSL: Record<UNIFORM_TYPES | string, UboUploadFunction>;
/** @internal */
export declare const uboSingleFunctionsSTD40: Record<UNIFORM_TYPES | string, UboUploadFunction>;
