/**
 * The precision types available in WebGL shaders.
 *
 * These types define the precision of floating-point calculations in shaders.
 * - `highp`: High precision, typically 32-bit floating-point.
 * - `mediump`: Medium precision, typically 16-bit floating-point.
 * - `lowp`: Low precision, typically 8-bit floating-point.
 * @category rendering
 * @advanced
 */
export type PRECISION = `highp` | `mediump` | `lowp`;
