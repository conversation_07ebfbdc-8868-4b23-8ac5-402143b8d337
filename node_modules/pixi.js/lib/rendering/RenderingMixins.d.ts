declare global
{
    namespace PixiMixins
    {

        // eslint-disable-next-line @typescript-eslint/no-empty-object-type
        interface ICanvas
        {

        }

        interface RendererOptions
        {
            resolution?: number;
            failIfMajorPerformanceCaveat?: boolean;
            roundPixels?: boolean;
        }

        // eslint-disable-next-line @typescript-eslint/no-empty-object-type
        interface WebGLOptions
        {

        }

        // eslint-disable-next-line @typescript-eslint/no-empty-object-type
        interface WebGPUOptions
        {

        }

        // eslint-disable-next-line @typescript-eslint/no-empty-object-type
        interface RendererSystems
        {

        }

        // eslint-disable-next-line @typescript-eslint/no-empty-object-type
        interface WebGLSystems
        {

        }

        // eslint-disable-next-line @typescript-eslint/no-empty-object-type
        interface WebGPUSystems
        {

        }

        // eslint-disable-next-line @typescript-eslint/no-empty-object-type
        interface CanvasSystems
        {

        }

        // eslint-disable-next-line @typescript-eslint/no-empty-object-type
        interface RendererPipes
        {

        }

        // eslint-disable-next-line @typescript-eslint/no-empty-object-type
        interface WebGLPipes
        {

        }

        // eslint-disable-next-line @typescript-eslint/no-empty-object-type
        interface WebGPUPipes
        {

        }

        // eslint-disable-next-line @typescript-eslint/no-empty-object-type
        interface CanvasPipes
        {

        }
    }
}

export {};
