import { ExtensionType } from '../../extensions/Extensions';
import { type Renderer } from '../../rendering/renderers/types';
import type { System } from '../../rendering/renderers/shared/system/System';
import type { Texture } from '../../rendering/renderers/shared/texture/Texture';
import type { HTMLTextOptions } from './HTMLText';
/**
 * System plugin to the renderer to manage HTMLText
 * @category rendering
 * @advanced
 */
export declare class HTMLTextSystem implements System {
    /** @ignore */
    static extension: {
        readonly type: readonly [ExtensionType.WebGLSystem, ExtensionType.WebGPUSystem, ExtensionType.CanvasSystem];
        readonly name: "htmlText";
    };
    /**
     * WebGPU has a cors issue when uploading an image that is an SVGImage
     * To get around this we need to create a canvas draw the image to it and upload that instead.
     * Bit of a shame.. but no other work around just yet!
     */
    private readonly _createCanvas;
    private readonly _renderer;
    constructor(renderer: Renderer);
    /**
     * @param options
     * @deprecated Use getTexturePromise instead
     */
    getTexture(options: HTMLTextOptions): Promise<Texture>;
    getTexturePromise(options: HTMLTextOptions): Promise<Texture>;
    private _buildTexturePromise;
    returnTexturePromise(texturePromise: Promise<Texture>): void;
    private _cleanUp;
    destroy(): void;
}
