import { ViewContainer } from '../view/ViewContainer';
import type { Point } from '../../maths/point/Point';
import type { Instruction } from '../../rendering/renderers/shared/instructions/Instruction';
import type { Renderer } from '../../rendering/renderers/types';
import type { Bounds, BoundsData } from './bounds/Bounds';
import type { ContainerOptions } from './Container';
/**
 * A function that takes a renderer and does the custom rendering logic.
 * This is the function that will be called each frame.
 * @param renderer - The current renderer
 * @example
 * ```js
 * import { RenderContainer } from 'pixi.js';
 *
 * // create a new render container
 * const renderContainer = new RenderContainer((renderer) => {
 *     // custom render logic here
 *     renderer.clear({
 *         clearColor: 'green', // clear the screen to green when rendering this item
 *     });
 * });
 * ```
 * @category scene
 * @advanced
 */
export type RenderFunction = (renderer: Renderer) => void;
/**
 * Options for the {@link RenderContainer} constructor.
 * @category scene
 * @advanced
 * @noInheritDoc
 */
export interface RenderContainerOptions extends ContainerOptions {
    /** the optional custom render function if you want to inject the function via the constructor */
    render?: RenderFunction;
    /** how to know if the custom render logic contains a point or not, used for interaction */
    containsPoint?: (point: Point) => boolean;
    /** how to add the bounds of this object when measuring */
    addBounds?: (bounds: BoundsData) => void;
}
/**
 * A container that allows for custom rendering logic. Its essentially calls the render function each frame
 * and allows for custom rendering logic - the render could be a WebGL renderer or WebGPU render or even a canvas render.
 * Its up to you to define the logic.
 *
 * This can be used in two ways, either by extending the class and overriding the render method,
 * or by passing a custom render function
 * @example
 * ```js
 * import { RenderContainer } from 'pixi.js';
 *
 * // extend the class
 * class MyRenderContainer extends RenderContainer
 * {
 *    render(renderer)
 *    {
 *      renderer.clear({
 *         clearColor: 'green', // clear the screen to green when rendering this item
 *      });
 *   }
 * }
 *
 * // override the render method
 * const renderContainer = new RenderContainer(
 * (renderer) =>  {
 *     renderer.clear({
 *       clearColor: 'green', // clear the screen to green when rendering this item
 *     });
 * })
 * ```
 * @category scene
 * @advanced
 */
export declare class RenderContainer extends ViewContainer implements Instruction {
    /** @internal */
    readonly renderPipeId: string;
    /** @internal */
    batched: boolean;
    /**
     * Adds the bounds of this text to the bounds object.
     * @param bounds - The output bounds object.
     */
    addBounds: (bounds: Bounds) => void;
    /**
     * @param options - The options for the container.
     */
    constructor(options: RenderContainerOptions | RenderFunction);
    /** @private */
    protected updateBounds(): void;
    /**
     * An overridable function that can be used to render the object using the current renderer.
     * @param _renderer - The current renderer
     */
    render(_renderer: Renderer): void;
}
