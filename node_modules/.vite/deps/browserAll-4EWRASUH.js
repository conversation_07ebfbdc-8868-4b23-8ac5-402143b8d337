import "./chunk-LY5VWRYW.js";
import {
  AccessibilitySystem,
  DOMPipe,
  EventSystem,
  FederatedContainer,
  accessibilityTarget
} from "./chunk-WOHOYKRX.js";
import "./chunk-OHOK77KS.js";
import "./chunk-7GM7XWZ4.js";
import {
  Container,
  extensions
} from "./chunk-RDKHSB2P.js";
import "./chunk-5WRI5ZAA.js";

// node_modules/pixi.js/lib/accessibility/init.mjs
extensions.add(AccessibilitySystem);
extensions.mixin(Container, accessibilityTarget);

// node_modules/pixi.js/lib/events/init.mjs
extensions.add(EventSystem);
extensions.mixin(Container, FederatedContainer);

// node_modules/pixi.js/lib/dom/init.mjs
extensions.add(DOMPipe);
//# sourceMappingURL=browserAll-4EWRASUH.js.map
