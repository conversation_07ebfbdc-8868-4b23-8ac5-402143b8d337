{"version": 3, "sources": ["../../pixi.js/src/app/init.ts", "../../pixi.js/src/scene/graphics/init.ts", "../../pixi.js/src/scene/mesh/init.ts", "../../pixi.js/src/scene/particle-container/init.ts", "../../pixi.js/src/scene/text/init.ts", "../../pixi.js/src/scene/text-bitmap/init.ts", "../../pixi.js/src/scene/text-html/init.ts", "../../pixi.js/src/scene/sprite-tiling/init.ts", "../../pixi.js/src/scene/sprite-nine-slice/init.ts", "../../pixi.js/src/filters/init.ts"], "sourcesContent": ["import { extensions } from '../extensions/Extensions';\nimport { ResizePlugin } from './ResizePlugin';\nimport { TickerPlugin } from './TickerPlugin';\n\nextensions.add(ResizePlugin);\nextensions.add(TickerPlugin);\n", "import { extensions } from '../../extensions/Extensions';\nimport { GraphicsContextSystem } from './shared/GraphicsContextSystem';\nimport { GraphicsPipe } from './shared/GraphicsPipe';\n\nextensions.add(GraphicsPipe);\nextensions.add(GraphicsContextSystem);\n", "import { extensions } from '../../extensions/Extensions';\nimport { MeshPipe } from './shared/MeshPipe';\n\nextensions.add(MeshPipe);\n", "import { extensions } from '../../extensions/Extensions';\nimport { GlParticleContainerPipe } from './shared/GlParticleContainerPipe';\nimport { GpuParticleContainerPipe } from './shared/GpuParticleContainerPipe';\n\n// NOTE: this is the first occurrence of needing both gl and gpu pipes in the same file\n// This could cause some issues with tree shaking in the future.\n// Right now these two files do not import anything specific for a renderer, so is not an issue for now.\nextensions.add(GlParticleContainerPipe);\nextensions.add(GpuParticleContainerPipe);\n", "import { extensions } from '../../extensions/Extensions';\nimport { CanvasTextPipe } from './canvas/CanvasTextPipe';\nimport { CanvasTextSystem } from './canvas/CanvasTextSystem';\n\nextensions.add(CanvasTextSystem);\nextensions.add(CanvasTextPipe);\n", "import { extensions } from '../../extensions/Extensions';\nimport { BitmapTextPipe } from './BitmapTextPipe';\n\nextensions.add(BitmapTextPipe);\n", "import { extensions } from '../../extensions/Extensions';\nimport { HTMLTextPipe } from './HTMLTextPipe';\nimport { HTMLTextSystem } from './HTMLTextSystem';\n\nextensions.add(HTMLTextSystem);\nextensions.add(HTMLTextPipe);\n", "import { extensions } from '../../extensions/Extensions';\nimport { TilingSpritePipe } from './TilingSpritePipe';\n\nextensions.add(TilingSpritePipe);\n", "import { extensions } from '../../extensions/Extensions';\nimport { NineSliceSpritePipe } from './NineSliceSpritePipe';\n\nextensions.add(NineSliceSpritePipe);\n", "import { extensions } from '../extensions/Extensions';\nimport { FilterPipe } from './FilterPipe';\nimport { FilterSystem } from './FilterSystem';\n\nextensions.add(FilterSystem);\nextensions.add(FilterPipe);\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAIA,WAAW,IAAI,YAAY;AAC3B,WAAW,IAAI,YAAY;;;ACD3B,WAAW,IAAI,YAAY;AAC3B,WAAW,IAAI,qBAAqB;;;ACFpC,WAAW,IAAI,QAAQ;;;ACIvB,WAAW,IAAI,uBAAuB;AACtC,WAAW,IAAI,wBAAwB;;;ACJvC,WAAW,IAAI,gBAAgB;AAC/B,WAAW,IAAI,cAAc;;;ACF7B,WAAW,IAAI,cAAc;;;ACC7B,WAAW,IAAI,cAAc;AAC7B,WAAW,IAAI,YAAY;;;ACF3B,WAAW,IAAI,gBAAgB;;;ACA/B,WAAW,IAAI,mBAAmB;;;ACClC,WAAW,IAAI,YAAY;AAC3B,WAAW,IAAI,UAAU;", "names": []}