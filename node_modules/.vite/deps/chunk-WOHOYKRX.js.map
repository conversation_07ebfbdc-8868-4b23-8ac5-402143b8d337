{"version": 3, "sources": ["../../pixi.js/src/events/FederatedEvent.ts", "../../ismobilejs/src/isMobile.ts", "../../pixi.js/src/utils/browser/isMobile.ts", "../../pixi.js/src/accessibility/AccessibilitySystem.ts", "../../pixi.js/src/accessibility/accessibilityTarget.ts", "../../pixi.js/src/events/EventTicker.ts", "../../pixi.js/src/events/FederatedMouseEvent.ts", "../../pixi.js/src/events/FederatedPointerEvent.ts", "../../pixi.js/src/events/FederatedWheelEvent.ts", "../../pixi.js/src/events/EventBoundary.ts", "../../pixi.js/src/events/EventSystem.ts", "../../pixi.js/src/events/FederatedEventTarget.ts", "../../pixi.js/src/dom/DOMPipe.ts", "../../pixi.js/src/dom/DOMContainer.ts"], "sourcesContent": ["import { Point } from '../maths/point/Point';\n\nimport type { Container } from '../scene/container/Container';\nimport type { EventBoundary } from './EventBoundary';\n\n/**\n * A PixiJS compatible touch event interface that extends the standard DOM Touch interface.\n * Provides additional properties to normalize touch input with mouse/pointer events.\n * @example\n * ```ts\n * // Access touch information\n * sprite.on('touchstart', (event) => {\n *     // Standard touch properties\n *     console.log('Touch position:', event.clientX, event.clientY);\n *     console.log('Touch ID:', event.pointerId);\n *\n *     // Additional PixiJS properties\n *     console.log('Pressure:', event.pressure);\n *     console.log('Size:', event.width, event.height);\n *     console.log('Tilt:', event.tiltX, event.tiltY);\n * });\n * ```\n * @category events\n * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/Touch} DOM Touch Interface\n * @standard\n */\nexport interface PixiTouch extends Touch\n{\n    /** The button being pressed (0: left, 1: middle, 2: right) */\n    button: number;\n\n    /** Bitmap of currently pressed buttons */\n    buttons: number;\n\n    /** Whether this is the primary touch point */\n    isPrimary: boolean;\n\n    /** The width of the touch contact area */\n    width: number;\n\n    /** The height of the touch contact area */\n    height: number;\n\n    /** The angle of tilt along the x-axis (in degrees) */\n    tiltX: number;\n\n    /** The angle of tilt along the y-axis (in degrees) */\n    tiltY: number;\n\n    /** The type of pointer that triggered this event */\n    pointerType: string;\n\n    /** Unique identifier for this touch point */\n    pointerId: number;\n\n    /** The normalized pressure of the pointer (0 to 1) */\n    pressure: number;\n\n    /** The rotation angle of the pointer (e.g., pen) */\n    twist: number;\n\n    /** The normalized tangential pressure of the pointer */\n    tangentialPressure: number;\n\n    /** The x coordinate relative to the current layer */\n    layerX: number;\n\n    /** The y coordinate relative to the current layer */\n    layerY: number;\n\n    /** The x coordinate relative to the target's offset parent */\n    offsetX: number;\n\n    /** The y coordinate relative to the target's offset parent */\n    offsetY: number;\n\n    /** Whether the event was normalized by PixiJS */\n    isNormalized: boolean;\n\n    /** The type of touch event */\n    type: string;\n}\n\n/**\n * A DOM-compatible synthetic event implementation for PixiJS's event system.\n * This class implements the standard DOM Event interface while providing additional\n * functionality specific to PixiJS events.\n * > [!NOTE] You wont receive an instance of this class directly, but rather a subclass\n * > of this class, such as {@link FederatedPointerEvent}, {@link FederatedMouseEvent}, or\n * > {@link FederatedWheelEvent}. This class is the base for all federated events.\n * @example\n * ```ts\n * // Basic event handling\n * sprite.on('pointerdown', (event: FederatedEvent) => {\n *     // Access standard DOM event properties\n *     console.log('Target:', event.target);\n *     console.log('Phase:', event.eventPhase);\n *     console.log('Type:', event.type);\n *\n *     // Control propagation\n *     event.stopPropagation();\n * });\n * ```\n * @typeParam N - The type of native event held. Can be either a UIEvent or PixiTouch.\n * @remarks\n * - Implements the standard DOM UIEvent interface\n * - Provides event bubbling and capturing phases\n * - Supports propagation control\n * - Manages event paths through display tree\n * - Normalizes native browser events\n * @see {@link https://dom.spec.whatwg.org/#event} DOM Event Specification\n * @see {@link FederatedPointerEvent} For pointer-specific events\n * @see {@link FederatedMouseEvent} For mouse-specific events\n * @see {@link FederatedWheelEvent} For wheel-specific events\n * @category events\n * @standard\n */\nexport class FederatedEvent<N extends UIEvent | PixiTouch = UIEvent | PixiTouch> implements UIEvent\n{\n    /** Flags whether this event bubbles. This will take effect only if it is set before propagation. */\n    public bubbles = true;\n\n    /** @deprecated since 7.0.0 */\n    public cancelBubble = true;\n\n    /**\n     * Flags whether this event can be canceled using {@link FederatedEvent.preventDefault}. This is always\n     * false (for now).\n     */\n    public readonly cancelable = false;\n\n    /**\n     * Flag added for compatibility with DOM `Event`. It is not used in the Federated Events\n     * API.\n     * @see https://dom.spec.whatwg.org/#dom-event-composed\n     * @ignore\n     */\n    public readonly composed = false;\n\n    /** The listeners of the event target that are being notified. */\n    public currentTarget: Container;\n\n    /** Flags whether the default response of the user agent was prevent through this event. */\n    public defaultPrevented = false;\n\n    /**\n     * The propagation phase.\n     * @default {@link FederatedEvent.NONE}\n     */\n    public eventPhase = FederatedEvent.prototype.NONE;\n\n    /** Flags whether this is a user-trusted event */\n    public isTrusted: boolean;\n\n    /** @deprecated since 7.0.0 */\n    public returnValue: boolean;\n\n    /** @deprecated since 7.0.0 */\n    public srcElement: EventTarget;\n\n    /** The event target that this will be dispatched to. */\n    public target: Container;\n\n    /** The timestamp of when the event was created. */\n    public timeStamp: number;\n\n    /** The type of event, e.g. `\"mouseup\"`. */\n    public type: string;\n\n    /** The native event that caused the foremost original event. */\n    public nativeEvent: N;\n\n    /** The original event that caused this event, if any. */\n    public originalEvent: FederatedEvent<N>;\n\n    /** Flags whether propagation was stopped. */\n    public propagationStopped = false;\n\n    /** Flags whether propagation was immediately stopped. */\n    public propagationImmediatelyStopped = false;\n\n    /** The composed path of the event's propagation. The `target` is at the end. */\n    public path: Container[];\n\n    /** The {@link EventBoundary} that manages this event. Null for root events. */\n    public readonly manager: EventBoundary;\n\n    /** Event-specific detail */\n    public detail: number;\n\n    /** The global Window object. */\n    public view: WindowProxy;\n\n    /**\n     * Not supported.\n     * @deprecated since 7.0.0\n     * @ignore\n     */\n    public which: number;\n\n    /** The coordinates of the event relative to the nearest DOM layer. This is a non-standard property. */\n    public layer: Point = new Point();\n\n    /** @readonly */\n    get layerX(): number { return this.layer.x; }\n\n    /** @readonly */\n    get layerY(): number { return this.layer.y; }\n\n    /** The coordinates of the event relative to the DOM document. This is a non-standard property. */\n    public page: Point = new Point();\n\n    /** @readonly */\n    get pageX(): number { return this.page.x; }\n\n    /** @readonly */\n    get pageY(): number { return this.page.y; }\n\n    /**\n     * @param manager - The event boundary which manages this event. Propagation can only occur\n     *  within the boundary's jurisdiction.\n     */\n    constructor(manager: EventBoundary)\n    {\n        this.manager = manager;\n    }\n\n    /**\n     * Fallback for the deprecated `InteractionEvent.data`.\n     * @deprecated since 7.0.0\n     */\n    get data(): this\n    {\n        return this;\n    }\n\n    /**\n     * The propagation path for this event. Alias for {@link EventBoundary.propagationPath}.\n     * @advanced\n     */\n    public composedPath(): Container[]\n    {\n        // Find the propagation path if it isn't cached or if the target has changed since since\n        // the last evaluation.\n        if (this.manager && (!this.path || this.path[this.path.length - 1] !== this.target))\n        {\n            this.path = this.target ? this.manager.propagationPath(this.target) : [];\n        }\n\n        return this.path;\n    }\n\n    /**\n     * Unimplemented method included for implementing the DOM interface `Event`. It will throw an `Error`.\n     * @deprecated\n     * @ignore\n     * @param _type\n     * @param _bubbles\n     * @param _cancelable\n     */\n    public initEvent(_type: string, _bubbles?: boolean, _cancelable?: boolean): void\n    {\n        throw new Error('initEvent() is a legacy DOM API. It is not implemented in the Federated Events API.');\n    }\n\n    /**\n     * Unimplemented method included for implementing the DOM interface `UIEvent`. It will throw an `Error`.\n     * @ignore\n     * @deprecated\n     * @param _typeArg\n     * @param _bubblesArg\n     * @param _cancelableArg\n     * @param _viewArg\n     * @param _detailArg\n     */\n    public initUIEvent(_typeArg: string, _bubblesArg?: boolean, _cancelableArg?: boolean, _viewArg?: Window | null,\n        _detailArg?: number): void\n    {\n        throw new Error('initUIEvent() is a legacy DOM API. It is not implemented in the Federated Events API.');\n    }\n\n    /**\n     * Prevent default behavior of both PixiJS and the user agent.\n     * @example\n     * ```ts\n     * sprite.on('click', (event) => {\n     *     // Prevent both browser's default click behavior\n     *     // and PixiJS's default handling\n     *     event.preventDefault();\n     *\n     *     // Custom handling\n     *     customClickHandler();\n     * });\n     * ```\n     * @remarks\n     * - Only works if the native event is cancelable\n     * - Does not stop event propagation\n     */\n    public preventDefault(): void\n    {\n        if (this.nativeEvent instanceof Event && this.nativeEvent.cancelable)\n        {\n            this.nativeEvent.preventDefault();\n        }\n\n        this.defaultPrevented = true;\n    }\n\n    /**\n     * Stop this event from propagating to any additional listeners, including those\n     * on the current target and any following targets in the propagation path.\n     * @example\n     * ```ts\n     * container.on('pointerdown', (event) => {\n     *     // Stop all further event handling\n     *     event.stopImmediatePropagation();\n     *\n     *     // These handlers won't be called:\n     *     // - Other pointerdown listeners on this container\n     *     // - Any pointerdown listeners on parent containers\n     * });\n     * ```\n     * @remarks\n     * - Immediately stops all event propagation\n     * - Prevents other listeners on same target from being called\n     * - More aggressive than stopPropagation()\n     */\n    public stopImmediatePropagation(): void\n    {\n        this.propagationImmediatelyStopped = true;\n    }\n\n    /**\n     * Stop this event from propagating to the next target in the propagation path.\n     * The rest of the listeners on the current target will still be notified.\n     * @example\n     * ```ts\n     * child.on('pointermove', (event) => {\n     *     // Handle event on child\n     *     updateChild();\n     *\n     *     // Prevent parent handlers from being called\n     *     event.stopPropagation();\n     * });\n     *\n     * // This won't be called if child handles the event\n     * parent.on('pointermove', (event) => {\n     *     updateParent();\n     * });\n     * ```\n     * @remarks\n     * - Stops event bubbling to parent containers\n     * - Does not prevent other listeners on same target\n     * - Less aggressive than stopImmediatePropagation()\n     */\n    public stopPropagation(): void\n    {\n        this.propagationStopped = true;\n    }\n\n    /**\n     * The event propagation phase NONE that indicates that the event is not in any phase.\n     * @default 0\n     * @advanced\n     */\n    public readonly NONE = 0;\n    /**\n     * The event propagation phase CAPTURING_PHASE that indicates that the event is in the capturing phase.\n     * @default 1\n     * @advanced\n     */\n    public readonly CAPTURING_PHASE = 1;\n    /**\n     * The event propagation phase AT_TARGET that indicates that the event is at the target.\n     * @default 2\n     * @advanced\n     */\n    public readonly AT_TARGET = 2;\n    /**\n     * The event propagation phase BUBBLING_PHASE that indicates that the event is in the bubbling phase.\n     * @default 3\n     * @advanced\n     */\n    public readonly BUBBLING_PHASE = 3;\n}\n", "const appleIphone = /iPhone/i;\nconst appleIpod = /iPod/i;\nconst appleTablet = /iPad/i;\nconst appleUniversal = /\\biOS-universal(?:.+)Mac\\b/i;\nconst androidPhone = /\\bAndroid(?:.+)Mobile\\b/i; // Match 'Android' AND 'Mobile'\nconst androidTablet = /Android/i;\nconst amazonPhone = /(?:SD4930UR|\\bSilk(?:.+)Mobile\\b)/i; // Match 'Silk' AND 'Mobile'\nconst amazonTablet = /Silk/i;\nconst windowsPhone = /Windows Phone/i;\nconst windowsTablet = /\\bWindows(?:.+)ARM\\b/i; // Match 'Windows' AND 'ARM'\nconst otherBlackBerry = /BlackBerry/i;\nconst otherBlackBerry10 = /BB10/i;\nconst otherOpera = /Opera Mini/i;\nconst otherChrome = /\\b(CriOS|Chrome)(?:.+)Mobile/i;\nconst otherFirefox = /Mobile(?:.+)Firefox\\b/i; // Match 'Mobile' AND 'Firefox'\n\nexport type UserAgent = string;\nexport type Navigator = {\n  userAgent: string;\n  platform: string;\n  maxTouchPoints?: number;\n};\n\nconst isAppleTabletOnIos13 = (navigator?: Navigator): boolean => {\n  return (\n    typeof navigator !== 'undefined' &&\n    navigator.platform === 'MacIntel' &&\n    typeof navigator.maxTouchPoints === 'number' &&\n    navigator.maxTouchPoints > 1 &&\n    typeof MSStream === 'undefined'\n  );\n};\n\nfunction createMatch(userAgent: UserAgent): (regex: RegExp) => boolean {\n  return (regex: RegExp): boolean => regex.test(userAgent);\n}\n\nexport type isMobileResult = {\n  apple: {\n    phone: boolean;\n    ipod: boolean;\n    tablet: boolean;\n    universal: boolean;\n    device: boolean;\n  };\n  amazon: {\n    phone: boolean;\n    tablet: boolean;\n    device: boolean;\n  };\n  android: {\n    phone: boolean;\n    tablet: boolean;\n    device: boolean;\n  };\n  windows: {\n    phone: boolean;\n    tablet: boolean;\n    device: boolean;\n  };\n  other: {\n    blackberry: boolean;\n    blackberry10: boolean;\n    opera: boolean;\n    firefox: boolean;\n    chrome: boolean;\n    device: boolean;\n  };\n  phone: boolean;\n  tablet: boolean;\n  any: boolean;\n};\n\nexport type IsMobileParameter = UserAgent | Navigator;\n\nexport default function isMobile(param?: IsMobileParameter): isMobileResult {\n  let nav: Navigator = {\n    userAgent: '',\n    platform: '',\n    maxTouchPoints: 0,\n  };\n\n  if (!param && typeof navigator !== 'undefined') {\n    nav = {\n      userAgent: navigator.userAgent,\n      platform: navigator.platform,\n      maxTouchPoints: navigator.maxTouchPoints || 0,\n    };\n  } else if (typeof param === 'string') {\n    nav.userAgent = param;\n  } else if (param && param.userAgent) {\n    nav = {\n      userAgent: param.userAgent,\n      platform: param.platform,\n      maxTouchPoints: param.maxTouchPoints || 0,\n    };\n  }\n\n  let userAgent = nav.userAgent;\n\n  // Facebook mobile app's integrated browser adds a bunch of strings that\n  // match everything. Strip it out if it exists.\n  let tmp = userAgent.split('[FBAN');\n  if (typeof tmp[1] !== 'undefined') {\n    userAgent = tmp[0];\n  }\n\n  // Twitter mobile app's integrated browser on iPad adds a \"Twitter for\n  // iPhone\" string. Same probably happens on other tablet platforms.\n  // This will confuse detection so strip it out if it exists.\n  tmp = userAgent.split('Twitter');\n  if (typeof tmp[1] !== 'undefined') {\n    userAgent = tmp[0];\n  }\n\n  const match = createMatch(userAgent);\n\n  const result: isMobileResult = {\n    apple: {\n      phone: match(appleIphone) && !match(windowsPhone),\n      ipod: match(appleIpod),\n      tablet:\n        !match(appleIphone) &&\n        (match(appleTablet) || isAppleTabletOnIos13(nav)) &&\n        !match(windowsPhone),\n      universal: match(appleUniversal),\n      device:\n        (match(appleIphone) ||\n          match(appleIpod) ||\n          match(appleTablet) ||\n          match(appleUniversal) ||\n          isAppleTabletOnIos13(nav)) &&\n        !match(windowsPhone),\n    },\n    amazon: {\n      phone: match(amazonPhone),\n      tablet: !match(amazonPhone) && match(amazonTablet),\n      device: match(amazonPhone) || match(amazonTablet),\n    },\n    android: {\n      phone:\n        (!match(windowsPhone) && match(amazonPhone)) ||\n        (!match(windowsPhone) && match(androidPhone)),\n      tablet:\n        !match(windowsPhone) &&\n        !match(amazonPhone) &&\n        !match(androidPhone) &&\n        (match(amazonTablet) || match(androidTablet)),\n      device:\n        (!match(windowsPhone) &&\n          (match(amazonPhone) ||\n            match(amazonTablet) ||\n            match(androidPhone) ||\n            match(androidTablet))) ||\n        match(/\\bokhttp\\b/i),\n    },\n    windows: {\n      phone: match(windowsPhone),\n      tablet: match(windowsTablet),\n      device: match(windowsPhone) || match(windowsTablet),\n    },\n    other: {\n      blackberry: match(otherBlackBerry),\n      blackberry10: match(otherBlackBerry10),\n      opera: match(otherOpera),\n      firefox: match(otherFirefox),\n      chrome: match(otherChrome),\n      device:\n        match(otherBlackBerry) ||\n        match(otherBlackBerry10) ||\n        match(otherOpera) ||\n        match(otherFirefox) ||\n        match(otherChrome),\n    },\n    any: false,\n    phone: false,\n    tablet: false,\n  };\n\n  result.any =\n    result.apple.device ||\n    result.android.device ||\n    result.windows.device ||\n    result.other.device;\n  // excludes 'other' devices and ipods, targeting touchscreen phones\n  result.phone =\n    result.apple.phone || result.android.phone || result.windows.phone;\n  result.tablet =\n    result.apple.tablet || result.android.tablet || result.windows.tablet;\n\n  return result;\n}\n", "import isMobileJs from 'ismobilejs';\n\n// ismobilejs have different import behavior for CJS and ESM, so here is the hack\ntype isMobileJsType = typeof isMobileJs & { default?: typeof isMobileJs };\nconst isMobileCall = (isMobileJs as isMobileJsType).default ?? isMobileJs;\n\n/**\n * The result of the mobile device detection system.\n * Provides detailed information about device type and platform.\n * @example\n * ```ts\n * // Type usage with isMobile\n * const deviceInfo: isMobileResult = isMobile;\n *\n * // Check device categories\n * if (deviceInfo.apple.device) {\n *     console.log('iOS Device Details:', {\n *         isPhone: deviceInfo.apple.phone,\n *         isTablet: deviceInfo.apple.tablet,\n *         isUniversal: deviceInfo.apple.universal\n *     });\n * }\n *\n * // Platform-specific checks\n * const platformInfo = {\n *     isApple: deviceInfo.apple.device,\n *     isAndroid: deviceInfo.android.device,\n *     isAmazon: deviceInfo.amazon.device,\n *     isWindows: deviceInfo.windows.device\n * };\n * ```\n * @category utils\n * @standard\n */\nexport type isMobileResult = {\n    /**\n     * Apple device detection information.\n     * Provides detailed iOS device categorization.\n     * @example\n     * ```ts\n     * // iOS device checks\n     * if (isMobile.apple.device) {\n     *     if (isMobile.apple.tablet) {\n     *         // iPad-specific code\n     *         useTabletLayout();\n     *     } else if (isMobile.apple.phone) {\n     *         // iPhone-specific code\n     *         usePhoneLayout();\n     *     }\n     * }\n     * ```\n     */\n    apple: {\n        /** Whether the device is an iPhone */\n        phone: boolean;\n        /** Whether the device is an iPod Touch */\n        ipod: boolean;\n        /** Whether the device is an iPad */\n        tablet: boolean;\n        /** Whether app is running in iOS universal mode */\n        universal: boolean;\n        /** Whether device is any Apple mobile device */\n        device: boolean;\n    };\n\n    /**\n     * Amazon device detection information.\n     * Identifies Amazon Fire tablets and phones.\n     * @example\n     * ```ts\n     * // Amazon Fire tablet detection\n     * if (isMobile.amazon.tablet) {\n     *     // Fire tablet optimizations\n     *     optimizeForFireTablet();\n     * }\n     * ```\n     */\n    amazon: {\n        /** Whether device is a Fire Phone */\n        phone: boolean;\n        /** Whether device is a Fire Tablet */\n        tablet: boolean;\n        /** Whether device is any Amazon mobile device */\n        device: boolean;\n    };\n\n    /**\n     * Android device detection information.\n     * Categorizes Android phones and tablets.\n     * @example\n     * ```ts\n     * // Android device handling\n     * if (isMobile.android.device) {\n     *     // Check specific type\n     *     const deviceType = isMobile.android.tablet ?\n     *         'tablet' : 'phone';\n     *     console.log(`Android ${deviceType} detected`);\n     * }\n     * ```\n     */\n    android: {\n        /** Whether device is an Android phone */\n        phone: boolean;\n        /** Whether device is an Android tablet */\n        tablet: boolean;\n        /** Whether device is any Android device */\n        device: boolean;\n    };\n\n    /**\n     * Windows device detection information.\n     * Identifies Windows phones and tablets.\n     * @example\n     * ```ts\n     * // Windows device checks\n     * if (isMobile.windows.tablet) {\n     *     // Surface tablet optimizations\n     *     enableTouchFeatures();\n     * }\n     * ```\n     */\n    windows: {\n        /** Whether device is a Windows Phone */\n        phone: boolean;\n        /** Whether device is a Windows tablet */\n        tablet: boolean;\n        /** Whether device is any Windows mobile device */\n        device: boolean;\n    };\n\n    /**\n     * Other device detection information.\n     * Covers additional platforms and browsers.\n     * @example\n     * ```ts\n     * // Check other platforms\n     * if (isMobile.other.blackberry10) {\n     *     // BlackBerry 10 specific code\n     * } else if (isMobile.other.chrome) {\n     *     // Chrome mobile specific code\n     * }\n     * ```\n     */\n    other: {\n        /** Whether device is a BlackBerry */\n        blackberry: boolean;\n        /** Whether device is a BlackBerry 10 */\n        blackberry10: boolean;\n        /** Whether browser is Opera Mobile */\n        opera: boolean;\n        /** Whether browser is Firefox Mobile */\n        firefox: boolean;\n        /** Whether browser is Chrome Mobile */\n        chrome: boolean;\n        /** Whether device is any other mobile device */\n        device: boolean;\n    };\n\n    /**\n     * Whether the device is any type of phone.\n     * Combines detection across all platforms.\n     * @example\n     * ```ts\n     * // Check if device is a phone\n     * if (isMobile.phone) {\n     *     console.log('Running on a mobile phone');\n     * }\n     * ```\n     */\n    phone: boolean;\n\n    /**\n     * Whether the device is any type of tablet.\n     * Combines detection across all platforms.\n     * @example\n     * ```ts\n     * // Check if device is a tablet\n     * if (isMobile.tablet) {\n     *     console.log('Running on a mobile tablet');\n     * }\n     * ```\n     */\n    tablet: boolean;\n\n    /**\n     * Whether the device is any type of mobile device.\n     * True if any mobile platform is detected.\n     * @example\n     * ```ts\n     * // Check if device is mobile\n     * if (isMobile.any) {\n     *     console.log('Running on a mobile device');\n     * }\n     * ```\n     */\n    any: boolean;\n};\n\n/**\n * Detects whether the device is mobile and what type of mobile device it is.\n * Provides a comprehensive detection system for mobile platforms and devices.\n * @example\n * ```ts\n * import { isMobile } from 'pixi.js';\n *\n * // Check specific device types\n * if (isMobile.apple.tablet) {\n *    console.log('Running on iPad');\n * }\n *\n * // Check platform categories\n * if (isMobile.android.any) {\n *    console.log('Running on Android');\n * }\n *\n * // Conditional rendering\n * if (isMobile.phone) {\n *    renderer.resolution = 2;\n *    view.style.width = '100vw';\n * }\n * ```\n * @remarks\n * - Detects all major mobile platforms\n * - Distinguishes between phones and tablets\n * - Updates when navigator changes\n * - Common in responsive design\n * @category utils\n * @standard\n * @see {@link isMobileResult} For full type definition\n */\nexport const isMobile: isMobileResult = isMobileCall(globalThis.navigator);\n", "import { FederatedEvent } from '../events/FederatedEvent';\nimport { ExtensionType } from '../extensions/Extensions';\nimport { isMobile } from '../utils/browser/isMobile';\nimport { removeItems } from '../utils/data/removeItems';\nimport { type AccessibleHTMLElement } from './accessibilityTarget';\n\nimport type { Rectangle } from '../maths/shapes/Rectangle';\nimport type { System } from '../rendering/renderers/shared/system/System';\nimport type { Renderer } from '../rendering/renderers/types';\nimport type { Container } from '../scene/container/Container';\nimport type { isMobileResult } from '../utils/browser/isMobile';\n\n/** @ignore */\nconst KEY_CODE_TAB = 9;\n\nconst DIV_TOUCH_SIZE = 100;\nconst DIV_TOUCH_POS_X = 0;\nconst DIV_TOUCH_POS_Y = 0;\nconst DIV_TOUCH_ZINDEX = 2;\n\nconst DIV_HOOK_SIZE = 1;\nconst DIV_HOOK_POS_X = -1000;\nconst DIV_HOOK_POS_Y = -1000;\nconst DIV_HOOK_ZINDEX = 2;\n\n/**\n * Initialisation options for the accessibility system when used with an Application.\n * @category accessibility\n * @advanced\n */\nexport interface AccessibilitySystemOptions\n{\n    /** Options for the accessibility system */\n    accessibilityOptions?: AccessibilityOptions;\n}\n\n/**\n * The options for the accessibility system.\n * @category accessibility\n * @advanced\n */\nexport interface AccessibilityOptions\n{\n    /** Whether to enable accessibility features on initialization instead of waiting for tab key */\n    enabledByDefault?: boolean;\n    /** Whether to visually show the accessibility divs for debugging */\n    debug?: boolean;\n    /** Whether to allow tab key press to activate accessibility features */\n    activateOnTab?: boolean;\n    /** Whether to deactivate accessibility when mouse moves */\n    deactivateOnMouseMove?: boolean;\n}\n\n/**\n * The Accessibility system provides screen reader and keyboard navigation support for PixiJS content.\n * It creates an accessible DOM layer over the canvas that can be controlled programmatically or through user interaction.\n *\n * By default, the system activates when users press the tab key. This behavior can be customized through options:\n * ```js\n * const app = new Application({\n *     accessibilityOptions: {\n *     // Enable immediately instead of waiting for tab\n *     enabledByDefault: true,\n *     // Disable tab key activation\n *     activateOnTab: false,\n *     // Show/hide accessibility divs\n *     debug: false,\n *     // Prevent accessibility from being deactivated when mouse moves\n *     deactivateOnMouseMove: false,\n * }\n * });\n * ```\n *\n * The system can also be controlled programmatically by accessing the `renderer.accessibility` property:\n * ```js\n * app.renderer.accessibility.setAccessibilityEnabled(true);\n * ```\n *\n * To make individual containers accessible:\n * ```js\n * container.accessible = true;\n * ```\n * There are several properties that can be set on a Container to control its accessibility which can\n * be found here: {@link AccessibleOptions}.\n * @category accessibility\n * @standard\n */\nexport class AccessibilitySystem implements System<AccessibilitySystemOptions>\n{\n    /** @ignore */\n    public static extension = {\n        type: [\n            ExtensionType.WebGLSystem,\n            ExtensionType.WebGPUSystem,\n        ],\n        name: 'accessibility',\n    } as const;\n\n    /**\n     * The default options used by the system.\n     * You can set these before initializing the {@link Application} to change the default behavior.\n     * @example\n     * ```js\n     * import { AccessibilitySystem } from 'pixi.js';\n     *\n     * AccessibilitySystem.defaultOptions.enabledByDefault = true;\n     *\n     * const app = new Application()\n     * app.init()\n     * ```\n     */\n    public static defaultOptions: AccessibilityOptions = {\n        /**\n         * Whether to enable accessibility features on initialization\n         * @default false\n         */\n        enabledByDefault: false,\n        /**\n         * Whether to visually show the accessibility divs for debugging\n         * @default false\n         */\n        debug: false,\n        /**\n         * Whether to activate accessibility when tab key is pressed\n         * @default true\n         */\n        activateOnTab: true,\n        /**\n         * Whether to deactivate accessibility when mouse moves\n         * @default true\n         */\n        deactivateOnMouseMove: true,\n    };\n\n    /** Whether accessibility divs are visible for debugging */\n    public debug = false;\n\n    /** Whether to activate on tab key press */\n    private _activateOnTab = true;\n\n    /** Whether to deactivate accessibility when mouse moves */\n    private _deactivateOnMouseMove = true;\n\n    /**\n     * The renderer this accessibility manager works for.\n     * @type {WebGLRenderer|WebGPURenderer}\n     */\n    private _renderer: Renderer;\n\n    /** Internal variable, see isActive getter. */\n    private _isActive = false;\n\n    /** Internal variable, see isMobileAccessibility getter. */\n    private _isMobileAccessibility = false;\n\n    /** Button element for handling touch hooks. */\n    private _hookDiv: HTMLElement | null;\n\n    /** This is the dom element that will sit over the PixiJS element. This is where the div overlays will go. */\n    private _div: HTMLElement | null = null;\n\n    /** A simple pool for storing divs. */\n    private _pool: AccessibleHTMLElement[] = [];\n\n    /** This is a tick used to check if an object is no longer being rendered. */\n    private _renderId = 0;\n\n    /** The array of currently active accessible items. */\n    private _children: Container[] = [];\n\n    /** Count to throttle div updates on android devices. */\n    private _androidUpdateCount = 0;\n\n    /**  The frequency to update the div elements. */\n    private readonly _androidUpdateFrequency = 500; // 2fps\n\n    // eslint-disable-next-line jsdoc/require-param\n    /**\n     * @param {WebGLRenderer|WebGPURenderer} renderer - A reference to the current renderer\n     */\n    constructor(renderer: Renderer, private readonly _mobileInfo: isMobileResult = isMobile)\n    {\n        this._hookDiv = null;\n\n        if (_mobileInfo.tablet || _mobileInfo.phone)\n        {\n            this._createTouchHook();\n        }\n\n        this._renderer = renderer;\n    }\n\n    /**\n     * Value of `true` if accessibility is currently active and accessibility layers are showing.\n     * @type {boolean}\n     * @readonly\n     */\n    get isActive(): boolean\n    {\n        return this._isActive;\n    }\n\n    /**\n     * Value of `true` if accessibility is enabled for touch devices.\n     * @type {boolean}\n     * @readonly\n     */\n    get isMobileAccessibility(): boolean\n    {\n        return this._isMobileAccessibility;\n    }\n\n    /**\n     * The DOM element that will sit over the PixiJS element. This is where the div overlays will go.\n     * @readonly\n     */\n    get hookDiv()\n    {\n        return this._hookDiv;\n    }\n\n    /**\n     * Creates the touch hooks.\n     * @private\n     */\n    private _createTouchHook(): void\n    {\n        const hookDiv = document.createElement('button');\n\n        hookDiv.style.width = `${DIV_HOOK_SIZE}px`;\n        hookDiv.style.height = `${DIV_HOOK_SIZE}px`;\n        hookDiv.style.position = 'absolute';\n        hookDiv.style.top = `${DIV_HOOK_POS_X}px`;\n        hookDiv.style.left = `${DIV_HOOK_POS_Y}px`;\n        hookDiv.style.zIndex = DIV_HOOK_ZINDEX.toString();\n        hookDiv.style.backgroundColor = '#FF0000';\n        hookDiv.title = 'select to enable accessibility for this content';\n\n        hookDiv.addEventListener('focus', () =>\n        {\n            this._isMobileAccessibility = true;\n            this._activate();\n            this._destroyTouchHook();\n        });\n\n        document.body.appendChild(hookDiv);\n        this._hookDiv = hookDiv;\n    }\n\n    /**\n     * Destroys the touch hooks.\n     * @private\n     */\n    private _destroyTouchHook(): void\n    {\n        if (!this._hookDiv)\n        {\n            return;\n        }\n        document.body.removeChild(this._hookDiv);\n        this._hookDiv = null;\n    }\n\n    /**\n     * Activating will cause the Accessibility layer to be shown.\n     * This is called when a user presses the tab key.\n     * @private\n     */\n    private _activate(): void\n    {\n        if (this._isActive)\n        {\n            return;\n        }\n\n        this._isActive = true;\n\n        // Create and add div if needed\n        if (!this._div)\n        {\n            this._div = document.createElement('div');\n            this._div.style.width = `${DIV_TOUCH_SIZE}px`;\n            this._div.style.height = `${DIV_TOUCH_SIZE}px`;\n            this._div.style.position = 'absolute';\n            this._div.style.top = `${DIV_TOUCH_POS_X}px`;\n            this._div.style.left = `${DIV_TOUCH_POS_Y}px`;\n            this._div.style.zIndex = DIV_TOUCH_ZINDEX.toString();\n            this._div.style.pointerEvents = 'none';\n        }\n\n        // Bind event handlers and add listeners when activating\n        if (this._activateOnTab)\n        {\n            this._onKeyDown = this._onKeyDown.bind(this);\n            globalThis.addEventListener('keydown', this._onKeyDown, false);\n        }\n\n        if (this._deactivateOnMouseMove)\n        {\n            this._onMouseMove = this._onMouseMove.bind(this);\n            globalThis.document.addEventListener('mousemove', this._onMouseMove, true);\n        }\n\n        // Check if canvas is in DOM\n        const canvas = this._renderer.view.canvas;\n\n        if (!canvas.parentNode)\n        {\n            const observer = new MutationObserver(() =>\n            {\n                if (canvas.parentNode)\n                {\n                    canvas.parentNode.appendChild(this._div);\n                    observer.disconnect();\n\n                    // Only start the postrender runner after div is ready\n                    this._initAccessibilitySetup();\n                }\n            });\n\n            observer.observe(document.body, { childList: true, subtree: true });\n        }\n        else\n        {\n            // Add to DOM\n            canvas.parentNode.appendChild(this._div);\n\n            // Div is ready, initialize accessibility\n            this._initAccessibilitySetup();\n        }\n    }\n\n    // New method to handle initialization after div is ready\n    private _initAccessibilitySetup(): void\n    {\n        // Add the postrender runner to start processing accessible objects\n        this._renderer.runners.postrender.add(this);\n\n        // Force an initial update of accessible objects\n        if (this._renderer.lastObjectRendered)\n        {\n            this._updateAccessibleObjects(this._renderer.lastObjectRendered as Container);\n        }\n    }\n\n    /**\n     * Deactivates the accessibility system. Removes listeners and accessibility elements.\n     * @private\n     */\n    private _deactivate(): void\n    {\n        if (!this._isActive || this._isMobileAccessibility)\n        {\n            return;\n        }\n\n        this._isActive = false;\n\n        // Switch listeners\n        globalThis.document.removeEventListener('mousemove', this._onMouseMove, true);\n        if (this._activateOnTab)\n        {\n            globalThis.addEventListener('keydown', this._onKeyDown, false);\n        }\n\n        this._renderer.runners.postrender.remove(this);\n\n        // Remove all active accessibility elements\n        for (const child of this._children)\n        {\n            if (child._accessibleDiv && child._accessibleDiv.parentNode)\n            {\n                child._accessibleDiv.parentNode.removeChild(child._accessibleDiv);\n                child._accessibleDiv = null;\n            }\n            child._accessibleActive = false;\n        }\n\n        // Clear the pool of divs\n        this._pool.forEach((div) =>\n        {\n            if (div.parentNode)\n            {\n                div.parentNode.removeChild(div);\n            }\n        });\n\n        // Remove parent div from DOM\n        if (this._div && this._div.parentNode)\n        {\n            this._div.parentNode.removeChild(this._div);\n        }\n\n        this._pool = [];\n        this._children = [];\n    }\n\n    /**\n     * This recursive function will run through the scene graph and add any new accessible objects to the DOM layer.\n     * @private\n     * @param {Container} container - The Container to check.\n     */\n    private _updateAccessibleObjects(container: Container): void\n    {\n        if (!container.visible || !container.accessibleChildren)\n        {\n            return;\n        }\n\n        // Separate check for accessibility without requiring interactivity\n        if (container.accessible)\n        {\n            if (!container._accessibleActive)\n            {\n                this._addChild(container);\n            }\n\n            container._renderId = this._renderId;\n        }\n\n        const children = container.children;\n\n        if (children)\n        {\n            for (let i = 0; i < children.length; i++)\n            {\n                this._updateAccessibleObjects(children[i] as Container);\n            }\n        }\n    }\n\n    /**\n     * Runner init called, view is available at this point.\n     * @ignore\n     */\n    public init(options?: AccessibilitySystemOptions): void\n    {\n        // Ensure we have the accessibilityOptions object\n        const defaultOpts = AccessibilitySystem.defaultOptions;\n        const mergedOptions = {\n            accessibilityOptions: {\n                ...defaultOpts,\n                ...(options?.accessibilityOptions || {})\n            }\n        };\n\n        this.debug = mergedOptions.accessibilityOptions.debug;\n        this._activateOnTab = mergedOptions.accessibilityOptions.activateOnTab;\n        this._deactivateOnMouseMove = mergedOptions.accessibilityOptions.deactivateOnMouseMove;\n\n        if (mergedOptions.accessibilityOptions.enabledByDefault)\n        {\n            this._activate();\n        }\n        else if (this._activateOnTab)\n        {\n            this._onKeyDown = this._onKeyDown.bind(this);\n            globalThis.addEventListener('keydown', this._onKeyDown, false);\n        }\n\n        this._renderer.runners.postrender.remove(this);\n    }\n\n    /**\n     * Updates the accessibility layer during rendering.\n     * - Removes divs for containers no longer in the scene\n     * - Updates the position and dimensions of the root div\n     * - Updates positions of active accessibility divs\n     * Only fires while the accessibility system is active.\n     * @ignore\n     */\n    public postrender(): void\n    {\n        /* On Android default web browser, tab order seems to be calculated by position rather than tabIndex,\n        *  moving buttons can cause focus to flicker between two buttons making it hard/impossible to navigate,\n        *  so I am just running update every half a second, seems to fix it.\n        */\n        const now = performance.now();\n\n        if (this._mobileInfo.android.device && now < this._androidUpdateCount)\n        {\n            return;\n        }\n\n        this._androidUpdateCount = now + this._androidUpdateFrequency;\n\n        if (!this._renderer.renderingToScreen || !this._renderer.view.canvas)\n        {\n            return;\n        }\n\n        // Track which containers are still active this frame\n        const activeIds = new Set<number>();\n\n        if (this._renderer.lastObjectRendered)\n        {\n            this._updateAccessibleObjects(this._renderer.lastObjectRendered as Container);\n\n            // Mark all updated containers as active\n            for (const child of this._children)\n            {\n                if (child._renderId === this._renderId)\n                {\n                    activeIds.add(this._children.indexOf(child));\n                }\n            }\n        }\n\n        // Remove any containers that weren't updated this frame\n        for (let i = this._children.length - 1; i >= 0; i--)\n        {\n            const child = this._children[i];\n\n            if (!activeIds.has(i))\n            {\n                // Container was removed, clean up its accessibility div\n                if (child._accessibleDiv && child._accessibleDiv.parentNode)\n                {\n                    child._accessibleDiv.parentNode.removeChild(child._accessibleDiv);\n\n                    this._pool.push(child._accessibleDiv);\n                    child._accessibleDiv = null;\n                }\n                child._accessibleActive = false;\n                removeItems(this._children, i, 1);\n            }\n        }\n\n        // Update root div dimensions if needed\n        if (this._renderer.renderingToScreen)\n        {\n            const { x, y, width: viewWidth, height: viewHeight } = this._renderer.screen;\n            const div = this._div;\n\n            div.style.left = `${x}px`;\n            div.style.top = `${y}px`;\n            div.style.width = `${viewWidth}px`;\n            div.style.height = `${viewHeight}px`;\n        }\n\n        // Update positions of existing divs\n        for (let i = 0; i < this._children.length; i++)\n        {\n            const child = this._children[i];\n\n            if (!child._accessibleActive || !child._accessibleDiv)\n            {\n                continue;\n            }\n\n            // Only update position-related properties\n            const div = child._accessibleDiv;\n            const hitArea = (child.hitArea || child.getBounds().rectangle) as Rectangle;\n\n            if (child.hitArea)\n            {\n                const wt = child.worldTransform;\n                const sx = this._renderer.resolution;\n                const sy = this._renderer.resolution;\n\n                div.style.left = `${(wt.tx + (hitArea.x * wt.a)) * sx}px`;\n                div.style.top = `${(wt.ty + (hitArea.y * wt.d)) * sy}px`;\n                div.style.width = `${hitArea.width * wt.a * sx}px`;\n                div.style.height = `${hitArea.height * wt.d * sy}px`;\n            }\n            else\n            {\n                this._capHitArea(hitArea);\n                const sx = this._renderer.resolution;\n                const sy = this._renderer.resolution;\n\n                div.style.left = `${hitArea.x * sx}px`;\n                div.style.top = `${hitArea.y * sy}px`;\n                div.style.width = `${hitArea.width * sx}px`;\n                div.style.height = `${hitArea.height * sy}px`;\n            }\n        }\n\n        // increment the render id..\n        this._renderId++;\n    }\n\n    /**\n     * private function that will visually add the information to the\n     * accessibility div\n     * @param {HTMLElement} div -\n     */\n    private _updateDebugHTML(div: AccessibleHTMLElement): void\n    {\n        div.innerHTML = `type: ${div.type}</br> title : ${div.title}</br> tabIndex: ${div.tabIndex}`;\n    }\n\n    /**\n     * Adjust the hit area based on the bounds of a display object\n     * @param {Rectangle} hitArea - Bounds of the child\n     */\n    private _capHitArea(hitArea: Rectangle): void\n    {\n        if (hitArea.x < 0)\n        {\n            hitArea.width += hitArea.x;\n            hitArea.x = 0;\n        }\n\n        if (hitArea.y < 0)\n        {\n            hitArea.height += hitArea.y;\n            hitArea.y = 0;\n        }\n\n        const { width: viewWidth, height: viewHeight } = this._renderer;\n\n        if (hitArea.x + hitArea.width > viewWidth)\n        {\n            hitArea.width = viewWidth - hitArea.x;\n        }\n\n        if (hitArea.y + hitArea.height > viewHeight)\n        {\n            hitArea.height = viewHeight - hitArea.y;\n        }\n    }\n\n    /**\n     * Creates or reuses a div element for a Container and adds it to the accessibility layer.\n     * Sets up ARIA attributes, event listeners, and positioning based on the container's properties.\n     * @private\n     * @param {Container} container - The child to make accessible.\n     */\n    private _addChild<T extends Container>(container: T): void\n    {\n        let div = this._pool.pop();\n\n        if (!div)\n        {\n            if (container.accessibleType === 'button')\n            {\n                div = document.createElement('button');\n            }\n            else\n            {\n                div = document.createElement(container.accessibleType);\n                div.style.cssText = `\n                        color: transparent;\n                        pointer-events: none;\n                        padding: 0;\n                        margin: 0;\n                        border: 0;\n                        outline: 0;\n                        background: transparent;\n                        box-sizing: border-box;\n                        user-select: none;\n                        -webkit-user-select: none;\n                        -moz-user-select: none;\n                        -ms-user-select: none;\n                    `;\n                if (container.accessibleText)\n                {\n                    div.innerText = container.accessibleText;\n                }\n            }\n            div.style.width = `${DIV_TOUCH_SIZE}px`;\n            div.style.height = `${DIV_TOUCH_SIZE}px`;\n            div.style.backgroundColor = this.debug ? 'rgba(255,255,255,0.5)' : 'transparent';\n            div.style.position = 'absolute';\n            div.style.zIndex = DIV_TOUCH_ZINDEX.toString();\n            div.style.borderStyle = 'none';\n\n            // ARIA attributes ensure that button title and hint updates are announced properly\n            if (navigator.userAgent.toLowerCase().includes('chrome'))\n            {\n                // Chrome doesn't need aria-live to work as intended; in fact it just gets more confused.\n                div.setAttribute('aria-live', 'off');\n            }\n            else\n            {\n                div.setAttribute('aria-live', 'polite');\n            }\n\n            if (navigator.userAgent.match(/rv:.*Gecko\\//))\n            {\n                // FireFox needs this to announce only the new button name\n                div.setAttribute('aria-relevant', 'additions');\n            }\n            else\n            {\n                // required by IE, other browsers don't much care\n                div.setAttribute('aria-relevant', 'text');\n            }\n\n            div.addEventListener('click', this._onClick.bind(this));\n            div.addEventListener('focus', this._onFocus.bind(this));\n            div.addEventListener('focusout', this._onFocusOut.bind(this));\n        }\n\n        // set pointer events\n        div.style.pointerEvents = container.accessiblePointerEvents;\n        // set the type, this defaults to button!\n        div.type = container.accessibleType;\n\n        if (container.accessibleTitle && container.accessibleTitle !== null)\n        {\n            div.title = container.accessibleTitle;\n        }\n        else if (!container.accessibleHint\n            || container.accessibleHint === null)\n        {\n            div.title = `container ${container.tabIndex}`;\n        }\n\n        if (container.accessibleHint\n            && container.accessibleHint !== null)\n        {\n            div.setAttribute('aria-label', container.accessibleHint);\n        }\n\n        if (this.debug)\n        {\n            this._updateDebugHTML(div);\n        }\n\n        container._accessibleActive = true;\n        container._accessibleDiv = div;\n        div.container = container;\n\n        this._children.push(container);\n        this._div.appendChild(container._accessibleDiv);\n        if (container.interactive)\n        {\n            container._accessibleDiv.tabIndex = container.tabIndex;\n        }\n    }\n\n    /**\n     * Dispatch events with the EventSystem.\n     * @param e\n     * @param type\n     * @private\n     */\n    private _dispatchEvent(e: UIEvent, type: string[]): void\n    {\n        const { container: target } = e.target as AccessibleHTMLElement;\n        const boundary = this._renderer.events.rootBoundary;\n        const event: FederatedEvent = Object.assign(new FederatedEvent(boundary), { target });\n\n        boundary.rootTarget = this._renderer.lastObjectRendered as Container;\n        type.forEach((type) => boundary.dispatchEvent(event, type));\n    }\n\n    /**\n     * Maps the div button press to pixi's EventSystem (click)\n     * @private\n     * @param {MouseEvent} e - The click event.\n     */\n    private _onClick(e: MouseEvent): void\n    {\n        this._dispatchEvent(e, ['click', 'pointertap', 'tap']);\n    }\n\n    /**\n     * Maps the div focus events to pixi's EventSystem (mouseover)\n     * @private\n     * @param {FocusEvent} e - The focus event.\n     */\n    private _onFocus(e: FocusEvent): void\n    {\n        if (!(e.target as Element).getAttribute('aria-live'))\n        {\n            (e.target as Element).setAttribute('aria-live', 'assertive');\n        }\n\n        this._dispatchEvent(e, ['mouseover']);\n    }\n\n    /**\n     * Maps the div focus events to pixi's EventSystem (mouseout)\n     * @private\n     * @param {FocusEvent} e - The focusout event.\n     */\n    private _onFocusOut(e: FocusEvent): void\n    {\n        if (!(e.target as Element).getAttribute('aria-live'))\n        {\n            (e.target as Element).setAttribute('aria-live', 'polite');\n        }\n\n        this._dispatchEvent(e, ['mouseout']);\n    }\n\n    /**\n     * Is called when a key is pressed\n     * @private\n     * @param {KeyboardEvent} e - The keydown event.\n     */\n    private _onKeyDown(e: KeyboardEvent): void\n    {\n        if (e.keyCode !== KEY_CODE_TAB || !this._activateOnTab)\n        {\n            return;\n        }\n\n        this._activate();\n    }\n\n    /**\n     * Is called when the mouse moves across the renderer element\n     * @private\n     * @param {MouseEvent} e - The mouse event.\n     */\n    private _onMouseMove(e: MouseEvent): void\n    {\n        if (e.movementX === 0 && e.movementY === 0)\n        {\n            return;\n        }\n\n        this._deactivate();\n    }\n\n    /**\n     * Destroys the accessibility system. Removes all elements and listeners.\n     * > [!IMPORTANT] This is typically called automatically when the {@link Application} is destroyed.\n     * > A typically user should not need to call this method directly.\n     */\n    public destroy(): void\n    {\n        this._deactivate();\n        this._destroyTouchHook();\n\n        this._div = null;\n        this._pool = null;\n        this._children = null;\n        this._renderer = null;\n\n        if (this._activateOnTab)\n        {\n            globalThis.removeEventListener('keydown', this._onKeyDown);\n        }\n    }\n\n    /**\n     * Enables or disables the accessibility system.\n     * @param enabled - Whether to enable or disable accessibility.\n     * @example\n     * ```js\n     * app.renderer.accessibility.setAccessibilityEnabled(true); // Enable accessibility\n     * app.renderer.accessibility.setAccessibilityEnabled(false); // Disable accessibility\n     * ```\n     */\n    public setAccessibilityEnabled(enabled: boolean): void\n    {\n        if (enabled)\n        {\n            this._activate();\n        }\n        else\n        {\n            this._deactivate();\n        }\n    }\n}\n", "import type { Container } from '../scene/container/Container';\n\n/**\n * The type of the pointer event to listen for.\n * @category accessibility\n * @standard\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/pointer-events\n */\nexport type PointerEvents = 'auto'\n| 'none'\n| 'visiblePainted'\n| 'visibleFill'\n| 'visibleStroke'\n| 'visible'\n| 'painted'\n| 'fill'\n| 'stroke'\n| 'all'\n| 'inherit';\n\n/**\n * When `accessible` is enabled on any display object, these properties will affect its accessibility.\n * @example\n * const container = new Container();\n * container.accessible = true;\n * container.accessibleTitle = 'My Container';\n * container.accessibleHint = 'This is a container';\n * container.tabIndex = 0;\n * @category accessibility\n * @standard\n */\nexport interface AccessibleOptions\n{\n    /**\n     * Flag for if the object is accessible. If true AccessibilityManager will overlay a\n     * shadow div with attributes set\n     * @default false\n     * @example\n     * ```js\n     * const container = new Container();\n     * container.accessible = true;\n     * ```\n     */\n    accessible: boolean;\n    /**\n     * Sets the title attribute of the shadow div\n     * If accessibleTitle AND accessibleHint has not been this will default to 'container [tabIndex]'\n     * @type {string}\n     * @default null\n     * @example\n     * ```js\n     * const container = new Container();\n     * container.accessible = true;\n     * container.accessibleTitle = 'My Container';\n     * ```\n     */\n    accessibleTitle: string | null;\n    /**\n     * Sets the aria-label attribute of the shadow div\n     * @default null\n     * @advanced\n     * @example\n     * ```js\n     * const container = new Container();\n     * container.accessible = true;\n     * container.accessibleHint = 'This is a container';\n     * ```\n     */\n    accessibleHint: string | null;\n    /**\n     * Sets the tabIndex of the shadow div. You can use this to set the order of the\n     * elements when using the tab key to navigate.\n     * @default 0\n     * @example\n     * ```js\n     * const container = new Container();\n     * container.accessible = true;\n     * container.tabIndex = 0;\n     *\n     * const sprite = new Sprite(texture);\n     * sprite.accessible = true;\n     * sprite.tabIndex = 1;\n     * ```\n     */\n    tabIndex: number;\n    /**\n     * Specify the type of div the accessible layer is. Screen readers treat the element differently\n     * depending on this type. Defaults to button.\n     * @default 'button'\n     * @type {string}\n     * @advanced\n     * @example\n     * ```js\n     * const container = new Container();\n     * container.accessible = true;\n     * container.accessibleType = 'button'; // or 'link', 'checkbox', etc.\n     * ```\n     */\n    accessibleType: keyof HTMLElementTagNameMap;\n    /**\n     * Specify the pointer-events the accessible div will use\n     * Defaults to auto.\n     * @default 'auto'\n     * @type {PointerEvents}\n     * @advanced\n     * @example\n     * ```js\n     * const container = new Container();\n     * container.accessible = true;\n     * container.accessiblePointerEvents = 'none'; // or 'auto', 'visiblePainted', etc.\n     * ```\n     */\n    accessiblePointerEvents: PointerEvents;\n\n    /**\n     * Sets the text content of the shadow\n     * @default null\n     * @example\n     * ```js\n     * const container = new Container();\n     * container.accessible = true;\n     * container.accessibleText = 'This is a container';\n     * ```\n     */\n    accessibleText: string | null;\n\n    /**\n     * Setting to false will prevent any children inside this container to\n     * be accessible. Defaults to true.\n     * @default true\n     * @example\n     * ```js\n     * const container = new Container();\n     * container.accessible = true;\n     * container.accessibleChildren = false; // This will prevent any children from being accessible\n     *\n     * const sprite = new Sprite(texture);\n     * sprite.accessible = true; // This will not work since accessibleChildren is false\n     * ```\n     */\n    accessibleChildren: boolean;\n}\n\n/**\n * The Accessibility object is attached to the {@link Container}.\n * @private\n */\nexport interface AccessibleTarget extends AccessibleOptions\n{\n    /** @private */\n    _accessibleActive: boolean;\n    /** @private */\n    _accessibleDiv: AccessibleHTMLElement | null;\n    /** @private */\n    _renderId: number;\n}\n\n/** @internal */\nexport interface AccessibleHTMLElement extends HTMLElement\n{\n    type?: string;\n    container?: Container;\n}\n\n/**\n * Default property values of accessible objects\n * used by {@link AccessibilitySystem}.\n * @internal\n * @example\n * import { accessibleTarget } from 'pixi.js';\n *\n * function MyObject() {}\n * Object.assign(MyObject.prototype, accessibleTarget);\n */\nexport const accessibilityTarget: AccessibleTarget = {\n    accessible: false,\n    accessibleTitle: null,\n    accessibleHint: null,\n    tabIndex: 0,\n    accessibleType: 'button',\n    accessibleText: null,\n    accessiblePointerEvents: 'auto',\n    accessibleChildren: true,\n    _accessibleActive: false,\n    _accessibleDiv: null,\n    _renderId: -1,\n};\n", "import { UPDATE_PRIORITY } from '../ticker/const';\nimport { Ticker } from '../ticker/Ticker';\n\nimport type { EventSystem } from './EventSystem';\n\n/** @advanced */\nclass EventsTickerClass\n{\n    /** The event system. */\n    public events: EventSystem;\n    /** The DOM element to listen to events on. */\n    public domElement: HTMLElement;\n    /** The frequency that fake events will be fired. */\n    public interactionFrequency = 10;\n\n    private _deltaTime = 0;\n    private _didMove = false;\n    private _tickerAdded = false;\n    private _pauseUpdate = true;\n\n    /**\n     * Initializes the event ticker.\n     * @param events - The event system.\n     */\n    public init(events: EventSystem): void\n    {\n        this.removeTickerListener();\n        this.events = events;\n        this.interactionFrequency = 10;\n        this._deltaTime = 0;\n        this._didMove = false;\n        this._tickerAdded = false;\n        this._pauseUpdate = true;\n    }\n\n    /** Whether to pause the update checks or not. */\n    get pauseUpdate(): boolean\n    {\n        return this._pauseUpdate;\n    }\n\n    set pauseUpdate(paused: boolean)\n    {\n        this._pauseUpdate = paused;\n    }\n\n    /** Adds the ticker listener. */\n    public addTickerListener(): void\n    {\n        if (this._tickerAdded || !this.domElement)\n        {\n            return;\n        }\n\n        Ticker.system.add(this._tickerUpdate, this, UPDATE_PRIORITY.INTERACTION);\n\n        this._tickerAdded = true;\n    }\n\n    /** Removes the ticker listener. */\n    public removeTickerListener(): void\n    {\n        if (!this._tickerAdded)\n        {\n            return;\n        }\n\n        Ticker.system.remove(this._tickerUpdate, this);\n\n        this._tickerAdded = false;\n    }\n\n    /** Sets flag to not fire extra events when the user has already moved there mouse */\n    public pointerMoved(): void\n    {\n        this._didMove = true;\n    }\n\n    /** Updates the state of interactive objects. */\n    private _update(): void\n    {\n        if (!this.domElement || this._pauseUpdate)\n        {\n            return;\n        }\n\n        // if the user move the mouse this check has already been done using the mouse move!\n        if (this._didMove)\n        {\n            this._didMove = false;\n\n            return;\n        }\n\n        // eslint-disable-next-line dot-notation\n        const rootPointerEvent = this.events['_rootPointerEvent'];\n\n        if (this.events.supportsTouchEvents && (rootPointerEvent as PointerEvent).pointerType === 'touch')\n        {\n            return;\n        }\n\n        globalThis.document.dispatchEvent(this.events.supportsPointerEvents ? new PointerEvent('pointermove', {\n            clientX: rootPointerEvent.clientX,\n            clientY: rootPointerEvent.clientY,\n            pointerType: rootPointerEvent.pointerType,\n            pointerId: rootPointerEvent.pointerId,\n        }) : new MouseEvent('mousemove', {\n            clientX: rootPointerEvent.clientX,\n            clientY: rootPointerEvent.clientY,\n        }));\n    }\n\n    /**\n     * Updates the state of interactive objects if at least {@link interactionFrequency}\n     * milliseconds have passed since the last invocation.\n     *\n     * Invoked by a throttled ticker update from {@link Ticker.system}.\n     * @param ticker - The throttled ticker.\n     */\n    private _tickerUpdate(ticker: Ticker): void\n    {\n        this._deltaTime += ticker.deltaTime;\n\n        if (this._deltaTime < this.interactionFrequency)\n        {\n            return;\n        }\n\n        this._deltaTime = 0;\n\n        this._update();\n    }\n}\n\n/**\n * This class handles automatic firing of PointerEvents\n * in the case where the pointer is stationary for too long.\n * This is to ensure that hit-tests are still run on moving objects.\n * @since 7.2.0\n * @category events\n * @class\n * @advanced\n */\nexport const EventsTicker = new EventsTickerClass();\n", "import { Point } from '../maths/point/Point';\nimport { FederatedEvent } from './FederatedEvent';\n\nimport type { PointData } from '../maths/point/PointData';\nimport type { Container } from '../scene/container/Container';\nimport type { PixiTouch } from './FederatedEvent';\n\n/**\n * A specialized event class for mouse interactions in PixiJS applications.\n * Extends {@link FederatedEvent} to provide mouse-specific properties and methods\n * while maintaining compatibility with the DOM MouseEvent interface.\n *\n * Key features:\n * - Tracks mouse button states\n * - Provides modifier key states\n * - Supports coordinate systems (client, screen, global)\n * - Enables precise position tracking\n * @example\n * ```ts\n * // Basic mouse event handling\n * sprite.on('mousemove', (event: FederatedMouseEvent) => {\n *     // Get coordinates in different spaces\n *     console.log('Global position:', event.global.x, event.global.y);\n *     console.log('Client position:', event.client.x, event.client.y);\n *     console.log('Screen position:', event.screen.x, event.screen.y);\n *\n *     // Check button and modifier states\n *     if (event.buttons === 1 && event.ctrlKey) {\n *         console.log('Left click + Control key');\n *     }\n *\n *     // Get local coordinates relative to any container\n *     const localPos = event.getLocalPosition(container);\n *     console.log('Local position:', localPos.x, localPos.y);\n * });\n *\n * // Handle mouse button states\n * sprite.on('mousedown', (event: FederatedMouseEvent) => {\n *     console.log('Mouse button:', event.button); // 0=left, 1=middle, 2=right\n *     console.log('Active buttons:', event.buttons);\n * });\n * ```\n * @category events\n * @see {@link FederatedEvent} For base event functionality\n * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/MouseEvent} DOM MouseEvent Interface\n * @standard\n */\nexport class FederatedMouseEvent extends FederatedEvent<\nMouseEvent | PointerEvent | PixiTouch\n> implements MouseEvent\n{\n    /** Whether the \"alt\" key was pressed when this mouse event occurred. */\n    public altKey: boolean;\n\n    /** The specific button that was pressed in this mouse event. */\n    public button: number;\n\n    /** The button depressed when this event occurred. */\n    public buttons: number;\n\n    /** Whether the \"control\" key was pressed when this mouse event occurred. */\n    public ctrlKey: boolean;\n\n    /** Whether the \"meta\" key was pressed when this mouse event occurred. */\n    public metaKey: boolean;\n\n    /** This is currently not implemented in the Federated Events API. */\n    public relatedTarget: EventTarget;\n\n    /** Whether the \"shift\" key was pressed when this mouse event occurred. */\n    public shiftKey: boolean;\n\n    /** The coordinates of the mouse event relative to the canvas. */\n    public client: Point = new Point();\n\n    /** @readonly */\n    public get clientX(): number { return this.client.x; }\n\n    /** @readonly */\n    public get clientY(): number { return this.client.y; }\n\n    /**\n     * Alias for {@link FederatedMouseEvent.clientX this.clientX}.\n     * @readonly\n     */\n    get x(): number { return this.clientX; }\n\n    /**\n     * Alias for {@link FederatedMouseEvent.clientY this.clientY}.\n     * @readonly\n     */\n    get y(): number { return this.clientY; }\n\n    /** This is the number of clicks that occurs in 200ms/click of each other. */\n    public detail: number;\n\n    /** The movement in this pointer relative to the last `mousemove` event. */\n    public movement: Point = new Point();\n\n    /** @readonly */\n    get movementX(): number { return this.movement.x; }\n\n    /** @readonly */\n    get movementY(): number { return this.movement.y; }\n\n    /** The offset of the pointer coordinates w.r.t. target Container in world space. This is not supported at the moment. */\n    public offset: Point = new Point();\n\n    /** @readonly */\n    get offsetX(): number { return this.offset.x; }\n\n    /** @readonly */\n    get offsetY(): number { return this.offset.y; }\n\n    /** The pointer coordinates in world space. */\n    public global: Point = new Point();\n\n    /** @readonly */\n    get globalX(): number { return this.global.x; }\n\n    /** @readonly */\n    get globalY(): number { return this.global.y; }\n\n    /**\n     * The pointer coordinates in the renderer's {@link AbstractRenderer.screen screen}. This has slightly\n     * different semantics than native PointerEvent screenX/screenY.\n     */\n    public screen: Point = new Point();\n\n    /**\n     * The pointer coordinates in the renderer's screen. Alias for `screen.x`.\n     * @readonly\n     */\n    get screenX(): number { return this.screen.x; }\n\n    /**\n     * The pointer coordinates in the renderer's screen. Alias for `screen.y`.\n     * @readonly\n     */\n    get screenY(): number { return this.screen.y; }\n\n    /**\n     * Converts global coordinates into container-local coordinates.\n     *\n     * This method transforms coordinates from world space to a container's local space,\n     * useful for precise positioning and hit testing.\n     * @param container - The Container to get local coordinates for\n     * @param point - Optional Point object to store the result. If not provided, a new Point will be created\n     * @param globalPos - Optional custom global coordinates. If not provided, the event's global position is used\n     * @returns The local coordinates as a Point object\n     * @example\n     * ```ts\n     * // Basic usage - get local coordinates relative to a container\n     * sprite.on('pointermove', (event: FederatedMouseEvent) => {\n     *     // Get position relative to the sprite\n     *     const localPos = event.getLocalPosition(sprite);\n     *     console.log('Local position:', localPos.x, localPos.y);\n     * });\n     * // Using custom global coordinates\n     * const customGlobal = new Point(100, 100);\n     * sprite.on('pointermove', (event: FederatedMouseEvent) => {\n     *     // Transform custom coordinates\n     *     const localPos = event.getLocalPosition(sprite, undefined, customGlobal);\n     *     console.log('Custom local position:', localPos.x, localPos.y);\n     * });\n     * ```\n     * @see {@link Container.worldTransform} For the transformation matrix\n     * @see {@link Point} For the point class used to store coordinates\n     */\n    public getLocalPosition<P extends PointData = Point>(container: Container, point?: P, globalPos?: PointData): P\n    {\n        return container.worldTransform.applyInverse<P>(globalPos || this.global, point);\n    }\n\n    /**\n     * Whether the modifier key was pressed when this event natively occurred.\n     * @param key - The modifier key.\n     */\n    public getModifierState(key: string): boolean\n    {\n        return 'getModifierState' in this.nativeEvent && this.nativeEvent.getModifierState(key);\n    }\n\n    /**\n     * Not supported.\n     * @param _typeArg\n     * @param _canBubbleArg\n     * @param _cancelableArg\n     * @param _viewArg\n     * @param _detailArg\n     * @param _screenXArg\n     * @param _screenYArg\n     * @param _clientXArg\n     * @param _clientYArg\n     * @param _ctrlKeyArg\n     * @param _altKeyArg\n     * @param _shiftKeyArg\n     * @param _metaKeyArg\n     * @param _buttonArg\n     * @param _relatedTargetArg\n     * @deprecated since 7.0.0\n     * @ignore\n     */\n    // eslint-disable-next-line max-params\n    public initMouseEvent(\n        _typeArg: string,\n        _canBubbleArg: boolean,\n        _cancelableArg: boolean,\n        _viewArg: Window,\n        _detailArg: number,\n        _screenXArg: number,\n        _screenYArg: number,\n        _clientXArg: number,\n        _clientYArg: number,\n        _ctrlKeyArg: boolean,\n        _altKeyArg: boolean,\n        _shiftKeyArg: boolean,\n        _metaKeyArg: boolean,\n        _buttonArg: number,\n        _relatedTargetArg: EventTarget\n    ): void\n    {\n        throw new Error('Method not implemented.');\n    }\n}\n", "import { FederatedMouseEvent } from './FederatedMouseEvent';\n\n/**\n * A specialized event class for pointer interactions in PixiJS applications.\n * Extends {@link FederatedMouseEvent} to provide advanced pointer-specific features\n * while maintaining compatibility with the DOM PointerEvent interface.\n *\n * Key features:\n * - Supports multi-touch interactions\n * - Provides pressure sensitivity\n * - Handles stylus input\n * - Tracks pointer dimensions\n * - Supports tilt detection\n * @example\n * ```ts\n * // Basic pointer event handling\n * sprite.on('pointerdown', (event: FederatedPointerEvent) => {\n *     // Access pointer information\n *     console.log('Pointer ID:', event.pointerId);\n *     console.log('Pointer Type:', event.pointerType);\n *     console.log('Is Primary:', event.isPrimary);\n *\n *     // Get pressure and tilt data\n *     console.log('Pressure:', event.pressure);\n *     console.log('Tilt:', event.tiltX, event.tiltY);\n *\n *     // Access contact geometry\n *     console.log('Size:', event.width, event.height);\n * });\n *\n * // Handle stylus-specific features\n * sprite.on('pointermove', (event: FederatedPointerEvent) => {\n *     if (event.pointerType === 'pen') {\n *         // Handle stylus tilt\n *         const tiltAngle = Math.atan2(event.tiltY, event.tiltX);\n *         console.log('Tilt angle:', tiltAngle);\n *\n *         // Use barrel button pressure\n *         console.log('Tangential pressure:', event.tangentialPressure);\n *     }\n * });\n * ```\n * @see {@link FederatedMouseEvent} For base mouse event functionality\n * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/PointerEvent} DOM PointerEvent Interface\n * @see {@link EventSystem} For the event management system\n * @category events\n * @standard\n */\nexport class FederatedPointerEvent extends FederatedMouseEvent implements PointerEvent\n{\n    /**\n     * The unique identifier of the pointer.\n     * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/PointerEvent/pointerId}\n     */\n    public pointerId: number;\n\n    /**\n     * The width of the pointer's contact along the x-axis, measured in CSS pixels.\n     * radiusX of TouchEvents will be represented by this value.\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/PointerEvent/width\n     */\n    public width = 0;\n\n    /**\n     * The angle in radians of a pointer or stylus measuring the vertical angle between\n     * the device's surface to the pointer or stylus.\n     * A stylus at 0 degrees would be directly parallel whereas at π/2 degrees it would be perpendicular.\n     * @see https://developer.mozilla.org/docs/Web/API/PointerEvent/altitudeAngle)\n     */\n    public altitudeAngle: number;\n\n    /**\n     * The angle in radians of a pointer or stylus measuring an arc from the X axis of the device to\n     * the pointer or stylus projected onto the screen's plane.\n     * A stylus at 0 degrees would be pointing to the \"0 o'clock\" whereas at π/2 degrees it would be pointing at \"6 o'clock\".\n     * @see https://developer.mozilla.org/docs/Web/API/PointerEvent/azimuthAngle)\n     */\n    public azimuthAngle: number;\n\n    /**\n     * The height of the pointer's contact along the y-axis, measured in CSS pixels.\n     * radiusY of TouchEvents will be represented by this value.\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/PointerEvent/height\n     */\n    public height = 0;\n\n    /**\n     * Indicates whether or not the pointer device that created the event is the primary pointer.\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/PointerEvent/isPrimary\n     */\n    public isPrimary = false;\n\n    /**\n     * The type of pointer that triggered the event.\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/PointerEvent/pointerType\n     */\n    public pointerType: string;\n\n    /**\n     * Pressure applied by the pointing device during the event.\n     *s\n     * A Touch's force property will be represented by this value.\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/PointerEvent/pressure\n     */\n    public pressure: number;\n\n    /**\n     * Barrel pressure on a stylus pointer.\n     * @see https://w3c.github.io/pointerevents/#pointerevent-interface\n     */\n    public tangentialPressure: number;\n\n    /**\n     * The angle, in degrees, between the pointer device and the screen.\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/PointerEvent/tiltX\n     */\n    public tiltX: number;\n\n    /**\n     * The angle, in degrees, between the pointer device and the screen.\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/PointerEvent/tiltY\n     */\n    public tiltY: number;\n\n    /**\n     * Twist of a stylus pointer.\n     * @see https://w3c.github.io/pointerevents/#pointerevent-interface\n     */\n    public twist: number;\n\n    /** This is the number of clicks that occurs in 200ms/click of each other. */\n    public detail: number;\n\n    /**\n     * Only included for completeness for now\n     * @ignore\n     */\n    public getCoalescedEvents(): PointerEvent[]\n    {\n        if (this.type === 'pointermove' || this.type === 'mousemove' || this.type === 'touchmove')\n        {\n            return [this];\n        }\n\n        return [];\n    }\n\n    /**\n     * Only included for completeness for now\n     * @ignore\n     */\n    public getPredictedEvents(): PointerEvent[]\n    {\n        throw new Error('getPredictedEvents is not supported!');\n    }\n}\n", "import { FederatedMouseEvent } from './FederatedMouseEvent';\n\n/**\n * A specialized event class for wheel/scroll interactions in PixiJS applications.\n * Extends {@link FederatedMouseEvent} to provide wheel-specific properties while\n * maintaining compatibility with the DOM WheelEvent interface.\n *\n * Key features:\n * - Provides scroll delta information\n * - Supports different scroll modes (pixel, line, page)\n * - Inherits mouse event properties\n * - Normalizes cross-browser wheel events\n * @example\n * ```ts\n * // Basic wheel event handling\n * sprite.on('wheel', (event: FederatedWheelEvent) => {\n *     // Get scroll amount\n *     console.log('Vertical scroll:', event.deltaY);\n *     console.log('Horizontal scroll:', event.deltaX);\n *\n *     // Check scroll mode\n *     if (event.deltaMode === FederatedWheelEvent.DOM_DELTA_LINE) {\n *         console.log('Scrolling by lines');\n *     } else if (event.deltaMode === FederatedWheelEvent.DOM_DELTA_PAGE) {\n *         console.log('Scrolling by pages');\n *     } else {\n *         console.log('Scrolling by pixels');\n *     }\n *\n *     // Get scroll position\n *     console.log('Scroll at:', event.global.x, event.global.y);\n * });\n *\n * // Common use case: Zoom control\n * container.on('wheel', (event: FederatedWheelEvent) => {\n *     // Prevent page scrolling\n *     event.preventDefault();\n *\n *     // Zoom in/out based on scroll direction\n *     const zoomFactor = 1 + (event.deltaY / 1000);\n *     container.scale.set(container.scale.x * zoomFactor);\n * });\n * ```\n * @see {@link FederatedMouseEvent} For base mouse event functionality\n * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/WheelEvent} DOM WheelEvent Interface\n * @see {@link EventSystem} For the event management system\n * @category events\n * @standard\n */\nexport class FederatedWheelEvent extends FederatedMouseEvent implements WheelEvent\n{\n    /**\n     * The units of `deltaX`, `deltaY`, and `deltaZ`. This is one of `DOM_DELTA_LINE`,\n     * `DOM_DELTA_PAGE`, `DOM_DELTA_PIXEL`.\n     */\n    public deltaMode: number;\n\n    /** Horizontal scroll amount */\n    public deltaX: number;\n\n    /** Vertical scroll amount */\n    public deltaY: number;\n\n    /** z-axis scroll amount. */\n    public deltaZ: number;\n\n    /**\n     * Units specified in pixels.\n     * @ignore\n     */\n    public static readonly DOM_DELTA_PIXEL = 0;\n\n    /**\n     * Units specified in pixels.\n     * @ignore\n     */\n    public readonly DOM_DELTA_PIXEL = 0;\n\n    /**\n     * Units specified in lines.\n     * @ignore\n     */\n    public static readonly DOM_DELTA_LINE = 1;\n\n    /**\n     * Units specified in lines.\n     * @ignore\n     */\n    public readonly DOM_DELTA_LINE = 1;\n\n    /**\n     * Units specified in pages.\n     * @ignore\n     */\n    public static readonly DOM_DELTA_PAGE = 2;\n\n    /**\n     * Units specified in pages.\n     * @ignore\n     */\n    public readonly DOM_DELTA_PAGE = 2;\n}\n", "import EventEmitter from 'eventemitter3';\nimport { Point } from '../maths/point/Point';\nimport { warn } from '../utils/logging/warn';\nimport { EventsTicker } from './EventTicker';\nimport { FederatedMouseEvent } from './FederatedMouseEvent';\nimport { FederatedPointerEvent } from './FederatedPointerEvent';\nimport { FederatedWheelEvent } from './FederatedWheelEvent';\n\nimport type { Renderable } from '../rendering/renderers/shared/Renderable';\nimport type { Container } from '../scene/container/Container';\nimport type { EmitterListeners, TrackingData } from './EventBoundaryTypes';\nimport type { FederatedEvent } from './FederatedEvent';\nimport type {\n    <PERSON>ursor, EventMode, FederatedEventHandler,\n} from './FederatedEventTarget';\n\n// The maximum iterations used in propagation. This prevent infinite loops.\nconst PROPAGATION_LIMIT = 2048;\n\nconst tempHitLocation = new Point();\nconst tempLocalMapping = new Point();\n\n/**\n * Event boundaries are \"barriers\" where events coming from an upstream scene are modified before downstream propagation.\n *\n * ## Root event boundary\n *\n * The {@link EventSystem#rootBoundary rootBoundary} handles events coming from the &lt;canvas /&gt;.\n * {@link EventSystem} handles the normalization from native {@link https://dom.spec.whatwg.org/#event Events}\n * into {@link FederatedEvent FederatedEvents}. The rootBoundary then does the hit-testing and event dispatch\n * for the upstream normalized event.\n *\n * ## Additional event boundaries\n *\n * An additional event boundary may be desired within an application's scene graph. For example, if a portion of the scene is\n * is flat with many children at one level - a spatial hash maybe needed to accelerate hit testing. In this scenario, the\n * container can be detached from the scene and glued using a custom event boundary.\n *\n * ```ts\n * import { Container } from 'pixi.js';\n * import { EventBoundary } from 'pixi.js';\n * import { SpatialHash } from 'pixi-spatial-hash';\n *\n * class HashedHitTestingEventBoundary\n * {\n *     private spatialHash: SpatialHash;\n *\n *     constructor(scene: Container, spatialHash: SpatialHash)\n *     {\n *         super(scene);\n *         this.spatialHash = spatialHash;\n *     }\n *\n *     hitTestRecursive(...)\n *     {\n *         // TODO: If target === this.rootTarget, then use spatial hash to get a\n *         // list of possible children that match the given (x,y) coordinates.\n *     }\n * }\n *\n * class VastScene extends Container\n * {\n *     protected eventBoundary: EventBoundary;\n *     protected scene: Container;\n *     protected spatialHash: SpatialHash;\n *\n *     constructor()\n *     {\n *         this.scene = new Container();\n *         this.spatialHash = new SpatialHash();\n *         this.eventBoundary = new HashedHitTestingEventBoundary(this.scene, this.spatialHash);\n *\n *         // Populate this.scene with a ton of children, while updating this.spatialHash\n *     }\n * }\n * ```\n * @category events\n * @advanced\n */\nexport class EventBoundary\n{\n    /**\n     * The root event-target residing below the event boundary.\n     * All events are dispatched trickling down and bubbling up to this `rootTarget`.\n     */\n    public rootTarget: Container;\n\n    /**\n     * Emits events after they were dispatched into the scene graph.\n     *\n     * This can be used for global events listening, regardless of the scene graph being used. It should\n     * not be used by interactive libraries for normal use.\n     *\n     * Special events that do not bubble all the way to the root target are not emitted from here,\n     * e.g. pointerenter, pointerleave, click.\n     */\n    public dispatch: EventEmitter = new EventEmitter();\n\n    /** The cursor preferred by the event targets underneath this boundary. */\n    public cursor: Cursor | (string & {});\n\n    /**\n     * This flag would emit `pointermove`, `touchmove`, and `mousemove` events on all Containers.\n     *\n     * The `moveOnAll` semantics mirror those of earlier versions of PixiJS. This was disabled in favor of\n     * the Pointer Event API's approach.\n     */\n    public moveOnAll = false;\n\n    /** Enables the global move events. `globalpointermove`, `globaltouchmove`, and `globalmousemove` */\n    public enableGlobalMoveEvents = true;\n\n    /**\n     * Maps event types to forwarding handles for them.\n     *\n     * {@link EventBoundary EventBoundary} provides mapping for \"pointerdown\", \"pointermove\",\n     * \"pointerout\", \"pointerleave\", \"pointerover\", \"pointerup\", and \"pointerupoutside\" by default.\n     * @see EventBoundary#addEventMapping\n     */\n    protected mappingTable: Record<string, Array<{\n        fn: (e: FederatedEvent) => void,\n        priority: number\n    }>>;\n\n    /**\n     * State object for mapping methods.\n     * @see EventBoundary#trackingData\n     */\n    protected mappingState: Record<string, any> = {\n        trackingData: {}\n    };\n\n    /**\n     * The event pool maps event constructors to an free pool of instances of those specific events.\n     * @see EventBoundary#allocateEvent\n     * @see EventBoundary#freeEvent\n     */\n    protected eventPool: Map<typeof FederatedEvent, FederatedEvent[]> = new Map();\n\n    /** Every interactive element gathered from the scene. Only used in `pointermove` */\n    private readonly _allInteractiveElements: Container[] = [];\n    /** Every element that passed the hit test. Only used in `pointermove` */\n    private _hitElements: Container[] = [];\n    /** Whether or not to collect all the interactive elements from the scene. Enabled in `pointermove` */\n    private _isPointerMoveEvent = false;\n\n    /**\n     * @param rootTarget - The holder of the event boundary.\n     */\n    constructor(rootTarget?: Container)\n    {\n        this.rootTarget = rootTarget;\n\n        this.hitPruneFn = this.hitPruneFn.bind(this);\n        this.hitTestFn = this.hitTestFn.bind(this);\n        this.mapPointerDown = this.mapPointerDown.bind(this);\n        this.mapPointerMove = this.mapPointerMove.bind(this);\n        this.mapPointerOut = this.mapPointerOut.bind(this);\n        this.mapPointerOver = this.mapPointerOver.bind(this);\n        this.mapPointerUp = this.mapPointerUp.bind(this);\n        this.mapPointerUpOutside = this.mapPointerUpOutside.bind(this);\n        this.mapWheel = this.mapWheel.bind(this);\n\n        this.mappingTable = {};\n        this.addEventMapping('pointerdown', this.mapPointerDown);\n        this.addEventMapping('pointermove', this.mapPointerMove);\n        this.addEventMapping('pointerout', this.mapPointerOut);\n        this.addEventMapping('pointerleave', this.mapPointerOut);\n        this.addEventMapping('pointerover', this.mapPointerOver);\n        this.addEventMapping('pointerup', this.mapPointerUp);\n        this.addEventMapping('pointerupoutside', this.mapPointerUpOutside);\n        this.addEventMapping('wheel', this.mapWheel);\n    }\n\n    /**\n     * Adds an event mapping for the event `type` handled by `fn`.\n     *\n     * Event mappings can be used to implement additional or custom events. They take an event\n     * coming from the upstream scene (or directly from the {@link EventSystem}) and dispatch new downstream events\n     * generally trickling down and bubbling up to {@link EventBoundary.rootTarget this.rootTarget}.\n     *\n     * To modify the semantics of existing events, the built-in mapping methods of EventBoundary should be overridden\n     * instead.\n     * @param type - The type of upstream event to map.\n     * @param fn - The mapping method. The context of this function must be bound manually, if desired.\n     */\n    public addEventMapping(type: string, fn: (e: FederatedEvent) => void): void\n    {\n        if (!this.mappingTable[type])\n        {\n            this.mappingTable[type] = [];\n        }\n\n        this.mappingTable[type].push({\n            fn,\n            priority: 0,\n        });\n        this.mappingTable[type].sort((a, b) => a.priority - b.priority);\n    }\n\n    /**\n     * Dispatches the given event\n     * @param e - The event to dispatch.\n     * @param type - The type of event to dispatch. Defaults to `e.type`.\n     */\n    public dispatchEvent(e: FederatedEvent, type?: string): void\n    {\n        e.propagationStopped = false;\n        e.propagationImmediatelyStopped = false;\n\n        this.propagate(e, type);\n        this.dispatch.emit(type || e.type, e);\n    }\n\n    /**\n     * Maps the given upstream event through the event boundary and propagates it downstream.\n     * @param e - The event to map.\n     */\n    public mapEvent(e: FederatedEvent): void\n    {\n        if (!this.rootTarget)\n        {\n            return;\n        }\n\n        const mappers = this.mappingTable[e.type];\n\n        if (mappers)\n        {\n            for (let i = 0, j = mappers.length; i < j; i++)\n            {\n                mappers[i].fn(e);\n            }\n        }\n        else\n        {\n            // #if _DEBUG\n            warn(`[EventBoundary]: Event mapping not defined for ${e.type}`);\n            // #endif\n        }\n    }\n\n    /**\n     * Finds the Container that is the target of a event at the given coordinates.\n     *\n     * The passed (x,y) coordinates are in the world space above this event boundary.\n     * @param x - The x coordinate of the event.\n     * @param y - The y coordinate of the event.\n     */\n    public hitTest(\n        x: number,\n        y: number,\n    ): Container\n    {\n        EventsTicker.pauseUpdate = true;\n        // if we are using global move events, we need to hit test the whole scene graph\n        const useMove = this._isPointerMoveEvent && this.enableGlobalMoveEvents;\n        const fn = useMove ? 'hitTestMoveRecursive' : 'hitTestRecursive';\n        const invertedPath = this[fn](\n            this.rootTarget,\n            this.rootTarget.eventMode,\n            tempHitLocation.set(x, y),\n            this.hitTestFn,\n            this.hitPruneFn,\n        );\n\n        return invertedPath && invertedPath[0];\n    }\n\n    /**\n     * Propagate the passed event from from {@link EventBoundary.rootTarget this.rootTarget} to its\n     * target `e.target`.\n     * @param e - The event to propagate.\n     * @param type - The type of event to propagate. Defaults to `e.type`.\n     */\n    public propagate(e: FederatedEvent, type?: string): void\n    {\n        if (!e.target)\n        {\n            // This usually occurs when the scene graph is not interactive.\n            return;\n        }\n\n        const composedPath = e.composedPath();\n\n        // Capturing phase\n        e.eventPhase = e.CAPTURING_PHASE;\n\n        for (let i = 0, j = composedPath.length - 1; i < j; i++)\n        {\n            e.currentTarget = composedPath[i];\n\n            this.notifyTarget(e, type);\n\n            if (e.propagationStopped || e.propagationImmediatelyStopped) return;\n        }\n\n        // At target phase\n        e.eventPhase = e.AT_TARGET;\n        e.currentTarget = e.target;\n\n        this.notifyTarget(e, type);\n\n        if (e.propagationStopped || e.propagationImmediatelyStopped) return;\n\n        // Bubbling phase\n        e.eventPhase = e.BUBBLING_PHASE;\n\n        for (let i = composedPath.length - 2; i >= 0; i--)\n        {\n            e.currentTarget = composedPath[i];\n\n            this.notifyTarget(e, type);\n\n            if (e.propagationStopped || e.propagationImmediatelyStopped) return;\n        }\n    }\n\n    /**\n     * Emits the event `e` to all interactive containers. The event is propagated in the bubbling phase always.\n     *\n     * This is used in the `globalpointermove` event.\n     * @param e - The emitted event.\n     * @param type - The listeners to notify.\n     * @param targets - The targets to notify.\n     */\n    public all(e: FederatedEvent, type?: string | string[], targets = this._allInteractiveElements): void\n    {\n        if (targets.length === 0) return;\n\n        e.eventPhase = e.BUBBLING_PHASE;\n\n        const events = Array.isArray(type) ? type : [type];\n\n        // loop through all interactive elements and notify them of the event\n        // loop through targets backwards\n        for (let i = targets.length - 1; i >= 0; i--)\n        {\n            events.forEach((event) =>\n            {\n                e.currentTarget = targets[i];\n                this.notifyTarget(e, event);\n            });\n        }\n    }\n\n    /**\n     * Finds the propagation path from {@link EventBoundary.rootTarget rootTarget} to the passed\n     * `target`. The last element in the path is `target`.\n     * @param target - The target to find the propagation path to.\n     */\n    public propagationPath(target: Container): Container[]\n    {\n        const propagationPath = [target];\n\n        for (let i = 0; i < PROPAGATION_LIMIT && (target !== this.rootTarget && target.parent); i++)\n        {\n            if (!target.parent)\n            {\n                throw new Error('Cannot find propagation path to disconnected target');\n            }\n\n            propagationPath.push(target.parent);\n\n            target = target.parent;\n        }\n\n        propagationPath.reverse();\n\n        return propagationPath;\n    }\n\n    protected hitTestMoveRecursive(\n        currentTarget: Container,\n        eventMode: EventMode,\n        location: Point,\n        testFn: (object: Container, pt: Point) => boolean,\n        pruneFn: (object: Container, pt: Point) => boolean,\n        ignore = false\n    ): Container[]\n    {\n        let shouldReturn = false;\n\n        // only bail out early if it is not interactive\n        if (this._interactivePrune(currentTarget)) return null;\n\n        if (currentTarget.eventMode === 'dynamic' || eventMode === 'dynamic')\n        {\n            EventsTicker.pauseUpdate = false;\n        }\n\n        if (currentTarget.interactiveChildren && currentTarget.children)\n        {\n            const children = currentTarget.children;\n\n            for (let i = children.length - 1; i >= 0; i--)\n            {\n                const child = children[i] as Container;\n\n                const nestedHit = this.hitTestMoveRecursive(\n                    child,\n                    this._isInteractive(eventMode) ? eventMode : child.eventMode,\n                    location,\n                    testFn,\n                    pruneFn,\n                    ignore || pruneFn(currentTarget, location)\n                );\n\n                if (nestedHit)\n                {\n                    // Its a good idea to check if a child has lost its parent.\n                    // this means it has been removed whilst looping so its best\n                    if (nestedHit.length > 0 && !nestedHit[nestedHit.length - 1].parent)\n                    {\n                        continue;\n                    }\n\n                    // Only add the current hit-test target to the hit-test chain if the chain\n                    // has already started (i.e. the event target has been found) or if the current\n                    // target is interactive (i.e. it becomes the event target).\n                    const isInteractive = currentTarget.isInteractive();\n\n                    if (nestedHit.length > 0 || isInteractive)\n                    {\n                        if (isInteractive) this._allInteractiveElements.push(currentTarget);\n                        nestedHit.push(currentTarget);\n                    }\n\n                    // store all hit elements to be returned once we have traversed the whole tree\n                    if (this._hitElements.length === 0) this._hitElements = nestedHit;\n\n                    shouldReturn = true;\n                }\n            }\n        }\n\n        const isInteractiveMode = this._isInteractive(eventMode);\n        const isInteractiveTarget = currentTarget.isInteractive();\n\n        if (isInteractiveTarget && isInteractiveTarget) this._allInteractiveElements.push(currentTarget);\n\n        // we don't carry on hit testing something once we have found a hit,\n        // now only care about gathering the interactive elements\n        if (ignore || this._hitElements.length > 0) return null;\n\n        if (shouldReturn) return this._hitElements as Container[];\n\n        // Finally, hit test this Container itself.\n        if (isInteractiveMode && (!pruneFn(currentTarget, location) && testFn(currentTarget, location)))\n        {\n            // The current hit-test target is the event's target only if it is interactive. Otherwise,\n            // the first interactive ancestor will be the event's target.\n            return isInteractiveTarget ? [currentTarget] : [];\n        }\n\n        return null;\n    }\n\n    /**\n     * Recursive implementation for {@link EventBoundary.hitTest hitTest}.\n     * @param currentTarget - The Container that is to be hit tested.\n     * @param eventMode - The event mode for the `currentTarget` or one of its parents.\n     * @param location - The location that is being tested for overlap.\n     * @param testFn - Callback that determines whether the target passes hit testing. This callback\n     *  can assume that `pruneFn` failed to prune the container.\n     * @param pruneFn - Callback that determiness whether the target and all of its children\n     *  cannot pass the hit test. It is used as a preliminary optimization to prune entire subtrees\n     *  of the scene graph.\n     * @returns An array holding the hit testing target and all its ancestors in order. The first element\n     *  is the target itself and the last is {@link EventBoundary.rootTarget rootTarget}. This is the opposite\n     *  order w.r.t. the propagation path. If no hit testing target is found, null is returned.\n     */\n    protected hitTestRecursive(\n        currentTarget: Container,\n        eventMode: EventMode,\n        location: Point,\n        testFn: (object: Container, pt: Point) => boolean,\n        pruneFn: (object: Container, pt: Point) => boolean\n    ): Container[]\n    {\n        // Attempt to prune this Container and its subtree as an optimization.\n        if (this._interactivePrune(currentTarget) || pruneFn(currentTarget, location))\n        {\n            return null;\n        }\n        if (currentTarget.eventMode === 'dynamic' || eventMode === 'dynamic')\n        {\n            EventsTicker.pauseUpdate = false;\n        }\n\n        // Find a child that passes the hit testing and return one, if any.\n        if (currentTarget.interactiveChildren && currentTarget.children)\n        {\n            const children = currentTarget.children;\n            const relativeLocation = location;\n\n            for (let i = children.length - 1; i >= 0; i--)\n            {\n                const child = children[i] as Container;\n\n                const nestedHit = this.hitTestRecursive(\n                    child,\n                    this._isInteractive(eventMode) ? eventMode : child.eventMode,\n                    relativeLocation,\n                    testFn,\n                    pruneFn\n                );\n\n                if (nestedHit)\n                {\n                    // Its a good idea to check if a child has lost its parent.\n                    // this means it has been removed whilst looping so its best\n                    if (nestedHit.length > 0 && !nestedHit[nestedHit.length - 1].parent)\n                    {\n                        continue;\n                    }\n\n                    // Only add the current hit-test target to the hit-test chain if the chain\n                    // has already started (i.e. the event target has been found) or if the current\n                    // target is interactive (i.e. it becomes the event target).\n                    const isInteractive = currentTarget.isInteractive();\n\n                    if (nestedHit.length > 0 || isInteractive) nestedHit.push(currentTarget);\n\n                    return nestedHit;\n                }\n            }\n        }\n\n        const isInteractiveMode = this._isInteractive(eventMode);\n        const isInteractiveTarget = currentTarget.isInteractive();\n\n        // Finally, hit test this Container itself.\n        if (isInteractiveMode && testFn(currentTarget, location))\n        {\n            // The current hit-test target is the event's target only if it is interactive. Otherwise,\n            // the first interactive ancestor will be the event's target.\n            return isInteractiveTarget ? [currentTarget] : [];\n        }\n\n        return null;\n    }\n\n    private _isInteractive(int: EventMode): int is 'static' | 'dynamic'\n    {\n        return int === 'static' || int === 'dynamic';\n    }\n\n    private _interactivePrune(container: Container): boolean\n    {\n        // If container is a mask, invisible, or not renderable then it cannot be hit directly.\n        if (!container || !container.visible || !container.renderable || !container.measurable)\n        {\n            return true;\n        }\n\n        // If this Container is none then it cannot be hit by anything.\n        if (container.eventMode === 'none')\n        {\n            return true;\n        }\n\n        // If this Container is passive and it has no interactive children then it cannot be hit\n        if (container.eventMode === 'passive' && !container.interactiveChildren)\n        {\n            return true;\n        }\n\n        return false;\n    }\n\n    /**\n     * Checks whether the container or any of its children cannot pass the hit test at all.\n     *\n     * {@link EventBoundary}'s implementation uses the {@link Container.hitArea hitArea}\n     * and {@link Container._maskEffect} for pruning.\n     * @param container - The container to prune.\n     * @param location - The location to test for overlap.\n     */\n    protected hitPruneFn(container: Container, location: Point): boolean\n    {\n        if (container.hitArea)\n        {\n            container.worldTransform.applyInverse(location, tempLocalMapping);\n\n            if (!container.hitArea.contains(tempLocalMapping.x, tempLocalMapping.y))\n            {\n                return true;\n            }\n        }\n\n        if (container.effects && container.effects.length)\n        {\n            for (let i = 0; i < container.effects.length; i++)\n            {\n                const effect = container.effects[i];\n\n                if (effect.containsPoint)\n                {\n                    const effectContainsPoint = effect.containsPoint(location, this.hitTestFn);\n\n                    if (!effectContainsPoint)\n                    {\n                        return true;\n                    }\n                }\n            }\n        }\n\n        return false;\n    }\n\n    /**\n     * Checks whether the container passes hit testing for the given location.\n     * @param container - The container to test.\n     * @param location - The location to test for overlap.\n     * @returns - Whether `container` passes hit testing for `location`.\n     */\n    protected hitTestFn(container: Container, location: Point): boolean\n    {\n        // If the container failed pruning with a hitArea, then it must pass it.\n        if (container.hitArea)\n        {\n            return true;\n        }\n\n        if ((container as Renderable)?.containsPoint)\n        {\n            container.worldTransform.applyInverse(location, tempLocalMapping);\n\n            return (container as Renderable).containsPoint(tempLocalMapping) as boolean;\n        }\n\n        // TODO: Should we hit test based on bounds?\n\n        return false;\n    }\n\n    /**\n     * Notify all the listeners to the event's `currentTarget`.\n     *\n     * If the `currentTarget` contains the property `on<type>`, then it is called here,\n     * simulating the behavior from version 6.x and prior.\n     * @param e - The event passed to the target.\n     * @param type - The type of event to notify. Defaults to `e.type`.\n     */\n    protected notifyTarget(e: FederatedEvent, type?: string): void\n    {\n        if (!e.currentTarget.isInteractive())\n        {\n            return;\n        }\n\n        type ??= e.type;\n\n        // call the `on${type}` for the current target if it exists\n        const handlerKey = `on${type}` as keyof Container;\n\n        (e.currentTarget[handlerKey] as FederatedEventHandler<FederatedEvent>)?.(e);\n\n        const key = e.eventPhase === e.CAPTURING_PHASE || e.eventPhase === e.AT_TARGET ? `${type}capture` : type;\n\n        this._notifyListeners(e, key);\n\n        if (e.eventPhase === e.AT_TARGET)\n        {\n            this._notifyListeners(e, type);\n        }\n    }\n\n    /**\n     * Maps the upstream `pointerdown` events to a downstream `pointerdown` event.\n     *\n     * `touchstart`, `rightdown`, `mousedown` events are also dispatched for specific pointer types.\n     * @param from - The upstream `pointerdown` event.\n     */\n    protected mapPointerDown(from: FederatedEvent): void\n    {\n        if (!(from instanceof FederatedPointerEvent))\n        {\n            // #if _DEBUG\n            warn('EventBoundary cannot map a non-pointer event as a pointer event');\n            // #endif\n\n            return;\n        }\n\n        const e = this.createPointerEvent(from);\n\n        this.dispatchEvent(e, 'pointerdown');\n\n        if (e.pointerType === 'touch')\n        {\n            this.dispatchEvent(e, 'touchstart');\n        }\n        else if (e.pointerType === 'mouse' || e.pointerType === 'pen')\n        {\n            const isRightButton = e.button === 2;\n\n            this.dispatchEvent(e, isRightButton ? 'rightdown' : 'mousedown');\n        }\n\n        const trackingData = this.trackingData(from.pointerId);\n\n        trackingData.pressTargetsByButton[from.button] = e.composedPath();\n\n        this.freeEvent(e);\n    }\n\n    /**\n     * Maps the upstream `pointermove` to downstream `pointerout`, `pointerover`, and `pointermove` events, in that order.\n     *\n     * The tracking data for the specific pointer has an updated `overTarget`. `mouseout`, `mouseover`,\n     * `mousemove`, and `touchmove` events are fired as well for specific pointer types.\n     * @param from - The upstream `pointermove` event.\n     */\n    protected mapPointerMove(from: FederatedEvent): void\n    {\n        if (!(from instanceof FederatedPointerEvent))\n        {\n            // #if _DEBUG\n            warn('EventBoundary cannot map a non-pointer event as a pointer event');\n            // #endif\n\n            return;\n        }\n\n        this._allInteractiveElements.length = 0;\n        this._hitElements.length = 0;\n        this._isPointerMoveEvent = true;\n        const e = this.createPointerEvent(from);\n\n        this._isPointerMoveEvent = false;\n        const isMouse = e.pointerType === 'mouse' || e.pointerType === 'pen';\n        const trackingData = this.trackingData(from.pointerId);\n        const outTarget = this.findMountedTarget(trackingData.overTargets);\n\n        // First pointerout/pointerleave\n        if (trackingData.overTargets?.length > 0 && outTarget !== e.target)\n        {\n            // pointerout always occurs on the overTarget when the pointer hovers over another element.\n            const outType = from.type === 'mousemove' ? 'mouseout' : 'pointerout';\n            const outEvent = this.createPointerEvent(from, outType, outTarget);\n\n            this.dispatchEvent(outEvent, 'pointerout');\n            if (isMouse) this.dispatchEvent(outEvent, 'mouseout');\n\n            // If the pointer exits overTarget and its descendants, then a pointerleave event is also fired. This event\n            // is dispatched to all ancestors that no longer capture the pointer.\n            if (!e.composedPath().includes(outTarget))\n            {\n                const leaveEvent = this.createPointerEvent(from, 'pointerleave', outTarget);\n\n                leaveEvent.eventPhase = leaveEvent.AT_TARGET;\n\n                while (leaveEvent.target && !e.composedPath().includes(leaveEvent.target))\n                {\n                    leaveEvent.currentTarget = leaveEvent.target;\n\n                    this.notifyTarget(leaveEvent);\n                    if (isMouse) this.notifyTarget(leaveEvent, 'mouseleave');\n\n                    leaveEvent.target = leaveEvent.target.parent;\n                }\n\n                this.freeEvent(leaveEvent);\n            }\n\n            this.freeEvent(outEvent);\n        }\n\n        // Then pointerover\n        if (outTarget !== e.target)\n        {\n            // pointerover always occurs on the new overTarget\n            const overType = from.type === 'mousemove' ? 'mouseover' : 'pointerover';\n            const overEvent = this.clonePointerEvent(e, overType);// clone faster\n\n            this.dispatchEvent(overEvent, 'pointerover');\n            if (isMouse) this.dispatchEvent(overEvent, 'mouseover');\n\n            // Probe whether the newly hovered Container is an ancestor of the original overTarget.\n            let overTargetAncestor = outTarget?.parent;\n\n            while (overTargetAncestor && overTargetAncestor !== this.rootTarget.parent)\n            {\n                if (overTargetAncestor === e.target) break;\n\n                overTargetAncestor = overTargetAncestor.parent;\n            }\n\n            // The pointer has entered a non-ancestor of the original overTarget. This means we need a pointerentered\n            // event.\n            const didPointerEnter = !overTargetAncestor || overTargetAncestor === this.rootTarget.parent;\n\n            if (didPointerEnter)\n            {\n                const enterEvent = this.clonePointerEvent(e, 'pointerenter');\n\n                enterEvent.eventPhase = enterEvent.AT_TARGET;\n\n                while (enterEvent.target\n                        && enterEvent.target !== outTarget\n                        && enterEvent.target !== this.rootTarget.parent)\n                {\n                    enterEvent.currentTarget = enterEvent.target;\n\n                    this.notifyTarget(enterEvent);\n                    if (isMouse) this.notifyTarget(enterEvent, 'mouseenter');\n\n                    enterEvent.target = enterEvent.target.parent;\n                }\n\n                this.freeEvent(enterEvent);\n            }\n\n            this.freeEvent(overEvent);\n        }\n\n        const allMethods: string[] = [];\n        const allowGlobalPointerEvents = this.enableGlobalMoveEvents ?? true;\n\n        this.moveOnAll ? allMethods.push('pointermove') : this.dispatchEvent(e, 'pointermove');\n        allowGlobalPointerEvents && allMethods.push('globalpointermove');\n\n        // Then pointermove\n        if (e.pointerType === 'touch')\n        {\n            this.moveOnAll ? allMethods.splice(1, 0, 'touchmove') : this.dispatchEvent(e, 'touchmove');\n            allowGlobalPointerEvents && allMethods.push('globaltouchmove');\n        }\n\n        if (isMouse)\n        {\n            this.moveOnAll ? allMethods.splice(1, 0, 'mousemove') : this.dispatchEvent(e, 'mousemove');\n            allowGlobalPointerEvents && allMethods.push('globalmousemove');\n            this.cursor = e.target?.cursor;\n        }\n\n        if (allMethods.length > 0)\n        {\n            this.all(e, allMethods);\n        }\n        this._allInteractiveElements.length = 0;\n        this._hitElements.length = 0;\n\n        trackingData.overTargets = e.composedPath();\n\n        this.freeEvent(e);\n    }\n\n    /**\n     * Maps the upstream `pointerover` to downstream `pointerover` and `pointerenter` events, in that order.\n     *\n     * The tracking data for the specific pointer gets a new `overTarget`.\n     * @param from - The upstream `pointerover` event.\n     */\n    protected mapPointerOver(from: FederatedEvent): void\n    {\n        if (!(from instanceof FederatedPointerEvent))\n        {\n            // #if _DEBUG\n            warn('EventBoundary cannot map a non-pointer event as a pointer event');\n            // #endif\n\n            return;\n        }\n\n        const trackingData = this.trackingData(from.pointerId);\n        const e = this.createPointerEvent(from);\n        const isMouse = e.pointerType === 'mouse' || e.pointerType === 'pen';\n\n        this.dispatchEvent(e, 'pointerover');\n        if (isMouse) this.dispatchEvent(e, 'mouseover');\n        if (e.pointerType === 'mouse') this.cursor = e.target?.cursor;\n\n        // pointerenter events must be fired since the pointer entered from upstream.\n        const enterEvent = this.clonePointerEvent(e, 'pointerenter');\n\n        enterEvent.eventPhase = enterEvent.AT_TARGET;\n\n        while (enterEvent.target && enterEvent.target !== this.rootTarget.parent)\n        {\n            enterEvent.currentTarget = enterEvent.target;\n\n            this.notifyTarget(enterEvent);\n            if (isMouse) this.notifyTarget(enterEvent, 'mouseenter');\n\n            enterEvent.target = enterEvent.target.parent;\n        }\n\n        trackingData.overTargets = e.composedPath();\n\n        this.freeEvent(e);\n        this.freeEvent(enterEvent);\n    }\n\n    /**\n     * Maps the upstream `pointerout` to downstream `pointerout`, `pointerleave` events, in that order.\n     *\n     * The tracking data for the specific pointer is cleared of a `overTarget`.\n     * @param from - The upstream `pointerout` event.\n     */\n    protected mapPointerOut(from: FederatedEvent): void\n    {\n        if (!(from instanceof FederatedPointerEvent))\n        {\n            // #if _DEBUG\n            warn('EventBoundary cannot map a non-pointer event as a pointer event');\n            // #endif\n\n            return;\n        }\n\n        const trackingData = this.trackingData(from.pointerId);\n\n        if (trackingData.overTargets)\n        {\n            const isMouse = from.pointerType === 'mouse' || from.pointerType === 'pen';\n            const outTarget = this.findMountedTarget(trackingData.overTargets);\n\n            // pointerout first\n            const outEvent = this.createPointerEvent(from, 'pointerout', outTarget);\n\n            this.dispatchEvent(outEvent);\n            if (isMouse) this.dispatchEvent(outEvent, 'mouseout');\n\n            // pointerleave(s) are also dispatched b/c the pointer must've left rootTarget and its descendants to\n            // get an upstream pointerout event (upstream events do not know rootTarget has descendants).\n            const leaveEvent = this.createPointerEvent(from, 'pointerleave', outTarget);\n\n            leaveEvent.eventPhase = leaveEvent.AT_TARGET;\n\n            while (leaveEvent.target && leaveEvent.target !== this.rootTarget.parent)\n            {\n                leaveEvent.currentTarget = leaveEvent.target;\n\n                this.notifyTarget(leaveEvent);\n                if (isMouse) this.notifyTarget(leaveEvent, 'mouseleave');\n\n                leaveEvent.target = leaveEvent.target.parent;\n            }\n\n            trackingData.overTargets = null;\n\n            this.freeEvent(outEvent);\n            this.freeEvent(leaveEvent);\n        }\n\n        this.cursor = null;\n    }\n\n    /**\n     * Maps the upstream `pointerup` event to downstream `pointerup`, `pointerupoutside`,\n     * and `click`/`rightclick`/`pointertap` events, in that order.\n     *\n     * The `pointerupoutside` event bubbles from the original `pointerdown` target to the most specific\n     * ancestor of the `pointerdown` and `pointerup` targets, which is also the `click` event's target. `touchend`,\n     * `rightup`, `mouseup`, `touchendoutside`, `rightupoutside`, `mouseupoutside`, and `tap` are fired as well for\n     * specific pointer types.\n     * @param from - The upstream `pointerup` event.\n     */\n    protected mapPointerUp(from: FederatedEvent): void\n    {\n        if (!(from instanceof FederatedPointerEvent))\n        {\n            // #if _DEBUG\n            warn('EventBoundary cannot map a non-pointer event as a pointer event');\n            // #endif\n\n            return;\n        }\n\n        const now = performance.now();\n        const e = this.createPointerEvent(from);\n\n        this.dispatchEvent(e, 'pointerup');\n\n        if (e.pointerType === 'touch')\n        {\n            this.dispatchEvent(e, 'touchend');\n        }\n        else if (e.pointerType === 'mouse' || e.pointerType === 'pen')\n        {\n            const isRightButton = e.button === 2;\n\n            this.dispatchEvent(e, isRightButton ? 'rightup' : 'mouseup');\n        }\n\n        const trackingData = this.trackingData(from.pointerId);\n        const pressTarget = this.findMountedTarget(trackingData.pressTargetsByButton[from.button]);\n\n        let clickTarget = pressTarget;\n\n        // pointerupoutside only bubbles. It only bubbles upto the parent that doesn't contain\n        // the pointerup location.\n        if (pressTarget && !e.composedPath().includes(pressTarget))\n        {\n            let currentTarget = pressTarget;\n\n            while (currentTarget && !e.composedPath().includes(currentTarget))\n            {\n                e.currentTarget = currentTarget;\n\n                this.notifyTarget(e, 'pointerupoutside');\n\n                if (e.pointerType === 'touch')\n                {\n                    this.notifyTarget(e, 'touchendoutside');\n                }\n                else if (e.pointerType === 'mouse' || e.pointerType === 'pen')\n                {\n                    const isRightButton = e.button === 2;\n\n                    this.notifyTarget(e, isRightButton ? 'rightupoutside' : 'mouseupoutside');\n                }\n\n                currentTarget = currentTarget.parent;\n            }\n\n            delete trackingData.pressTargetsByButton[from.button];\n\n            // currentTarget is the most specific ancestor holding both the pointerdown and pointerup\n            // targets. That is - it's our click target!\n            clickTarget = currentTarget;\n        }\n\n        // click!\n        if (clickTarget)\n        {\n            const clickEvent = this.clonePointerEvent(e, 'click');\n\n            clickEvent.target = clickTarget;\n            clickEvent.path = null;\n\n            if (!trackingData.clicksByButton[from.button])\n            {\n                trackingData.clicksByButton[from.button] = {\n                    clickCount: 0,\n                    target: clickEvent.target,\n                    timeStamp: now,\n                };\n            }\n\n            const clickHistory = trackingData.clicksByButton[from.button];\n\n            if (clickHistory.target === clickEvent.target\n                && now - clickHistory.timeStamp < 200)\n            {\n                ++clickHistory.clickCount;\n            }\n            else\n            {\n                clickHistory.clickCount = 1;\n            }\n\n            clickHistory.target = clickEvent.target;\n            clickHistory.timeStamp = now;\n\n            clickEvent.detail = clickHistory.clickCount;\n\n            if (clickEvent.pointerType === 'mouse')\n            {\n                const isRightButton = clickEvent.button === 2;\n\n                this.dispatchEvent(clickEvent, isRightButton ? 'rightclick' : 'click');\n            }\n            else if (clickEvent.pointerType === 'touch')\n            {\n                this.dispatchEvent(clickEvent, 'tap');\n            }\n\n            this.dispatchEvent(clickEvent, 'pointertap');\n\n            this.freeEvent(clickEvent);\n        }\n\n        this.freeEvent(e);\n    }\n\n    /**\n     * Maps the upstream `pointerupoutside` event to a downstream `pointerupoutside` event, bubbling from the original\n     * `pointerdown` target to `rootTarget`.\n     *\n     * (The most specific ancestor of the `pointerdown` event and the `pointerup` event must the\n     * `{@link EventBoundary}'s root because the `pointerup` event occurred outside of the boundary.)\n     *\n     * `touchendoutside`, `mouseupoutside`, and `rightupoutside` events are fired as well for specific pointer\n     * types. The tracking data for the specific pointer is cleared of a `pressTarget`.\n     * @param from - The upstream `pointerupoutside` event.\n     */\n    protected mapPointerUpOutside(from: FederatedEvent): void\n    {\n        if (!(from instanceof FederatedPointerEvent))\n        {\n            // #if _DEBUG\n            warn('EventBoundary cannot map a non-pointer event as a pointer event');\n            // #endif\n\n            return;\n        }\n\n        const trackingData = this.trackingData(from.pointerId);\n        const pressTarget = this.findMountedTarget(trackingData.pressTargetsByButton[from.button]);\n        const e = this.createPointerEvent(from);\n\n        if (pressTarget)\n        {\n            let currentTarget = pressTarget;\n\n            while (currentTarget)\n            {\n                e.currentTarget = currentTarget;\n\n                this.notifyTarget(e, 'pointerupoutside');\n\n                if (e.pointerType === 'touch')\n                {\n                    this.notifyTarget(e, 'touchendoutside');\n                }\n                else if (e.pointerType === 'mouse' || e.pointerType === 'pen')\n                {\n                    this.notifyTarget(e, e.button === 2 ? 'rightupoutside' : 'mouseupoutside');\n                }\n\n                currentTarget = currentTarget.parent;\n            }\n\n            delete trackingData.pressTargetsByButton[from.button];\n        }\n\n        this.freeEvent(e);\n    }\n\n    /**\n     * Maps the upstream `wheel` event to a downstream `wheel` event.\n     * @param from - The upstream `wheel` event.\n     */\n    protected mapWheel(from: FederatedEvent): void\n    {\n        if (!(from instanceof FederatedWheelEvent))\n        {\n            // #if _DEBUG\n            warn('EventBoundary cannot map a non-wheel event as a wheel event');\n            // #endif\n\n            return;\n        }\n\n        const wheelEvent = this.createWheelEvent(from);\n\n        this.dispatchEvent(wheelEvent);\n        this.freeEvent(wheelEvent);\n    }\n\n    /**\n     * Finds the most specific event-target in the given propagation path that is still mounted in the scene graph.\n     *\n     * This is used to find the correct `pointerup` and `pointerout` target in the case that the original `pointerdown`\n     * or `pointerover` target was unmounted from the scene graph.\n     * @param propagationPath - The propagation path was valid in the past.\n     * @returns - The most specific event-target still mounted at the same location in the scene graph.\n     */\n    protected findMountedTarget(propagationPath: Container[]): Container\n    {\n        if (!propagationPath)\n        {\n            return null;\n        }\n\n        let currentTarget = propagationPath[0];\n\n        for (let i = 1; i < propagationPath.length; i++)\n        {\n            // Set currentTarget to the next target in the path only if it is still attached to the\n            // scene graph (i.e. parent still points to the expected ancestor).\n            if (propagationPath[i].parent === currentTarget)\n            {\n                currentTarget = propagationPath[i];\n            }\n            else\n            {\n                break;\n            }\n        }\n\n        return currentTarget;\n    }\n\n    /**\n     * Creates an event whose `originalEvent` is `from`, with an optional `type` and `target` override.\n     *\n     * The event is allocated using {@link EventBoundary#allocateEvent this.allocateEvent}.\n     * @param from - The `originalEvent` for the returned event.\n     * @param [type=from.type] - The type of the returned event.\n     * @param target - The target of the returned event.\n     */\n    protected createPointerEvent(\n        from: FederatedPointerEvent,\n        type?: string,\n        target?: Container\n    ): FederatedPointerEvent\n    {\n        const event = this.allocateEvent(FederatedPointerEvent);\n\n        this.copyPointerData(from, event);\n        this.copyMouseData(from, event);\n        this.copyData(from, event);\n\n        event.nativeEvent = from.nativeEvent;\n        event.originalEvent = from;\n        event.target = target\n            ?? this.hitTest(event.global.x, event.global.y) as Container\n            ?? this._hitElements[0];\n\n        if (typeof type === 'string')\n        {\n            event.type = type;\n        }\n\n        return event;\n    }\n\n    /**\n     * Creates a wheel event whose `originalEvent` is `from`.\n     *\n     * The event is allocated using {@link EventBoundary#allocateEvent this.allocateEvent}.\n     * @param from - The upstream wheel event.\n     */\n    protected createWheelEvent(from: FederatedWheelEvent): FederatedWheelEvent\n    {\n        const event = this.allocateEvent(FederatedWheelEvent);\n\n        this.copyWheelData(from, event);\n        this.copyMouseData(from, event);\n        this.copyData(from, event);\n\n        event.nativeEvent = from.nativeEvent;\n        event.originalEvent = from;\n        event.target = this.hitTest(event.global.x, event.global.y);\n\n        return event;\n    }\n\n    /**\n     * Clones the event `from`, with an optional `type` override.\n     *\n     * The event is allocated using {@link EventBoundary#allocateEvent this.allocateEvent}.\n     * @param from - The event to clone.\n     * @param [type=from.type] - The type of the returned event.\n     */\n    protected clonePointerEvent(from: FederatedPointerEvent, type?: string): FederatedPointerEvent\n    {\n        const event = this.allocateEvent(FederatedPointerEvent);\n\n        event.nativeEvent = from.nativeEvent;\n        event.originalEvent = from.originalEvent;\n\n        this.copyPointerData(from, event);\n        this.copyMouseData(from, event);\n        this.copyData(from, event);\n\n        // copy propagation path for perf\n        event.target = from.target;\n        event.path = from.composedPath().slice();\n        event.type = type ?? event.type;\n\n        return event;\n    }\n\n    /**\n     * Copies wheel {@link FederatedWheelEvent} data from `from` into `to`.\n     *\n     * The following properties are copied:\n     * + deltaMode\n     * + deltaX\n     * + deltaY\n     * + deltaZ\n     * @param from - The event to copy data from.\n     * @param to - The event to copy data into.\n     */\n    protected copyWheelData(from: FederatedWheelEvent, to: FederatedWheelEvent): void\n    {\n        to.deltaMode = from.deltaMode;\n        to.deltaX = from.deltaX;\n        to.deltaY = from.deltaY;\n        to.deltaZ = from.deltaZ;\n    }\n\n    /**\n     * Copies pointer {@link FederatedPointerEvent} data from `from` into `to`.\n     *\n     * The following properties are copied:\n     * + pointerId\n     * + width\n     * + height\n     * + isPrimary\n     * + pointerType\n     * + pressure\n     * + tangentialPressure\n     * + tiltX\n     * + tiltY\n     * @param from - The event to copy data from.\n     * @param to - The event to copy data into.\n     */\n    protected copyPointerData(from: FederatedEvent, to: FederatedEvent): void\n    {\n        if (!(from instanceof FederatedPointerEvent && to instanceof FederatedPointerEvent)) return;\n\n        to.pointerId = from.pointerId;\n        to.width = from.width;\n        to.height = from.height;\n        to.isPrimary = from.isPrimary;\n        to.pointerType = from.pointerType;\n        to.pressure = from.pressure;\n        to.tangentialPressure = from.tangentialPressure;\n        to.tiltX = from.tiltX;\n        to.tiltY = from.tiltY;\n        to.twist = from.twist;\n    }\n\n    /**\n     * Copies mouse {@link FederatedMouseEvent} data from `from` to `to`.\n     *\n     * The following properties are copied:\n     * + altKey\n     * + button\n     * + buttons\n     * + clientX\n     * + clientY\n     * + metaKey\n     * + movementX\n     * + movementY\n     * + pageX\n     * + pageY\n     * + x\n     * + y\n     * + screen\n     * + shiftKey\n     * + global\n     * @param from - The event to copy data from.\n     * @param to - The event to copy data into.\n     */\n    protected copyMouseData(from: FederatedEvent, to: FederatedEvent): void\n    {\n        if (!(from instanceof FederatedMouseEvent && to instanceof FederatedMouseEvent)) return;\n\n        to.altKey = from.altKey;\n        to.button = from.button;\n        to.buttons = from.buttons;\n        to.client.copyFrom(from.client);\n        to.ctrlKey = from.ctrlKey;\n        to.metaKey = from.metaKey;\n        to.movement.copyFrom(from.movement);\n        to.screen.copyFrom(from.screen);\n        to.shiftKey = from.shiftKey;\n        to.global.copyFrom(from.global);\n    }\n\n    /**\n     * Copies base {@link FederatedEvent} data from `from` into `to`.\n     *\n     * The following properties are copied:\n     * + isTrusted\n     * + srcElement\n     * + timeStamp\n     * + type\n     * @param from - The event to copy data from.\n     * @param to - The event to copy data into.\n     */\n    protected copyData(from: FederatedEvent, to: FederatedEvent): void\n    {\n        to.isTrusted = from.isTrusted;\n        to.srcElement = from.srcElement;\n        to.timeStamp = performance.now();\n        to.type = from.type;\n        to.detail = from.detail;\n        to.view = from.view;\n        to.which = from.which;\n        to.layer.copyFrom(from.layer);\n        to.page.copyFrom(from.page);\n    }\n\n    /**\n     * @param id - The pointer ID.\n     * @returns The tracking data stored for the given pointer. If no data exists, a blank\n     *  state will be created.\n     */\n    protected trackingData(id: number): TrackingData\n    {\n        if (!this.mappingState.trackingData[id])\n        {\n            this.mappingState.trackingData[id] = {\n                pressTargetsByButton: {},\n                clicksByButton: {},\n                overTarget: null\n            };\n        }\n\n        return this.mappingState.trackingData[id];\n    }\n\n    /**\n     * Allocate a specific type of event from {@link EventBoundary#eventPool this.eventPool}.\n     *\n     * This allocation is constructor-agnostic, as long as it only takes one argument - this event\n     * boundary.\n     * @param constructor - The event's constructor.\n     */\n    protected allocateEvent<T extends FederatedEvent>(\n        constructor: { new(boundary: EventBoundary): T }\n    ): T\n    {\n        if (!this.eventPool.has(constructor as any))\n        {\n            this.eventPool.set(constructor as any, []);\n        }\n\n        const event = this.eventPool.get(constructor as any).pop() as T\n            || new constructor(this);\n\n        event.eventPhase = event.NONE;\n        event.currentTarget = null;\n        event.defaultPrevented = false;\n        event.path = null;\n        event.target = null;\n\n        return event;\n    }\n\n    /**\n     * Frees the event and puts it back into the event pool.\n     *\n     * It is illegal to reuse the event until it is allocated again, using `this.allocateEvent`.\n     *\n     * It is also advised that events not allocated from {@link EventBoundary#allocateEvent this.allocateEvent}\n     * not be freed. This is because of the possibility that the same event is freed twice, which can cause\n     * it to be allocated twice & result in overwriting.\n     * @param event - The event to be freed.\n     * @throws Error if the event is managed by another event boundary.\n     */\n    protected freeEvent<T extends FederatedEvent>(event: T): void\n    {\n        if (event.manager !== this) throw new Error('It is illegal to free an event not managed by this EventBoundary!');\n\n        const constructor = event.constructor;\n\n        if (!this.eventPool.has(constructor as any))\n        {\n            this.eventPool.set(constructor as any, []);\n        }\n\n        this.eventPool.get(constructor as any).push(event);\n    }\n\n    /**\n     * Similar to {@link EventEmitter.emit}, except it stops if the `propagationImmediatelyStopped` flag\n     * is set on the event.\n     * @param e - The event to call each listener with.\n     * @param type - The event key.\n     */\n    private _notifyListeners(e: FederatedEvent, type: string): void\n    {\n        const listeners = ((e.currentTarget as any)._events as EmitterListeners)[type];\n\n        if (!listeners) return;\n\n        if ('fn' in listeners)\n        {\n            if (listeners.once) e.currentTarget.removeListener(type, listeners.fn, undefined, true);\n            listeners.fn.call(listeners.context, e);\n        }\n        else\n        {\n            for (\n                let i = 0, j = listeners.length;\n                i < j && !e.propagationImmediatelyStopped;\n                i++)\n            {\n                if (listeners[i].once) e.currentTarget.removeListener(type, listeners[i].fn, undefined, true);\n                listeners[i].fn.call(listeners[i].context, e);\n            }\n        }\n    }\n}\n", "import { ExtensionType } from '../extensions/Extensions';\nimport { EventBoundary } from './EventBoundary';\nimport { EventsTicker } from './EventTicker';\nimport { FederatedPointerEvent } from './FederatedPointerEvent';\nimport { FederatedWheelEvent } from './FederatedWheelEvent';\n\nimport type { ExtensionMetadata } from '../extensions/Extensions';\nimport type { PointData } from '../maths/point/PointData';\nimport type { System } from '../rendering/renderers/shared/system/System';\nimport type { Renderer } from '../rendering/renderers/types';\nimport type { PixiTouch } from './FederatedEvent';\nimport type { EventMode } from './FederatedEventTarget';\nimport type { FederatedMouseEvent } from './FederatedMouseEvent';\n\nconst MOUSE_POINTER_ID = 1;\nconst TOUCH_TO_POINTER: Record<string, string> = {\n    touchstart: 'pointerdown',\n    touchend: 'pointerup',\n    touchendoutside: 'pointerupoutside',\n    touchmove: 'pointermove',\n    touchcancel: 'pointercancel',\n};\n\n/**\n * Options for configuring the PixiJS event system. These options control how the event system\n * handles different types of interactions and event propagation.\n * @example\n * ```ts\n * // Basic event system configuration\n * const app = new Application();\n * await app.init({\n *     // Configure default interaction mode\n *     eventMode: 'static',\n *\n *     // Configure event features\n *     eventFeatures: {\n *         move: true,           // Enable pointer movement events\n *         globalMove: false,    // Disable global move events\n *         click: true,          // Enable click events\n *         wheel: true          // Enable wheel/scroll events\n *     }\n * });\n *\n * // Access event system after initialization\n * const eventSystem = app.renderer.events;\n * console.log(eventSystem.features); // Check enabled features\n * ```\n * @see {@link EventSystem} For the main event system implementation\n * @see {@link EventMode} For interaction mode details\n * @see {@link EventSystemFeatures} For all available feature options\n * @advanced\n * @category events\n */\nexport interface EventSystemOptions\n{\n    /**\n     * The default event mode for all display objects.\n     * Controls how objects respond to interaction events.\n     *\n     * Possible values:\n     * - `'none'`: No interaction events\n     * - `'passive'`: Only container's children receive events (default)\n     * - `'auto'`: Receives events when parent is interactive\n     * - `'static'`: Standard interaction events\n     * - `'dynamic'`: Like static but with additional synthetic events\n     * @default 'passive'\n     */\n    eventMode?: EventMode;\n\n    /**\n     * Configuration for enabling/disabling specific event features.\n     * Use this to optimize performance by turning off unused functionality.\n     * @example\n     * ```ts\n     * const app = new Application();\n     * await app.init({\n     *     eventFeatures: {\n     *         // Core interaction events\n     *         move: true,        // Pointer/mouse/touch movement\n     *         click: true,       // Click/tap events\n     *         wheel: true,       // Mouse wheel/scroll events\n     *         // Global tracking\n     *         globalMove: false  // Global pointer movement\n     *     }\n     * });\n     * ```\n     */\n    eventFeatures?: Partial<EventSystemFeatures>;\n}\n\n/**\n * The event features that are enabled by the EventSystem. These features control\n * different types of interaction events in your PixiJS application.\n * @example\n * ```ts\n * // Configure features during application initialization\n * const app = new Application();\n * await app.init({\n *     eventFeatures: {\n *         // Basic interaction events\n *         move: true,        // Enable pointer movement tracking\n *         click: true,       // Enable click/tap events\n *         wheel: true,       // Enable mouse wheel/scroll events\n *         // Advanced features\n *         globalMove: false  // Disable global move tracking for performance\n *     }\n * });\n *\n * // Or configure after initialization\n * app.renderer.events.features.move = false;      // Disable movement events\n * app.renderer.events.features.globalMove = true; // Enable global tracking\n * ```\n * @since 7.2.0\n * @category events\n * @advanced\n */\nexport interface EventSystemFeatures\n{\n    /**\n     * Enables pointer events associated with pointer movement.\n     *\n     * When enabled, these events will fire:\n     * - `pointermove` / `mousemove` / `touchmove`\n     * - `pointerout` / `mouseout`\n     * - `pointerover` / `mouseover`\n     * @example\n     * ```ts\n     * // Enable movement events\n     * app.renderer.events.features.move = true;\n     *\n     * // Listen for movement\n     * sprite.on('pointermove', (event) => {\n     *     console.log('Pointer position:', event.global.x, event.global.y);\n     * });\n     * ```\n     * @default true\n     */\n    move: boolean;\n\n    /**\n     * Enables global pointer move events that fire regardless of target.\n     *\n     * When enabled, these events will fire:\n     * - `globalpointermove`\n     * - `globalmousemove`\n     * - `globaltouchmove`\n     * @example\n     * ```ts\n     * // Enable global tracking\n     * app.renderer.events.features.globalMove = true;\n     *\n     * // Track pointer globally\n     * sprite.on('globalpointermove', (event) => {\n     *     // Fires even when pointer is not over sprite\n     *     console.log('Global position:', event.global.x, event.global.y);\n     * });\n     * ```\n     * @default true\n     */\n    globalMove: boolean;\n    /**\n     * Enables pointer events associated with clicking/tapping.\n     *\n     * When enabled, these events will fire:\n     * - `pointerdown` / `mousedown` / `touchstart` / `rightdown`\n     * - `pointerup` / `mouseup` / `touchend` / `rightup`\n     * - `pointerupoutside` / `mouseupoutside` / `touchendoutside` / `rightupoutside`\n     * - `click` / `tap`\n     * @example\n     * ```ts\n     * // Enable click events\n     * app.renderer.events.features.click = true;\n     *\n     * // Handle clicks\n     * sprite.on('click', (event) => {\n     *     console.log('Clicked at:', event.global.x, event.global.y);\n     * });\n     * ```\n     * @default true\n     */\n    click: boolean;\n    /**\n     * Enables mouse wheel/scroll events.\n     * @example\n     * ```ts\n     * // Enable wheel events\n     * app.renderer.events.features.wheel = true;\n     *\n     * // Handle scrolling\n     * sprite.on('wheel', (event) => {\n     *     // Zoom based on scroll direction\n     *     const scale = 1 + (event.deltaY / 1000);\n     *     sprite.scale.set(sprite.scale.x * scale);\n     * });\n     * ```\n     * @default true\n     */\n    wheel: boolean;\n}\n\n/**\n * The system for handling UI events in PixiJS applications. This class manages mouse, touch, and pointer events,\n * normalizing them into a consistent event model.\n * @example\n * ```ts\n * // Access event system through renderer\n * const eventSystem = app.renderer.events;\n *\n * // Configure event features\n * eventSystem.features.globalMove = false;  // Disable global move events\n * eventSystem.features.click = true;        // Enable click events\n *\n * // Set custom cursor styles\n * eventSystem.cursorStyles.default = 'pointer';\n * eventSystem.cursorStyles.grab = 'grab';\n *\n * // Get current pointer position\n * const pointer = eventSystem.pointer;\n * console.log(pointer.global.x, pointer.global.y);\n * ```\n *\n * Features:\n * - Normalizes browser events into consistent format\n * - Supports mouse, touch, and pointer events\n * - Handles event delegation and bubbling\n * - Provides cursor management\n * - Configurable event features\n * @see {@link EventBoundary} For event propagation and handling\n * @see {@link FederatedEvent} For the base event class\n * @see {@link EventMode} For interaction modes\n * @category events\n * @standard\n */\nexport class EventSystem implements System<EventSystemOptions>\n{\n    /** @ignore */\n    public static extension: ExtensionMetadata = {\n        name: 'events',\n        type: [\n            ExtensionType.WebGLSystem,\n            ExtensionType.CanvasSystem,\n            ExtensionType.WebGPUSystem,\n        ],\n        priority: -1,\n    };\n\n    /**\n     * The event features that are enabled by the EventSystem\n     * @since 7.2.0\n     * @example\n     * ```ts\n     * import { EventSystem, EventSystemFeatures } from 'pixi.js';\n     * // Access the default event features\n     * EventSystem.defaultEventFeatures = {\n     *     // Enable pointer movement events\n     *     move: true,\n     *     // Enable global pointer move events\n     *     globalMove: true,\n     *     // Enable click events\n     *     click: true,\n     *     // Enable wheel events\n     *     wheel: true,\n     * };\n     * ```\n     */\n    public static defaultEventFeatures: EventSystemFeatures = {\n        /** Enables pointer events associated with pointer movement. */\n        move: true,\n        /** Enables global pointer move events. */\n        globalMove: true,\n        /** Enables pointer events associated with clicking. */\n        click: true,\n        /** Enables wheel events. */\n        wheel: true,\n    };\n\n    private static _defaultEventMode: EventMode;\n\n    /**\n     * The default interaction mode for all display objects.\n     * @see Container.eventMode\n     * @type {EventMode}\n     * @readonly\n     * @since 7.2.0\n     */\n    public static get defaultEventMode()\n    {\n        return this._defaultEventMode;\n    }\n\n    /**\n     * The {@link EventBoundary} for the stage.\n     *\n     * The {@link EventBoundary#rootTarget rootTarget} of this root boundary is automatically set to\n     * the last rendered object before any event processing is initiated. This means the main scene\n     * needs to be rendered atleast once before UI events will start propagating.\n     *\n     * The root boundary should only be changed during initialization. Otherwise, any state held by the\n     * event boundary may be lost (like hovered & pressed Containers).\n     * @advanced\n     */\n    public readonly rootBoundary: EventBoundary;\n\n    /**\n     * Indicates whether the current device supports touch events according to the W3C Touch Events spec.\n     * This is used to determine the appropriate event handling strategy.\n     * @see {@link https://www.w3.org/TR/touch-events/} W3C Touch Events Specification\n     * @readonly\n     * @default 'ontouchstart' in globalThis\n     */\n    public readonly supportsTouchEvents = 'ontouchstart' in globalThis;\n\n    /**\n     * Indicates whether the current device supports pointer events according to the W3C Pointer Events spec.\n     * Used to optimize event handling and provide more consistent cross-device interaction.\n     * @see {@link https://www.w3.org/TR/pointerevents/} W3C Pointer Events Specification\n     * @readonly\n     * @default !!globalThis.PointerEvent\n     */\n    public readonly supportsPointerEvents = !!globalThis.PointerEvent;\n\n    /**\n     * Controls whether default browser actions are automatically prevented on pointer events.\n     * When true, prevents default browser actions from occurring on pointer events.\n     * @remarks\n     * - Does not apply to pointer events for backwards compatibility\n     * - preventDefault on pointer events stops mouse events from firing\n     * - For every pointer event, there will always be either a mouse or touch event alongside it\n     * - Setting this to false allows default browser actions (text selection, dragging images, etc.)\n     * @example\n     * ```ts\n     * // Allow default browser actions\n     * app.renderer.events.autoPreventDefault = false;\n     *\n     * // Block default actions (default)\n     * app.renderer.events.autoPreventDefault = true;\n     *\n     * // Example with text selection\n     * const text = new Text('Selectable text');\n     * text.eventMode = 'static';\n     * app.renderer.events.autoPreventDefault = false; // Allow text selection\n     * ```\n     * @default true\n     */\n    public autoPreventDefault: boolean;\n\n    /**\n     * Dictionary of custom cursor styles that can be used across the application.\n     * Used to define how different cursor modes are handled when interacting with display objects.\n     * @example\n     * ```ts\n     * // Access event system through renderer\n     * const eventSystem = app.renderer.events;\n     *\n     * // Set string-based cursor styles\n     * eventSystem.cursorStyles.default = 'pointer';\n     * eventSystem.cursorStyles.hover = 'grab';\n     * eventSystem.cursorStyles.drag = 'grabbing';\n     *\n     * // Use CSS object for complex styling\n     * eventSystem.cursorStyles.custom = {\n     *     cursor: 'url(\"custom.png\") 2 2, auto',\n     *     userSelect: 'none'\n     * };\n     *\n     * // Use a url for custom cursors\n     * const defaultIcon = 'url(\\'https://pixijs.com/assets/bunny.png\\'),auto';\n     * eventSystem.cursorStyles.icon = defaultIcon;\n     *\n     * // Use callback function for dynamic cursors\n     * eventSystem.cursorStyles.dynamic = (mode) => {\n     *     // Update cursor based on mode\n     *     document.body.style.cursor = mode === 'hover'\n     *         ? 'pointer'\n     *         : 'default';\n     * };\n     *\n     * // Apply cursor style to a sprite\n     * sprite.cursor = 'hover'; // Will use the hover style defined above\n     * sprite.cursor = 'icon'; // Will apply the icon cursor\n     * sprite.cursor = 'custom'; // Will apply the custom CSS styles\n     * sprite.cursor = 'drag'; // Will apply the grabbing cursor\n     * sprite.cursor = 'default'; // Will apply the default pointer cursor\n     * sprite.cursor = 'dynamic'; // Will call the dynamic function\n     * ```\n     * @remarks\n     * - Strings are treated as CSS cursor values\n     * - Objects are applied as CSS styles to the DOM element\n     * - Functions are called directly for custom cursor handling\n     * - Default styles for 'default' and 'pointer' are provided\n     * @default\n     * ```ts\n     * {\n     *     default: 'inherit',\n     *     pointer: 'pointer' // Default cursor styles\n     * }\n     * ```\n     */\n    public cursorStyles: Record<string, string | ((mode: string) => void) | CSSStyleDeclaration>;\n\n    /**\n     * The DOM element to which the root event listeners are bound. This is automatically set to\n     * the renderer's {@link Renderer#view view}.\n     */\n    public domElement: HTMLElement = null;\n\n    /** The resolution used to convert between the DOM client space into world space. */\n    public resolution = 1;\n\n    /** The renderer managing this {@link EventSystem}. */\n    public renderer: Renderer;\n\n    /**\n     * The event features that are enabled by the EventSystem\n     * @since 7.2.0\n     * @example\n     * const app = new Application()\n     * app.renderer.events.features.globalMove = false\n     *\n     * // to override all features use Object.assign\n     * Object.assign(app.renderer.events.features, {\n     *  move: false,\n     *  globalMove: false,\n     *  click: false,\n     *  wheel: false,\n     * })\n     */\n    public readonly features: EventSystemFeatures;\n\n    private _currentCursor: string;\n    private readonly _rootPointerEvent: FederatedPointerEvent;\n    private readonly _rootWheelEvent: FederatedWheelEvent;\n    private _eventsAdded: boolean;\n\n    /**\n     * @param {Renderer} renderer\n     */\n    constructor(renderer: Renderer)\n    {\n        this.renderer = renderer;\n        this.rootBoundary = new EventBoundary(null);\n        EventsTicker.init(this);\n\n        this.autoPreventDefault = true;\n        this._eventsAdded = false;\n\n        this._rootPointerEvent = new FederatedPointerEvent(null);\n        this._rootWheelEvent = new FederatedWheelEvent(null);\n\n        this.cursorStyles = {\n            default: 'inherit',\n            pointer: 'pointer',\n        };\n\n        this.features = new Proxy({ ...EventSystem.defaultEventFeatures }, {\n            set: (target, key, value) =>\n            {\n                if (key === 'globalMove')\n                {\n                    this.rootBoundary.enableGlobalMoveEvents = value;\n                }\n                target[key as keyof EventSystemFeatures] = value;\n\n                return true;\n            }\n        });\n\n        this._onPointerDown = this._onPointerDown.bind(this);\n        this._onPointerMove = this._onPointerMove.bind(this);\n        this._onPointerUp = this._onPointerUp.bind(this);\n        this._onPointerOverOut = this._onPointerOverOut.bind(this);\n        this.onWheel = this.onWheel.bind(this);\n    }\n\n    /**\n     * Runner init called, view is available at this point.\n     * @ignore\n     */\n    public init(options: EventSystemOptions): void\n    {\n        const { canvas, resolution } = this.renderer;\n\n        this.setTargetElement(canvas as HTMLCanvasElement);\n        this.resolution = resolution;\n        EventSystem._defaultEventMode = options.eventMode ?? 'passive';\n        Object.assign(this.features, options.eventFeatures ?? {});\n        this.rootBoundary.enableGlobalMoveEvents = this.features.globalMove;\n    }\n\n    /**\n     * Handle changing resolution.\n     * @ignore\n     */\n    public resolutionChange(resolution: number): void\n    {\n        this.resolution = resolution;\n    }\n\n    /** Destroys all event listeners and detaches the renderer. */\n    public destroy(): void\n    {\n        this.setTargetElement(null);\n        this.renderer = null;\n        this._currentCursor = null;\n    }\n\n    /**\n     * Sets the current cursor mode, handling any callbacks or CSS style changes.\n     * The cursor can be a CSS cursor string, a custom callback function, or a key from the cursorStyles dictionary.\n     * @param mode - Cursor mode to set. Can be:\n     * - A CSS cursor string (e.g., 'pointer', 'grab')\n     * - A key from the cursorStyles dictionary\n     * - null/undefined to reset to default\n     * @example\n     * ```ts\n     * // Using predefined cursor styles\n     * app.renderer.events.setCursor('pointer');    // Set standard pointer cursor\n     * app.renderer.events.setCursor('grab');       // Set grab cursor\n     * app.renderer.events.setCursor(null);         // Reset to default\n     *\n     * // Using custom cursor styles\n     * app.renderer.events.cursorStyles.custom = 'url(\"cursor.png\"), auto';\n     * app.renderer.events.setCursor('custom');     // Apply custom cursor\n     *\n     * // Using callback-based cursor\n     * app.renderer.events.cursorStyles.dynamic = (mode) => {\n     *     document.body.style.cursor = mode === 'hover' ? 'pointer' : 'default';\n     * };\n     * app.renderer.events.setCursor('dynamic');    // Trigger cursor callback\n     * ```\n     * @remarks\n     * - Has no effect on OffscreenCanvas except for callback-based cursors\n     * - Caches current cursor to avoid unnecessary DOM updates\n     * - Supports CSS cursor values, style objects, and callback functions\n     * @see {@link EventSystem.cursorStyles} For defining custom cursor styles\n     * @see {@link https://developer.mozilla.org/en-US/docs/Web/CSS/cursor} MDN Cursor Reference\n     */\n    public setCursor(mode: string): void\n    {\n        mode ||= 'default';\n        let applyStyles = true;\n\n        // offscreen canvas does not support setting styles, but cursor modes can be functions,\n        // in order to handle pixi rendered cursors, so we can't bail\n        if (globalThis.OffscreenCanvas && this.domElement instanceof OffscreenCanvas)\n        {\n            applyStyles = false;\n        }\n        // if the mode didn't actually change, bail early\n        if (this._currentCursor === mode)\n        {\n            return;\n        }\n        this._currentCursor = mode;\n        const style = this.cursorStyles[mode];\n\n        // only do things if there is a cursor style for it\n        if (style)\n        {\n            switch (typeof style)\n            {\n                case 'string':\n                    // string styles are handled as cursor CSS\n                    if (applyStyles)\n                    {\n                        this.domElement.style.cursor = style;\n                    }\n                    break;\n                case 'function':\n                    // functions are just called, and passed the cursor mode\n                    style(mode);\n                    break;\n                case 'object':\n                    // if it is an object, assume that it is a dictionary of CSS styles,\n                    // apply it to the interactionDOMElement\n                    if (applyStyles)\n                    {\n                        Object.assign(this.domElement.style, style);\n                    }\n                    break;\n            }\n        }\n        else if (applyStyles && typeof mode === 'string' && !Object.prototype.hasOwnProperty.call(this.cursorStyles, mode))\n        {\n            // if it mode is a string (not a Symbol) and cursorStyles doesn't have any entry\n            // for the mode, then assume that the dev wants it to be CSS for the cursor.\n            this.domElement.style.cursor = mode;\n        }\n    }\n\n    /**\n     * The global pointer event instance containing the most recent pointer state.\n     * This is useful for accessing pointer information without listening to events.\n     * @example\n     * ```ts\n     * // Access current pointer position at any time\n     * const eventSystem = app.renderer.events;\n     * const pointer = eventSystem.pointer;\n     *\n     * // Get global coordinates\n     * console.log('Position:', pointer.global.x, pointer.global.y);\n     *\n     * // Check button state\n     * console.log('Buttons pressed:', pointer.buttons);\n     *\n     * // Get pointer type and pressure\n     * console.log('Type:', pointer.pointerType);\n     * console.log('Pressure:', pointer.pressure);\n     * ```\n     * @readonly\n     * @since 7.2.0\n     * @see {@link FederatedPointerEvent} For all available pointer properties\n     */\n    public get pointer(): Readonly<FederatedPointerEvent>\n    {\n        return this._rootPointerEvent;\n    }\n\n    /**\n     * Event handler for pointer down events on {@link EventSystem#domElement this.domElement}.\n     * @param nativeEvent - The native mouse/pointer/touch event.\n     */\n    private _onPointerDown(nativeEvent: MouseEvent | PointerEvent | TouchEvent): void\n    {\n        if (!this.features.click) return;\n        this.rootBoundary.rootTarget = this.renderer.lastObjectRendered;\n\n        const events = this._normalizeToPointerData(nativeEvent);\n\n        /*\n         * No need to prevent default on natural pointer events, as there are no side effects\n         * Normalized events, however, may have the double mousedown/touchstart issue on the native android browser,\n         * so still need to be prevented.\n         */\n\n        // Guaranteed that there will be at least one event in events, and all events must have the same pointer type\n\n        if (this.autoPreventDefault && (events[0] as any).isNormalized)\n        {\n            const cancelable = nativeEvent.cancelable || !('cancelable' in nativeEvent);\n\n            if (cancelable)\n            {\n                nativeEvent.preventDefault();\n            }\n        }\n\n        for (let i = 0, j = events.length; i < j; i++)\n        {\n            const nativeEvent = events[i];\n            const federatedEvent = this._bootstrapEvent(this._rootPointerEvent, nativeEvent);\n\n            this.rootBoundary.mapEvent(federatedEvent);\n        }\n\n        this.setCursor(this.rootBoundary.cursor);\n    }\n\n    /**\n     * Event handler for pointer move events on on {@link EventSystem#domElement this.domElement}.\n     * @param nativeEvent - The native mouse/pointer/touch events.\n     */\n    private _onPointerMove(nativeEvent: MouseEvent | PointerEvent | TouchEvent): void\n    {\n        if (!this.features.move) return;\n        this.rootBoundary.rootTarget = this.renderer.lastObjectRendered;\n\n        EventsTicker.pointerMoved();\n\n        const normalizedEvents = this._normalizeToPointerData(nativeEvent);\n\n        for (let i = 0, j = normalizedEvents.length; i < j; i++)\n        {\n            const event = this._bootstrapEvent(this._rootPointerEvent, normalizedEvents[i]);\n\n            this.rootBoundary.mapEvent(event);\n        }\n\n        this.setCursor(this.rootBoundary.cursor);\n    }\n\n    /**\n     * Event handler for pointer up events on {@link EventSystem#domElement this.domElement}.\n     * @param nativeEvent - The native mouse/pointer/touch event.\n     */\n    private _onPointerUp(nativeEvent: MouseEvent | PointerEvent | TouchEvent): void\n    {\n        if (!this.features.click) return;\n        this.rootBoundary.rootTarget = this.renderer.lastObjectRendered;\n\n        let target = nativeEvent.target;\n\n        // if in shadow DOM use composedPath to access target\n        if (nativeEvent.composedPath && nativeEvent.composedPath().length > 0)\n        {\n            target = nativeEvent.composedPath()[0];\n        }\n\n        const outside = target !== this.domElement ? 'outside' : '';\n        const normalizedEvents = this._normalizeToPointerData(nativeEvent);\n\n        for (let i = 0, j = normalizedEvents.length; i < j; i++)\n        {\n            const event = this._bootstrapEvent(this._rootPointerEvent, normalizedEvents[i]);\n\n            event.type += outside;\n\n            this.rootBoundary.mapEvent(event);\n        }\n\n        this.setCursor(this.rootBoundary.cursor);\n    }\n\n    /**\n     * Event handler for pointer over & out events on {@link EventSystem#domElement this.domElement}.\n     * @param nativeEvent - The native mouse/pointer/touch event.\n     */\n    private _onPointerOverOut(nativeEvent: MouseEvent | PointerEvent | TouchEvent): void\n    {\n        if (!this.features.click) return;\n        this.rootBoundary.rootTarget = this.renderer.lastObjectRendered;\n\n        const normalizedEvents = this._normalizeToPointerData(nativeEvent);\n\n        for (let i = 0, j = normalizedEvents.length; i < j; i++)\n        {\n            const event = this._bootstrapEvent(this._rootPointerEvent, normalizedEvents[i]);\n\n            this.rootBoundary.mapEvent(event);\n        }\n\n        this.setCursor(this.rootBoundary.cursor);\n    }\n\n    /**\n     * Passive handler for `wheel` events on {@link EventSystem.domElement this.domElement}.\n     * @param nativeEvent - The native wheel event.\n     */\n    protected onWheel(nativeEvent: WheelEvent): void\n    {\n        if (!this.features.wheel) return;\n        const wheelEvent = this.normalizeWheelEvent(nativeEvent);\n\n        this.rootBoundary.rootTarget = this.renderer.lastObjectRendered;\n        this.rootBoundary.mapEvent(wheelEvent);\n    }\n\n    /**\n     * Sets the {@link EventSystem#domElement domElement} and binds event listeners.\n     * This method manages the DOM event bindings for the event system, allowing you to\n     * change or remove the target element that receives input events.\n     * > [!IMPORTANT] This will default to the canvas element of the renderer, so you\n     * > should not need to call this unless you are using a custom element.\n     * @param element - The new DOM element to bind events to, or null to remove all event bindings\n     * @example\n     * ```ts\n     * // Set a new canvas element as the target\n     * const canvas = document.createElement('canvas');\n     * app.renderer.events.setTargetElement(canvas);\n     *\n     * // Remove all event bindings\n     * app.renderer.events.setTargetElement(null);\n     *\n     * // Switch to a different canvas\n     * const newCanvas = document.querySelector('#game-canvas');\n     * app.renderer.events.setTargetElement(newCanvas);\n     * ```\n     * @remarks\n     * - Automatically removes event listeners from previous element\n     * - Required for the event system to function\n     * - Safe to call multiple times\n     * @see {@link EventSystem#domElement} The current DOM element\n     * @see {@link EventsTicker} For the ticker system that tracks pointer movement\n     */\n    public setTargetElement(element: HTMLElement): void\n    {\n        this._removeEvents();\n        this.domElement = element;\n        EventsTicker.domElement = element;\n        this._addEvents();\n    }\n\n    /** Register event listeners on {@link Renderer#domElement this.domElement}. */\n    private _addEvents(): void\n    {\n        if (this._eventsAdded || !this.domElement)\n        {\n            return;\n        }\n\n        EventsTicker.addTickerListener();\n\n        const style = this.domElement.style as CrossCSSStyleDeclaration;\n\n        if (style)\n        {\n            if ((globalThis.navigator as any).msPointerEnabled)\n            {\n                style.msContentZooming = 'none';\n                style.msTouchAction = 'none';\n            }\n            else if (this.supportsPointerEvents)\n            {\n                style.touchAction = 'none';\n            }\n        }\n\n        /*\n         * These events are added first, so that if pointer events are normalized, they are fired\n         * in the same order as non-normalized events. ie. pointer event 1st, mouse / touch 2nd\n         */\n        if (this.supportsPointerEvents)\n        {\n            globalThis.document.addEventListener('pointermove', this._onPointerMove, true);\n            this.domElement.addEventListener('pointerdown', this._onPointerDown, true);\n            // pointerout is fired in addition to pointerup (for touch events) and pointercancel\n            // we already handle those, so for the purposes of what we do in onPointerOut, we only\n            // care about the pointerleave event\n            this.domElement.addEventListener('pointerleave', this._onPointerOverOut, true);\n            this.domElement.addEventListener('pointerover', this._onPointerOverOut, true);\n            // globalThis.addEventListener('pointercancel', this.onPointerCancel, true);\n            globalThis.addEventListener('pointerup', this._onPointerUp, true);\n        }\n        else\n        {\n            globalThis.document.addEventListener('mousemove', this._onPointerMove, true);\n            this.domElement.addEventListener('mousedown', this._onPointerDown, true);\n            this.domElement.addEventListener('mouseout', this._onPointerOverOut, true);\n            this.domElement.addEventListener('mouseover', this._onPointerOverOut, true);\n            globalThis.addEventListener('mouseup', this._onPointerUp, true);\n\n            if (this.supportsTouchEvents)\n            {\n                this.domElement.addEventListener('touchstart', this._onPointerDown, true);\n                // this.domElement.addEventListener('touchcancel', this.onPointerCancel, true);\n                this.domElement.addEventListener('touchend', this._onPointerUp, true);\n                this.domElement.addEventListener('touchmove', this._onPointerMove, true);\n            }\n        }\n\n        this.domElement.addEventListener('wheel', this.onWheel, {\n            passive: true,\n            capture: true,\n        });\n\n        this._eventsAdded = true;\n    }\n\n    /** Unregister event listeners on {@link EventSystem#domElement this.domElement}. */\n    private _removeEvents(): void\n    {\n        if (!this._eventsAdded || !this.domElement)\n        {\n            return;\n        }\n\n        EventsTicker.removeTickerListener();\n\n        const style = this.domElement.style as CrossCSSStyleDeclaration;\n\n        // offscreen canvas does not have style, so check first\n        if (style)\n        {\n            if ((globalThis.navigator as any).msPointerEnabled)\n            {\n                style.msContentZooming = '';\n                style.msTouchAction = '';\n            }\n            else if (this.supportsPointerEvents)\n            {\n                style.touchAction = '';\n            }\n        }\n\n        if (this.supportsPointerEvents)\n        {\n            globalThis.document.removeEventListener('pointermove', this._onPointerMove, true);\n            this.domElement.removeEventListener('pointerdown', this._onPointerDown, true);\n            this.domElement.removeEventListener('pointerleave', this._onPointerOverOut, true);\n            this.domElement.removeEventListener('pointerover', this._onPointerOverOut, true);\n            // globalThis.removeEventListener('pointercancel', this.onPointerCancel, true);\n            globalThis.removeEventListener('pointerup', this._onPointerUp, true);\n        }\n        else\n        {\n            globalThis.document.removeEventListener('mousemove', this._onPointerMove, true);\n            this.domElement.removeEventListener('mousedown', this._onPointerDown, true);\n            this.domElement.removeEventListener('mouseout', this._onPointerOverOut, true);\n            this.domElement.removeEventListener('mouseover', this._onPointerOverOut, true);\n            globalThis.removeEventListener('mouseup', this._onPointerUp, true);\n\n            if (this.supportsTouchEvents)\n            {\n                this.domElement.removeEventListener('touchstart', this._onPointerDown, true);\n                // this.domElement.removeEventListener('touchcancel', this.onPointerCancel, true);\n                this.domElement.removeEventListener('touchend', this._onPointerUp, true);\n                this.domElement.removeEventListener('touchmove', this._onPointerMove, true);\n            }\n        }\n\n        this.domElement.removeEventListener('wheel', this.onWheel, true);\n\n        this.domElement = null;\n        this._eventsAdded = false;\n    }\n\n    /**\n     * Maps coordinates from DOM/screen space into PixiJS normalized coordinates.\n     * This takes into account the current scale, position, and resolution of the DOM element.\n     * @param point - The point to store the mapped coordinates in\n     * @param x - The x coordinate in DOM/client space\n     * @param y - The y coordinate in DOM/client space\n     * @example\n     * ```ts\n     * // Map mouse coordinates to PixiJS space\n     * const point = new Point();\n     * app.renderer.events.mapPositionToPoint(\n     *     point,\n     *     event.clientX,\n     *     event.clientY\n     * );\n     * console.log('Mapped position:', point.x, point.y);\n     *\n     * // Using with pointer events\n     * sprite.on('pointermove', (event) => {\n     *     // event.global already contains mapped coordinates\n     *     console.log('Global:', event.global.x, event.global.y);\n     *\n     *     // Map to local coordinates\n     *     const local = event.getLocalPosition(sprite);\n     *     console.log('Local:', local.x, local.y);\n     * });\n     * ```\n     * @remarks\n     * - Accounts for element scaling and positioning\n     * - Adjusts for device pixel ratio/resolution\n     */\n    public mapPositionToPoint(point: PointData, x: number, y: number): void\n    {\n        const rect = this.domElement.isConnected\n            ? this.domElement.getBoundingClientRect()\n            : {\n                x: 0,\n                y: 0,\n                width: (this.domElement as any).width,\n                height: (this.domElement as any).height,\n                left: 0,\n                top: 0\n            };\n\n        const resolutionMultiplier = 1.0 / this.resolution;\n\n        point.x = ((x - rect.left) * ((this.domElement as any).width / rect.width)) * resolutionMultiplier;\n        point.y = ((y - rect.top) * ((this.domElement as any).height / rect.height)) * resolutionMultiplier;\n    }\n\n    /**\n     * Ensures that the original event object contains all data that a regular pointer event would have\n     * @param event - The original event data from a touch or mouse event\n     * @returns An array containing a single normalized pointer event, in the case of a pointer\n     *  or mouse event, or a multiple normalized pointer events if there are multiple changed touches\n     */\n    private _normalizeToPointerData(event: TouchEvent | MouseEvent | PointerEvent): PointerEvent[]\n    {\n        const normalizedEvents = [];\n\n        if (this.supportsTouchEvents && event instanceof TouchEvent)\n        {\n            for (let i = 0, li = event.changedTouches.length; i < li; i++)\n            {\n                const touch = event.changedTouches[i] as PixiTouch;\n\n                if (typeof touch.button === 'undefined') touch.button = 0;\n                if (typeof touch.buttons === 'undefined') touch.buttons = 1;\n                if (typeof touch.isPrimary === 'undefined')\n                {\n                    touch.isPrimary = event.touches.length === 1 && event.type === 'touchstart';\n                }\n                if (typeof touch.width === 'undefined') touch.width = touch.radiusX || 1;\n                if (typeof touch.height === 'undefined') touch.height = touch.radiusY || 1;\n                if (typeof touch.tiltX === 'undefined') touch.tiltX = 0;\n                if (typeof touch.tiltY === 'undefined') touch.tiltY = 0;\n                if (typeof touch.pointerType === 'undefined') touch.pointerType = 'touch';\n                if (typeof touch.pointerId === 'undefined') touch.pointerId = touch.identifier || 0;\n                if (typeof touch.pressure === 'undefined') touch.pressure = touch.force || 0.5;\n                if (typeof touch.twist === 'undefined') touch.twist = 0;\n                if (typeof touch.tangentialPressure === 'undefined') touch.tangentialPressure = 0;\n                // TODO: Remove these, as layerX/Y is not a standard, is deprecated, has uneven\n                // support, and the fill ins are not quite the same\n                // offsetX/Y might be okay, but is not the same as clientX/Y when the canvas's top\n                // left is not 0,0 on the page\n                if (typeof touch.layerX === 'undefined') touch.layerX = touch.offsetX = touch.clientX;\n                if (typeof touch.layerY === 'undefined') touch.layerY = touch.offsetY = touch.clientY;\n\n                // mark the touch as normalized, just so that we know we did it\n                touch.isNormalized = true;\n                touch.type = event.type;\n\n                normalizedEvents.push(touch);\n            }\n        }\n        // apparently PointerEvent subclasses MouseEvent, so yay\n        else if (!globalThis.MouseEvent\n            || (event instanceof MouseEvent && (!this.supportsPointerEvents || !(event instanceof globalThis.PointerEvent))))\n        {\n            const tempEvent = event as PixiPointerEvent;\n\n            if (typeof tempEvent.isPrimary === 'undefined') tempEvent.isPrimary = true;\n            if (typeof tempEvent.width === 'undefined') tempEvent.width = 1;\n            if (typeof tempEvent.height === 'undefined') tempEvent.height = 1;\n            if (typeof tempEvent.tiltX === 'undefined') tempEvent.tiltX = 0;\n            if (typeof tempEvent.tiltY === 'undefined') tempEvent.tiltY = 0;\n            if (typeof tempEvent.pointerType === 'undefined') tempEvent.pointerType = 'mouse';\n            if (typeof tempEvent.pointerId === 'undefined') tempEvent.pointerId = MOUSE_POINTER_ID;\n            if (typeof tempEvent.pressure === 'undefined') tempEvent.pressure = 0.5;\n            if (typeof tempEvent.twist === 'undefined') tempEvent.twist = 0;\n            if (typeof tempEvent.tangentialPressure === 'undefined') tempEvent.tangentialPressure = 0;\n\n            // mark the mouse event as normalized, just so that we know we did it\n            tempEvent.isNormalized = true;\n\n            normalizedEvents.push(tempEvent);\n        }\n        else\n        {\n            normalizedEvents.push(event);\n        }\n\n        return normalizedEvents as PointerEvent[];\n    }\n\n    /**\n     * Normalizes the native {@link https://w3c.github.io/uievents/#interface-wheelevent WheelEvent}.\n     *\n     * The returned {@link FederatedWheelEvent} is a shared instance. It will not persist across\n     * multiple native wheel events.\n     * @param nativeEvent - The native wheel event that occurred on the canvas.\n     * @returns A federated wheel event.\n     */\n    protected normalizeWheelEvent(nativeEvent: WheelEvent): FederatedWheelEvent\n    {\n        const event = this._rootWheelEvent;\n\n        this._transferMouseData(event, nativeEvent);\n\n        // When WheelEvent is triggered by scrolling with mouse wheel, reading WheelEvent.deltaMode\n        // before deltaX/deltaY/deltaZ on Firefox will result in WheelEvent.DOM_DELTA_LINE (1),\n        // while reading WheelEvent.deltaMode after deltaX/deltaY/deltaZ on Firefox or reading\n        // in any order on other browsers will result in WheelEvent.DOM_DELTA_PIXEL (0).\n        // Therefore, we need to read WheelEvent.deltaMode after deltaX/deltaY/deltaZ in order to\n        // make its behavior more consistent across browsers.\n        // @see https://github.com/pixijs/pixijs/issues/8970\n        event.deltaX = nativeEvent.deltaX;\n        event.deltaY = nativeEvent.deltaY;\n        event.deltaZ = nativeEvent.deltaZ;\n        event.deltaMode = nativeEvent.deltaMode;\n\n        this.mapPositionToPoint(event.screen, nativeEvent.clientX, nativeEvent.clientY);\n        event.global.copyFrom(event.screen);\n        event.offset.copyFrom(event.screen);\n\n        event.nativeEvent = nativeEvent;\n        event.type = nativeEvent.type;\n\n        return event;\n    }\n\n    /**\n     * Normalizes the `nativeEvent` into a federateed {@link FederatedPointerEvent}.\n     * @param event\n     * @param nativeEvent\n     */\n    private _bootstrapEvent(event: FederatedPointerEvent, nativeEvent: PointerEvent): FederatedPointerEvent\n    {\n        event.originalEvent = null;\n        event.nativeEvent = nativeEvent;\n\n        event.pointerId = nativeEvent.pointerId;\n        event.width = nativeEvent.width;\n        event.height = nativeEvent.height;\n        event.isPrimary = nativeEvent.isPrimary;\n        event.pointerType = nativeEvent.pointerType;\n        event.pressure = nativeEvent.pressure;\n        event.tangentialPressure = nativeEvent.tangentialPressure;\n        event.tiltX = nativeEvent.tiltX;\n        event.tiltY = nativeEvent.tiltY;\n        event.twist = nativeEvent.twist;\n        this._transferMouseData(event, nativeEvent);\n\n        this.mapPositionToPoint(event.screen, nativeEvent.clientX, nativeEvent.clientY);\n        event.global.copyFrom(event.screen);// global = screen for top-level\n        event.offset.copyFrom(event.screen);// EventBoundary recalculates using its rootTarget\n\n        event.isTrusted = nativeEvent.isTrusted;\n        if (event.type === 'pointerleave')\n        {\n            event.type = 'pointerout';\n        }\n        if (event.type.startsWith('mouse'))\n        {\n            event.type = event.type.replace('mouse', 'pointer');\n        }\n        if (event.type.startsWith('touch'))\n        {\n            event.type = TOUCH_TO_POINTER[event.type] || event.type;\n        }\n\n        return event;\n    }\n\n    /**\n     * Transfers base & mouse event data from the `nativeEvent` to the federated event.\n     * @param event\n     * @param nativeEvent\n     */\n    private _transferMouseData(event: FederatedMouseEvent, nativeEvent: MouseEvent): void\n    {\n        event.isTrusted = nativeEvent.isTrusted;\n        event.srcElement = nativeEvent.srcElement;\n        event.timeStamp = performance.now();\n        event.type = nativeEvent.type;\n\n        event.altKey = nativeEvent.altKey;\n        event.button = nativeEvent.button;\n        event.buttons = nativeEvent.buttons;\n        event.client.x = nativeEvent.clientX;\n        event.client.y = nativeEvent.clientY;\n        event.ctrlKey = nativeEvent.ctrlKey;\n        event.metaKey = nativeEvent.metaKey;\n        event.movement.x = nativeEvent.movementX;\n        event.movement.y = nativeEvent.movementY;\n        event.page.x = nativeEvent.pageX;\n        event.page.y = nativeEvent.pageY;\n        event.relatedTarget = null;\n        event.shiftKey = nativeEvent.shiftKey;\n    }\n}\n\ninterface CrossCSSStyleDeclaration extends CSSStyleDeclaration\n{\n    msContentZooming: string;\n    msTouchAction: string;\n}\n\ninterface PixiPointerEvent extends PointerEvent\n{\n    isPrimary: boolean;\n    width: number;\n    height: number;\n    tiltX: number;\n    tiltY: number;\n    pointerType: string;\n    pointerId: number;\n    pressure: number;\n    twist: number;\n    tangentialPressure: number;\n    isNormalized: boolean;\n    type: string;\n}\n", "import { EventSystem } from './EventSystem';\nimport { FederatedEvent } from './FederatedEvent';\n\nimport type EventEmitter from 'eventemitter3';\nimport type { Container } from '../scene/container/Container';\nimport type { AllFederatedEventMap } from './FederatedEventMap';\nimport type { FederatedPointerEvent } from './FederatedPointerEvent';\nimport type { FederatedWheelEvent } from './FederatedWheelEvent';\n\n/**\n * The type of cursor to use when the mouse pointer is hovering over an interactive element.\n * Accepts any valid CSS cursor value.\n * @example\n * ```ts\n * // Basic cursor types\n * sprite.cursor = 'pointer';    // Hand cursor for clickable elements\n * sprite.cursor = 'grab';       // Grab cursor for draggable elements\n * sprite.cursor = 'crosshair';  // Precise cursor for selection\n *\n * // Direction cursors\n * sprite.cursor = 'n-resize';   // North resize\n * sprite.cursor = 'ew-resize';  // East-west resize\n * sprite.cursor = 'nesw-resize';// Northeast-southwest resize\n *\n * // Custom cursor with fallback\n * sprite.cursor = 'url(\"custom.png\"), auto';\n * ```\n *\n * Common cursor values:\n * - Basic: `auto`, `default`, `none`, `pointer`, `wait`\n * - Text: `text`, `vertical-text`\n * - Links: `alias`, `copy`, `move`\n * - Selection: `cell`, `crosshair`\n * - Drag: `grab`, `grabbing`\n * - Disabled: `not-allowed`, `no-drop`\n * - Resize: `n-resize`, `e-resize`, `s-resize`, `w-resize`\n * - Bidirectional: `ns-resize`, `ew-resize`, `nesw-resize`, `nwse-resize`\n * - Other: `help`, `progress`\n * @see {@link https://developer.mozilla.org/en-US/docs/Web/CSS/cursor} MDN Cursor Documentation\n * @category events\n * @standard\n */\nexport type Cursor = 'auto'\n| 'default'\n| 'none'\n| 'context-menu'\n| 'help'\n| 'pointer'\n| 'progress'\n| 'wait'\n| 'cell'\n| 'crosshair'\n| 'text'\n| 'vertical-text'\n| 'alias'\n| 'copy'\n| 'move'\n| 'no-drop'\n| 'not-allowed'\n| 'e-resize'\n| 'n-resize'\n| 'ne-resize'\n| 'nw-resize'\n| 's-resize'\n| 'se-resize'\n| 'sw-resize'\n| 'w-resize'\n| 'ns-resize'\n| 'ew-resize'\n| 'nesw-resize'\n| 'col-resize'\n| 'nwse-resize'\n| 'row-resize'\n| 'all-scroll'\n| 'zoom-in'\n| 'zoom-out'\n| 'grab'\n| 'grabbing';\n\n/**\n * Interface defining a hit area for pointer interaction. The hit area specifies\n * the region in which pointer events should be captured by a display object.\n * @example\n * ```ts\n * // Create a rectangular hit area\n * sprite.hitArea = new Rectangle(0, 0, 100, 100);\n *\n * // Create a circular hit area\n * sprite.hitArea = new Circle(50, 50, 50);\n *\n * // Custom hit area implementation\n * sprite.hitArea = {\n *     contains(x: number, y: number) {\n *         // Custom hit testing logic\n *         return x >= 0 && x <= 100 && y >= 0 && y <= 100;\n *     }\n * };\n * ```\n * @remarks\n * - Hit areas override the default bounds-based hit testing\n * - Can improve performance by simplifying hit tests\n * - Useful for irregular shapes or precise interaction areas\n * - Common implementations include Rectangle, Circle, Polygon\n * @see {@link Container.eventMode} For enabling interactivity\n * @see {@link Container.interactive} For backwards compatibility\n * @category events\n * @standard\n */\nexport interface IHitArea\n{\n    /**\n     * Checks if the given coordinates are inside this hit area.\n     * @param {number} x - The x coordinate to check\n     * @param {number} y - The y coordinate to check\n     * @returns True if the coordinates are inside the hit area\n     */\n    contains(x: number, y: number): boolean;\n}\n\n/**\n * Function type for handlers, e.g., onclick\n * @category events\n * @advanced\n */\nexport type FederatedEventHandler<T = FederatedPointerEvent> = (event: T) => void;\n\n/**\n * The type of interaction behavior for a Container. This is set via the {@link Container#eventMode} property.\n * @example\n * ```ts\n * // Basic event mode setup\n * const sprite = new Sprite(texture);\n * sprite.eventMode = 'static';    // Enable standard interaction\n * sprite.on('pointerdown', () => { console.log('clicked!'); });\n *\n * // Different event modes\n * sprite.eventMode = 'none';      // Disable all interaction\n * sprite.eventMode = 'passive';   // Only allow interaction on children\n * sprite.eventMode = 'auto';      // Like DOM pointer-events: auto\n * sprite.eventMode = 'dynamic';   // For moving/animated objects\n * ```\n *\n * Available modes:\n * - `'none'`: Ignores all interaction events, even on its children\n * - `'passive'`: **(default)** Does not emit events and ignores hit testing on itself and non-interactive children.\n * Interactive children will still emit events.\n * - `'auto'`: Does not emit events but is hit tested if parent is interactive. Same as `interactive = false` in v7\n * - `'static'`: Emit events and is hit tested. Same as `interactive = true` in v7\n * - `'dynamic'`: Emits events and is hit tested but will also receive mock interaction events fired from\n * a ticker to allow for interaction when the mouse isn't moving\n *\n * Performance tips:\n * - Use `'none'` for pure visual elements\n * - Use `'passive'` for containers with some interactive children\n * - Use `'static'` for standard buttons/controls\n * - Use `'dynamic'` only for moving/animated interactive elements\n * @since 7.2.0\n * @category events\n * @standard\n */\nexport type EventMode = 'none' | 'passive' | 'auto' | 'static' | 'dynamic';\n\n/**\n * The properties available for any interactive object. This interface defines the core interaction\n * properties and event handlers that can be set on any Container in PixiJS.\n * @example\n * ```ts\n * // Basic interactive setup\n * const sprite = new Sprite(texture);\n * sprite.eventMode = 'static';\n * sprite.cursor = 'pointer';\n *\n * // Using event handlers\n * sprite.on('click', (event) => console.log('Sprite clicked!', event));\n * sprite.on('pointerdown', (event) => console.log('Pointer down!', event));\n *\n * // Using property-based event handlers\n * sprite.onclick = (event) => console.log('Clicked!');\n * sprite.onpointerenter = () => sprite.alpha = 0.7;\n * sprite.onpointerleave = () => sprite.alpha = 1.0;\n *\n * // Custom hit area\n * sprite.hitArea = new Rectangle(0, 0, 100, 100);\n * ```\n *\n * Core Properties:\n * - `eventMode`: Controls how the object handles interaction events\n * - `cursor`: Sets the mouse cursor when hovering\n * - `hitArea`: Defines custom hit testing area\n * - `interactive`: Alias for `eventMode` to enable interaction with \"static\" or \"passive\" modes\n * - `interactiveChildren`: Controls hit testing on children\n *\n * Event Handlers:\n * - Mouse: click, mousedown, mouseup, mousemove, mouseenter, mouseleave\n * - Touch: touchstart, touchend, touchmove, tap\n * - Pointer: pointerdown, pointerup, pointermove, pointerover\n * - Global: globalpointermove, globalmousemove, globaltouchmove\n * > [!IMPORTANT] Global events are fired when the pointer moves even if it is outside the bounds of the Container.\n * @see {@link EventMode} For interaction mode details\n * @see {@link Cursor} For cursor style options\n * @see {@link IHitArea} For hit area implementation\n * @category events\n * @standard\n */\nexport interface FederatedOptions\n{\n    /**\n     * The cursor style to display when the mouse pointer is hovering over the object.\n     * Accepts any valid CSS cursor value or custom cursor URL.\n     * @example\n     * ```ts\n     * // Common cursor types\n     * sprite.cursor = 'pointer';     // Hand cursor for clickable elements\n     * sprite.cursor = 'grab';        // Grab cursor for draggable elements\n     * sprite.cursor = 'crosshair';   // Precise cursor for selection\n     * sprite.cursor = 'not-allowed'; // Indicate disabled state\n     *\n     * // Direction cursors\n     * sprite.cursor = 'n-resize';    // North resize\n     * sprite.cursor = 'ew-resize';   // East-west resize\n     * sprite.cursor = 'nesw-resize'; // Northeast-southwest resize\n     *\n     * // Custom cursor with fallback\n     * sprite.cursor = 'url(\"custom.png\"), auto';\n     * sprite.cursor = 'url(\"cursor.cur\") 2 2, pointer'; // With hotspot offset\n     * ```\n     * @type {Cursor | string}\n     * @default undefined\n     * @see {@link EventSystem.cursorStyles} For setting global cursor styles\n     * @see {@link https://developer.mozilla.org/en-US/docs/Web/CSS/cursor} MDN Cursor Documentation\n     */\n    cursor?: Cursor | (string & {});\n    /**\n     * Enable interaction events for the Container. Touch, pointer and mouse events are supported.\n     * @example\n     * ```ts\n     * const sprite = new Sprite(texture);\n     *\n     * // Enable standard interaction (like buttons)\n     * sprite.eventMode = 'static';\n     * sprite.on('pointerdown', () => console.log('clicked!'));\n     *\n     * // Enable for moving objects\n     * sprite.eventMode = 'dynamic';\n     * sprite.on('pointermove', () => updatePosition());\n     *\n     * // Disable all interaction\n     * sprite.eventMode = 'none';\n     *\n     * // Only allow child interactions\n     * sprite.eventMode = 'passive';\n     * ```\n     *\n     * Available modes:\n     *\n     * - `'none'`: Ignores all interaction events, even on its children. Best for pure visuals.\n     * - `'passive'`: **(default)** Does not emit events and ignores hit testing on itself and non-interactive\n     * children. Interactive children will still emit events.\n     * - `'auto'`: Does not emit events but is hit tested if parent is interactive. Same as `interactive = false` in v7.\n     * - `'static'`: Emit events and is hit tested. Same as `interactive = true` in v7. Best for buttons/UI.\n     * - `'dynamic'`: Like static but also receives synthetic events when pointer is idle. Best for moving objects.\n     *\n     * Performance tips:\n     * - Use `'none'` for pure visual elements\n     * - Use `'passive'` for containers with some interactive children\n     * - Use `'static'` for standard UI elements\n     * - Use `'dynamic'` only when needed for moving/animated elements\n     * @since 7.2.0\n     */\n    eventMode?: EventMode;\n    /**\n     * Whether this object should fire UI events. This is an alias for `eventMode` set to `'static'` or `'passive'`.\n     * Setting this to true will enable interaction events like `pointerdown`, `click`, etc.\n     * Setting it to false will disable all interaction events on this object.\n     * @see {@link Container.eventMode}\n     * @example\n     * ```ts\n     * // Enable interaction events\n     * sprite.interactive = true;  // Sets eventMode = 'static'\n     * sprite.interactive = false; // Sets eventMode = 'passive'\n     * ```\n     */\n    interactive?: boolean\n    /**\n     * Controls whether children of this container can receive pointer events.\n     *\n     * Setting this to false allows PixiJS to skip hit testing on all children,\n     * improving performance for containers with many non-interactive children.\n     * @default true\n     * @example\n     * ```ts\n     * // Container with many visual-only children\n     * const container = new Container();\n     * container.interactiveChildren = false; // Skip hit testing children\n     *\n     * // Menu with interactive buttons\n     * const menu = new Container();\n     * menu.interactiveChildren = true; // Test all children\n     * menu.addChild(button1, button2, button3);\n     *\n     * // Performance optimization\n     * background.interactiveChildren = false;\n     * foreground.interactiveChildren = true;\n     * ```\n     */\n    interactiveChildren?: boolean;\n    /**\n     * Defines a custom hit area for pointer interaction testing. When set, this shape will be used\n     * for hit testing instead of the container's standard bounds.\n     * @example\n     * ```ts\n     * import { Rectangle, Circle, Sprite } from 'pixi.js';\n     *\n     * // Rectangular hit area\n     * const button = new Sprite(texture);\n     * button.eventMode = 'static';\n     * button.hitArea = new Rectangle(0, 0, 100, 50);\n     *\n     * // Circular hit area\n     * const icon = new Sprite(texture);\n     * icon.eventMode = 'static';\n     * icon.hitArea = new Circle(32, 32, 32);\n     *\n     * // Custom hit area with polygon\n     * const custom = new Sprite(texture);\n     * custom.eventMode = 'static';\n     * custom.hitArea = new Polygon([0,0, 100,0, 100,100, 0,100]);\n     *\n     * // Custom hit testing logic\n     * sprite.hitArea = {\n     *     contains(x: number, y: number) {\n     *         // Custom collision detection\n     *         return x >= 0 && x <= width && y >= 0 && y <= height;\n     *     }\n     * };\n     * ```\n     * @remarks\n     * - Takes precedence over the container's bounds for hit testing\n     * - Can improve performance by simplifying collision checks\n     * - Useful for irregular shapes or precise click areas\n     */\n    hitArea?: IHitArea | null;\n\n    /**\n     * Property-based event handler for the `click` event.\n     * Fired when a pointer device (mouse, touch, etc.) completes a click action.\n     * @example\n     * ```ts\n     * const sprite = new Sprite(texture);\n     * sprite.eventMode = 'static';\n     *\n     * // Using emitter handler\n     * sprite.on('click', (event) => {\n     *    console.log('Sprite clicked at:', event.global.x, event.global.y);\n     * });\n     * // Using property-based handler\n     * sprite.onclick = (event) => {\n     *     console.log('Clicked at:', event.global.x, event.global.y);\n     * };\n     * ```\n     * @default null\n     */\n    onclick?: FederatedEventHandler | null;\n\n    /**\n     * Property-based event handler for the `mousedown` event.\n     * Fired when a mouse button is pressed while the pointer is over the object.\n     * @example\n     * ```ts\n     * const sprite = new Sprite(texture);\n     * sprite.eventMode = 'static';\n     *\n     * // Using emitter handler\n     * sprite.on('mousedown', (event) => {\n     *    sprite.alpha = 0.5; // Visual feedback\n     *    console.log('Mouse button:', event.button);\n     * });\n     * // Using property-based handler\n     * sprite.onmousedown = (event) => {\n     *     sprite.alpha = 0.5; // Visual feedback\n     *     console.log('Mouse button:', event.button);\n     * };\n     * ```\n     * @default null\n     */\n    onmousedown?: FederatedEventHandler | null;\n\n    /**\n     * Property-based event handler for the `mouseenter` event.\n     * Fired when the mouse pointer enters the bounds of the object. Does not bubble.\n     * @example\n     * ```ts\n     * const sprite = new Sprite(texture);\n     * sprite.eventMode = 'static';\n     *\n     * // Using emitter handler\n     * sprite.on('mouseenter', (event) => {\n     *     sprite.scale.set(1.1);\n     * });\n     * // Using property-based handler\n     * sprite.onmouseenter = (event) => {\n     *     sprite.scale.set(1.1);\n     * };\n     * ```\n     * @default null\n     */\n    onmouseenter?: FederatedEventHandler | null;\n\n    /**\n     * Property-based event handler for the `mouseleave` event.\n     * Fired when the pointer leaves the bounds of the display object. Does not bubble.\n     * @example\n     * ```ts\n     * const sprite = new Sprite(texture);\n     * sprite.eventMode = 'static';\n     *\n     * // Using emitter handler\n     * sprite.on('mouseleave', (event) => {\n     *    sprite.scale.set(1.0);\n     * });\n     * // Using property-based handler\n     * sprite.onmouseleave = (event) => {\n     *     sprite.scale.set(1.0);\n     * };\n     * ```\n     * @default null\n     */\n    onmouseleave?: FederatedEventHandler | null;\n\n    /**\n     * Property-based event handler for the `mousemove` event.\n     * Fired when the pointer moves while over the display object.\n     * @example\n     * ```ts\n     * const sprite = new Sprite(texture);\n     * sprite.eventMode = 'static';\n     *\n     * // Using emitter handler\n     * sprite.on('mousemove', (event) => {\n     *    // Get coordinates relative to the sprite\n     *   console.log('Local:', event.getLocalPosition(sprite));\n     * });\n     * // Using property-based handler\n     * sprite.onmousemove = (event) => {\n     *     // Get coordinates relative to the sprite\n     *     console.log('Local:', event.getLocalPosition(sprite));\n     * };\n     * ```\n     * @default null\n     */\n    onmousemove?: FederatedEventHandler | null;\n\n    /**\n     * Property-based event handler for the `globalmousemove` event.\n     *\n     * Fired when the mouse moves anywhere, regardless of whether the pointer is over this object.\n     * The object must have `eventMode` set to 'static' or 'dynamic' to receive this event.\n     * @example\n     * ```ts\n     * const sprite = new Sprite(texture);\n     * sprite.eventMode = 'static';\n     *\n     * // Using emitter handler\n     * sprite.on('globalmousemove', (event) => {\n     *     // Move sprite to mouse position\n     *     sprite.position.copyFrom(event.global);\n     * });\n     * // Using property-based handler\n     * sprite.onglobalmousemove = (event) => {\n     *     // Move sprite to mouse position\n     *     sprite.position.copyFrom(event.global);\n     * };\n     * ```\n     * @default null\n     * @remarks\n     * - Fires even when the mouse is outside the object's bounds\n     * - Useful for drag operations or global mouse tracking\n     * - Must have `eventMode` set appropriately to receive events\n     * - Part of the global move events family along with `globalpointermove` and `globaltouchmove`\n     */\n    onglobalmousemove?: FederatedEventHandler | null;\n\n    /**\n     * Property-based event handler for the `mouseout` event.\n     * Fired when the pointer moves out of the bounds of the display object.\n     * @example\n     * ```ts\n     * const sprite = new Sprite(texture);\n     * sprite.eventMode = 'static';\n     *\n     * // Using emitter handler\n     * sprite.on('mouseout', (event) => {\n     *    sprite.scale.set(1.0);\n     * });\n     * // Using property-based handler\n     * sprite.onmouseout = (event) => {\n     *     sprite.scale.set(1.0);\n     * };\n     * ```\n     * @default null\n     */\n    onmouseout?: FederatedEventHandler | null;\n\n    /**\n     * Property-based event handler for the `mouseover` event.\n     * Fired when the pointer moves onto the bounds of the display object.\n     * @example\n     * ```ts\n     * const sprite = new Sprite(texture);\n     * sprite.eventMode = 'static';\n     *\n     * // Using emitter handler\n     * sprite.on('mouseover', (event) => {\n     *      sprite.scale.set(1.1);\n     * });\n     * // Using property-based handler\n     * sprite.onmouseover = (event) => {\n     *     sprite.scale.set(1.1);\n     * };\n     * ```\n     * @default null\n     */\n    onmouseover?: FederatedEventHandler | null;\n\n    /**\n     * Property-based event handler for the `mouseup` event.\n     * Fired when a mouse button is released over the display object.\n     * @example\n     * ```ts\n     * const sprite = new Sprite(texture);\n     * sprite.eventMode = 'static';\n     *\n     * // Using emitter handler\n     * sprite.on('mouseup', (event) => {\n     *     sprite.scale.set(1.0);\n     * });\n     * // Using property-based handler\n     * sprite.onmouseup = (event) => {\n     *      sprite.scale.set(1.0);\n     * };\n     * ```\n     * @default null\n     */\n    onmouseup?: FederatedEventHandler | null;\n\n    /**\n     * Property-based event handler for the `mouseupoutside` event.\n     * Fired when a mouse button is released outside the display object that initially\n     * registered a mousedown.\n     * @example\n     * ```ts\n     * const sprite = new Sprite(texture);\n     * sprite.eventMode = 'static';\n     *\n     * // Using emitter handler\n     * sprite.on('mouseupoutside', (event) => {\n     *     sprite.scale.set(1.0);\n     * });\n     * // Using property-based handler\n     * sprite.onmouseupoutside = (event) => {\n     *     sprite.scale.set(1.0);\n     * };\n     * ```\n     * @default null\n     */\n    onmouseupoutside?: FederatedEventHandler | null;\n\n    /**\n     * Property-based event handler for the `pointercancel` event.\n     * Fired when a pointer device interaction is canceled or lost.\n     * @example\n     * ```ts\n     * const sprite = new Sprite(texture);\n     * sprite.eventMode = 'static';\n     *\n     * // Using emitter handler\n     * sprite.on('pointercancel', (event) => {\n     *     sprite.scale.set(1.0);\n     * });\n     * // Using property-based handler\n     * sprite.onpointercancel = (event) => {\n     *     sprite.scale.set(1.0);\n     * };\n     * ```\n     * @default null\n     */\n    onpointercancel?: FederatedEventHandler | null;\n\n    /**\n     * Property-based event handler for the `pointerdown` event.\n     * Fired when a pointer device button (mouse, touch, pen, etc.) is pressed.\n     * @example\n     * ```ts\n     * const sprite = new Sprite(texture);\n     * sprite.eventMode = 'static';\n     *\n     * // Using emitter handler\n     * sprite.on('pointerdown', (event) => {\n     *     sprite.position.set(event.global.x, event.global.y);\n     * });\n     * // Using property-based handler\n     * sprite.onpointerdown = (event) => {\n     *     sprite.position.set(event.global.x, event.global.y);\n     * };\n     * ```\n     * @default null\n     */\n    onpointerdown?: FederatedEventHandler | null;\n\n    /**\n     * Property-based event handler for the `pointerenter` event.\n     * Fired when a pointer device enters the bounds of the display object. Does not bubble.\n     * @example\n     * ```ts\n     * const sprite = new Sprite(texture);\n     * sprite.eventMode = 'static';\n     *\n     * // Using emitter handler\n     * sprite.on('pointerenter', (event) => {\n     *     sprite.scale.set(1.2);\n     * });\n     * // Using property-based handler\n     * sprite.onpointerenter = (event) => {\n     *     sprite.scale.set(1.2);\n     * };\n     * ```\n     * @default null\n     */\n    onpointerenter?: FederatedEventHandler | null;\n\n    /**\n     * Property-based event handler for the `pointerleave` event.\n     * Fired when a pointer device leaves the bounds of the display object. Does not bubble.\n     * @example\n     * ```ts\n     * const sprite = new Sprite(texture);\n     * sprite.eventMode = 'static';\n     * // Using emitter handler\n     * sprite.on('pointerleave', (event) => {\n     *     sprite.scale.set(1.0);\n     * });\n     * // Using property-based handler\n     * sprite.onpointerleave = (event) => {\n     *     sprite.scale.set(1.0);\n     * };\n     * ```\n     * @default null\n     */\n    onpointerleave?: FederatedEventHandler | null;\n\n    /**\n     * Property-based event handler for the `pointermove` event.\n     * Fired when a pointer device moves while over the display object.\n     * @example\n     * ```ts\n     * const sprite = new Sprite(texture);\n     * sprite.eventMode = 'static';\n     *\n     * // Using emitter handler\n     * sprite.on('pointermove', (event) => {\n     *     sprite.position.set(event.global.x, event.global.y);\n     * });\n     * // Using property-based handler\n     * sprite.onpointermove = (event) => {\n     *     sprite.position.set(event.global.x, event.global.y);\n     * };\n     * ```\n     * @default null\n     */\n    onpointermove?: FederatedEventHandler | null;\n\n    /**\n     * Property-based event handler for the `globalpointermove` event.\n     *\n     * Fired when the pointer moves anywhere, regardless of whether the pointer is over this object.\n     * The object must have `eventMode` set to 'static' or 'dynamic' to receive this event.\n     * @example\n     * ```ts\n     * const sprite = new Sprite(texture);\n     * sprite.eventMode = 'static';\n     *\n     * // Using emitter handler\n     * sprite.on('globalpointermove', (event) => {\n     *     sprite.position.set(event.global.x, event.global.y);\n     * });\n     * // Using property-based handler\n     * sprite.onglobalpointermove = (event) => {\n     *     sprite.position.set(event.global.x, event.global.y);\n     * };\n     * ```\n     * @default null\n     * @remarks\n     * - Fires even when the mouse is outside the object's bounds\n     * - Useful for drag operations or global mouse tracking\n     * - Must have `eventMode` set appropriately to receive events\n     * - Part of the global move events family along with `globalpointermove` and `globaltouchmove`\n     */\n    onglobalpointermove?: FederatedEventHandler | null;\n\n    /**\n     * Property-based event handler for the `pointerout` event.\n     * Fired when the pointer moves out of the bounds of the display object.\n     * @example\n     * ```ts\n     * const sprite = new Sprite(texture);\n     * sprite.eventMode = 'static';\n     *\n     * // Using emitter handler\n     * sprite.on('pointerout', (event) => {\n     *    sprite.scale.set(1.0);\n     * });\n     * // Using property-based handler\n     * sprite.onpointerout = (event) => {\n     *    sprite.scale.set(1.0);\n     * };\n     * ```\n     * @default null\n     */\n    onpointerout?: FederatedEventHandler | null;\n\n    /**\n     * Property-based event handler for the `pointerover` event.\n     * Fired when the pointer moves over the bounds of the display object.\n     * @example\n     * ```ts\n     * const sprite = new Sprite(texture);\n     * sprite.eventMode = 'static';\n     *\n     * // Using emitter handler\n     * sprite.on('pointerover', (event) => {\n     *     sprite.scale.set(1.2);\n     * });\n     * // Using property-based handler\n     * sprite.onpointerover = (event) => {\n     *     sprite.scale.set(1.2);\n     * };\n     * ```\n     * @default null\n     */\n    onpointerover?: FederatedEventHandler | null;\n\n    /**\n     * Property-based event handler for the `pointertap` event.\n     * Fired when a pointer device completes a tap action (e.g., touch or mouse click).\n     * @example\n     * ```ts\n     * const sprite = new Sprite(texture);\n     * sprite.eventMode = 'static';\n     *\n     * // Using emitter handler\n     * sprite.on('pointertap', (event) => {\n     *     console.log('Sprite tapped at:', event.global.x, event.global.y);\n     * });\n     * // Using property-based handler\n     * sprite.onpointertap = (event) => {\n     *     console.log('Sprite tapped at:', event.global.x, event.global.y);\n     * };\n     * ```\n     * @default null\n     */\n    onpointertap?: FederatedEventHandler | null;\n\n    /**\n     * Property-based event handler for the `pointerup` event.\n     * Fired when a pointer device button (mouse, touch, pen, etc.) is released.\n     * @example\n     * ```ts\n     * const sprite = new Sprite(texture);\n     * sprite.eventMode = 'static';\n     *\n     * // Using emitter handler\n     * sprite.on('pointerup', (event) => {\n     *     sprite.scale.set(1.0);\n     * });\n     * // Using property-based handler\n     * sprite.onpointerup = (event) => {\n     *     sprite.scale.set(1.0);\n     * };\n     * ```\n     * @default null\n     */\n    onpointerup?: FederatedEventHandler | null;\n\n    /**\n     * Property-based event handler for the `pointerupoutside` event.\n     * Fired when a pointer device button is released outside the bounds of the display object\n     * that initially registered a pointerdown.\n     * @example\n     * ```ts\n     * const sprite = new Sprite(texture);\n     * sprite.eventMode = 'static';\n     *\n     * // Using emitter handler\n     * sprite.on('pointerupoutside', (event) => {\n     *     sprite.scale.set(1.0);\n     * });\n     * // Using property-based handler\n     * sprite.onpointerupoutside = (event) => {\n     *     sprite.scale.set(1.0);\n     * };\n     * ```\n     * @default null\n     */\n    onpointerupoutside?: FederatedEventHandler | null;\n\n    /**\n     * Property-based event handler for the `rightclick` event.\n     * Fired when a right-click (context menu) action is performed on the object.\n     * @example\n     * ```ts\n     * const sprite = new Sprite(texture);\n     * sprite.eventMode = 'static';\n     *\n     * // Using emitter handler\n     * sprite.on('rightclick', (event) => {\n     *     console.log('Right-clicked at:', event.global.x, event.global.y);\n     * });\n     * // Using property-based handler\n     * sprite.onrightclick = (event) => {\n     *     console.log('Right-clicked at:', event.global.x, event.global.y);\n     * };\n     * ```\n     * @default null\n     */\n    onrightclick?: FederatedEventHandler | null;\n\n    /**\n     * Property-based event handler for the `rightdown` event.\n     * Fired when a right mouse button is pressed down over the display object.\n     * @example\n     * ```ts\n     * const sprite = new Sprite(texture);\n     * sprite.eventMode = 'static';\n     *\n     * // Using emitter handler\n     * sprite.on('rightdown', (event) => {\n     *     sprite.scale.set(0.9);\n     * });\n     * // Using property-based handler\n     * sprite.onrightdown = (event) => {\n     *     sprite.scale.set(0.9);\n     * };\n     * ```\n     * @default null\n     */\n    onrightdown?: FederatedEventHandler | null;\n\n    /**\n     * Property-based event handler for the `rightup` event.\n     * Fired when a right mouse button is released over the display object.\n     * @example\n     * ```ts\n     * const sprite = new Sprite(texture);\n     * sprite.eventMode = 'static';\n     *\n     * // Using emitter handler\n     * sprite.on('rightup', (event) => {\n     *     sprite.scale.set(1.0);\n     * });\n     * // Using property-based handler\n     * sprite.onrightup = (event) => {\n     *     sprite.scale.set(1.0);\n     * };\n     * ```\n     * @default null\n     */\n    onrightup?: FederatedEventHandler | null;\n\n    /**\n     * Property-based event handler for the `rightupoutside` event.\n     * Fired when a right mouse button is released outside the bounds of the display object\n     * that initially registered a rightdown.\n     * @example\n     * ```ts\n     * const sprite = new Sprite(texture);\n     * sprite.eventMode = 'static';\n     *\n     * // Using emitter handler\n     * sprite.on('rightupoutside', (event) => {\n     *     sprite.scale.set(1.0);\n     * });\n     * // Using property-based handler\n     * sprite.onrightupoutside = (event) => {\n     *     sprite.scale.set(1.0);\n     * };\n     * ```\n     * @default null\n     */\n    onrightupoutside?: FederatedEventHandler | null;\n\n    /**\n     * Property-based event handler for the `tap` event.\n     * Fired when a tap action (touch) is completed on the object.\n     * @example\n     * ```ts\n     * const sprite = new Sprite(texture);\n     * sprite.eventMode = 'static';\n     *\n     * // Using emitter handler\n     * sprite.on('tap', (event) => {\n     *     console.log('Sprite tapped at:', event.global.x, event.global.y);\n     * });\n     * // Using property-based handler\n     * sprite.ontap = (event) => {\n     *     console.log('Sprite tapped at:', event.global.x, event.global.y);\n     * };\n     * ```\n     * @default null\n     */\n    ontap?: FederatedEventHandler | null;\n\n    /**\n     * Property-based event handler for the `touchcancel` event.\n     * Fired when a touch interaction is canceled, such as when the touch is interrupted.\n     * @example\n     * ```ts\n     * const sprite = new Sprite(texture);\n     * sprite.eventMode = 'static';\n     *\n     * // Using emitter handler\n     * sprite.on('touchcancel', (event) => {\n     *     console.log('Touch canceled at:', event.global.x, event.global.y);\n     * });\n     * // Using property-based handler\n     * sprite.ontouchcancel = (event) => {\n     *     console.log('Touch canceled at:', event.global.x, event.global.y);\n     * };\n     * ```\n     * @default null\n     */\n    ontouchcancel?: FederatedEventHandler | null;\n\n    /**\n     * Property-based event handler for the `touchend` event.\n     * Fired when a touch interaction ends, such as when the finger is lifted from the screen.\n     * @example\n     * ```ts\n     * const sprite = new Sprite(texture);\n     * sprite.eventMode = 'static';\n     *\n     * // Using emitter handler\n     * sprite.on('touchend', (event) => {\n     *     sprite.scale.set(1.0);\n     * });\n     * // Using property-based handler\n     * sprite.ontouchend = (event) => {\n     *    sprite.scale.set(1.0);\n     * };\n     * ```\n     * @default null\n     */\n    ontouchend?: FederatedEventHandler | null;\n\n    /**\n     * Property-based event handler for the `touchendoutside` event.\n     * Fired when a touch interaction ends outside the bounds of the display object\n     * that initially registered a touchstart.\n     * @example\n     * ```ts\n     * const sprite = new Sprite(texture);\n     * sprite.eventMode = 'static';\n     *\n     * // Using emitter handler\n     * sprite.on('touchendoutside', (event) => {\n     *     sprite.scale.set(1.0);\n     * });\n     * // Using property-based handler\n     * sprite.ontouchendoutside = (event) => {\n     *     sprite.scale.set(1.0);\n     * };\n     * ```\n     * @default null\n     */\n    ontouchendoutside?: FederatedEventHandler | null;\n\n    /**\n     * Property-based event handler for the `touchmove` event.\n     * Fired when a touch interaction moves while over the display object.\n     * @example\n     * ```ts\n     * const sprite = new Sprite(texture);\n     * sprite.eventMode = 'static';\n     *\n     * // Using emitter handler\n     * sprite.on('touchmove', (event) => {\n     *     sprite.position.set(event.global.x, event.global.y);\n     * });\n     * // Using property-based handler\n     * sprite.ontouchmove = (event) => {\n     *     sprite.position.set(event.global.x, event.global.y);\n     * };\n     * ```\n     * @default null\n     */\n    ontouchmove?: FederatedEventHandler | null;\n\n    /**\n     * Property-based event handler for the `globaltouchmove` event.\n     *\n     * Fired when a touch interaction moves anywhere, regardless of whether the pointer is over this object.\n     * The object must have `eventMode` set to 'static' or 'dynamic' to receive this event.\n     * @example\n     * ```ts\n     * const sprite = new Sprite(texture);\n     * sprite.eventMode = 'static';\n     *\n     * // Using emitter handler\n     * sprite.on('globaltouchmove', (event) => {\n     *     sprite.position.set(event.global.x, event.global.y);\n     * });\n     * // Using property-based handler\n     * sprite.onglobaltouchmove = (event) => {\n     *     sprite.position.set(event.global.x, event.global.y);\n     * };\n     * ```\n     * @default null\n     * @remarks\n     * - Fires even when the touch is outside the object's bounds\n     * - Useful for drag operations or global touch tracking\n     * - Must have `eventMode` set appropriately to receive events\n     * - Part of the global move events family along with `globalpointermove` and `globalmousemove`\n     */\n    onglobaltouchmove?: FederatedEventHandler | null;\n\n    /**\n     * Property-based event handler for the `touchstart` event.\n     * Fired when a touch interaction starts, such as when a finger touches the screen.\n     * @example\n     * ```ts\n     * const sprite = new Sprite(texture);\n     * sprite.eventMode = 'static';\n     *\n     * // Using emitter handler\n     * sprite.on('touchstart', (event) => {\n     *     sprite.scale.set(0.9);\n     * });\n     * // Using property-based handler\n     * sprite.ontouchstart = (event) => {\n     *     sprite.scale.set(0.9);\n     * };\n     * ```\n     * @default null\n     */\n    ontouchstart?: FederatedEventHandler | null;\n\n    /**\n     * Property-based event handler for the `wheel` event.\n     * Fired when the mouse wheel is scrolled while over the display object.\n     * @example\n     * ```ts\n     * const sprite = new Sprite(texture);\n     * sprite.eventMode = 'static';\n     *\n     * // Using emitter handler\n     * sprite.on('wheel', (event) => {\n     *     sprite.scale.x += event.deltaY * 0.01; // Zoom in/out\n     *     sprite.scale.y += event.deltaY * 0.01; // Zoom in/out\n     * });\n     * // Using property-based handler\n     * sprite.onwheel = (event) => {\n     *     sprite.scale.x += event.deltaY * 0.01; // Zoom in/out\n     *     sprite.scale.y += event.deltaY * 0.01; // Zoom in/out\n     * };\n     * ```\n     * @default null\n     */\n    onwheel?: FederatedEventHandler<FederatedWheelEvent> | null;\n}\n\n/**\n * The options for the `addEventListener` method.\n * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/EventTarget/addEventListener}\n * @category events\n * @advanced\n */\nexport type AddListenerOptions = boolean | AddEventListenerOptions;\n/**\n * The options for the `removeEventListener` method.\n * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/EventTarget/removeEventListener}\n * @category events\n * @advanced\n */\nexport type RemoveListenerOptions = boolean | EventListenerOptions;\n\n/**\n * Additional properties for a Container that is used for interaction events.\n * @category events\n * @advanced\n */\nexport interface IFederatedContainer extends FederatedOptions\n{\n    /** The parent of this event target. */\n    readonly parent?: Container;\n\n    /** The children of this event target. */\n    readonly children?: ReadonlyArray<Container>;\n\n    /** @private */\n    _internalEventMode: EventMode;\n\n    /**\n     * Determines if the container is interactive or not\n     * @returns {boolean} Whether the container is interactive or not\n     * @since 7.2.0\n     * @example\n     * import { Sprite } from 'pixi.js';\n     *\n     * const sprite = new Sprite(texture);\n     * sprite.eventMode = 'static';\n     * sprite.isInteractive(); // true\n     *\n     * sprite.eventMode = 'dynamic';\n     * sprite.isInteractive(); // true\n     *\n     * sprite.eventMode = 'none';\n     * sprite.isInteractive(); // false\n     *\n     * sprite.eventMode = 'passive';\n     * sprite.isInteractive(); // false\n     *\n     * sprite.eventMode = 'auto';\n     * sprite.isInteractive(); // false\n     */\n    isInteractive: () => boolean;\n    /**\n     * Unlike `on` or `addListener` which are methods from EventEmitter, `addEventListener`\n     * seeks to be compatible with the DOM's `addEventListener` with support for options.\n     * @param {any} type - The type of event to listen to.\n     * @param {any} listener - The listener callback or object.\n     * @param {any} options - Listener options, used for capture phase.\n     * @example\n     * // Tell the user whether they did a single, double, triple, or nth click.\n     * button.addEventListener('click', {\n     *     handleEvent(e): {\n     *         let prefix;\n     *\n     *         switch (e.detail) {\n     *             case 1: prefix = 'single'; break;\n     *             case 2: prefix = 'double'; break;\n     *             case 3: prefix = 'triple'; break;\n     *             default: prefix = e.detail + 'th'; break;\n     *         }\n     *\n     *         console.log('That was a ' + prefix + 'click');\n     *     }\n     * });\n     *\n     * // But skip the first click!\n     * button.parent.addEventListener('click', function blockClickOnce(e) {\n     *     e.stopImmediatePropagation();\n     *     button.parent.removeEventListener('click', blockClickOnce, true);\n     * }, {\n     *     capture: true,\n     * });\n     */\n    addEventListener<K extends keyof AllFederatedEventMap>(\n        type: K,\n        listener: (e: AllFederatedEventMap[K]) => any,\n        options?: AddListenerOptions\n    ): void;\n    addEventListener(\n        type: string,\n        listener: EventListenerOrEventListenerObject,\n        options?: AddListenerOptions\n    ): void;\n    /**\n     * Unlike `off` or `removeListener` which are methods from EventEmitter, `removeEventListener`\n     * seeks to be compatible with the DOM's `removeEventListener` with support for options.\n     * @param {K} type - The type of event the listener is bound to.\n     * @param {any} listener - The listener callback or object.\n     * @param {RemoveListenerOptions} options - The original listener options.\n     * This is required to deregister a capture phase listener.\n     */\n    removeEventListener<K extends keyof AllFederatedEventMap>(\n        type: K,\n        listener: (e: AllFederatedEventMap[K]) => any,\n        options?: RemoveListenerOptions\n    ): void;\n    removeEventListener(\n        type: string,\n        listener: EventListenerOrEventListenerObject,\n        options?: RemoveListenerOptions\n    ): void;\n    /**\n     * Dispatch the event on this {@link Container} using the event's {@link EventBoundary}.\n     *\n     * The target of the event is set to `this` and the `defaultPrevented` flag is cleared before dispatch.\n     * @param {FederatedEvent} e - The event to dispatch.\n     * @returns Whether the {@link FederatedEvent.preventDefault preventDefault}() method was not invoked.\n     * @example\n     * // Reuse a click event!\n     * button.dispatchEvent(clickEvent);\n     */\n    dispatchEvent(e: FederatedEvent): boolean;\n}\n\n/** @internal */\nexport const FederatedContainer: IFederatedContainer = {\n    onclick: null,\n    onmousedown: null,\n    onmouseenter: null,\n    onmouseleave: null,\n    onmousemove: null,\n    onglobalmousemove: null,\n    onmouseout: null,\n    onmouseover:  null,\n    onmouseup:  null,\n    onmouseupoutside: null,\n    onpointercancel: null,\n    onpointerdown:  null,\n    onpointerenter: null,\n    onpointerleave:  null,\n    onpointermove:  null,\n    onglobalpointermove:  null,\n    onpointerout:  null,\n    onpointerover:  null,\n    onpointertap:  null,\n    onpointerup:  null,\n    onpointerupoutside:  null,\n    onrightclick:  null,\n    onrightdown:  null,\n    onrightup:  null,\n    onrightupoutside:  null,\n    ontap:  null,\n    ontouchcancel:  null,\n    ontouchend:  null,\n    ontouchendoutside:  null,\n    ontouchmove:  null,\n    onglobaltouchmove:  null,\n    ontouchstart:  null,\n    onwheel:  null,\n    get interactive()\n    {\n        return this.eventMode === 'dynamic' || this.eventMode === 'static';\n    },\n    set interactive(value: boolean)\n    {\n        this.eventMode = value ? 'static' : 'passive';\n    },\n    _internalEventMode: undefined,\n    get eventMode()\n    {\n        return this._internalEventMode ?? EventSystem.defaultEventMode;\n    },\n    set eventMode(value)\n    {\n        this._internalEventMode = value;\n    },\n    isInteractive(): boolean\n    {\n        return this.eventMode === 'static' || this.eventMode === 'dynamic';\n    },\n    interactiveChildren: true,\n    hitArea: null,\n    addEventListener(\n        type: string,\n        listener: EventListenerOrEventListenerObject,\n        options?: AddListenerOptions\n    )\n    {\n        const capture = (typeof options === 'boolean' && options)\n        || (typeof options === 'object' && options.capture);\n        const signal = typeof options === 'object' ? options.signal : undefined;\n        const once = typeof options === 'object' ? (options.once === true) : false;\n        const context = typeof listener === 'function' ? undefined : listener;\n\n        type = capture ? `${type}capture` : type;\n        const listenerFn = typeof listener === 'function' ? listener : listener.handleEvent;\n\n        const emitter = (this as unknown as EventEmitter);\n\n        if (signal)\n        {\n            signal.addEventListener('abort', () =>\n            {\n                emitter.off(type, listenerFn, context);\n            });\n        }\n\n        if (once)\n        {\n            emitter.once(type, listenerFn, context);\n        }\n        else\n        {\n            emitter.on(type, listenerFn, context);\n        }\n    },\n    removeEventListener(\n        type: string,\n        listener: EventListenerOrEventListenerObject,\n        options?: RemoveListenerOptions\n    )\n    {\n        const capture = (typeof options === 'boolean' && options)\n            || (typeof options === 'object' && options.capture);\n        const context = typeof listener === 'function' ? undefined : listener;\n\n        type = capture ? `${type}capture` : type;\n        listener = typeof listener === 'function' ? listener : listener.handleEvent;\n\n        (this as unknown as EventEmitter).off(type, listener, context);\n    },\n    dispatchEvent(e: Event): boolean\n    {\n        if (!(e instanceof FederatedEvent))\n        {\n            throw new Error('Container cannot propagate events outside of the Federated Events API');\n        }\n\n        e.defaultPrevented = false;\n        e.path = null;\n        e.target = this as Container;\n        e.manager.dispatchEvent(e);\n\n        return !e.defaultPrevented;\n    }\n};\n", "import { ExtensionType } from '../extensions/Extensions';\nimport { type DOMContainer } from './DOMContainer';\n\nimport type { InstructionSet } from '../rendering/renderers/shared/instructions/InstructionSet';\nimport type { RenderPipe } from '../rendering/renderers/shared/instructions/RenderPipe';\nimport type { Renderer } from '../rendering/renderers/types';\n\n/**\n * The DOMPipe class is responsible for managing and rendering DOM elements within a PixiJS scene.\n * It maps dom elements to the canvas and ensures they are correctly positioned and visible.\n * @internal\n */\nexport class DOMPipe implements RenderPipe<DOMContainer>\n{\n    /**\n     * Static property defining the extension type and name for the DOMPipe.\n     * This is used to register the DOMPipe with different rendering pipelines.\n     */\n    public static extension = {\n        type: [\n            ExtensionType.WebGLPipes,\n            ExtensionType.WebGPUPipes,\n            ExtensionType.CanvasPipes,\n        ],\n        name: 'dom',\n    } as const;\n\n    private _renderer: Renderer;\n\n    /** Array to keep track of attached DOM elements */\n    private readonly _attachedDomElements: DOMContainer[] = [];\n    /** The main DOM element that acts as a container for other DOM elements */\n    private readonly _domElement: HTMLDivElement;\n\n    /**\n     * Constructor for the DOMPipe class.\n     * @param renderer - The renderer instance that this DOMPipe will be associated with.\n     */\n    constructor(renderer: Renderer)\n    {\n        this._renderer = renderer;\n\n        // Add this DOMPipe to the postrender runner of the renderer\n        // we want to dom elements are calculated after all things have been rendered\n        this._renderer.runners.postrender.add(this);\n\n        // Create a main DOM element to contain other DOM elements\n        this._domElement = document.createElement('div');\n        this._domElement.style.position = 'absolute';\n        this._domElement.style.top = '0';\n        this._domElement.style.left = '0';\n        this._domElement.style.pointerEvents = 'none';\n        this._domElement.style.zIndex = '1000';\n    }\n\n    /**\n     * Adds a renderable DOM container to the list of attached elements.\n     * @param domContainer - The DOM container to be added.\n     * @param _instructionSet - The instruction set (unused).\n     */\n    public addRenderable(domContainer: DOMContainer, _instructionSet: InstructionSet): void\n    {\n        if (!this._attachedDomElements.includes(domContainer))\n        {\n            this._attachedDomElements.push(domContainer);\n        }\n    }\n\n    /**\n     * Updates a renderable DOM container.\n     * @param _domContainer - The DOM container to be updated (unused).\n     */\n    public updateRenderable(_domContainer: DOMContainer): void\n    {\n        // Updates happen in postrender\n    }\n\n    /**\n     * Validates a renderable DOM container.\n     * @param _domContainer - The DOM container to be validated (unused).\n     * @returns Always returns true as validation is not required.\n     */\n    public validateRenderable(_domContainer: DOMContainer): boolean\n    {\n        return true;\n    }\n\n    /** Handles the post-rendering process, ensuring DOM elements are correctly positioned and visible. */\n    public postrender(): void\n    {\n        const attachedDomElements = this._attachedDomElements;\n\n        if (attachedDomElements.length === 0)\n        {\n            this._domElement.remove();\n\n            return;\n        }\n\n        const canvas = this._renderer.view.canvas as HTMLCanvasElement;\n\n        if (this._domElement.parentNode !== canvas.parentNode)\n        {\n            canvas.parentNode?.appendChild(this._domElement);\n        }\n\n        const sx = (parseFloat(canvas.style.width) / canvas.width) * this._renderer.resolution;\n        const sy = (parseFloat(canvas.style.height) / canvas.height) * this._renderer.resolution;\n        // scale according to the canvas scale and translate\n\n        this._domElement.style.transform = `translate(${canvas.offsetLeft}px, ${canvas.offsetTop}px) scale(${sx}, ${sy})`;\n\n        for (let i = 0; i < attachedDomElements.length; i++)\n        {\n            const domContainer = attachedDomElements[i];\n            const element = domContainer.element;\n\n            if (!domContainer.parent || domContainer.globalDisplayStatus < 0b111)\n            {\n                element?.remove();\n                attachedDomElements.splice(i, 1);\n                i--;\n            }\n            else\n            {\n                if (!this._domElement.contains(element))\n                {\n                    element.style.position = 'absolute';\n                    element.style.pointerEvents = 'auto';\n                    this._domElement.appendChild(element);\n                }\n\n                const wt = domContainer.worldTransform;\n                const anchor = domContainer._anchor;\n                const ax = domContainer.width * anchor.x;\n                const ay = domContainer.height * anchor.y;\n\n                element.style.transformOrigin = `${ax}px ${ay}px`;\n                element.style.transform = `matrix(${wt.a}, ${wt.b}, ${wt.c}, ${wt.d}, ${wt.tx - ax}, ${wt.ty - ay})`;\n                element.style.opacity = domContainer.groupAlpha.toString();\n            }\n        }\n    }\n\n    /** Destroys the DOMPipe, removing all attached DOM elements and cleaning up resources. */\n    public destroy(): void\n    {\n        this._renderer.runners.postrender.remove(this);\n\n        for (let i = 0; i < this._attachedDomElements.length; i++)\n        {\n            const domContainer = this._attachedDomElements[i];\n\n            domContainer.element?.remove();\n        }\n\n        this._attachedDomElements.length = 0;\n        this._domElement.remove();\n        this._renderer = null;\n    }\n}\n", "import { Point } from '../maths/point/Point';\nimport { ViewContainer, type ViewContainerOptions } from '../scene/view/ViewContainer';\n\nimport type { PointData } from '../maths/point/PointData';\n\n/**\n * Options for configuring a {@link DOMContainer}.\n * Controls how DOM elements are integrated into the PixiJS scene graph.\n * @example\n * ```ts\n * // Create with a custom element\n * const domContainer = new DOMContainer({\n *     element: document.createElement('input'),\n *     anchor: { x: 0.5, y: 0.5 } // or anchor: 0.5 to center both x and y\n * });\n * ```\n * @category scene\n * @standard\n * @noInheritDoc\n */\nexport interface DOMContainerOptions extends ViewContainerOptions\n{\n    /**\n     * The DOM element to use for the container.\n     * Can be any HTML element like div, input, textarea, etc.\n     *\n     * If not provided, creates a new div element.\n     * @default document.createElement('div')\n     */\n    element?: HTMLElement;\n\n    /**\n     * The anchor point of the container.\n     * - Can be a single number to set both x and y\n     * - Can be a point-like object with x,y coordinates\n     * - (0,0) is top-left\n     * - (1,1) is bottom-right\n     * - (0.5,0.5) is center\n     * @default 0\n     */\n    anchor?: PointData | number;\n}\n\n/**\n * The DOMContainer object is used to render DOM elements within the PixiJS scene graph.\n * It allows you to integrate HTML elements into your PixiJS application while maintaining\n * proper transform hierarchy and visibility.\n *\n * DOMContainer is especially useful for rendering standard DOM elements\n * that handle user input, such as `<input>` or `<textarea>`.\n * This is often simpler and more flexible than trying to implement text input\n * directly in PixiJS. For instance, if you need text fields or text areas,\n * you can embed them through this container for native browser text handling.\n *\n * --------- EXPERIMENTAL ---------\n *\n * This is a new API, things may change and it may not work as expected.\n * We want to hear your feedback as we go!\n *\n * --------------------------------\n * @example\n * @example\n * ```ts\n * // Basic text display\n * const textContainer = new DOMContainer();\n * textContainer.element.innerHTML = 'Hello World!';\n * app.stage.addChild(textContainer);\n *\n * // Input field with centered anchor\n * const inputContainer = new DOMContainer({\n *     element: document.createElement('input'),\n *     anchor: 0.5\n * });\n * inputContainer.position.set(400, 300);\n * app.stage.addChild(inputContainer);\n *\n * // Rich text area\n * const textArea = new DOMContainer({\n *     element: document.createElement('textarea'),\n *     anchor: { x: 0, y: 0 }\n * });\n * textArea.scale.set(2);\n * app.stage.addChild(textArea);\n * ```\n * @category scene\n * @standard\n */\nexport class DOMContainer extends ViewContainer<never>\n{\n    /** @internal */\n    public override readonly renderPipeId: string = 'dom';\n\n    /** @internal */\n    public batched = false;\n    /**\n     * The anchor point of the container.\n     * @internal\n     */\n    public readonly _anchor: Point;\n\n    /** The DOM element that this container is using. */\n    private _element: HTMLElement;\n\n    /**\n     * @param options - The options for creating the DOM container.\n     */\n    constructor(options: DOMContainerOptions = {})\n    {\n        const { element, anchor, ...rest } = options;\n\n        super({\n            label: 'DOMContainer',\n            ...rest\n        });\n\n        this._anchor = new Point(0, 0);\n\n        if (anchor)\n        {\n            this.anchor = anchor;\n        }\n\n        this.element = options.element || document.createElement('div');\n    }\n\n    /**\n     * The anchor sets the origin point of the container.\n     * Controls the relative positioning of the DOM element.\n     *\n     * The default is `(0,0)`, this means the container's origin is the top left.\n     * Setting the anchor to `(0.5,0.5)` means the container's origin is centered.\n     * Setting the anchor to `(1,1)` would mean the container's origin point will be the bottom right corner.\n     * @example\n     * ```ts\n     * const container = new DOMContainer();\n     *\n     * // Set anchor to center (shorthand)\n     * container.anchor = 0.5;\n     *\n     * // Set anchor to bottom-right\n     * container.anchor = { x: 1, y: 1 };\n     *\n     * // Set anchor to custom position\n     * container.anchor = new Point(0.3, 0.7);\n     * ```\n     */\n    get anchor(): Point\n    {\n        return this._anchor;\n    }\n\n    /**\n     * Sets the anchor point of the container.\n     * @param value - New anchor value:\n     * - number: Sets both x and y to same value\n     * - PointData: Sets x and y separately\n     */\n    set anchor(value: PointData | number)\n    {\n        typeof value === 'number' ? this._anchor.set(value) : this._anchor.copyFrom(value);\n    }\n\n    /**\n     * Sets the DOM element for this container.\n     * This will replace the current element and update the view.\n     * @param value - The new DOM element to use\n     * @example\n     * ```ts\n     * const domContainer = new DOMContainer();\n     * domContainer.element = document.createElement('input');\n     * ```\n     */\n    set element(value: HTMLElement)\n    {\n        if (this._element === value) return;\n\n        this._element = value;\n        this.onViewUpdate();\n    }\n\n    /**\n     * The DOM element associated with this container.\n     * @example\n     * ```ts\n     * const domContainer = new DOMContainer();\n     * domContainer.element.innerHTML = 'Hello World!';\n     * document.body.appendChild(domContainer.element);\n     * ```\n     */\n    get element(): HTMLElement\n    {\n        return this._element;\n    }\n\n    /** @private */\n    protected updateBounds()\n    {\n        const bounds = this._bounds;\n        const element = this._element;\n\n        if (!element)\n        {\n            bounds.minX = 0;\n            bounds.minY = 0;\n            bounds.maxX = 0;\n            bounds.maxY = 0;\n\n            return;\n        }\n\n        const { offsetWidth, offsetHeight } = element;\n\n        bounds.minX = 0;\n        bounds.maxX = offsetWidth;\n        bounds.minY = 0;\n        bounds.maxY = offsetHeight;\n    }\n\n    /**\n     * Destroys this DOM container.\n     * @param options - Options parameter. A boolean will act as if all options\n     *  have been set to that\n     * @example\n     * domContainer.destroy();\n     * domContainer.destroy(true);\n     */\n    public override destroy(options: boolean = false)\n    {\n        super.destroy(options);\n\n        this._element?.parentNode?.removeChild(this._element);\n        this._element = null;\n        (this._anchor as null) = null;\n    }\n}\n"], "mappings": ";;;;;;;;;;;;AAqHO,IAAM,iBAAN,MAAM,gBACb;;;;;EAwGI,YAAY,SACZ;AAvGA,SAAO,UAAU;AAGjB,SAAO,eAAe;AAMtB,SAAgB,aAAa;AAQ7B,SAAgB,WAAW;AAM3B,SAAO,mBAAmB;AAMnB,SAAA,aAAa,gBAAe,UAAU;AA2B7C,SAAO,qBAAqB;AAG5B,SAAO,gCAAgC;AAsBhC,SAAA,QAAe,IAAI,MAAM;AASzB,SAAA,OAAc,IAAI,MAAM;AA2J/B,SAAgB,OAAO;AAMvB,SAAgB,kBAAkB;AAMlC,SAAgB,YAAY;AAM5B,SAAgB,iBAAiB;AA/J7B,SAAK,UAAU;EAAA;;EApBnB,IAAI,SAAiB;AAAE,WAAO,KAAK,MAAM;EAAA;;EAGzC,IAAI,SAAiB;AAAE,WAAO,KAAK,MAAM;EAAA;;EAMzC,IAAI,QAAgB;AAAE,WAAO,KAAK,KAAK;EAAA;;EAGvC,IAAI,QAAgB;AAAE,WAAO,KAAK,KAAK;EAAA;;;;;EAevC,IAAI,OACJ;AACW,WAAA;EAAA;;;;;EAOJ,eACP;AAGI,QAAI,KAAK,YAAY,CAAC,KAAK,QAAQ,KAAK,KAAK,KAAK,KAAK,SAAS,CAAC,MAAM,KAAK,SAC5E;AACS,WAAA,OAAO,KAAK,SAAS,KAAK,QAAQ,gBAAgB,KAAK,MAAM,IAAI,CAAA;IAAC;AAG3E,WAAO,KAAK;EAAA;;;;;;;;;EAWT,UAAU,OAAe,UAAoB,aACpD;AACU,UAAA,IAAI,MAAM,qFAAqF;EAAA;;;;;;;;;;;EAalG,YAAY,UAAkB,aAAuB,gBAA0B,UAClF,YACJ;AACU,UAAA,IAAI,MAAM,uFAAuF;EAAA;;;;;;;;;;;;;;;;;;EAoBpG,iBACP;AACI,QAAI,KAAK,uBAAuB,SAAS,KAAK,YAAY,YAC1D;AACI,WAAK,YAAY,eAAe;IAAA;AAGpC,SAAK,mBAAmB;EAAA;;;;;;;;;;;;;;;;;;;;EAsBrB,2BACP;AACI,SAAK,gCAAgC;EAAA;;;;;;;;;;;;;;;;;;;;;;;;EA0BlC,kBACP;AACI,SAAK,qBAAqB;EAAA;AA2BlC;;;AChYA,IAAM,cAAc;AACpB,IAAM,YAAY;AAClB,IAAM,cAAc;AACpB,IAAM,iBAAiB;AACvB,IAAM,eAAe;AACrB,IAAM,gBAAgB;AACtB,IAAM,cAAc;AACpB,IAAM,eAAe;AACrB,IAAM,eAAe;AACrB,IAAM,gBAAgB;AACtB,IAAM,kBAAkB;AACxB,IAAM,oBAAoB;AAC1B,IAAM,aAAa;AACnB,IAAM,cAAc;AACpB,IAAM,eAAe;AASrB,IAAM,uBAAuB,SAACA,YAAqB;AACjD,SACE,OAAOA,eAAc,eACrBA,WAAU,aAAa,cACvB,OAAOA,WAAU,mBAAmB,YACpCA,WAAU,iBAAiB,KAC3B,OAAO,aAAa;AAExB;AAEA,SAAS,YAAY,WAAoB;AACvC,SAAO,SAAC,OAAa;AAAc,WAAA,MAAM,KAAK,SAAS;EAApB;AACrC;AAwCc,SAAP,SAA0B,OAAyB;AACxD,MAAI,MAAiB;IACnB,WAAW;IACX,UAAU;IACV,gBAAgB;;AAGlB,MAAI,CAAC,SAAS,OAAO,cAAc,aAAa;AAC9C,UAAM;MACJ,WAAW,UAAU;MACrB,UAAU,UAAU;MACpB,gBAAgB,UAAU,kBAAkB;;aAErC,OAAO,UAAU,UAAU;AACpC,QAAI,YAAY;aACP,SAAS,MAAM,WAAW;AACnC,UAAM;MACJ,WAAW,MAAM;MACjB,UAAU,MAAM;MAChB,gBAAgB,MAAM,kBAAkB;;;AAI5C,MAAI,YAAY,IAAI;AAIpB,MAAI,MAAM,UAAU,MAAM,OAAO;AACjC,MAAI,OAAO,IAAI,CAAC,MAAM,aAAa;AACjC,gBAAY,IAAI,CAAC;;AAMnB,QAAM,UAAU,MAAM,SAAS;AAC/B,MAAI,OAAO,IAAI,CAAC,MAAM,aAAa;AACjC,gBAAY,IAAI,CAAC;;AAGnB,MAAM,QAAQ,YAAY,SAAS;AAEnC,MAAM,SAAyB;IAC7B,OAAO;MACL,OAAO,MAAM,WAAW,KAAK,CAAC,MAAM,YAAY;MAChD,MAAM,MAAM,SAAS;MACrB,QACE,CAAC,MAAM,WAAW,MACjB,MAAM,WAAW,KAAK,qBAAqB,GAAG,MAC/C,CAAC,MAAM,YAAY;MACrB,WAAW,MAAM,cAAc;MAC/B,SACG,MAAM,WAAW,KAChB,MAAM,SAAS,KACf,MAAM,WAAW,KACjB,MAAM,cAAc,KACpB,qBAAqB,GAAG,MAC1B,CAAC,MAAM,YAAY;;IAEvB,QAAQ;MACN,OAAO,MAAM,WAAW;MACxB,QAAQ,CAAC,MAAM,WAAW,KAAK,MAAM,YAAY;MACjD,QAAQ,MAAM,WAAW,KAAK,MAAM,YAAY;;IAElD,SAAS;MACP,OACG,CAAC,MAAM,YAAY,KAAK,MAAM,WAAW,KACzC,CAAC,MAAM,YAAY,KAAK,MAAM,YAAY;MAC7C,QACE,CAAC,MAAM,YAAY,KACnB,CAAC,MAAM,WAAW,KAClB,CAAC,MAAM,YAAY,MAClB,MAAM,YAAY,KAAK,MAAM,aAAa;MAC7C,QACG,CAAC,MAAM,YAAY,MACjB,MAAM,WAAW,KAChB,MAAM,YAAY,KAClB,MAAM,YAAY,KAClB,MAAM,aAAa,MACvB,MAAM,aAAa;;IAEvB,SAAS;MACP,OAAO,MAAM,YAAY;MACzB,QAAQ,MAAM,aAAa;MAC3B,QAAQ,MAAM,YAAY,KAAK,MAAM,aAAa;;IAEpD,OAAO;MACL,YAAY,MAAM,eAAe;MACjC,cAAc,MAAM,iBAAiB;MACrC,OAAO,MAAM,UAAU;MACvB,SAAS,MAAM,YAAY;MAC3B,QAAQ,MAAM,WAAW;MACzB,QACE,MAAM,eAAe,KACrB,MAAM,iBAAiB,KACvB,MAAM,UAAU,KAChB,MAAM,YAAY,KAClB,MAAM,WAAW;;IAErB,KAAK;IACL,OAAO;IACP,QAAQ;;AAGV,SAAO,MACL,OAAO,MAAM,UACb,OAAO,QAAQ,UACf,OAAO,QAAQ,UACf,OAAO,MAAM;AAEf,SAAO,QACL,OAAO,MAAM,SAAS,OAAO,QAAQ,SAAS,OAAO,QAAQ;AAC/D,SAAO,SACL,OAAO,MAAM,UAAU,OAAO,QAAQ,UAAU,OAAO,QAAQ;AAEjE,SAAO;AACT;;;AC3LA,IAAM,eAAgB,SAA8B,WAAW;AAkOlD,IAAAC,YAA2B,aAAa,WAAW,SAAS;;;ACzNzE,IAAM,eAAe;AAErB,IAAM,iBAAiB;AACvB,IAAM,kBAAkB;AACxB,IAAM,kBAAkB;AACxB,IAAM,mBAAmB;AAEzB,IAAM,gBAAgB;AACtB,IAAM,iBAAiB;AACvB,IAAM,iBAAiB;AACvB,IAAM,kBAAkB;AAgEjB,IAAM,uBAAN,MAAMC,sBACb;;;;;;EA4FI,YAAY,UAAqC,cAA8BC,WAC/E;AADiD,SAAA,cAAA;AA7CjD,SAAO,QAAQ;AAGf,SAAQ,iBAAiB;AAGzB,SAAQ,yBAAyB;AASjC,SAAQ,YAAY;AAGpB,SAAQ,yBAAyB;AAMjC,SAAQ,OAA2B;AAGnC,SAAQ,QAAiC,CAAA;AAGzC,SAAQ,YAAY;AAGpB,SAAQ,YAAyB,CAAA;AAGjC,SAAQ,sBAAsB;AAG9B,SAAiB,0BAA0B;AAQvC,SAAK,WAAW;AAEZ,QAAA,YAAY,UAAU,YAAY,OACtC;AACI,WAAK,iBAAiB;IAAA;AAG1B,SAAK,YAAY;EAAA;;;;;;EAQrB,IAAI,WACJ;AACI,WAAO,KAAK;EAAA;;;;;;EAQhB,IAAI,wBACJ;AACI,WAAO,KAAK;EAAA;;;;;EAOhB,IAAI,UACJ;AACI,WAAO,KAAK;EAAA;;;;;EAOR,mBACR;AACU,UAAA,UAAU,SAAS,cAAc,QAAQ;AAEvC,YAAA,MAAM,QAAQ,GAAG,aAAa;AAC9B,YAAA,MAAM,SAAS,GAAG,aAAa;AACvC,YAAQ,MAAM,WAAW;AACjB,YAAA,MAAM,MAAM,GAAG,cAAc;AAC7B,YAAA,MAAM,OAAO,GAAG,cAAc;AAC9B,YAAA,MAAM,SAAS,gBAAgB,SAAS;AAChD,YAAQ,MAAM,kBAAkB;AAChC,YAAQ,QAAQ;AAER,YAAA,iBAAiB,SAAS,MAClC;AACI,WAAK,yBAAyB;AAC9B,WAAK,UAAU;AACf,WAAK,kBAAkB;IAAA,CAC1B;AAEQ,aAAA,KAAK,YAAY,OAAO;AACjC,SAAK,WAAW;EAAA;;;;;EAOZ,oBACR;AACQ,QAAA,CAAC,KAAK,UACV;AACI;IAAA;AAEK,aAAA,KAAK,YAAY,KAAK,QAAQ;AACvC,SAAK,WAAW;EAAA;;;;;;EAQZ,YACR;AACI,QAAI,KAAK,WACT;AACI;IAAA;AAGJ,SAAK,YAAY;AAGb,QAAA,CAAC,KAAK,MACV;AACS,WAAA,OAAO,SAAS,cAAc,KAAK;AACxC,WAAK,KAAK,MAAM,QAAQ,GAAG,cAAc;AACzC,WAAK,KAAK,MAAM,SAAS,GAAG,cAAc;AACrC,WAAA,KAAK,MAAM,WAAW;AAC3B,WAAK,KAAK,MAAM,MAAM,GAAG,eAAe;AACxC,WAAK,KAAK,MAAM,OAAO,GAAG,eAAe;AACzC,WAAK,KAAK,MAAM,SAAS,iBAAiB,SAAS;AAC9C,WAAA,KAAK,MAAM,gBAAgB;IAAA;AAIpC,QAAI,KAAK,gBACT;AACI,WAAK,aAAa,KAAK,WAAW,KAAK,IAAI;AAC3C,iBAAW,iBAAiB,WAAW,KAAK,YAAY,KAAK;IAAA;AAGjE,QAAI,KAAK,wBACT;AACI,WAAK,eAAe,KAAK,aAAa,KAAK,IAAI;AAC/C,iBAAW,SAAS,iBAAiB,aAAa,KAAK,cAAc,IAAI;IAAA;AAIvE,UAAA,SAAS,KAAK,UAAU,KAAK;AAE/B,QAAA,CAAC,OAAO,YACZ;AACU,YAAA,WAAW,IAAI,iBAAiB,MACtC;AACI,YAAI,OAAO,YACX;AACW,iBAAA,WAAW,YAAY,KAAK,IAAI;AACvC,mBAAS,WAAW;AAGpB,eAAK,wBAAwB;QAAA;MACjC,CACH;AAEQ,eAAA,QAAQ,SAAS,MAAM,EAAE,WAAW,MAAM,SAAS,KAAA,CAAM;IAAA,OAGtE;AAEW,aAAA,WAAW,YAAY,KAAK,IAAI;AAGvC,WAAK,wBAAwB;IAAA;EACjC;;EAII,0BACR;AAEI,SAAK,UAAU,QAAQ,WAAW,IAAI,IAAI;AAGtC,QAAA,KAAK,UAAU,oBACnB;AACS,WAAA,yBAAyB,KAAK,UAAU,kBAA+B;IAAA;EAChF;;;;;EAOI,cACR;AACI,QAAI,CAAC,KAAK,aAAa,KAAK,wBAC5B;AACI;IAAA;AAGJ,SAAK,YAAY;AAGjB,eAAW,SAAS,oBAAoB,aAAa,KAAK,cAAc,IAAI;AAC5E,QAAI,KAAK,gBACT;AACI,iBAAW,iBAAiB,WAAW,KAAK,YAAY,KAAK;IAAA;AAGjE,SAAK,UAAU,QAAQ,WAAW,OAAO,IAAI;AAGlC,eAAA,SAAS,KAAK,WACzB;AACI,UAAI,MAAM,kBAAkB,MAAM,eAAe,YACjD;AACI,cAAM,eAAe,WAAW,YAAY,MAAM,cAAc;AAChE,cAAM,iBAAiB;MAAA;AAE3B,YAAM,oBAAoB;IAAA;AAIzB,SAAA,MAAM,QAAQ,CAAC,QACpB;AACI,UAAI,IAAI,YACR;AACQ,YAAA,WAAW,YAAY,GAAG;MAAA;IAClC,CACH;AAGD,QAAI,KAAK,QAAQ,KAAK,KAAK,YAC3B;AACI,WAAK,KAAK,WAAW,YAAY,KAAK,IAAI;IAAA;AAG9C,SAAK,QAAQ,CAAA;AACb,SAAK,YAAY,CAAA;EAAC;;;;;;EAQd,yBAAyB,WACjC;AACI,QAAI,CAAC,UAAU,WAAW,CAAC,UAAU,oBACrC;AACI;IAAA;AAIJ,QAAI,UAAU,YACd;AACQ,UAAA,CAAC,UAAU,mBACf;AACI,aAAK,UAAU,SAAS;MAAA;AAG5B,gBAAU,YAAY,KAAK;IAAA;AAG/B,UAAM,WAAW,UAAU;AAE3B,QAAI,UACJ;AACI,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KACrC;AACS,aAAA,yBAAyB,SAAS,CAAC,CAAc;MAAA;IAC1D;EACJ;;;;;EAOG,KAAK,SACZ;AAEI,UAAM,cAAcD,sBAAoB;AACxC,UAAM,gBAAgB;MAClB,sBAAsB;QAClB,GAAG;QACH,GAAI,SAAS,wBAAwB,CAAA;MAAC;IAC1C;AAGC,SAAA,QAAQ,cAAc,qBAAqB;AAC3C,SAAA,iBAAiB,cAAc,qBAAqB;AACpD,SAAA,yBAAyB,cAAc,qBAAqB;AAE7D,QAAA,cAAc,qBAAqB,kBACvC;AACI,WAAK,UAAU;IAAA,WAEV,KAAK,gBACd;AACI,WAAK,aAAa,KAAK,WAAW,KAAK,IAAI;AAC3C,iBAAW,iBAAiB,WAAW,KAAK,YAAY,KAAK;IAAA;AAGjE,SAAK,UAAU,QAAQ,WAAW,OAAO,IAAI;EAAA;;;;;;;;;EAW1C,aACP;AAKU,UAAA,MAAM,YAAY,IAAI;AAE5B,QAAI,KAAK,YAAY,QAAQ,UAAU,MAAM,KAAK,qBAClD;AACI;IAAA;AAGC,SAAA,sBAAsB,MAAM,KAAK;AAElC,QAAA,CAAC,KAAK,UAAU,qBAAqB,CAAC,KAAK,UAAU,KAAK,QAC9D;AACI;IAAA;AAIE,UAAA,YAAA,oBAAgB,IAAY;AAE9B,QAAA,KAAK,UAAU,oBACnB;AACS,WAAA,yBAAyB,KAAK,UAAU,kBAA+B;AAGjE,iBAAA,SAAS,KAAK,WACzB;AACQ,YAAA,MAAM,cAAc,KAAK,WAC7B;AACI,oBAAU,IAAI,KAAK,UAAU,QAAQ,KAAK,CAAC;QAAA;MAC/C;IACJ;AAIJ,aAAS,IAAI,KAAK,UAAU,SAAS,GAAG,KAAK,GAAG,KAChD;AACU,YAAA,QAAQ,KAAK,UAAU,CAAC;AAE9B,UAAI,CAAC,UAAU,IAAI,CAAC,GACpB;AAEI,YAAI,MAAM,kBAAkB,MAAM,eAAe,YACjD;AACI,gBAAM,eAAe,WAAW,YAAY,MAAM,cAAc;AAE3D,eAAA,MAAM,KAAK,MAAM,cAAc;AACpC,gBAAM,iBAAiB;QAAA;AAE3B,cAAM,oBAAoB;AACd,oBAAA,KAAK,WAAW,GAAG,CAAC;MAAA;IACpC;AAIA,QAAA,KAAK,UAAU,mBACnB;AACU,YAAA,EAAE,GAAG,GAAG,OAAO,WAAW,QAAQ,WAAA,IAAe,KAAK,UAAU;AACtE,YAAM,MAAM,KAAK;AAEb,UAAA,MAAM,OAAO,GAAG,CAAC;AACjB,UAAA,MAAM,MAAM,GAAG,CAAC;AAChB,UAAA,MAAM,QAAQ,GAAG,SAAS;AAC1B,UAAA,MAAM,SAAS,GAAG,UAAU;IAAA;AAIpC,aAAS,IAAI,GAAG,IAAI,KAAK,UAAU,QAAQ,KAC3C;AACU,YAAA,QAAQ,KAAK,UAAU,CAAC;AAE9B,UAAI,CAAC,MAAM,qBAAqB,CAAC,MAAM,gBACvC;AACI;MAAA;AAIJ,YAAM,MAAM,MAAM;AAClB,YAAM,UAAW,MAAM,WAAW,MAAM,UAAA,EAAY;AAEpD,UAAI,MAAM,SACV;AACI,cAAM,KAAK,MAAM;AACX,cAAA,KAAK,KAAK,UAAU;AACpB,cAAA,KAAK,KAAK,UAAU;AAEtB,YAAA,MAAM,OAAO,IAAI,GAAG,KAAM,QAAQ,IAAI,GAAG,KAAM,EAAE;AACjD,YAAA,MAAM,MAAM,IAAI,GAAG,KAAM,QAAQ,IAAI,GAAG,KAAM,EAAE;AACpD,YAAI,MAAM,QAAQ,GAAG,QAAQ,QAAQ,GAAG,IAAI,EAAE;AAC9C,YAAI,MAAM,SAAS,GAAG,QAAQ,SAAS,GAAG,IAAI,EAAE;MAAA,OAGpD;AACI,aAAK,YAAY,OAAO;AAClB,cAAA,KAAK,KAAK,UAAU;AACpB,cAAA,KAAK,KAAK,UAAU;AAE1B,YAAI,MAAM,OAAO,GAAG,QAAQ,IAAI,EAAE;AAClC,YAAI,MAAM,MAAM,GAAG,QAAQ,IAAI,EAAE;AACjC,YAAI,MAAM,QAAQ,GAAG,QAAQ,QAAQ,EAAE;AACvC,YAAI,MAAM,SAAS,GAAG,QAAQ,SAAS,EAAE;MAAA;IAC7C;AAIC,SAAA;EAAA;;;;;;EAQD,iBAAiB,KACzB;AACQ,QAAA,YAAY,SAAS,IAAI,IAAI,iBAAiB,IAAI,KAAK,mBAAmB,IAAI,QAAQ;EAAA;;;;;EAOtF,YAAY,SACpB;AACQ,QAAA,QAAQ,IAAI,GAChB;AACI,cAAQ,SAAS,QAAQ;AACzB,cAAQ,IAAI;IAAA;AAGZ,QAAA,QAAQ,IAAI,GAChB;AACI,cAAQ,UAAU,QAAQ;AAC1B,cAAQ,IAAI;IAAA;AAGhB,UAAM,EAAE,OAAO,WAAW,QAAQ,WAAA,IAAe,KAAK;AAEtD,QAAI,QAAQ,IAAI,QAAQ,QAAQ,WAChC;AACY,cAAA,QAAQ,YAAY,QAAQ;IAAA;AAGxC,QAAI,QAAQ,IAAI,QAAQ,SAAS,YACjC;AACY,cAAA,SAAS,aAAa,QAAQ;IAAA;EAC1C;;;;;;;EASI,UAA+B,WACvC;AACQ,QAAA,MAAM,KAAK,MAAM,IAAI;AAEzB,QAAI,CAAC,KACL;AACQ,UAAA,UAAU,mBAAmB,UACjC;AACU,cAAA,SAAS,cAAc,QAAQ;MAAA,OAGzC;AACU,cAAA,SAAS,cAAc,UAAU,cAAc;AACrD,YAAI,MAAM,UAAU;;;;;;;;;;;;;;AAcpB,YAAI,UAAU,gBACd;AACI,cAAI,YAAY,UAAU;QAAA;MAC9B;AAEA,UAAA,MAAM,QAAQ,GAAG,cAAc;AAC/B,UAAA,MAAM,SAAS,GAAG,cAAc;AACpC,UAAI,MAAM,kBAAkB,KAAK,QAAQ,0BAA0B;AACnE,UAAI,MAAM,WAAW;AACjB,UAAA,MAAM,SAAS,iBAAiB,SAAS;AAC7C,UAAI,MAAM,cAAc;AAGxB,UAAI,UAAU,UAAU,YAAA,EAAc,SAAS,QAAQ,GACvD;AAEQ,YAAA,aAAa,aAAa,KAAK;MAAA,OAGvC;AACQ,YAAA,aAAa,aAAa,QAAQ;MAAA;AAG1C,UAAI,UAAU,UAAU,MAAM,cAAc,GAC5C;AAEQ,YAAA,aAAa,iBAAiB,WAAW;MAAA,OAGjD;AAEQ,YAAA,aAAa,iBAAiB,MAAM;MAAA;AAG5C,UAAI,iBAAiB,SAAS,KAAK,SAAS,KAAK,IAAI,CAAC;AACtD,UAAI,iBAAiB,SAAS,KAAK,SAAS,KAAK,IAAI,CAAC;AACtD,UAAI,iBAAiB,YAAY,KAAK,YAAY,KAAK,IAAI,CAAC;IAAA;AAI5D,QAAA,MAAM,gBAAgB,UAAU;AAEpC,QAAI,OAAO,UAAU;AAErB,QAAI,UAAU,mBAAmB,UAAU,oBAAoB,MAC/D;AACI,UAAI,QAAQ,UAAU;IAAA,WAEjB,CAAC,UAAU,kBACb,UAAU,mBAAmB,MACpC;AACQ,UAAA,QAAQ,aAAa,UAAU,QAAQ;IAAA;AAG/C,QAAI,UAAU,kBACP,UAAU,mBAAmB,MACpC;AACQ,UAAA,aAAa,cAAc,UAAU,cAAc;IAAA;AAG3D,QAAI,KAAK,OACT;AACI,WAAK,iBAAiB,GAAG;IAAA;AAG7B,cAAU,oBAAoB;AAC9B,cAAU,iBAAiB;AAC3B,QAAI,YAAY;AAEX,SAAA,UAAU,KAAK,SAAS;AACxB,SAAA,KAAK,YAAY,UAAU,cAAc;AAC9C,QAAI,UAAU,aACd;AACc,gBAAA,eAAe,WAAW,UAAU;IAAA;EAClD;;;;;;;EASI,eAAe,GAAY,MACnC;AACI,UAAM,EAAE,WAAW,OAAO,IAAI,EAAE;AAC1B,UAAA,WAAW,KAAK,UAAU,OAAO;AACjC,UAAA,QAAwB,OAAO,OAAO,IAAI,eAAe,QAAQ,GAAG,EAAE,OAAA,CAAQ;AAE3E,aAAA,aAAa,KAAK,UAAU;AACrC,SAAK,QAAQ,CAACE,UAAS,SAAS,cAAc,OAAOA,KAAI,CAAC;EAAA;;;;;;EAQtD,SAAS,GACjB;AACI,SAAK,eAAe,GAAG,CAAC,SAAS,cAAc,KAAK,CAAC;EAAA;;;;;;EAQjD,SAAS,GACjB;AACI,QAAI,CAAE,EAAE,OAAmB,aAAa,WAAW,GACnD;AACK,QAAE,OAAmB,aAAa,aAAa,WAAW;IAAA;AAG/D,SAAK,eAAe,GAAG,CAAC,WAAW,CAAC;EAAA;;;;;;EAQhC,YAAY,GACpB;AACI,QAAI,CAAE,EAAE,OAAmB,aAAa,WAAW,GACnD;AACK,QAAE,OAAmB,aAAa,aAAa,QAAQ;IAAA;AAG5D,SAAK,eAAe,GAAG,CAAC,UAAU,CAAC;EAAA;;;;;;EAQ/B,WAAW,GACnB;AACI,QAAI,EAAE,YAAY,gBAAgB,CAAC,KAAK,gBACxC;AACI;IAAA;AAGJ,SAAK,UAAU;EAAA;;;;;;EAQX,aAAa,GACrB;AACI,QAAI,EAAE,cAAc,KAAK,EAAE,cAAc,GACzC;AACI;IAAA;AAGJ,SAAK,YAAY;EAAA;;;;;;EAQd,UACP;AACI,SAAK,YAAY;AACjB,SAAK,kBAAkB;AAEvB,SAAK,OAAO;AACZ,SAAK,QAAQ;AACb,SAAK,YAAY;AACjB,SAAK,YAAY;AAEjB,QAAI,KAAK,gBACT;AACe,iBAAA,oBAAoB,WAAW,KAAK,UAAU;IAAA;EAC7D;;;;;;;;;;EAYG,wBAAwB,SAC/B;AACI,QAAI,SACJ;AACI,WAAK,UAAU;IAAA,OAGnB;AACI,WAAK,YAAY;IAAA;EACrB;AAER;AArwBa,qBAGK,YAAY;EACtB,MAAM;IACF,cAAc;IACd,cAAc;EAAA;EAElB,MAAM;AACV;AATS,qBAwBK,iBAAuC;;;;;EAKjD,kBAAkB;;;;;EAKlB,OAAO;;;;;EAKP,eAAe;;;;;EAKf,uBAAuB;AAC3B;AA7CG,IAAM,sBAAN;;;ACuFA,IAAM,sBAAwC;EACjD,YAAY;EACZ,iBAAiB;EACjB,gBAAgB;EAChB,UAAU;EACV,gBAAgB;EAChB,gBAAgB;EAChB,yBAAyB;EACzB,oBAAoB;EACpB,mBAAmB;EACnB,gBAAgB;EAChB,WAAW;AACf;;;ACpLA,IAAM,oBAAN,MACA;EADA,cAAA;AAOI,SAAO,uBAAuB;AAE9B,SAAQ,aAAa;AACrB,SAAQ,WAAW;AACnB,SAAQ,eAAe;AACvB,SAAQ,eAAe;EAAA;;;;;EAMhB,KAAK,QACZ;AACI,SAAK,qBAAqB;AAC1B,SAAK,SAAS;AACd,SAAK,uBAAuB;AAC5B,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,SAAK,eAAe;AACpB,SAAK,eAAe;EAAA;;EAIxB,IAAI,cACJ;AACI,WAAO,KAAK;EAAA;EAGhB,IAAI,YAAY,QAChB;AACI,SAAK,eAAe;EAAA;;EAIjB,oBACP;AACI,QAAI,KAAK,gBAAgB,CAAC,KAAK,YAC/B;AACI;IAAA;AAGJ,WAAO,OAAO,IAAI,KAAK,eAAe,MAAM,gBAAgB,WAAW;AAEvE,SAAK,eAAe;EAAA;;EAIjB,uBACP;AACQ,QAAA,CAAC,KAAK,cACV;AACI;IAAA;AAGJ,WAAO,OAAO,OAAO,KAAK,eAAe,IAAI;AAE7C,SAAK,eAAe;EAAA;;EAIjB,eACP;AACI,SAAK,WAAW;EAAA;;EAIZ,UACR;AACI,QAAI,CAAC,KAAK,cAAc,KAAK,cAC7B;AACI;IAAA;AAIJ,QAAI,KAAK,UACT;AACI,WAAK,WAAW;AAEhB;IAAA;AAIE,UAAA,mBAAmB,KAAK,OAAO,mBAAmB;AAExD,QAAI,KAAK,OAAO,uBAAwB,iBAAkC,gBAAgB,SAC1F;AACI;IAAA;AAGJ,eAAW,SAAS,cAAc,KAAK,OAAO,wBAAwB,IAAI,aAAa,eAAe;MAClG,SAAS,iBAAiB;MAC1B,SAAS,iBAAiB;MAC1B,aAAa,iBAAiB;MAC9B,WAAW,iBAAiB;IAAA,CAC/B,IAAI,IAAI,WAAW,aAAa;MAC7B,SAAS,iBAAiB;MAC1B,SAAS,iBAAiB;IAAA,CAC7B,CAAC;EAAA;;;;;;;;EAUE,cAAc,QACtB;AACI,SAAK,cAAc,OAAO;AAEtB,QAAA,KAAK,aAAa,KAAK,sBAC3B;AACI;IAAA;AAGJ,SAAK,aAAa;AAElB,SAAK,QAAQ;EAAA;AAErB;AAWa,IAAA,eAAe,IAAI,kBAAkB;;;ACjG3C,IAAM,sBAAN,cAAkC,eAGzC;EAHO,cAAA;AAAA,UAAA,GAAA,SAAA;AA0BI,SAAA,SAAgB,IAAI,MAAM;AAwB1B,SAAA,WAAkB,IAAI,MAAM;AAS5B,SAAA,SAAgB,IAAI,MAAM;AAS1B,SAAA,SAAgB,IAAI,MAAM;AAY1B,SAAA,SAAgB,IAAI,MAAM;EAAA;;EAnDjC,IAAW,UAAkB;AAAE,WAAO,KAAK,OAAO;EAAA;;EAGlD,IAAW,UAAkB;AAAE,WAAO,KAAK,OAAO;EAAA;;;;;EAMlD,IAAI,IAAY;AAAE,WAAO,KAAK;EAAA;;;;;EAM9B,IAAI,IAAY;AAAE,WAAO,KAAK;EAAA;;EAS9B,IAAI,YAAoB;AAAE,WAAO,KAAK,SAAS;EAAA;;EAG/C,IAAI,YAAoB;AAAE,WAAO,KAAK,SAAS;EAAA;;EAM/C,IAAI,UAAkB;AAAE,WAAO,KAAK,OAAO;EAAA;;EAG3C,IAAI,UAAkB;AAAE,WAAO,KAAK,OAAO;EAAA;;EAM3C,IAAI,UAAkB;AAAE,WAAO,KAAK,OAAO;EAAA;;EAG3C,IAAI,UAAkB;AAAE,WAAO,KAAK,OAAO;EAAA;;;;;EAY3C,IAAI,UAAkB;AAAE,WAAO,KAAK,OAAO;EAAA;;;;;EAM3C,IAAI,UAAkB;AAAE,WAAO,KAAK,OAAO;EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA8BpC,iBAA8C,WAAsB,OAAW,WACtF;AACI,WAAO,UAAU,eAAe,aAAgB,aAAa,KAAK,QAAQ,KAAK;EAAA;;;;;EAO5E,iBAAiB,KACxB;AACI,WAAO,sBAAsB,KAAK,eAAe,KAAK,YAAY,iBAAiB,GAAG;EAAA;;;;;;;;;;;;;;;;;;;;;;EAwBnF,eACH,UACA,eACA,gBACA,UACA,YACA,aACA,aACA,aACA,aACA,aACA,YACA,cACA,aACA,YACA,mBAEJ;AACU,UAAA,IAAI,MAAM,yBAAyB;EAAA;AAEjD;;;AChLO,IAAM,wBAAN,cAAoC,oBAC3C;EADO,cAAA;AAAA,UAAA,GAAA,SAAA;AAaH,SAAO,QAAQ;AAuBf,SAAO,SAAS;AAMhB,SAAO,YAAY;EAAA;;;;;EA+CZ,qBACP;AACQ,QAAA,KAAK,SAAS,iBAAiB,KAAK,SAAS,eAAe,KAAK,SAAS,aAC9E;AACI,aAAO,CAAC,IAAI;IAAA;AAGhB,WAAO,CAAA;EAAC;;;;;EAOL,qBACP;AACU,UAAA,IAAI,MAAM,sCAAsC;EAAA;AAE9D;;;AC1GO,IAAM,sBAAN,cAAkC,oBACzC;EADO,cAAA;AAAA,UAAA,GAAA,SAAA;AA2BH,SAAgB,kBAAkB;AAYlC,SAAgB,iBAAiB;AAYjC,SAAgB,iBAAiB;EAAA;AACrC;AApDa,oBAqBc,kBAAkB;AArBhC,oBAiCc,iBAAiB;AAjC/B,oBA6Cc,iBAAiB;;;AC7E5C,IAAM,oBAAoB;AAE1B,IAAM,kBAAkB,IAAI,MAAM;AAClC,IAAM,mBAAmB,IAAI,MAAM;AA2D5B,IAAM,gBAAN,MACP;;;;EAqEI,YAAY,YACZ;AAtDO,SAAA,WAAyB,IAAI,sBAAa;AAWjD,SAAO,YAAY;AAGnB,SAAO,yBAAyB;AAkBhC,SAAU,eAAoC;MAC1C,cAAc,CAAA;IAAC;AAQT,SAAA,YAAA,oBAA8D,IAAI;AAG5E,SAAiB,0BAAuC,CAAA;AAExD,SAAQ,eAA4B,CAAA;AAEpC,SAAQ,sBAAsB;AAO1B,SAAK,aAAa;AAElB,SAAK,aAAa,KAAK,WAAW,KAAK,IAAI;AAC3C,SAAK,YAAY,KAAK,UAAU,KAAK,IAAI;AACzC,SAAK,iBAAiB,KAAK,eAAe,KAAK,IAAI;AACnD,SAAK,iBAAiB,KAAK,eAAe,KAAK,IAAI;AACnD,SAAK,gBAAgB,KAAK,cAAc,KAAK,IAAI;AACjD,SAAK,iBAAiB,KAAK,eAAe,KAAK,IAAI;AACnD,SAAK,eAAe,KAAK,aAAa,KAAK,IAAI;AAC/C,SAAK,sBAAsB,KAAK,oBAAoB,KAAK,IAAI;AAC7D,SAAK,WAAW,KAAK,SAAS,KAAK,IAAI;AAEvC,SAAK,eAAe,CAAA;AACf,SAAA,gBAAgB,eAAe,KAAK,cAAc;AAClD,SAAA,gBAAgB,eAAe,KAAK,cAAc;AAClD,SAAA,gBAAgB,cAAc,KAAK,aAAa;AAChD,SAAA,gBAAgB,gBAAgB,KAAK,aAAa;AAClD,SAAA,gBAAgB,eAAe,KAAK,cAAc;AAClD,SAAA,gBAAgB,aAAa,KAAK,YAAY;AAC9C,SAAA,gBAAgB,oBAAoB,KAAK,mBAAmB;AAC5D,SAAA,gBAAgB,SAAS,KAAK,QAAQ;EAAA;;;;;;;;;;;;;EAexC,gBAAgB,MAAc,IACrC;AACI,QAAI,CAAC,KAAK,aAAa,IAAI,GAC3B;AACS,WAAA,aAAa,IAAI,IAAI,CAAA;IAAC;AAG1B,SAAA,aAAa,IAAI,EAAE,KAAK;MACzB;MACA,UAAU;IAAA,CACb;AACI,SAAA,aAAa,IAAI,EAAE,KAAK,CAAC,GAAG,MAAM,EAAE,WAAW,EAAE,QAAQ;EAAA;;;;;;EAQ3D,cAAc,GAAmB,MACxC;AACI,MAAE,qBAAqB;AACvB,MAAE,gCAAgC;AAE7B,SAAA,UAAU,GAAG,IAAI;AACtB,SAAK,SAAS,KAAK,QAAQ,EAAE,MAAM,CAAC;EAAA;;;;;EAOjC,SAAS,GAChB;AACQ,QAAA,CAAC,KAAK,YACV;AACI;IAAA;AAGJ,UAAM,UAAU,KAAK,aAAa,EAAE,IAAI;AAExC,QAAI,SACJ;AACI,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,IAAI,GAAG,KAC3C;AACY,gBAAA,CAAC,EAAE,GAAG,CAAC;MAAA;IACnB,OAGJ;AAES,WAAA,kDAAkD,EAAE,IAAI,EAAE;IAAA;EAEnE;;;;;;;;EAUG,QACH,GACA,GAEJ;AACI,iBAAa,cAAc;AAErB,UAAA,UAAU,KAAK,uBAAuB,KAAK;AAC3C,UAAA,KAAK,UAAU,yBAAyB;AACxC,UAAA,eAAe,KAAK,EAAE;MACxB,KAAK;MACL,KAAK,WAAW;MAChB,gBAAgB,IAAI,GAAG,CAAC;MACxB,KAAK;MACL,KAAK;IAAA;AAGF,WAAA,gBAAgB,aAAa,CAAC;EAAA;;;;;;;EASlC,UAAU,GAAmB,MACpC;AACQ,QAAA,CAAC,EAAE,QACP;AAEI;IAAA;AAGE,UAAA,eAAe,EAAE,aAAa;AAGpC,MAAE,aAAa,EAAE;AAER,aAAA,IAAI,GAAG,IAAI,aAAa,SAAS,GAAG,IAAI,GAAG,KACpD;AACM,QAAA,gBAAgB,aAAa,CAAC;AAE3B,WAAA,aAAa,GAAG,IAAI;AAErB,UAAA,EAAE,sBAAsB,EAAE;AAA+B;IAAA;AAIjE,MAAE,aAAa,EAAE;AACjB,MAAE,gBAAgB,EAAE;AAEf,SAAA,aAAa,GAAG,IAAI;AAErB,QAAA,EAAE,sBAAsB,EAAE;AAA+B;AAG7D,MAAE,aAAa,EAAE;AAEjB,aAAS,IAAI,aAAa,SAAS,GAAG,KAAK,GAAG,KAC9C;AACM,QAAA,gBAAgB,aAAa,CAAC;AAE3B,WAAA,aAAa,GAAG,IAAI;AAErB,UAAA,EAAE,sBAAsB,EAAE;AAA+B;IAAA;EACjE;;;;;;;;;EAWG,IAAI,GAAmB,MAA0B,UAAU,KAAK,yBACvE;AACI,QAAI,QAAQ,WAAW;AAAG;AAE1B,MAAE,aAAa,EAAE;AAEjB,UAAM,SAAS,MAAM,QAAQ,IAAI,IAAI,OAAO,CAAC,IAAI;AAIjD,aAAS,IAAI,QAAQ,SAAS,GAAG,KAAK,GAAG,KACzC;AACW,aAAA,QAAQ,CAAC,UAChB;AACM,UAAA,gBAAgB,QAAQ,CAAC;AACtB,aAAA,aAAa,GAAG,KAAK;MAAA,CAC7B;IAAA;EACL;;;;;;EAQG,gBAAgB,QACvB;AACU,UAAA,kBAAkB,CAAC,MAAM;AAEtB,aAAA,IAAI,GAAG,IAAI,sBAAsB,WAAW,KAAK,cAAc,OAAO,SAAS,KACxF;AACQ,UAAA,CAAC,OAAO,QACZ;AACU,cAAA,IAAI,MAAM,qDAAqD;MAAA;AAGzD,sBAAA,KAAK,OAAO,MAAM;AAElC,eAAS,OAAO;IAAA;AAGpB,oBAAgB,QAAQ;AAEjB,WAAA;EAAA;EAGD,qBACN,eACA,WACA,UACA,QACA,SACA,SAAS,OAEb;AACI,QAAI,eAAe;AAGf,QAAA,KAAK,kBAAkB,aAAa;AAAU,aAAA;AAElD,QAAI,cAAc,cAAc,aAAa,cAAc,WAC3D;AACI,mBAAa,cAAc;IAAA;AAG3B,QAAA,cAAc,uBAAuB,cAAc,UACvD;AACI,YAAM,WAAW,cAAc;AAE/B,eAAS,IAAI,SAAS,SAAS,GAAG,KAAK,GAAG,KAC1C;AACU,cAAA,QAAQ,SAAS,CAAC;AAExB,cAAM,YAAY,KAAK;UACnB;UACA,KAAK,eAAe,SAAS,IAAI,YAAY,MAAM;UACnD;UACA;UACA;UACA,UAAU,QAAQ,eAAe,QAAQ;QAAA;AAG7C,YAAI,WACJ;AAGQ,cAAA,UAAU,SAAS,KAAK,CAAC,UAAU,UAAU,SAAS,CAAC,EAAE,QAC7D;AACI;UAAA;AAME,gBAAA,gBAAgB,cAAc,cAAc;AAE9C,cAAA,UAAU,SAAS,KAAK,eAC5B;AACQ,gBAAA;AAAoB,mBAAA,wBAAwB,KAAK,aAAa;AAClE,sBAAU,KAAK,aAAa;UAAA;AAI5B,cAAA,KAAK,aAAa,WAAW;AAAG,iBAAK,eAAe;AAEzC,yBAAA;QAAA;MACnB;IACJ;AAGE,UAAA,oBAAoB,KAAK,eAAe,SAAS;AACjD,UAAA,sBAAsB,cAAc,cAAc;AAExD,QAAI,uBAAuB;AAA0B,WAAA,wBAAwB,KAAK,aAAa;AAI3F,QAAA,UAAU,KAAK,aAAa,SAAS;AAAU,aAAA;AAE/C,QAAA;AAAc,aAAO,KAAK;AAG1B,QAAA,sBAAsB,CAAC,QAAQ,eAAe,QAAQ,KAAK,OAAO,eAAe,QAAQ,IAC7F;AAGI,aAAO,sBAAsB,CAAC,aAAa,IAAI,CAAA;IAAC;AAG7C,WAAA;EAAA;;;;;;;;;;;;;;;EAiBD,iBACN,eACA,WACA,UACA,QACA,SAEJ;AAEI,QAAI,KAAK,kBAAkB,aAAa,KAAK,QAAQ,eAAe,QAAQ,GAC5E;AACW,aAAA;IAAA;AAEX,QAAI,cAAc,cAAc,aAAa,cAAc,WAC3D;AACI,mBAAa,cAAc;IAAA;AAI3B,QAAA,cAAc,uBAAuB,cAAc,UACvD;AACI,YAAM,WAAW,cAAc;AAC/B,YAAM,mBAAmB;AAEzB,eAAS,IAAI,SAAS,SAAS,GAAG,KAAK,GAAG,KAC1C;AACU,cAAA,QAAQ,SAAS,CAAC;AAExB,cAAM,YAAY,KAAK;UACnB;UACA,KAAK,eAAe,SAAS,IAAI,YAAY,MAAM;UACnD;UACA;UACA;QAAA;AAGJ,YAAI,WACJ;AAGQ,cAAA,UAAU,SAAS,KAAK,CAAC,UAAU,UAAU,SAAS,CAAC,EAAE,QAC7D;AACI;UAAA;AAME,gBAAA,gBAAgB,cAAc,cAAc;AAE9C,cAAA,UAAU,SAAS,KAAK;AAAe,sBAAU,KAAK,aAAa;AAEhE,iBAAA;QAAA;MACX;IACJ;AAGE,UAAA,oBAAoB,KAAK,eAAe,SAAS;AACjD,UAAA,sBAAsB,cAAc,cAAc;AAGxD,QAAI,qBAAqB,OAAO,eAAe,QAAQ,GACvD;AAGI,aAAO,sBAAsB,CAAC,aAAa,IAAI,CAAA;IAAC;AAG7C,WAAA;EAAA;EAGH,eAAe,KACvB;AACW,WAAA,QAAQ,YAAY,QAAQ;EAAA;EAG/B,kBAAkB,WAC1B;AAEQ,QAAA,CAAC,aAAa,CAAC,UAAU,WAAW,CAAC,UAAU,cAAc,CAAC,UAAU,YAC5E;AACW,aAAA;IAAA;AAIP,QAAA,UAAU,cAAc,QAC5B;AACW,aAAA;IAAA;AAIX,QAAI,UAAU,cAAc,aAAa,CAAC,UAAU,qBACpD;AACW,aAAA;IAAA;AAGJ,WAAA;EAAA;;;;;;;;;EAWD,WAAW,WAAsB,UAC3C;AACI,QAAI,UAAU,SACd;AACc,gBAAA,eAAe,aAAa,UAAU,gBAAgB;AAE5D,UAAA,CAAC,UAAU,QAAQ,SAAS,iBAAiB,GAAG,iBAAiB,CAAC,GACtE;AACW,eAAA;MAAA;IACX;AAGJ,QAAI,UAAU,WAAW,UAAU,QAAQ,QAC3C;AACI,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,QAAQ,KAC9C;AACU,cAAA,SAAS,UAAU,QAAQ,CAAC;AAElC,YAAI,OAAO,eACX;AACI,gBAAM,sBAAsB,OAAO,cAAc,UAAU,KAAK,SAAS;AAEzE,cAAI,CAAC,qBACL;AACW,mBAAA;UAAA;QACX;MACJ;IACJ;AAGG,WAAA;EAAA;;;;;;;EASD,UAAU,WAAsB,UAC1C;AAEI,QAAI,UAAU,SACd;AACW,aAAA;IAAA;AAGX,QAAK,WAA0B,eAC/B;AACc,gBAAA,eAAe,aAAa,UAAU,gBAAgB;AAExD,aAAA,UAAyB,cAAc,gBAAgB;IAAA;AAK5D,WAAA;EAAA;;;;;;;;;EAWD,aAAa,GAAmB,MAC1C;AACI,QAAI,CAAC,EAAE,cAAc,cAAA,GACrB;AACI;IAAA;AAGJ,aAAA,OAAS,EAAE;AAGL,UAAA,aAAa,KAAK,IAAI;AAE3B,MAAE,cAAc,UAAU,IAA8C,CAAC;AAEpE,UAAA,MAAM,EAAE,eAAe,EAAE,mBAAmB,EAAE,eAAe,EAAE,YAAY,GAAG,IAAI,YAAY;AAE/F,SAAA,iBAAiB,GAAG,GAAG;AAExB,QAAA,EAAE,eAAe,EAAE,WACvB;AACS,WAAA,iBAAiB,GAAG,IAAI;IAAA;EACjC;;;;;;;EASM,eAAe,MACzB;AACQ,QAAA,EAAE,gBAAgB,wBACtB;AAEI,WAAK,iEAAiE;AAGtE;IAAA;AAGE,UAAA,IAAI,KAAK,mBAAmB,IAAI;AAEjC,SAAA,cAAc,GAAG,aAAa;AAE/B,QAAA,EAAE,gBAAgB,SACtB;AACS,WAAA,cAAc,GAAG,YAAY;IAAA,WAE7B,EAAE,gBAAgB,WAAW,EAAE,gBAAgB,OACxD;AACU,YAAA,gBAAgB,EAAE,WAAW;AAEnC,WAAK,cAAc,GAAG,gBAAgB,cAAc,WAAW;IAAA;AAGnE,UAAM,eAAe,KAAK,aAAa,KAAK,SAAS;AAErD,iBAAa,qBAAqB,KAAK,MAAM,IAAI,EAAE,aAAa;AAEhE,SAAK,UAAU,CAAC;EAAA;;;;;;;;EAUV,eAAe,MACzB;AACQ,QAAA,EAAE,gBAAgB,wBACtB;AAEI,WAAK,iEAAiE;AAGtE;IAAA;AAGJ,SAAK,wBAAwB,SAAS;AACtC,SAAK,aAAa,SAAS;AAC3B,SAAK,sBAAsB;AACrB,UAAA,IAAI,KAAK,mBAAmB,IAAI;AAEtC,SAAK,sBAAsB;AAC3B,UAAM,UAAU,EAAE,gBAAgB,WAAW,EAAE,gBAAgB;AAC/D,UAAM,eAAe,KAAK,aAAa,KAAK,SAAS;AACrD,UAAM,YAAY,KAAK,kBAAkB,aAAa,WAAW;AAGjE,QAAI,aAAa,aAAa,SAAS,KAAK,cAAc,EAAE,QAC5D;AAEI,YAAM,UAAU,KAAK,SAAS,cAAc,aAAa;AACzD,YAAM,WAAW,KAAK,mBAAmB,MAAM,SAAS,SAAS;AAE5D,WAAA,cAAc,UAAU,YAAY;AACrC,UAAA;AAAc,aAAA,cAAc,UAAU,UAAU;AAIpD,UAAI,CAAC,EAAE,aAAA,EAAe,SAAS,SAAS,GACxC;AACI,cAAM,aAAa,KAAK,mBAAmB,MAAM,gBAAgB,SAAS;AAE1E,mBAAW,aAAa,WAAW;AAE5B,eAAA,WAAW,UAAU,CAAC,EAAE,aAAA,EAAe,SAAS,WAAW,MAAM,GACxE;AACI,qBAAW,gBAAgB,WAAW;AAEtC,eAAK,aAAa,UAAU;AACxB,cAAA;AAAc,iBAAA,aAAa,YAAY,YAAY;AAE5C,qBAAA,SAAS,WAAW,OAAO;QAAA;AAG1C,aAAK,UAAU,UAAU;MAAA;AAG7B,WAAK,UAAU,QAAQ;IAAA;AAIvB,QAAA,cAAc,EAAE,QACpB;AAEI,YAAM,WAAW,KAAK,SAAS,cAAc,cAAc;AAC3D,YAAM,YAAY,KAAK,kBAAkB,GAAG,QAAQ;AAE/C,WAAA,cAAc,WAAW,aAAa;AACvC,UAAA;AAAc,aAAA,cAAc,WAAW,WAAW;AAGtD,UAAI,qBAAqB,WAAW;AAEpC,aAAO,sBAAsB,uBAAuB,KAAK,WAAW,QACpE;AACI,YAAI,uBAAuB,EAAE;AAAQ;AAErC,6BAAqB,mBAAmB;MAAA;AAK5C,YAAM,kBAAkB,CAAC,sBAAsB,uBAAuB,KAAK,WAAW;AAEtF,UAAI,iBACJ;AACI,cAAM,aAAa,KAAK,kBAAkB,GAAG,cAAc;AAE3D,mBAAW,aAAa,WAAW;AAE5B,eAAA,WAAW,UACP,WAAW,WAAW,aACtB,WAAW,WAAW,KAAK,WAAW,QACjD;AACI,qBAAW,gBAAgB,WAAW;AAEtC,eAAK,aAAa,UAAU;AACxB,cAAA;AAAc,iBAAA,aAAa,YAAY,YAAY;AAE5C,qBAAA,SAAS,WAAW,OAAO;QAAA;AAG1C,aAAK,UAAU,UAAU;MAAA;AAG7B,WAAK,UAAU,SAAS;IAAA;AAG5B,UAAM,aAAuB,CAAA;AACvB,UAAA,2BAA2B,KAAK,0BAA0B;AAE3D,SAAA,YAAY,WAAW,KAAK,aAAa,IAAI,KAAK,cAAc,GAAG,aAAa;AACzD,gCAAA,WAAW,KAAK,mBAAmB;AAG3D,QAAA,EAAE,gBAAgB,SACtB;AACS,WAAA,YAAY,WAAW,OAAO,GAAG,GAAG,WAAW,IAAI,KAAK,cAAc,GAAG,WAAW;AAC7D,kCAAA,WAAW,KAAK,iBAAiB;IAAA;AAGjE,QAAI,SACJ;AACS,WAAA,YAAY,WAAW,OAAO,GAAG,GAAG,WAAW,IAAI,KAAK,cAAc,GAAG,WAAW;AAC7D,kCAAA,WAAW,KAAK,iBAAiB;AACxD,WAAA,SAAS,EAAE,QAAQ;IAAA;AAGxB,QAAA,WAAW,SAAS,GACxB;AACS,WAAA,IAAI,GAAG,UAAU;IAAA;AAE1B,SAAK,wBAAwB,SAAS;AACtC,SAAK,aAAa,SAAS;AAEd,iBAAA,cAAc,EAAE,aAAa;AAE1C,SAAK,UAAU,CAAC;EAAA;;;;;;;EASV,eAAe,MACzB;AACQ,QAAA,EAAE,gBAAgB,wBACtB;AAEI,WAAK,iEAAiE;AAGtE;IAAA;AAGJ,UAAM,eAAe,KAAK,aAAa,KAAK,SAAS;AAC/C,UAAA,IAAI,KAAK,mBAAmB,IAAI;AACtC,UAAM,UAAU,EAAE,gBAAgB,WAAW,EAAE,gBAAgB;AAE1D,SAAA,cAAc,GAAG,aAAa;AAC/B,QAAA;AAAc,WAAA,cAAc,GAAG,WAAW;AAC9C,QAAI,EAAE,gBAAgB;AAAc,WAAA,SAAS,EAAE,QAAQ;AAGvD,UAAM,aAAa,KAAK,kBAAkB,GAAG,cAAc;AAE3D,eAAW,aAAa,WAAW;AAEnC,WAAO,WAAW,UAAU,WAAW,WAAW,KAAK,WAAW,QAClE;AACI,iBAAW,gBAAgB,WAAW;AAEtC,WAAK,aAAa,UAAU;AACxB,UAAA;AAAc,aAAA,aAAa,YAAY,YAAY;AAE5C,iBAAA,SAAS,WAAW,OAAO;IAAA;AAG7B,iBAAA,cAAc,EAAE,aAAa;AAE1C,SAAK,UAAU,CAAC;AAChB,SAAK,UAAU,UAAU;EAAA;;;;;;;EASnB,cAAc,MACxB;AACQ,QAAA,EAAE,gBAAgB,wBACtB;AAEI,WAAK,iEAAiE;AAGtE;IAAA;AAGJ,UAAM,eAAe,KAAK,aAAa,KAAK,SAAS;AAErD,QAAI,aAAa,aACjB;AACI,YAAM,UAAU,KAAK,gBAAgB,WAAW,KAAK,gBAAgB;AACrE,YAAM,YAAY,KAAK,kBAAkB,aAAa,WAAW;AAGjE,YAAM,WAAW,KAAK,mBAAmB,MAAM,cAAc,SAAS;AAEtE,WAAK,cAAc,QAAQ;AACvB,UAAA;AAAc,aAAA,cAAc,UAAU,UAAU;AAIpD,YAAM,aAAa,KAAK,mBAAmB,MAAM,gBAAgB,SAAS;AAE1E,iBAAW,aAAa,WAAW;AAEnC,aAAO,WAAW,UAAU,WAAW,WAAW,KAAK,WAAW,QAClE;AACI,mBAAW,gBAAgB,WAAW;AAEtC,aAAK,aAAa,UAAU;AACxB,YAAA;AAAc,eAAA,aAAa,YAAY,YAAY;AAE5C,mBAAA,SAAS,WAAW,OAAO;MAAA;AAG1C,mBAAa,cAAc;AAE3B,WAAK,UAAU,QAAQ;AACvB,WAAK,UAAU,UAAU;IAAA;AAG7B,SAAK,SAAS;EAAA;;;;;;;;;;;EAaR,aAAa,MACvB;AACQ,QAAA,EAAE,gBAAgB,wBACtB;AAEI,WAAK,iEAAiE;AAGtE;IAAA;AAGE,UAAA,MAAM,YAAY,IAAI;AACtB,UAAA,IAAI,KAAK,mBAAmB,IAAI;AAEjC,SAAA,cAAc,GAAG,WAAW;AAE7B,QAAA,EAAE,gBAAgB,SACtB;AACS,WAAA,cAAc,GAAG,UAAU;IAAA,WAE3B,EAAE,gBAAgB,WAAW,EAAE,gBAAgB,OACxD;AACU,YAAA,gBAAgB,EAAE,WAAW;AAEnC,WAAK,cAAc,GAAG,gBAAgB,YAAY,SAAS;IAAA;AAG/D,UAAM,eAAe,KAAK,aAAa,KAAK,SAAS;AACrD,UAAM,cAAc,KAAK,kBAAkB,aAAa,qBAAqB,KAAK,MAAM,CAAC;AAEzF,QAAI,cAAc;AAIlB,QAAI,eAAe,CAAC,EAAE,aAAA,EAAe,SAAS,WAAW,GACzD;AACI,UAAI,gBAAgB;AAEpB,aAAO,iBAAiB,CAAC,EAAE,aAAA,EAAe,SAAS,aAAa,GAChE;AACI,UAAE,gBAAgB;AAEb,aAAA,aAAa,GAAG,kBAAkB;AAEnC,YAAA,EAAE,gBAAgB,SACtB;AACS,eAAA,aAAa,GAAG,iBAAiB;QAAA,WAEjC,EAAE,gBAAgB,WAAW,EAAE,gBAAgB,OACxD;AACU,gBAAA,gBAAgB,EAAE,WAAW;AAEnC,eAAK,aAAa,GAAG,gBAAgB,mBAAmB,gBAAgB;QAAA;AAG5E,wBAAgB,cAAc;MAAA;AAG3B,aAAA,aAAa,qBAAqB,KAAK,MAAM;AAItC,oBAAA;IAAA;AAIlB,QAAI,aACJ;AACI,YAAM,aAAa,KAAK,kBAAkB,GAAG,OAAO;AAEpD,iBAAW,SAAS;AACpB,iBAAW,OAAO;AAElB,UAAI,CAAC,aAAa,eAAe,KAAK,MAAM,GAC5C;AACiB,qBAAA,eAAe,KAAK,MAAM,IAAI;UACvC,YAAY;UACZ,QAAQ,WAAW;UACnB,WAAW;QAAA;MACf;AAGJ,YAAM,eAAe,aAAa,eAAe,KAAK,MAAM;AAE5D,UAAI,aAAa,WAAW,WAAW,UAChC,MAAM,aAAa,YAAY,KACtC;AACI,UAAE,aAAa;MAAA,OAGnB;AACI,qBAAa,aAAa;MAAA;AAG9B,mBAAa,SAAS,WAAW;AACjC,mBAAa,YAAY;AAEzB,iBAAW,SAAS,aAAa;AAE7B,UAAA,WAAW,gBAAgB,SAC/B;AACU,cAAA,gBAAgB,WAAW,WAAW;AAE5C,aAAK,cAAc,YAAY,gBAAgB,eAAe,OAAO;MAAA,WAEhE,WAAW,gBAAgB,SACpC;AACS,aAAA,cAAc,YAAY,KAAK;MAAA;AAGnC,WAAA,cAAc,YAAY,YAAY;AAE3C,WAAK,UAAU,UAAU;IAAA;AAG7B,SAAK,UAAU,CAAC;EAAA;;;;;;;;;;;;EAcV,oBAAoB,MAC9B;AACQ,QAAA,EAAE,gBAAgB,wBACtB;AAEI,WAAK,iEAAiE;AAGtE;IAAA;AAGJ,UAAM,eAAe,KAAK,aAAa,KAAK,SAAS;AACrD,UAAM,cAAc,KAAK,kBAAkB,aAAa,qBAAqB,KAAK,MAAM,CAAC;AACnF,UAAA,IAAI,KAAK,mBAAmB,IAAI;AAEtC,QAAI,aACJ;AACI,UAAI,gBAAgB;AAEpB,aAAO,eACP;AACI,UAAE,gBAAgB;AAEb,aAAA,aAAa,GAAG,kBAAkB;AAEnC,YAAA,EAAE,gBAAgB,SACtB;AACS,eAAA,aAAa,GAAG,iBAAiB;QAAA,WAEjC,EAAE,gBAAgB,WAAW,EAAE,gBAAgB,OACxD;AACI,eAAK,aAAa,GAAG,EAAE,WAAW,IAAI,mBAAmB,gBAAgB;QAAA;AAG7E,wBAAgB,cAAc;MAAA;AAG3B,aAAA,aAAa,qBAAqB,KAAK,MAAM;IAAA;AAGxD,SAAK,UAAU,CAAC;EAAA;;;;;EAOV,SAAS,MACnB;AACQ,QAAA,EAAE,gBAAgB,sBACtB;AAEI,WAAK,6DAA6D;AAGlE;IAAA;AAGE,UAAA,aAAa,KAAK,iBAAiB,IAAI;AAE7C,SAAK,cAAc,UAAU;AAC7B,SAAK,UAAU,UAAU;EAAA;;;;;;;;;EAWnB,kBAAkB,iBAC5B;AACI,QAAI,CAAC,iBACL;AACW,aAAA;IAAA;AAGP,QAAA,gBAAgB,gBAAgB,CAAC;AAErC,aAAS,IAAI,GAAG,IAAI,gBAAgB,QAAQ,KAC5C;AAGI,UAAI,gBAAgB,CAAC,EAAE,WAAW,eAClC;AACI,wBAAgB,gBAAgB,CAAC;MAAA,OAGrC;AACI;MAAA;IACJ;AAGG,WAAA;EAAA;;;;;;;;;EAWD,mBACN,MACA,MACA,QAEJ;AACU,UAAA,QAAQ,KAAK,cAAc,qBAAqB;AAEjD,SAAA,gBAAgB,MAAM,KAAK;AAC3B,SAAA,cAAc,MAAM,KAAK;AACzB,SAAA,SAAS,MAAM,KAAK;AAEzB,UAAM,cAAc,KAAK;AACzB,UAAM,gBAAgB;AACtB,UAAM,SAAS,UACR,KAAK,QAAQ,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,KAC3C,KAAK,aAAa,CAAC;AAEtB,QAAA,OAAO,SAAS,UACpB;AACI,YAAM,OAAO;IAAA;AAGV,WAAA;EAAA;;;;;;;EASD,iBAAiB,MAC3B;AACU,UAAA,QAAQ,KAAK,cAAc,mBAAmB;AAE/C,SAAA,cAAc,MAAM,KAAK;AACzB,SAAA,cAAc,MAAM,KAAK;AACzB,SAAA,SAAS,MAAM,KAAK;AAEzB,UAAM,cAAc,KAAK;AACzB,UAAM,gBAAgB;AAChB,UAAA,SAAS,KAAK,QAAQ,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC;AAEnD,WAAA;EAAA;;;;;;;;EAUD,kBAAkB,MAA6B,MACzD;AACU,UAAA,QAAQ,KAAK,cAAc,qBAAqB;AAEtD,UAAM,cAAc,KAAK;AACzB,UAAM,gBAAgB,KAAK;AAEtB,SAAA,gBAAgB,MAAM,KAAK;AAC3B,SAAA,cAAc,MAAM,KAAK;AACzB,SAAA,SAAS,MAAM,KAAK;AAGzB,UAAM,SAAS,KAAK;AACpB,UAAM,OAAO,KAAK,aAAa,EAAE,MAAM;AACjC,UAAA,OAAO,QAAQ,MAAM;AAEpB,WAAA;EAAA;;;;;;;;;;;;EAcD,cAAc,MAA2B,IACnD;AACI,OAAG,YAAY,KAAK;AACpB,OAAG,SAAS,KAAK;AACjB,OAAG,SAAS,KAAK;AACjB,OAAG,SAAS,KAAK;EAAA;;;;;;;;;;;;;;;;;EAmBX,gBAAgB,MAAsB,IAChD;AACQ,QAAA,EAAE,gBAAgB,yBAAyB,cAAc;AAAwB;AAErF,OAAG,YAAY,KAAK;AACpB,OAAG,QAAQ,KAAK;AAChB,OAAG,SAAS,KAAK;AACjB,OAAG,YAAY,KAAK;AACpB,OAAG,cAAc,KAAK;AACtB,OAAG,WAAW,KAAK;AACnB,OAAG,qBAAqB,KAAK;AAC7B,OAAG,QAAQ,KAAK;AAChB,OAAG,QAAQ,KAAK;AAChB,OAAG,QAAQ,KAAK;EAAA;;;;;;;;;;;;;;;;;;;;;;;EAyBV,cAAc,MAAsB,IAC9C;AACQ,QAAA,EAAE,gBAAgB,uBAAuB,cAAc;AAAsB;AAEjF,OAAG,SAAS,KAAK;AACjB,OAAG,SAAS,KAAK;AACjB,OAAG,UAAU,KAAK;AACf,OAAA,OAAO,SAAS,KAAK,MAAM;AAC9B,OAAG,UAAU,KAAK;AAClB,OAAG,UAAU,KAAK;AACf,OAAA,SAAS,SAAS,KAAK,QAAQ;AAC/B,OAAA,OAAO,SAAS,KAAK,MAAM;AAC9B,OAAG,WAAW,KAAK;AAChB,OAAA,OAAO,SAAS,KAAK,MAAM;EAAA;;;;;;;;;;;;EAcxB,SAAS,MAAsB,IACzC;AACI,OAAG,YAAY,KAAK;AACpB,OAAG,aAAa,KAAK;AAClB,OAAA,YAAY,YAAY,IAAI;AAC/B,OAAG,OAAO,KAAK;AACf,OAAG,SAAS,KAAK;AACjB,OAAG,OAAO,KAAK;AACf,OAAG,QAAQ,KAAK;AACb,OAAA,MAAM,SAAS,KAAK,KAAK;AACzB,OAAA,KAAK,SAAS,KAAK,IAAI;EAAA;;;;;;EAQpB,aAAa,IACvB;AACI,QAAI,CAAC,KAAK,aAAa,aAAa,EAAE,GACtC;AACS,WAAA,aAAa,aAAa,EAAE,IAAI;QACjC,sBAAsB,CAAA;QACtB,gBAAgB,CAAA;QAChB,YAAY;MAAA;IAChB;AAGG,WAAA,KAAK,aAAa,aAAa,EAAE;EAAA;;;;;;;;EAUlC,cACN,aAEJ;AACI,QAAI,CAAC,KAAK,UAAU,IAAI,WAAkB,GAC1C;AACI,WAAK,UAAU,IAAI,aAAoB,CAAA,CAAE;IAAA;AAGvC,UAAA,QAAQ,KAAK,UAAU,IAAI,WAAkB,EAAE,IAAI,KAClD,IAAI,YAAY,IAAI;AAE3B,UAAM,aAAa,MAAM;AACzB,UAAM,gBAAgB;AACtB,UAAM,mBAAmB;AACzB,UAAM,OAAO;AACb,UAAM,SAAS;AAER,WAAA;EAAA;;;;;;;;;;;;EAcD,UAAoC,OAC9C;AACI,QAAI,MAAM,YAAY;AAAY,YAAA,IAAI,MAAM,mEAAmE;AAE/G,UAAM,cAAc,MAAM;AAE1B,QAAI,CAAC,KAAK,UAAU,IAAI,WAAkB,GAC1C;AACI,WAAK,UAAU,IAAI,aAAoB,CAAA,CAAE;IAAA;AAG7C,SAAK,UAAU,IAAI,WAAkB,EAAE,KAAK,KAAK;EAAA;;;;;;;EAS7C,iBAAiB,GAAmB,MAC5C;AACI,UAAM,YAAc,EAAE,cAAsB,QAA6B,IAAI;AAE7E,QAAI,CAAC;AAAW;AAEhB,QAAI,QAAQ,WACZ;AACI,UAAI,UAAU;AAAM,UAAE,cAAc,eAAe,MAAM,UAAU,IAAI,QAAW,IAAI;AACtF,gBAAU,GAAG,KAAK,UAAU,SAAS,CAAC;IAAA,OAG1C;AAEY,eAAA,IAAI,GAAG,IAAI,UAAU,QACzB,IAAI,KAAK,CAAC,EAAE,+BACZ,KACJ;AACQ,YAAA,UAAU,CAAC,EAAE;AAAQ,YAAA,cAAc,eAAe,MAAM,UAAU,CAAC,EAAE,IAAI,QAAW,IAAI;AAClF,kBAAA,CAAC,EAAE,GAAG,KAAK,UAAU,CAAC,EAAE,SAAS,CAAC;MAAA;IAChD;EACJ;AAER;;;AC97CA,IAAM,mBAAmB;AACzB,IAAM,mBAA2C;EAC7C,YAAY;EACZ,UAAU;EACV,iBAAiB;EACjB,WAAW;EACX,aAAa;AACjB;AAoNO,IAAM,eAAN,MAAMC,cACb;;;;EA2MI,YAAY,UACZ;AAhIA,SAAgB,sBAAsB,kBAAkB;AASxC,SAAA,wBAAwB,CAAC,CAAC,WAAW;AAqFrD,SAAO,aAA0B;AAGjC,SAAO,aAAa;AAgChB,SAAK,WAAW;AACX,SAAA,eAAe,IAAI,cAAc,IAAI;AAC1C,iBAAa,KAAK,IAAI;AAEtB,SAAK,qBAAqB;AAC1B,SAAK,eAAe;AAEf,SAAA,oBAAoB,IAAI,sBAAsB,IAAI;AAClD,SAAA,kBAAkB,IAAI,oBAAoB,IAAI;AAEnD,SAAK,eAAe;MAChB,SAAS;MACT,SAAS;IAAA;AAGb,SAAK,WAAW,IAAI,MAAM,EAAE,GAAGA,cAAY,qBAAA,GAAwB;MAC/D,KAAK,CAAC,QAAQ,KAAK,UACnB;AACI,YAAI,QAAQ,cACZ;AACI,eAAK,aAAa,yBAAyB;QAAA;AAE/C,eAAO,GAAgC,IAAI;AAEpC,eAAA;MAAA;IACX,CACH;AAED,SAAK,iBAAiB,KAAK,eAAe,KAAK,IAAI;AACnD,SAAK,iBAAiB,KAAK,eAAe,KAAK,IAAI;AACnD,SAAK,eAAe,KAAK,aAAa,KAAK,IAAI;AAC/C,SAAK,oBAAoB,KAAK,kBAAkB,KAAK,IAAI;AACzD,SAAK,UAAU,KAAK,QAAQ,KAAK,IAAI;EAAA;;;;;;;;EA1LzC,WAAkB,mBAClB;AACI,WAAO,KAAK;EAAA;;;;;EA+LT,KAAK,SACZ;AACI,UAAM,EAAE,QAAQ,WAAW,IAAI,KAAK;AAEpC,SAAK,iBAAiB,MAA2B;AACjD,SAAK,aAAa;AACN,IAAAA,cAAA,oBAAoB,QAAQ,aAAa;AACrD,WAAO,OAAO,KAAK,UAAU,QAAQ,iBAAiB,CAAA,CAAE;AACnD,SAAA,aAAa,yBAAyB,KAAK,SAAS;EAAA;;;;;EAOtD,iBAAiB,YACxB;AACI,SAAK,aAAa;EAAA;;EAIf,UACP;AACI,SAAK,iBAAiB,IAAI;AAC1B,SAAK,WAAW;AAChB,SAAK,iBAAiB;EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAkCnB,UAAU,MACjB;AACa,aAAA,OAAA;AACT,QAAI,cAAc;AAIlB,QAAI,WAAW,mBAAmB,KAAK,sBAAsB,iBAC7D;AACkB,oBAAA;IAAA;AAGd,QAAA,KAAK,mBAAmB,MAC5B;AACI;IAAA;AAEJ,SAAK,iBAAiB;AAChB,UAAA,QAAQ,KAAK,aAAa,IAAI;AAGpC,QAAI,OACJ;AACI,cAAQ,OAAO,OACf;QACI,KAAK;AAED,cAAI,aACJ;AACS,iBAAA,WAAW,MAAM,SAAS;UAAA;AAEnC;QACJ,KAAK;AAED,gBAAM,IAAI;AACV;QACJ,KAAK;AAGD,cAAI,aACJ;AACI,mBAAO,OAAO,KAAK,WAAW,OAAO,KAAK;UAAA;AAE9C;MAAA;IACR,WAEK,eAAe,OAAO,SAAS,YAAY,CAAC,OAAO,UAAU,eAAe,KAAK,KAAK,cAAc,IAAI,GACjH;AAGS,WAAA,WAAW,MAAM,SAAS;IAAA;EACnC;;;;;;;;;;;;;;;;;;;;;;;;EA0BJ,IAAW,UACX;AACI,WAAO,KAAK;EAAA;;;;;EAOR,eAAe,aACvB;AACQ,QAAA,CAAC,KAAK,SAAS;AAAO;AACrB,SAAA,aAAa,aAAa,KAAK,SAAS;AAEvC,UAAA,SAAS,KAAK,wBAAwB,WAAW;AAUvD,QAAI,KAAK,sBAAuB,OAAO,CAAC,EAAU,cAClD;AACI,YAAM,aAAa,YAAY,cAAc,EAAE,gBAAgB;AAE/D,UAAI,YACJ;AACI,oBAAY,eAAe;MAAA;IAC/B;AAGJ,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,KAC1C;AACUC,YAAAA,eAAc,OAAO,CAAC;AAC5B,YAAM,iBAAiB,KAAK,gBAAgB,KAAK,mBAAmBA,YAAW;AAE1E,WAAA,aAAa,SAAS,cAAc;IAAA;AAGxC,SAAA,UAAU,KAAK,aAAa,MAAM;EAAA;;;;;EAOnC,eAAe,aACvB;AACQ,QAAA,CAAC,KAAK,SAAS;AAAM;AACpB,SAAA,aAAa,aAAa,KAAK,SAAS;AAE7C,iBAAa,aAAa;AAEpB,UAAA,mBAAmB,KAAK,wBAAwB,WAAW;AAEjE,aAAS,IAAI,GAAG,IAAI,iBAAiB,QAAQ,IAAI,GAAG,KACpD;AACI,YAAM,QAAQ,KAAK,gBAAgB,KAAK,mBAAmB,iBAAiB,CAAC,CAAC;AAEzE,WAAA,aAAa,SAAS,KAAK;IAAA;AAG/B,SAAA,UAAU,KAAK,aAAa,MAAM;EAAA;;;;;EAOnC,aAAa,aACrB;AACQ,QAAA,CAAC,KAAK,SAAS;AAAO;AACrB,SAAA,aAAa,aAAa,KAAK,SAAS;AAE7C,QAAI,SAAS,YAAY;AAGzB,QAAI,YAAY,gBAAgB,YAAY,aAAa,EAAE,SAAS,GACpE;AACa,eAAA,YAAY,aAAa,EAAE,CAAC;IAAA;AAGzC,UAAM,UAAU,WAAW,KAAK,aAAa,YAAY;AACnD,UAAA,mBAAmB,KAAK,wBAAwB,WAAW;AAEjE,aAAS,IAAI,GAAG,IAAI,iBAAiB,QAAQ,IAAI,GAAG,KACpD;AACI,YAAM,QAAQ,KAAK,gBAAgB,KAAK,mBAAmB,iBAAiB,CAAC,CAAC;AAE9E,YAAM,QAAQ;AAET,WAAA,aAAa,SAAS,KAAK;IAAA;AAG/B,SAAA,UAAU,KAAK,aAAa,MAAM;EAAA;;;;;EAOnC,kBAAkB,aAC1B;AACQ,QAAA,CAAC,KAAK,SAAS;AAAO;AACrB,SAAA,aAAa,aAAa,KAAK,SAAS;AAEvC,UAAA,mBAAmB,KAAK,wBAAwB,WAAW;AAEjE,aAAS,IAAI,GAAG,IAAI,iBAAiB,QAAQ,IAAI,GAAG,KACpD;AACI,YAAM,QAAQ,KAAK,gBAAgB,KAAK,mBAAmB,iBAAiB,CAAC,CAAC;AAEzE,WAAA,aAAa,SAAS,KAAK;IAAA;AAG/B,SAAA,UAAU,KAAK,aAAa,MAAM;EAAA;;;;;EAOjC,QAAQ,aAClB;AACQ,QAAA,CAAC,KAAK,SAAS;AAAO;AACpB,UAAA,aAAa,KAAK,oBAAoB,WAAW;AAElD,SAAA,aAAa,aAAa,KAAK,SAAS;AACxC,SAAA,aAAa,SAAS,UAAU;EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA8BlC,iBAAiB,SACxB;AACI,SAAK,cAAc;AACnB,SAAK,aAAa;AAClB,iBAAa,aAAa;AAC1B,SAAK,WAAW;EAAA;;EAIZ,aACR;AACI,QAAI,KAAK,gBAAgB,CAAC,KAAK,YAC/B;AACI;IAAA;AAGJ,iBAAa,kBAAkB;AAEzB,UAAA,QAAQ,KAAK,WAAW;AAE9B,QAAI,OACJ;AACS,UAAA,WAAW,UAAkB,kBAClC;AACI,cAAM,mBAAmB;AACzB,cAAM,gBAAgB;MAAA,WAEjB,KAAK,uBACd;AACI,cAAM,cAAc;MAAA;IACxB;AAOJ,QAAI,KAAK,uBACT;AACI,iBAAW,SAAS,iBAAiB,eAAe,KAAK,gBAAgB,IAAI;AAC7E,WAAK,WAAW,iBAAiB,eAAe,KAAK,gBAAgB,IAAI;AAIzE,WAAK,WAAW,iBAAiB,gBAAgB,KAAK,mBAAmB,IAAI;AAC7E,WAAK,WAAW,iBAAiB,eAAe,KAAK,mBAAmB,IAAI;AAE5E,iBAAW,iBAAiB,aAAa,KAAK,cAAc,IAAI;IAAA,OAGpE;AACI,iBAAW,SAAS,iBAAiB,aAAa,KAAK,gBAAgB,IAAI;AAC3E,WAAK,WAAW,iBAAiB,aAAa,KAAK,gBAAgB,IAAI;AACvE,WAAK,WAAW,iBAAiB,YAAY,KAAK,mBAAmB,IAAI;AACzE,WAAK,WAAW,iBAAiB,aAAa,KAAK,mBAAmB,IAAI;AAC1E,iBAAW,iBAAiB,WAAW,KAAK,cAAc,IAAI;AAE9D,UAAI,KAAK,qBACT;AACI,aAAK,WAAW,iBAAiB,cAAc,KAAK,gBAAgB,IAAI;AAExE,aAAK,WAAW,iBAAiB,YAAY,KAAK,cAAc,IAAI;AACpE,aAAK,WAAW,iBAAiB,aAAa,KAAK,gBAAgB,IAAI;MAAA;IAC3E;AAGJ,SAAK,WAAW,iBAAiB,SAAS,KAAK,SAAS;MACpD,SAAS;MACT,SAAS;IAAA,CACZ;AAED,SAAK,eAAe;EAAA;;EAIhB,gBACR;AACI,QAAI,CAAC,KAAK,gBAAgB,CAAC,KAAK,YAChC;AACI;IAAA;AAGJ,iBAAa,qBAAqB;AAE5B,UAAA,QAAQ,KAAK,WAAW;AAG9B,QAAI,OACJ;AACS,UAAA,WAAW,UAAkB,kBAClC;AACI,cAAM,mBAAmB;AACzB,cAAM,gBAAgB;MAAA,WAEjB,KAAK,uBACd;AACI,cAAM,cAAc;MAAA;IACxB;AAGJ,QAAI,KAAK,uBACT;AACI,iBAAW,SAAS,oBAAoB,eAAe,KAAK,gBAAgB,IAAI;AAChF,WAAK,WAAW,oBAAoB,eAAe,KAAK,gBAAgB,IAAI;AAC5E,WAAK,WAAW,oBAAoB,gBAAgB,KAAK,mBAAmB,IAAI;AAChF,WAAK,WAAW,oBAAoB,eAAe,KAAK,mBAAmB,IAAI;AAE/E,iBAAW,oBAAoB,aAAa,KAAK,cAAc,IAAI;IAAA,OAGvE;AACI,iBAAW,SAAS,oBAAoB,aAAa,KAAK,gBAAgB,IAAI;AAC9E,WAAK,WAAW,oBAAoB,aAAa,KAAK,gBAAgB,IAAI;AAC1E,WAAK,WAAW,oBAAoB,YAAY,KAAK,mBAAmB,IAAI;AAC5E,WAAK,WAAW,oBAAoB,aAAa,KAAK,mBAAmB,IAAI;AAC7E,iBAAW,oBAAoB,WAAW,KAAK,cAAc,IAAI;AAEjE,UAAI,KAAK,qBACT;AACI,aAAK,WAAW,oBAAoB,cAAc,KAAK,gBAAgB,IAAI;AAE3E,aAAK,WAAW,oBAAoB,YAAY,KAAK,cAAc,IAAI;AACvE,aAAK,WAAW,oBAAoB,aAAa,KAAK,gBAAgB,IAAI;MAAA;IAC9E;AAGJ,SAAK,WAAW,oBAAoB,SAAS,KAAK,SAAS,IAAI;AAE/D,SAAK,aAAa;AAClB,SAAK,eAAe;EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAkCjB,mBAAmB,OAAkB,GAAW,GACvD;AACI,UAAM,OAAO,KAAK,WAAW,cACvB,KAAK,WAAW,sBAAA,IAChB;MACE,GAAG;MACH,GAAG;MACH,OAAQ,KAAK,WAAmB;MAChC,QAAS,KAAK,WAAmB;MACjC,MAAM;MACN,KAAK;IAAA;AAGP,UAAA,uBAAuB,IAAM,KAAK;AAElC,UAAA,KAAM,IAAI,KAAK,SAAU,KAAK,WAAmB,QAAQ,KAAK,SAAU;AACxE,UAAA,KAAM,IAAI,KAAK,QAAS,KAAK,WAAmB,SAAS,KAAK,UAAW;EAAA;;;;;;;EAS3E,wBAAwB,OAChC;AACI,UAAM,mBAAmB,CAAA;AAErB,QAAA,KAAK,uBAAuB,iBAAiB,YACjD;AACa,eAAA,IAAI,GAAG,KAAK,MAAM,eAAe,QAAQ,IAAI,IAAI,KAC1D;AACU,cAAA,QAAQ,MAAM,eAAe,CAAC;AAEhC,YAAA,OAAO,MAAM,WAAW;AAAa,gBAAM,SAAS;AACpD,YAAA,OAAO,MAAM,YAAY;AAAa,gBAAM,UAAU;AACtD,YAAA,OAAO,MAAM,cAAc,aAC/B;AACI,gBAAM,YAAY,MAAM,QAAQ,WAAW,KAAK,MAAM,SAAS;QAAA;AAE/D,YAAA,OAAO,MAAM,UAAU;AAAmB,gBAAA,QAAQ,MAAM,WAAW;AACnE,YAAA,OAAO,MAAM,WAAW;AAAmB,gBAAA,SAAS,MAAM,WAAW;AACrE,YAAA,OAAO,MAAM,UAAU;AAAa,gBAAM,QAAQ;AAClD,YAAA,OAAO,MAAM,UAAU;AAAa,gBAAM,QAAQ;AAClD,YAAA,OAAO,MAAM,gBAAgB;AAAa,gBAAM,cAAc;AAC9D,YAAA,OAAO,MAAM,cAAc;AAAmB,gBAAA,YAAY,MAAM,cAAc;AAC9E,YAAA,OAAO,MAAM,aAAa;AAAmB,gBAAA,WAAW,MAAM,SAAS;AACvE,YAAA,OAAO,MAAM,UAAU;AAAa,gBAAM,QAAQ;AAClD,YAAA,OAAO,MAAM,uBAAuB;AAAa,gBAAM,qBAAqB;AAK5E,YAAA,OAAO,MAAM,WAAW;AAAmB,gBAAA,SAAS,MAAM,UAAU,MAAM;AAC1E,YAAA,OAAO,MAAM,WAAW;AAAmB,gBAAA,SAAS,MAAM,UAAU,MAAM;AAG9E,cAAM,eAAe;AACrB,cAAM,OAAO,MAAM;AAEnB,yBAAiB,KAAK,KAAK;MAAA;IAC/B,WAGK,CAAC,WAAW,cACb,iBAAiB,eAAe,CAAC,KAAK,yBAAyB,EAAE,iBAAiB,WAAW,gBACrG;AACI,YAAM,YAAY;AAEd,UAAA,OAAO,UAAU,cAAc;AAAa,kBAAU,YAAY;AAClE,UAAA,OAAO,UAAU,UAAU;AAAa,kBAAU,QAAQ;AAC1D,UAAA,OAAO,UAAU,WAAW;AAAa,kBAAU,SAAS;AAC5D,UAAA,OAAO,UAAU,UAAU;AAAa,kBAAU,QAAQ;AAC1D,UAAA,OAAO,UAAU,UAAU;AAAa,kBAAU,QAAQ;AAC1D,UAAA,OAAO,UAAU,gBAAgB;AAAa,kBAAU,cAAc;AACtE,UAAA,OAAO,UAAU,cAAc;AAAa,kBAAU,YAAY;AAClE,UAAA,OAAO,UAAU,aAAa;AAAa,kBAAU,WAAW;AAChE,UAAA,OAAO,UAAU,UAAU;AAAa,kBAAU,QAAQ;AAC1D,UAAA,OAAO,UAAU,uBAAuB;AAAa,kBAAU,qBAAqB;AAGxF,gBAAU,eAAe;AAEzB,uBAAiB,KAAK,SAAS;IAAA,OAGnC;AACI,uBAAiB,KAAK,KAAK;IAAA;AAGxB,WAAA;EAAA;;;;;;;;;EAWD,oBAAoB,aAC9B;AACI,UAAM,QAAQ,KAAK;AAEd,SAAA,mBAAmB,OAAO,WAAW;AAS1C,UAAM,SAAS,YAAY;AAC3B,UAAM,SAAS,YAAY;AAC3B,UAAM,SAAS,YAAY;AAC3B,UAAM,YAAY,YAAY;AAE9B,SAAK,mBAAmB,MAAM,QAAQ,YAAY,SAAS,YAAY,OAAO;AACxE,UAAA,OAAO,SAAS,MAAM,MAAM;AAC5B,UAAA,OAAO,SAAS,MAAM,MAAM;AAElC,UAAM,cAAc;AACpB,UAAM,OAAO,YAAY;AAElB,WAAA;EAAA;;;;;;EAQH,gBAAgB,OAA8B,aACtD;AACI,UAAM,gBAAgB;AACtB,UAAM,cAAc;AAEpB,UAAM,YAAY,YAAY;AAC9B,UAAM,QAAQ,YAAY;AAC1B,UAAM,SAAS,YAAY;AAC3B,UAAM,YAAY,YAAY;AAC9B,UAAM,cAAc,YAAY;AAChC,UAAM,WAAW,YAAY;AAC7B,UAAM,qBAAqB,YAAY;AACvC,UAAM,QAAQ,YAAY;AAC1B,UAAM,QAAQ,YAAY;AAC1B,UAAM,QAAQ,YAAY;AACrB,SAAA,mBAAmB,OAAO,WAAW;AAE1C,SAAK,mBAAmB,MAAM,QAAQ,YAAY,SAAS,YAAY,OAAO;AACxE,UAAA,OAAO,SAAS,MAAM,MAAM;AAC5B,UAAA,OAAO,SAAS,MAAM,MAAM;AAElC,UAAM,YAAY,YAAY;AAC1B,QAAA,MAAM,SAAS,gBACnB;AACI,YAAM,OAAO;IAAA;AAEjB,QAAI,MAAM,KAAK,WAAW,OAAO,GACjC;AACI,YAAM,OAAO,MAAM,KAAK,QAAQ,SAAS,SAAS;IAAA;AAEtD,QAAI,MAAM,KAAK,WAAW,OAAO,GACjC;AACI,YAAM,OAAO,iBAAiB,MAAM,IAAI,KAAK,MAAM;IAAA;AAGhD,WAAA;EAAA;;;;;;EAQH,mBAAmB,OAA4B,aACvD;AACI,UAAM,YAAY,YAAY;AAC9B,UAAM,aAAa,YAAY;AACzB,UAAA,YAAY,YAAY,IAAI;AAClC,UAAM,OAAO,YAAY;AAEzB,UAAM,SAAS,YAAY;AAC3B,UAAM,SAAS,YAAY;AAC3B,UAAM,UAAU,YAAY;AACtB,UAAA,OAAO,IAAI,YAAY;AACvB,UAAA,OAAO,IAAI,YAAY;AAC7B,UAAM,UAAU,YAAY;AAC5B,UAAM,UAAU,YAAY;AACtB,UAAA,SAAS,IAAI,YAAY;AACzB,UAAA,SAAS,IAAI,YAAY;AACzB,UAAA,KAAK,IAAI,YAAY;AACrB,UAAA,KAAK,IAAI,YAAY;AAC3B,UAAM,gBAAgB;AACtB,UAAM,WAAW,YAAY;EAAA;AAErC;AAv4Ba,aAGK,YAA+B;EACzC,MAAM;EACN,MAAM;IACF,cAAc;IACd,cAAc;IACd,cAAc;EAAA;EAElB,UAAU;AACd;AAXS,aAgCK,uBAA4C;;EAEtD,MAAM;;EAEN,YAAY;;EAEZ,OAAO;;EAEP,OAAO;AACX;AAzCG,IAAM,cAAN;;;ACo8BA,IAAM,qBAA0C;EACnD,SAAS;EACT,aAAa;EACb,cAAc;EACd,cAAc;EACd,aAAa;EACb,mBAAmB;EACnB,YAAY;EACZ,aAAc;EACd,WAAY;EACZ,kBAAkB;EAClB,iBAAiB;EACjB,eAAgB;EAChB,gBAAgB;EAChB,gBAAiB;EACjB,eAAgB;EAChB,qBAAsB;EACtB,cAAe;EACf,eAAgB;EAChB,cAAe;EACf,aAAc;EACd,oBAAqB;EACrB,cAAe;EACf,aAAc;EACd,WAAY;EACZ,kBAAmB;EACnB,OAAQ;EACR,eAAgB;EAChB,YAAa;EACb,mBAAoB;EACpB,aAAc;EACd,mBAAoB;EACpB,cAAe;EACf,SAAU;EACV,IAAI,cACJ;AACI,WAAO,KAAK,cAAc,aAAa,KAAK,cAAc;EAAA;EAE9D,IAAI,YAAY,OAChB;AACS,SAAA,YAAY,QAAQ,WAAW;EAAA;EAExC,oBAAoB;EACpB,IAAI,YACJ;AACW,WAAA,KAAK,sBAAsB,YAAY;EAAA;EAElD,IAAI,UAAU,OACd;AACI,SAAK,qBAAqB;EAAA;EAE9B,gBACA;AACI,WAAO,KAAK,cAAc,YAAY,KAAK,cAAc;EAAA;EAE7D,qBAAqB;EACrB,SAAS;EACT,iBACI,MACA,UACA,SAEJ;AACU,UAAA,UAAW,OAAO,YAAY,aAAa,WAC7C,OAAO,YAAY,YAAY,QAAQ;AAC3C,UAAM,SAAS,OAAO,YAAY,WAAW,QAAQ,SAAS;AAC9D,UAAM,OAAO,OAAO,YAAY,WAAY,QAAQ,SAAS,OAAQ;AACrE,UAAM,UAAU,OAAO,aAAa,aAAa,SAAY;AAEtD,WAAA,UAAU,GAAG,IAAI,YAAY;AACpC,UAAM,aAAa,OAAO,aAAa,aAAa,WAAW,SAAS;AAExE,UAAM,UAAW;AAEjB,QAAI,QACJ;AACW,aAAA,iBAAiB,SAAS,MACjC;AACY,gBAAA,IAAI,MAAM,YAAY,OAAO;MAAA,CACxC;IAAA;AAGL,QAAI,MACJ;AACY,cAAA,KAAK,MAAM,YAAY,OAAO;IAAA,OAG1C;AACY,cAAA,GAAG,MAAM,YAAY,OAAO;IAAA;EACxC;EAEJ,oBACI,MACA,UACA,SAEJ;AACU,UAAA,UAAW,OAAO,YAAY,aAAa,WACzC,OAAO,YAAY,YAAY,QAAQ;AAC/C,UAAM,UAAU,OAAO,aAAa,aAAa,SAAY;AAEtD,WAAA,UAAU,GAAG,IAAI,YAAY;AACpC,eAAW,OAAO,aAAa,aAAa,WAAW,SAAS;AAE/D,SAAiC,IAAI,MAAM,UAAU,OAAO;EAAA;EAEjE,cAAc,GACd;AACQ,QAAA,EAAE,aAAa,iBACnB;AACU,YAAA,IAAI,MAAM,uEAAuE;IAAA;AAG3F,MAAE,mBAAmB;AACrB,MAAE,OAAO;AACT,MAAE,SAAS;AACT,MAAA,QAAQ,cAAc,CAAC;AAEzB,WAAO,CAAC,EAAE;EAAA;AAElB;;;ACzxCO,IAAM,UAAN,MACP;;;;;EAyBI,YAAY,UACZ;AATA,SAAiB,uBAAuC,CAAA;AAUpD,SAAK,YAAY;AAIjB,SAAK,UAAU,QAAQ,WAAW,IAAI,IAAI;AAGrC,SAAA,cAAc,SAAS,cAAc,KAAK;AAC1C,SAAA,YAAY,MAAM,WAAW;AAC7B,SAAA,YAAY,MAAM,MAAM;AACxB,SAAA,YAAY,MAAM,OAAO;AACzB,SAAA,YAAY,MAAM,gBAAgB;AAClC,SAAA,YAAY,MAAM,SAAS;EAAA;;;;;;EAQ7B,cAAc,cAA4B,iBACjD;AACI,QAAI,CAAC,KAAK,qBAAqB,SAAS,YAAY,GACpD;AACS,WAAA,qBAAqB,KAAK,YAAY;IAAA;EAC/C;;;;;EAOG,iBAAiB,eACxB;EAAA;;;;;;EASO,mBAAmB,eAC1B;AACW,WAAA;EAAA;;EAIJ,aACP;AACI,UAAM,sBAAsB,KAAK;AAE7B,QAAA,oBAAoB,WAAW,GACnC;AACI,WAAK,YAAY,OAAO;AAExB;IAAA;AAGE,UAAA,SAAS,KAAK,UAAU,KAAK;AAEnC,QAAI,KAAK,YAAY,eAAe,OAAO,YAC3C;AACW,aAAA,YAAY,YAAY,KAAK,WAAW;IAAA;AAG7C,UAAA,KAAM,WAAW,OAAO,MAAM,KAAK,IAAI,OAAO,QAAS,KAAK,UAAU;AACtE,UAAA,KAAM,WAAW,OAAO,MAAM,MAAM,IAAI,OAAO,SAAU,KAAK,UAAU;AAG9E,SAAK,YAAY,MAAM,YAAY,aAAa,OAAO,UAAU,OAAO,OAAO,SAAS,aAAa,EAAE,KAAK,EAAE;AAE9G,aAAS,IAAI,GAAG,IAAI,oBAAoB,QAAQ,KAChD;AACU,YAAA,eAAe,oBAAoB,CAAC;AAC1C,YAAM,UAAU,aAAa;AAE7B,UAAI,CAAC,aAAa,UAAU,aAAa,sBAAsB,GAC/D;AACI,iBAAS,OAAO;AACI,4BAAA,OAAO,GAAG,CAAC;AAC/B;MAAA,OAGJ;AACI,YAAI,CAAC,KAAK,YAAY,SAAS,OAAO,GACtC;AACI,kBAAQ,MAAM,WAAW;AACzB,kBAAQ,MAAM,gBAAgB;AACzB,eAAA,YAAY,YAAY,OAAO;QAAA;AAGxC,cAAM,KAAK,aAAa;AACxB,cAAM,SAAS,aAAa;AACtB,cAAA,KAAK,aAAa,QAAQ,OAAO;AACjC,cAAA,KAAK,aAAa,SAAS,OAAO;AAExC,gBAAQ,MAAM,kBAAkB,GAAG,EAAE,MAAM,EAAE;AACrC,gBAAA,MAAM,YAAY,UAAU,GAAG,CAAC,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,KAAK,GAAG,KAAK,EAAE,KAAK,GAAG,KAAK,EAAE;AACjG,gBAAQ,MAAM,UAAU,aAAa,WAAW,SAAS;MAAA;IAC7D;EACJ;;EAIG,UACP;AACI,SAAK,UAAU,QAAQ,WAAW,OAAO,IAAI;AAE7C,aAAS,IAAI,GAAG,IAAI,KAAK,qBAAqB,QAAQ,KACtD;AACU,YAAA,eAAe,KAAK,qBAAqB,CAAC;AAEhD,mBAAa,SAAS,OAAO;IAAA;AAGjC,SAAK,qBAAqB,SAAS;AACnC,SAAK,YAAY,OAAO;AACxB,SAAK,YAAY;EAAA;AAEzB;AApJa,QAMK,YAAY;EACtB,MAAM;IACF,cAAc;IACd,cAAc;IACd,cAAc;EAAA;EAElB,MAAM;AACV;;;AC8DG,IAAM,eAAN,cAA2B,cAClC;;;;EAkBI,YAAY,UAA+B,CAAA,GAC3C;AACI,UAAM,EAAE,SAAS,QAAQ,GAAG,KAAA,IAAS;AAE/B,UAAA;MACF,OAAO;MACP,GAAG;IAAA,CACN;AAvBL,SAAyB,eAAuB;AAGhD,SAAO,UAAU;AAsBb,SAAK,UAAU,IAAI,MAAM,GAAG,CAAC;AAE7B,QAAI,QACJ;AACI,WAAK,SAAS;IAAA;AAGlB,SAAK,UAAU,QAAQ,WAAW,SAAS,cAAc,KAAK;EAAA;;;;;;;;;;;;;;;;;;;;;;EAwBlE,IAAI,SACJ;AACI,WAAO,KAAK;EAAA;;;;;;;EAShB,IAAI,OAAO,OACX;AACW,WAAA,UAAU,WAAW,KAAK,QAAQ,IAAI,KAAK,IAAI,KAAK,QAAQ,SAAS,KAAK;EAAA;;;;;;;;;;;EAarF,IAAI,QAAQ,OACZ;AACI,QAAI,KAAK,aAAa;AAAO;AAE7B,SAAK,WAAW;AAChB,SAAK,aAAa;EAAA;;;;;;;;;;EAYtB,IAAI,UACJ;AACI,WAAO,KAAK;EAAA;;EAIN,eACV;AACI,UAAM,SAAS,KAAK;AACpB,UAAM,UAAU,KAAK;AAErB,QAAI,CAAC,SACL;AACI,aAAO,OAAO;AACd,aAAO,OAAO;AACd,aAAO,OAAO;AACd,aAAO,OAAO;AAEd;IAAA;AAGE,UAAA,EAAE,aAAa,aAAA,IAAiB;AAEtC,WAAO,OAAO;AACd,WAAO,OAAO;AACd,WAAO,OAAO;AACd,WAAO,OAAO;EAAA;;;;;;;;;EAWF,QAAQ,UAAmB,OAC3C;AACI,UAAM,QAAQ,OAAO;AAErB,SAAK,UAAU,YAAY,YAAY,KAAK,QAAQ;AACpD,SAAK,WAAW;AACf,SAAK,UAAmB;EAAA;AAEjC;", "names": ["navigator", "isMobile", "_AccessibilitySystem", "isMobile", "type", "_EventSystem", "nativeEvent"]}