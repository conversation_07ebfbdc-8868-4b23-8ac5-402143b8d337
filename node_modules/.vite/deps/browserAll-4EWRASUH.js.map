{"version": 3, "sources": ["../../pixi.js/src/accessibility/init.ts", "../../pixi.js/src/events/init.ts", "../../pixi.js/src/dom/init.ts"], "sourcesContent": ["import { extensions } from '../extensions/Extensions';\nimport { Container } from '../scene/container/Container';\nimport { AccessibilitySystem } from './AccessibilitySystem';\nimport { accessibilityTarget } from './accessibilityTarget';\n\nextensions.add(AccessibilitySystem);\nextensions.mixin(Container, accessibilityTarget);\n", "import { extensions } from '../extensions/Extensions';\nimport { Container } from '../scene/container/Container';\nimport { EventSystem } from './EventSystem';\nimport { FederatedContainer } from './FederatedEventTarget';\n\nextensions.add(EventSystem);\nextensions.mixin(Container, FederatedContainer);\n", "import { extensions } from '../extensions/Extensions';\nimport { DOMPipe } from './DOMPipe';\n\nexport * from './index';\n\nextensions.add(DOMPipe);\n"], "mappings": ";;;;;;;;;;;;;;;;;AAKA,WAAW,IAAI,mBAAmB;AAClC,WAAW,MAAM,WAAW,mBAAmB;;;ACD/C,WAAW,IAAI,WAAW;AAC1B,WAAW,MAAM,WAAW,kBAAkB;;;ACD9C,WAAW,IAAI,OAAO;", "names": []}