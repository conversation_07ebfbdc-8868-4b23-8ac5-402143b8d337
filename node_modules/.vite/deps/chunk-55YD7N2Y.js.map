{"version": 3, "sources": ["../../pixi.js/src/scene/graphics/gl/GlGraphicsAdaptor.ts", "../../pixi.js/src/scene/mesh/gl/GlMeshAdaptor.ts", "../../pixi.js/src/rendering/batcher/gl/GlBatchAdaptor.ts", "../../pixi.js/src/rendering/renderers/gl/buffer/const.ts", "../../pixi.js/src/rendering/renderers/gl/buffer/GlBuffer.ts", "../../pixi.js/src/rendering/renderers/gl/buffer/GlBufferSystem.ts", "../../pixi.js/src/rendering/renderers/gl/context/GlContextSystem.ts", "../../pixi.js/src/rendering/renderers/gl/texture/const.ts", "../../pixi.js/src/rendering/renderers/gl/geometry/utils/getGlTypeFromFormat.ts", "../../pixi.js/src/rendering/renderers/gl/geometry/GlGeometrySystem.ts", "../../pixi.js/src/rendering/renderers/gl/GlBackBufferSystem.ts", "../../pixi.js/src/rendering/renderers/gl/GlColorMaskSystem.ts", "../../pixi.js/src/rendering/renderers/gl/GlEncoderSystem.ts", "../../pixi.js/src/rendering/renderers/gl/GlLimitsSystem.ts", "../../pixi.js/src/rendering/renderers/gl/GlStencilSystem.ts", "../../pixi.js/src/rendering/renderers/gl/shader/utils/createUboElementsSTD40.ts", "../../pixi.js/src/rendering/renderers/gl/shader/utils/generateArraySyncSTD40.ts", "../../pixi.js/src/rendering/renderers/gl/shader/utils/createUboSyncSTD40.ts", "../../pixi.js/src/rendering/renderers/gl/GlUboSystem.ts", "../../pixi.js/src/rendering/renderers/gl/GlRenderTarget.ts", "../../pixi.js/src/rendering/renderers/gl/renderTarget/GlRenderTargetAdaptor.ts", "../../pixi.js/src/rendering/renderers/gl/renderTarget/GlRenderTargetSystem.ts", "../../pixi.js/src/rendering/renderers/gl/shader/GenerateShaderSyncCode.ts", "../../pixi.js/src/rendering/renderers/gl/shader/GlProgramData.ts", "../../pixi.js/src/rendering/renderers/gl/shader/program/compileShader.ts", "../../pixi.js/src/rendering/renderers/gl/shader/program/defaultValue.ts", "../../pixi.js/src/rendering/renderers/gl/shader/program/mapType.ts", "../../pixi.js/src/rendering/renderers/gl/shader/program/extractAttributesFromGlProgram.ts", "../../pixi.js/src/rendering/renderers/gl/shader/program/getUboData.ts", "../../pixi.js/src/rendering/renderers/gl/shader/program/getUniformData.ts", "../../pixi.js/src/rendering/renderers/gl/shader/program/logProgramError.ts", "../../pixi.js/src/rendering/renderers/gl/shader/program/generateProgram.ts", "../../pixi.js/src/rendering/renderers/gl/shader/GlShaderSystem.ts", "../../pixi.js/src/rendering/renderers/gl/shader/utils/generateUniformsSyncTypes.ts", "../../pixi.js/src/rendering/renderers/gl/shader/utils/generateUniformsSync.ts", "../../pixi.js/src/rendering/renderers/gl/shader/GlUniformGroupSystem.ts", "../../pixi.js/src/rendering/renderers/gl/state/mapWebGLBlendModesToPixi.ts", "../../pixi.js/src/rendering/renderers/gl/state/GlStateSystem.ts", "../../pixi.js/src/rendering/renderers/gl/texture/GlTexture.ts", "../../pixi.js/src/rendering/renderers/gl/texture/uploaders/glUploadBufferImageResource.ts", "../../pixi.js/src/rendering/renderers/gl/texture/uploaders/glUploadCompressedTextureResource.ts", "../../pixi.js/src/rendering/renderers/gl/texture/uploaders/glUploadImageResource.ts", "../../pixi.js/src/rendering/renderers/gl/texture/uploaders/glUploadVideoResource.ts", "../../pixi.js/src/rendering/renderers/gl/texture/utils/pixiToGlMaps.ts", "../../pixi.js/src/rendering/renderers/gl/texture/utils/applyStyleParams.ts", "../../pixi.js/src/rendering/renderers/gl/texture/utils/mapFormatToGlFormat.ts", "../../pixi.js/src/rendering/renderers/gl/texture/utils/mapFormatToGlInternalFormat.ts", "../../pixi.js/src/rendering/renderers/gl/texture/utils/mapFormatToGlType.ts", "../../pixi.js/src/rendering/renderers/gl/texture/utils/unpremultiplyAlpha.ts", "../../pixi.js/src/rendering/renderers/gl/texture/GlTextureSystem.ts", "../../pixi.js/src/rendering/renderers/gl/WebGLRenderer.ts"], "sourcesContent": ["import { ExtensionType } from '../../../extensions/Extensions';\nimport { Matrix } from '../../../maths/matrix/Matrix';\nimport { compileHighShaderGlProgram } from '../../../rendering/high-shader/compileHighShaderToProgram';\nimport { colorBitGl } from '../../../rendering/high-shader/shader-bits/colorBit';\nimport { generateTextureBatchBitGl } from '../../../rendering/high-shader/shader-bits/generateTextureBatchBit';\nimport { localUniformBitGl } from '../../../rendering/high-shader/shader-bits/localUniformBit';\nimport { roundPixelsBitGl } from '../../../rendering/high-shader/shader-bits/roundPixelsBit';\nimport { getBatchSamplersUniformGroup } from '../../../rendering/renderers/gl/shader/getBatchSamplersUniformGroup';\nimport { Shader } from '../../../rendering/renderers/shared/shader/Shader';\nimport { UniformGroup } from '../../../rendering/renderers/shared/shader/UniformGroup';\nimport { type Renderer } from '../../../rendering/renderers/types';\n\nimport type { Batch } from '../../../rendering/batcher/shared/Batcher';\nimport type { WebGLRenderer } from '../../../rendering/renderers/gl/WebGLRenderer';\nimport type { Graphics } from '../shared/Graphics';\nimport type { GraphicsAdaptor, GraphicsPipe } from '../shared/GraphicsPipe';\n\n/**\n * A GraphicsAdaptor that uses WebGL to render graphics.\n * @category rendering\n * @ignore\n */\nexport class GlGraphicsAdaptor implements GraphicsAdaptor\n{\n    /** @ignore */\n    public static extension = {\n        type: [\n            ExtensionType.WebGLPipesAdaptor,\n        ],\n        name: 'graphics',\n    } as const;\n\n    public shader: Shader;\n\n    public contextChange(renderer: Renderer): void\n    {\n        const uniforms = new UniformGroup({\n            uColor: { value: new Float32Array([1, 1, 1, 1]), type: 'vec4<f32>' },\n            uTransformMatrix: { value: new Matrix(), type: 'mat3x3<f32>' },\n            uRound: { value: 0, type: 'f32' },\n        });\n\n        const maxTextures = renderer.limits.maxBatchableTextures;\n\n        const glProgram = compileHighShaderGlProgram({\n            name: 'graphics',\n            bits: [\n                colorBitGl,\n                generateTextureBatchBitGl(maxTextures),\n                localUniformBitGl,\n                roundPixelsBitGl,\n            ]\n        });\n\n        this.shader = new Shader({\n            glProgram,\n            resources: {\n                localUniforms: uniforms,\n                batchSamplers: getBatchSamplersUniformGroup(maxTextures),\n            }\n        });\n    }\n\n    public execute(graphicsPipe: GraphicsPipe, renderable: Graphics): void\n    {\n        const context = renderable.context;\n        const shader = context.customShader || this.shader;\n        const renderer = graphicsPipe.renderer as WebGLRenderer;\n        const contextSystem = renderer.graphicsContext;\n\n        const {\n            batcher, instructions,\n        } = contextSystem.getContextRenderData(context);\n\n        // WebGL specific..\n        shader.groups[0] = renderer.globalUniforms.bindGroup;\n\n        renderer.state.set(graphicsPipe.state);\n\n        renderer.shader.bind(shader);\n\n        renderer.geometry.bind(batcher.geometry, shader.glProgram);\n\n        const batches = instructions.instructions as Batch[];\n\n        for (let i = 0; i < instructions.instructionSize; i++)\n        {\n            const batch = batches[i];\n\n            if (batch.size)\n            {\n                for (let j = 0; j < batch.textures.count; j++)\n                {\n                    renderer.texture.bind(batch.textures.textures[j], j);\n                }\n\n                renderer.geometry.draw(batch.topology, batch.size, batch.start);\n            }\n        }\n    }\n\n    public destroy(): void\n    {\n        this.shader.destroy(true);\n        this.shader = null;\n    }\n}\n", "import { ExtensionType } from '../../../extensions/Extensions';\nimport { Matrix } from '../../../maths/matrix/Matrix';\nimport { compileHighShaderGlProgram } from '../../../rendering/high-shader/compileHighShaderToProgram';\nimport { localUniformBitGl } from '../../../rendering/high-shader/shader-bits/localUniformBit';\nimport { roundPixelsBitGl } from '../../../rendering/high-shader/shader-bits/roundPixelsBit';\nimport { textureBitGl } from '../../../rendering/high-shader/shader-bits/textureBit';\nimport { Shader } from '../../../rendering/renderers/shared/shader/Shader';\nimport { Texture } from '../../../rendering/renderers/shared/texture/Texture';\nimport { warn } from '../../../utils/logging/warn';\n\nimport type { Mesh } from '../shared/Mesh';\nimport type { MeshAdaptor, MeshPipe } from '../shared/MeshPipe';\n\n/**\n * A MeshAdaptor that uses the WebGL to render meshes.\n * @category rendering\n * @ignore\n */\nexport class GlMeshAdaptor implements MeshAdaptor\n{\n    public static extension = {\n        type: [\n            ExtensionType.WebGLPipesAdaptor,\n        ],\n        name: 'mesh',\n    } as const;\n\n    private _shader: Shader;\n\n    public init(): void\n    {\n        const glProgram = compileHighShaderGlProgram({\n            name: 'mesh',\n            bits: [\n                localUniformBitGl,\n                textureBitGl,\n                roundPixelsBitGl,\n            ]\n        });\n\n        this._shader = new Shader({\n            glProgram,\n            resources: {\n                uTexture: Texture.EMPTY.source,\n                textureUniforms: {\n                    uTextureMatrix: { type: 'mat3x3<f32>', value: new Matrix() },\n                }\n            }\n        });\n    }\n\n    public execute(meshPipe: MeshPipe, mesh: Mesh): void\n    {\n        const renderer = meshPipe.renderer;\n\n        let shader: Shader = mesh._shader;\n\n        if (!shader)\n        {\n            shader = this._shader;\n\n            const texture = mesh.texture;\n            const source = texture.source;\n\n            shader.resources.uTexture = source;\n            shader.resources.uSampler = source.style;\n            shader.resources.textureUniforms.uniforms.uTextureMatrix = texture.textureMatrix.mapCoord;\n        }\n        else if (!shader.glProgram)\n        {\n            // #if _DEBUG\n            warn('Mesh shader has no glProgram', mesh.shader);\n            // #endif\n\n            return;\n        }\n\n        // setting the groups to be high to be compatible and not\n        // overlap any other groups\n        shader.groups[100] = renderer.globalUniforms.bindGroup;\n        shader.groups[101] = meshPipe.localUniformsBindGroup;\n\n        renderer.encoder.draw({\n            geometry: mesh._geometry,\n            shader,\n            state: mesh.state,\n        });\n    }\n\n    public destroy(): void\n    {\n        this._shader.destroy(true);\n        this._shader = null;\n    }\n}\n", "import { ExtensionType } from '../../../extensions/Extensions';\nimport { State } from '../../renderers/shared/state/State';\n\nimport type { WebGLRenderer } from '../../renderers/gl/WebGLRenderer';\nimport type { Geometry } from '../../renderers/shared/geometry/Geometry';\nimport type { Shader } from '../../renderers/shared/shader/Shader';\nimport type { Batch } from '../shared/Batcher';\nimport type { BatcherAdaptor, BatcherPipe } from '../shared/BatcherPipe';\n\n/**\n * A BatcherAdaptor that uses WebGL to render batches.\n * @category rendering\n * @ignore\n */\nexport class GlBatchAdaptor implements BatcherAdaptor\n{\n    /** @ignore */\n    public static extension = {\n        type: [\n            ExtensionType.WebGLPipesAdaptor,\n        ],\n        name: 'batch',\n    } as const;\n\n    private readonly _tempState = State.for2d();\n\n    /**\n     * We only want to sync the a batched shaders uniforms once on first use\n     * this is a hash of shader uids to a boolean value.  When the shader is first bound\n     * we set the value to true.  When the shader is bound again we check the value and\n     * if it is true we know that the uniforms have already been synced and we skip it.\n     */\n    private _didUploadHash: Record<string, boolean> = {};\n    public init(batcherPipe: BatcherPipe): void\n    {\n        batcherPipe.renderer.runners.contextChange.add(this);\n    }\n\n    public contextChange(): void\n    {\n        this._didUploadHash = {};\n    }\n\n    public start(batchPipe: BatcherPipe, geometry: Geometry, shader: Shader): void\n    {\n        const renderer = batchPipe.renderer as WebGLRenderer;\n\n        const didUpload = this._didUploadHash[shader.uid];\n\n        // only want to sync the shade ron its first bind!\n        renderer.shader.bind(shader, didUpload);\n\n        if (!didUpload)\n        {\n            this._didUploadHash[shader.uid] = true;\n        }\n\n        renderer.shader.updateUniformGroup(renderer.globalUniforms.uniformGroup);\n\n        renderer.geometry.bind(geometry, shader.glProgram);\n    }\n\n    public execute(batchPipe: BatcherPipe, batch: Batch): void\n    {\n        const renderer = batchPipe.renderer as WebGLRenderer;\n\n        this._tempState.blendMode = batch.blendMode;\n\n        renderer.state.set(this._tempState);\n\n        const textures = batch.textures.textures;\n\n        for (let i = 0; i < batch.textures.count; i++)\n        {\n            renderer.texture.bind(textures[i], i);\n        }\n\n        renderer.geometry.draw(batch.topology, batch.size, batch.start);\n    }\n}\n", "/**\n * Constants for various buffer types in Pixi\n * @category rendering\n * @advanced\n */\nexport enum BUFFER_TYPE\n{\n    /** buffer type for using as an index buffer */\n    ELEMENT_ARRAY_BUFFER = 34963,\n    /** buffer type for using attribute data */\n    ARRAY_BUFFER = 34962,\n    /** the buffer type is for uniform buffer objects */\n    UNIFORM_BUFFER = 35345,\n}\n\n", "import type { BUFFER_TYPE } from './const';\n\n/** @internal */\nexport class GlBuffer\n{\n    public buffer: WebGLBuffer;\n    public updateID: number;\n    public byteLength: number;\n    public type: number;\n\n    public _lastBindBaseLocation: number = -1;\n    public _lastBindCallId: number = -1;\n\n    constructor(buffer: WebGLBuffer, type: BUFFER_TYPE)\n    {\n        this.buffer = buffer || null;\n        this.updateID = -1;\n        this.byteLength = -1;\n        this.type = type;\n    }\n}\n", "import { ExtensionType } from '../../../../extensions/Extensions';\nimport { BufferUsage } from '../../shared/buffer/const';\nimport { BUFFER_TYPE } from './const';\nimport { GlBuffer } from './GlBuffer';\n\nimport type { Buffer } from '../../shared/buffer/Buffer';\nimport type { System } from '../../shared/system/System';\nimport type { GlRenderingContext } from '../context/GlRenderingContext';\nimport type { WebGLRenderer } from '../WebGLRenderer';\n\n/**\n * System plugin to the renderer to manage buffers.\n *\n * WebGL uses Buffers as a way to store objects to the GPU.\n * This system makes working with them a lot easier.\n *\n * Buffers are used in three main places in WebGL\n * - geometry information\n * - Uniform information (via uniform buffer objects - a WebGL 2 only feature)\n * - Transform feedback information. (WebGL 2 only feature)\n *\n * This system will handle the binding of buffers to the GPU as well as uploading\n * them. With this system, you never need to work directly with GPU buffers, but instead work with\n * the Buffer class.\n * @class\n * @category rendering\n * @advanced\n */\nexport class GlBufferSystem implements System\n{\n    /** @ignore */\n    public static extension = {\n        type: [\n            ExtensionType.WebGLSystem,\n        ],\n        name: 'buffer',\n    } as const;\n\n    private _gl: GlRenderingContext;\n    private _gpuBuffers: {[key: number]: GlBuffer} = Object.create(null);\n\n    /** Cache keeping track of the base bound buffer bases */\n    private _boundBufferBases: {[key: number]: GlBuffer} = Object.create(null);\n\n    private _renderer: WebGLRenderer;\n\n    private _minBaseLocation = 0;\n    private _maxBindings: number;\n    private _nextBindBaseIndex = this._minBaseLocation;\n    private _bindCallId = 0;\n\n    /**\n     * @param {Renderer} renderer - The renderer this System works for.\n     */\n    constructor(renderer: WebGLRenderer)\n    {\n        this._renderer = renderer;\n\n        this._renderer.renderableGC.addManagedHash(this, '_gpuBuffers');\n    }\n\n    /** @ignore */\n    public destroy(): void\n    {\n        this._renderer = null;\n        this._gl = null;\n        this._gpuBuffers = null;\n        (this._boundBufferBases as null) = null;\n    }\n\n    /** Sets up the renderer context and necessary buffers. */\n    protected contextChange(): void\n    {\n        this._gl = this._renderer.gl;\n\n        this._gpuBuffers = Object.create(null);\n        this._maxBindings = this._renderer.limits.maxUniformBindings;\n    }\n\n    public getGlBuffer(buffer: Buffer): GlBuffer\n    {\n        return this._gpuBuffers[buffer.uid] || this.createGLBuffer(buffer);\n    }\n\n    /**\n     * This binds specified buffer. On first run, it will create the webGL buffers for the context too\n     * @param buffer - the buffer to bind to the renderer\n     */\n    public bind(buffer: Buffer): void\n    {\n        const { _gl: gl } = this;\n\n        const glBuffer = this.getGlBuffer(buffer);\n\n        gl.bindBuffer(glBuffer.type, glBuffer.buffer);\n    }\n\n    /**\n     * Binds an uniform buffer to at the given index.\n     *\n     * A cache is used so a buffer will not be bound again if already bound.\n     * @param glBuffer - the buffer to bind\n     * @param index - the base index to bind it to.\n     */\n    public bindBufferBase(glBuffer: GlBuffer, index: number): void\n    {\n        const { _gl: gl } = this;\n\n        if (this._boundBufferBases[index] !== glBuffer)\n        {\n            this._boundBufferBases[index] = glBuffer;\n            glBuffer._lastBindBaseLocation = index;\n\n            gl.bindBufferBase(gl.UNIFORM_BUFFER, index, glBuffer.buffer);\n        }\n    }\n\n    public nextBindBase(hasTransformFeedback: boolean)\n    {\n        this._bindCallId++;\n        this._minBaseLocation = 0;\n        if (hasTransformFeedback)\n        {\n            this._boundBufferBases[0] = null;\n            this._minBaseLocation = 1;\n            if (this._nextBindBaseIndex < 1)\n            {\n                this._nextBindBaseIndex = 1;\n            }\n        }\n    }\n\n    public freeLocationForBufferBase(glBuffer: GlBuffer): number\n    {\n        let freeIndex = this.getLastBindBaseLocation(glBuffer);\n\n        // check if it is already bound..\n        if (freeIndex >= this._minBaseLocation)\n        {\n            glBuffer._lastBindCallId = this._bindCallId;\n\n            return freeIndex;\n        }\n\n        let loop = 0;\n        let nextIndex = this._nextBindBaseIndex;\n\n        while (loop < 2)\n        {\n            if (nextIndex >= this._maxBindings)\n            {\n                nextIndex = this._minBaseLocation;\n                loop++;\n            }\n\n            const curBuf = this._boundBufferBases[nextIndex];\n\n            if (curBuf && curBuf._lastBindCallId === this._bindCallId)\n            {\n                nextIndex++;\n                continue;\n            }\n            break;\n        }\n\n        freeIndex = nextIndex;\n        this._nextBindBaseIndex = nextIndex + 1;\n\n        if (loop >= 2)\n        {\n            // TODO: error\n            return -1;\n        }\n\n        glBuffer._lastBindCallId = this._bindCallId;\n        this._boundBufferBases[freeIndex] = null;\n\n        return freeIndex;\n    }\n\n    public getLastBindBaseLocation(glBuffer: GlBuffer): number\n    {\n        const index = glBuffer._lastBindBaseLocation;\n\n        if (this._boundBufferBases[index] === glBuffer)\n        {\n            return index;\n        }\n\n        return -1;\n    }\n\n    /**\n     * Binds a buffer whilst also binding its range.\n     * This will make the buffer start from the offset supplied rather than 0 when it is read.\n     * @param glBuffer - the buffer to bind\n     * @param index - the base index to bind at, defaults to 0\n     * @param offset - the offset to bind at (this is blocks of 256). 0 = 0, 1 = 256, 2 = 512 etc\n     * @param size - the size to bind at (this is blocks of 256).\n     */\n    public bindBufferRange(glBuffer: GlBuffer, index?: number, offset?: number, size?: number): void\n    {\n        const { _gl: gl } = this;\n\n        offset ||= 0;\n        index ||= 0;\n\n        this._boundBufferBases[index] = null;\n\n        gl.bindBufferRange(gl.UNIFORM_BUFFER, index || 0, glBuffer.buffer, offset * 256, size || 256);\n    }\n\n    /**\n     * Will ensure the data in the buffer is uploaded to the GPU.\n     * @param {Buffer} buffer - the buffer to update\n     */\n    public updateBuffer(buffer: Buffer): GlBuffer\n    {\n        const { _gl: gl } = this;\n\n        const glBuffer = this.getGlBuffer(buffer);\n\n        if (buffer._updateID === glBuffer.updateID)\n        {\n            return glBuffer;\n        }\n\n        glBuffer.updateID = buffer._updateID;\n\n        gl.bindBuffer(glBuffer.type, glBuffer.buffer);\n\n        const data = buffer.data;\n\n        const drawType = (buffer.descriptor.usage & BufferUsage.STATIC) ? gl.STATIC_DRAW : gl.DYNAMIC_DRAW;\n\n        if (data)\n        {\n            if (glBuffer.byteLength >= data.byteLength)\n            {\n                // assuming our buffers are aligned to 4 bits...\n                // offset is always zero for now!\n                gl.bufferSubData(glBuffer.type, 0, data, 0, buffer._updateSize / data.BYTES_PER_ELEMENT);\n            }\n            else\n            {\n                glBuffer.byteLength = data.byteLength;\n                // assuming our buffers are aligned to 4 bits...\n                gl.bufferData(glBuffer.type, data, drawType);\n            }\n        }\n        else\n        {\n            glBuffer.byteLength = buffer.descriptor.size;\n            gl.bufferData(glBuffer.type, glBuffer.byteLength, drawType);\n        }\n\n        return glBuffer;\n    }\n\n    /** dispose all WebGL resources of all managed buffers */\n    public destroyAll(): void\n    {\n        const gl = this._gl;\n\n        for (const id in this._gpuBuffers)\n        {\n            gl.deleteBuffer(this._gpuBuffers[id].buffer);\n        }\n\n        this._gpuBuffers = Object.create(null);\n    }\n\n    /**\n     * Disposes buffer\n     * @param {Buffer} buffer - buffer with data\n     * @param {boolean} [contextLost=false] - If context was lost, we suppress deleteVertexArray\n     */\n    protected onBufferDestroy(buffer: Buffer, contextLost?: boolean): void\n    {\n        const glBuffer = this._gpuBuffers[buffer.uid];\n\n        const gl = this._gl;\n\n        if (!contextLost)\n        {\n            gl.deleteBuffer(glBuffer.buffer);\n        }\n\n        this._gpuBuffers[buffer.uid] = null;\n    }\n\n    /**\n     * creates and attaches a GLBuffer object tied to the current context.\n     * @param buffer\n     * @protected\n     */\n    protected createGLBuffer(buffer: Buffer): GlBuffer\n    {\n        const { _gl: gl } = this;\n\n        let type = BUFFER_TYPE.ARRAY_BUFFER;\n\n        if ((buffer.descriptor.usage & BufferUsage.INDEX))\n        {\n            type = BUFFER_TYPE.ELEMENT_ARRAY_BUFFER;\n        }\n        else if ((buffer.descriptor.usage & BufferUsage.UNIFORM))\n        {\n            type = BUFFER_TYPE.UNIFORM_BUFFER;\n        }\n\n        const glBuffer = new GlBuffer(gl.createBuffer(), type);\n\n        this._gpuBuffers[buffer.uid] = glBuffer;\n\n        buffer.on('destroy', this.onBufferDestroy, this);\n\n        return glBuffer;\n    }\n\n    public resetState(): void\n    {\n        this._boundBufferBases = Object.create(null);\n    }\n}\n", "import { DOMAdapter } from '../../../../environment/adapter';\nimport { ExtensionType } from '../../../../extensions/Extensions';\nimport { warn } from '../../../../utils/logging/warn';\nimport { type GpuPowerPreference } from '../../types';\n\nimport type { ICanvas } from '../../../../environment/canvas/ICanvas';\nimport type { System } from '../../shared/system/System';\nimport type { WebGLRenderer } from '../WebGLRenderer';\nimport type { WebGLExtensions } from './WebGLExtensions';\n\n/**\n * Options for the context system.\n * @category rendering\n * @advanced\n * @property {WebGL2RenderingContext | null} [context=null] - User-provided WebGL rendering context object.\n * @property {GpuPowerPreference} [powerPreference='default'] - An optional hint indicating what configuration\n * of GPU is suitable for the WebGL context, can be `'high-performance'` or `'low-power'`. Setting to `'high-performance'`\n * will prioritize rendering performance over power consumption, while setting to `'low-power'` will prioritize power saving\n * over rendering performance.\n * @property {boolean} [premultipliedAlpha=true] - Whether the compositor will assume the drawing buffer contains\n * colors with premultiplied alpha.\n * @property {boolean} [preserveDrawingBuffer=false] - Whether to enable drawing buffer preservation.\n * If enabled, the drawing buffer will preserve\n * its value until cleared or overwritten. Enable this if you need to call `toDataUrl` on the WebGL context.\n * @property {boolean} [antialias] - Whether to enable antialiasing.\n * @property {1 | 2} [preferWebGLVersion=2] - The preferred WebGL version to use.\n */\nexport interface ContextSystemOptions\n{\n    /**\n     * User-provided WebGL rendering context object.\n     * @default null\n     */\n    context: WebGL2RenderingContext | null;\n    /**\n     * An optional hint indicating what configuration of GPU is suitable for the WebGL context,\n     * can be `'high-performance'` or `'low-power'`.\n     * Setting to `'high-performance'` will prioritize rendering performance over power consumption,\n     * while setting to `'low-power'` will prioritize power saving over rendering performance.\n     * @default undefined\n     */\n    powerPreference?: GpuPowerPreference;\n\n    /**\n     * Whether the compositor will assume the drawing buffer contains colors with premultiplied alpha.\n     * @default true\n     */\n    premultipliedAlpha: boolean;\n    /**\n     * Whether to enable drawing buffer preservation. If enabled, the drawing buffer will preserve\n     * its value until cleared or overwritten. Enable this if you need to call `toDataUrl` on the WebGL context.\n     * @default false\n     */\n    preserveDrawingBuffer: boolean;\n\n    antialias?: boolean;\n\n    /**\n     * The preferred WebGL version to use.\n     * @default 2\n     */\n    preferWebGLVersion?: 1 | 2;\n\n    /**\n     * Whether to enable multi-view rendering. Set to true when rendering to multiple\n     * canvases on the dom.\n     * @default false\n     */\n    multiView: boolean;\n}\n\n/**\n * System plugin to the renderer to manage the context\n * @category rendering\n * @advanced\n */\nexport class GlContextSystem implements System<ContextSystemOptions>\n{\n    /** @ignore */\n    public static extension = {\n        type: [\n            ExtensionType.WebGLSystem,\n        ],\n        name: 'context',\n    } as const;\n\n    /** The default options for the system. */\n    public static defaultOptions: ContextSystemOptions = {\n        /**\n         * {@link WebGLOptions.context}\n         * @default null\n         */\n        context: null,\n        /**\n         * {@link WebGLOptions.premultipliedAlpha}\n         * @default true\n         */\n        premultipliedAlpha: true,\n        /**\n         * {@link WebGLOptions.preserveDrawingBuffer}\n         * @default false\n         */\n        preserveDrawingBuffer: false,\n        /**\n         * {@link WebGLOptions.powerPreference}\n         * @default default\n         */\n        powerPreference: undefined,\n        /**\n         * {@link WebGLOptions.webGLVersion}\n         * @default 2\n         */\n        preferWebGLVersion: 2,\n        /**\n         * {@link WebGLOptions.multiView}\n         * @default false\n         */\n        multiView: false\n    };\n\n    protected CONTEXT_UID: number;\n    protected gl: WebGL2RenderingContext;\n\n    /**\n     * Features supported by current renderer.\n     * @type {object}\n     * @readonly\n     */\n    public supports = {\n        /** Support for 32-bit indices buffer. */\n        uint32Indices: true,\n        /** Support for UniformBufferObjects */\n        uniformBufferObject: true,\n        /** Support for VertexArrayObjects */\n        vertexArrayObject: true,\n        /** Support for SRGB texture format */\n        srgbTextures: true,\n        /** Support for wrapping modes if a texture is non-power of two */\n        nonPowOf2wrapping: true,\n        /** Support for MSAA (antialiasing of dynamic textures) */\n        msaa: true,\n        /** Support for mipmaps if a texture is non-power of two */\n        nonPowOf2mipmaps: true,\n    };\n\n    /**\n     * Extensions available.\n     * @type {object}\n     * @readonly\n     * @property {WEBGL_draw_buffers} drawBuffers - WebGL v1 extension\n     * @property {WEBGL_depth_texture} depthTexture - WebGL v1 extension\n     * @property {OES_texture_float} floatTexture - WebGL v1 extension\n     * @property {WEBGL_lose_context} loseContext - WebGL v1 extension\n     * @property {OES_vertex_array_object} vertexArrayObject - WebGL v1 extension\n     * @property {EXT_texture_filter_anisotropic} anisotropicFiltering - WebGL v1 and v2 extension\n     */\n    public extensions: WebGLExtensions;\n\n    public webGLVersion: 1 | 2;\n\n    /**\n     * Whether to enable multi-view rendering. Set to true when rendering to multiple\n     * canvases on the dom.\n     * @default false\n     */\n    public multiView: boolean;\n\n    /**\n     * The canvas that the WebGL Context is rendering to.\n     * This will be the view canvas. But if multiView is enabled, this canvas will not be attached to the DOM.\n     * It will be rendered to and then copied to the target canvas.\n     * @readonly\n     */\n    public canvas: ICanvas;\n\n    private _renderer: WebGLRenderer;\n    private _contextLossForced: boolean;\n\n    /** @param renderer - The renderer this System works for. */\n    constructor(renderer: WebGLRenderer)\n    {\n        this._renderer = renderer;\n\n        this.extensions = Object.create(null);\n\n        // Bind functions\n        this.handleContextLost = this.handleContextLost.bind(this);\n        this.handleContextRestored = this.handleContextRestored.bind(this);\n    }\n\n    /**\n     * `true` if the context is lost\n     * @readonly\n     */\n    get isLost(): boolean\n    {\n        return (!this.gl || this.gl.isContextLost());\n    }\n\n    /**\n     * Handles the context change event.\n     * @param {WebGLRenderingContext} gl - New WebGL context.\n     */\n    protected contextChange(gl: WebGL2RenderingContext): void\n    {\n        this.gl = gl;\n        this._renderer.gl = gl;\n    }\n\n    public init(options: ContextSystemOptions): void\n    {\n        options = { ...GlContextSystem.defaultOptions, ...options };\n\n        // TODO add to options\n        let multiView = this.multiView = options.multiView;\n\n        if (options.context && multiView)\n        {\n            // eslint-disable-next-line max-len\n            warn('Renderer created with both a context and multiview enabled. Disabling multiView as both cannot work together.');\n\n            multiView = false;\n        }\n\n        if (multiView)\n        {\n            this.canvas = DOMAdapter.get()\n                .createCanvas(this._renderer.canvas.width, this._renderer.canvas.height);\n        }\n        else\n        {\n            this.canvas = this._renderer.view.canvas;\n        }\n        /*\n         * The options passed in to create a new WebGL context.\n         */\n        if (options.context)\n        {\n            this.initFromContext(options.context);\n        }\n        else\n        {\n            const alpha = this._renderer.background.alpha < 1;\n            const premultipliedAlpha = options.premultipliedAlpha ?? true;\n            const antialias = options.antialias && !this._renderer.backBuffer.useBackBuffer;\n\n            this.createContext(options.preferWebGLVersion, {\n                alpha,\n                premultipliedAlpha,\n                antialias,\n                stencil: true,\n                preserveDrawingBuffer: options.preserveDrawingBuffer,\n                powerPreference: options.powerPreference ?? 'default',\n            });\n        }\n    }\n\n    public ensureCanvasSize(targetCanvas: ICanvas): void\n    {\n        if (!this.multiView)\n        {\n            if (targetCanvas !== this.canvas)\n            {\n                warn('multiView is disabled, but targetCanvas is not the main canvas');\n            }\n\n            return;\n        }\n\n        const { canvas } = this;\n\n        if (canvas.width < targetCanvas.width || canvas.height < targetCanvas.height)\n        {\n            canvas.width = Math.max(targetCanvas.width, targetCanvas.width);\n            canvas.height = Math.max(targetCanvas.height, targetCanvas.height);\n        }\n    }\n\n    /**\n     * Initializes the context.\n     * @protected\n     * @param {WebGLRenderingContext} gl - WebGL context\n     */\n    protected initFromContext(gl: WebGL2RenderingContext): void\n    {\n        this.gl = gl;\n\n        this.webGLVersion = gl instanceof DOMAdapter.get().getWebGLRenderingContext() ? 1 : 2;\n\n        this.getExtensions();\n\n        this.validateContext(gl);\n\n        this._renderer.runners.contextChange.emit(gl);\n\n        const element = this._renderer.view.canvas;\n\n        (element as any).addEventListener('webglcontextlost', this.handleContextLost, false);\n        element.addEventListener('webglcontextrestored', this.handleContextRestored, false);\n    }\n\n    /**\n     * Initialize from context options\n     * @protected\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/HTMLCanvasElement/getContext\n     * @param preferWebGLVersion\n     * @param {object} options - context attributes\n     */\n    protected createContext(preferWebGLVersion: 1 | 2, options: WebGLContextAttributes): void\n    {\n        let gl: WebGL2RenderingContext | WebGLRenderingContext;\n\n        const canvas = this.canvas;\n\n        if (preferWebGLVersion === 2)\n        {\n            gl = canvas.getContext('webgl2', options);\n        }\n\n        if (!gl)\n        {\n            gl = canvas.getContext('webgl', options);\n\n            if (!gl)\n            {\n                // fail, not able to get a context\n                throw new Error('This browser does not support WebGL. Try using the canvas renderer');\n            }\n        }\n\n        this.gl = gl as WebGL2RenderingContext;\n\n        this.initFromContext(this.gl);\n    }\n\n    /** Auto-populate the {@link GlContextSystem.extensions extensions}. */\n    protected getExtensions(): void\n    {\n        // time to set up default extensions that Pixi uses.\n        const { gl } = this;\n\n        const common = {\n            anisotropicFiltering: gl.getExtension('EXT_texture_filter_anisotropic'),\n            floatTextureLinear: gl.getExtension('OES_texture_float_linear'),\n\n            s3tc: gl.getExtension('WEBGL_compressed_texture_s3tc'),\n            s3tc_sRGB: gl.getExtension('WEBGL_compressed_texture_s3tc_srgb'), // eslint-disable-line camelcase\n            etc: gl.getExtension('WEBGL_compressed_texture_etc'),\n            etc1: gl.getExtension('WEBGL_compressed_texture_etc1'),\n            pvrtc: gl.getExtension('WEBGL_compressed_texture_pvrtc')\n                || gl.getExtension('WEBKIT_WEBGL_compressed_texture_pvrtc'),\n            atc: gl.getExtension('WEBGL_compressed_texture_atc'),\n            astc: gl.getExtension('WEBGL_compressed_texture_astc'),\n            bptc: gl.getExtension('EXT_texture_compression_bptc'),\n            rgtc: gl.getExtension('EXT_texture_compression_rgtc'),\n            loseContext: gl.getExtension('WEBGL_lose_context'),\n        };\n\n        if (this.webGLVersion === 1)\n        {\n            this.extensions = {\n                ...common,\n\n                drawBuffers: gl.getExtension('WEBGL_draw_buffers'),\n                depthTexture: gl.getExtension('WEBGL_depth_texture'),\n                vertexArrayObject: gl.getExtension('OES_vertex_array_object')\n                    || gl.getExtension('MOZ_OES_vertex_array_object')\n                    || gl.getExtension('WEBKIT_OES_vertex_array_object'),\n                uint32ElementIndex: gl.getExtension('OES_element_index_uint'),\n                // Floats and half-floats\n                floatTexture: gl.getExtension('OES_texture_float'),\n                floatTextureLinear: gl.getExtension('OES_texture_float_linear'),\n                textureHalfFloat: gl.getExtension('OES_texture_half_float'),\n                textureHalfFloatLinear: gl.getExtension('OES_texture_half_float_linear'),\n                vertexAttribDivisorANGLE: gl.getExtension('ANGLE_instanced_arrays'),\n                srgb: gl.getExtension('EXT_sRGB'),\n            };\n        }\n        else\n        {\n            this.extensions = {\n                ...common,\n                colorBufferFloat: gl.getExtension('EXT_color_buffer_float'),\n            };\n\n            const provokeExt = gl.getExtension('WEBGL_provoking_vertex');\n\n            if (provokeExt)\n            {\n                provokeExt.provokingVertexWEBGL(provokeExt.FIRST_VERTEX_CONVENTION_WEBGL);\n            }\n        }\n    }\n\n    /**\n     * Handles a lost webgl context\n     * @param {WebGLContextEvent} event - The context lost event.\n     */\n    protected handleContextLost(event: WebGLContextEvent): void\n    {\n        event.preventDefault();\n\n        // only restore if we purposefully nuked it\n        if (this._contextLossForced)\n        {\n            this._contextLossForced = false;\n            // Restore the context after this event has exited\n            setTimeout(() =>\n            {\n                if (this.gl.isContextLost())\n                {\n                    this.extensions.loseContext?.restoreContext();\n                }\n            }, 0);\n        }\n    }\n\n    /** Handles a restored webgl context. */\n    protected handleContextRestored(): void\n    {\n        this.getExtensions(); // restore extensions state\n        this._renderer.runners.contextChange.emit(this.gl);\n    }\n\n    public destroy(): void\n    {\n        const element = this._renderer.view.canvas;\n\n        this._renderer = null;\n\n        // remove listeners\n        (element as any).removeEventListener('webglcontextlost', this.handleContextLost);\n        element.removeEventListener('webglcontextrestored', this.handleContextRestored);\n\n        this.gl.useProgram(null);\n\n        this.extensions.loseContext?.loseContext();\n    }\n\n    /**\n     * this function can be called to force a webGL context loss\n     * this will release all resources on the GPU.\n     * Useful if you need to put Pixi to sleep, and save some GPU memory\n     *\n     * As soon as render is called - all resources will be created again.\n     */\n    public forceContextLoss(): void\n    {\n        this.extensions.loseContext?.loseContext();\n        this._contextLossForced = true;\n    }\n    /**\n     * Validate context.\n     * @param {WebGLRenderingContext} gl - Render context.\n     */\n    protected validateContext(gl: WebGL2RenderingContext): void\n    {\n        const attributes = gl.getContextAttributes();\n\n        // this is going to be fairly simple for now.. but at least we have room to grow!\n        if (attributes && !attributes.stencil)\n        {\n            // #if _DEBUG\n            warn('Provided WebGL context does not have a stencil buffer, masks may not render correctly');\n            // #endif\n        }\n\n        // support\n        const supports = this.supports;\n\n        const isWebGl2 = this.webGLVersion === 2;\n        const extensions = this.extensions;\n\n        supports.uint32Indices = isWebGl2 || !!extensions.uint32ElementIndex;\n        supports.uniformBufferObject = isWebGl2;\n        supports.vertexArrayObject = isWebGl2 || !!extensions.vertexArrayObject;\n        supports.srgbTextures = isWebGl2 || !!extensions.srgb;\n        supports.nonPowOf2wrapping = isWebGl2;\n        supports.nonPowOf2mipmaps = isWebGl2;\n        supports.msaa = isWebGl2;\n\n        if (!supports.uint32Indices)\n        {\n            // #if _DEBUG\n            warn('Provided WebGL context does not support 32 index buffer, large scenes may not render correctly');\n            // #endif\n        }\n    }\n}\n", "/**\n * Various GL texture/resources formats.\n * @category rendering\n * @advanced\n */\nexport enum GL_FORMATS\n{\n    RGBA = 6408,\n    RGB = 6407,\n    RG = 33319,\n    RED = 6403,\n    RGBA_INTEGER = 36249,\n    RGB_INTEGER = 36248,\n    RG_INTEGER = 33320,\n    RED_INTEGER = 36244,\n    ALPHA = 6406,\n    LUMINANCE = 6409,\n    LUMINANCE_ALPHA = 6410,\n    DEPTH_COMPONENT = 6402,\n    DEPTH_STENCIL = 34041,\n}\n\n/**\n * Various GL target types.\n * @category rendering\n * @advanced\n */\nexport enum GL_TARGETS\n{\n    TEXTURE_2D = 3553,\n    TEXTURE_CUBE_MAP = 34067,\n    TEXTURE_2D_ARRAY = 35866,\n    TEXTURE_CUBE_MAP_POSITIVE_X = 34069,\n    TEXTURE_CUBE_MAP_NEGATIVE_X = 34070,\n    TEXTURE_CUBE_MAP_POSITIVE_Y = 34071,\n    TEXTURE_CUBE_MAP_NEGATIVE_Y = 34072,\n    TEXTURE_CUBE_MAP_POSITIVE_Z = 34073,\n    TEXTURE_CUBE_MAP_NEGATIVE_Z = 34074,\n}\n\n/**\n * The wrap modes that are supported by pixi.\n *\n * The {@link WRAP_MODE} wrap mode affects the default wrapping mode of future operations.\n * It can be re-assigned to either CLAMP or REPEAT, depending upon suitability.\n * If the texture is non power of two then clamp will be used regardless as WebGL can\n * only use REPEAT if the texture is po2.\n *\n * This property only affects WebGL.\n * @category rendering\n * @advanced\n */\nexport enum GL_WRAP_MODES\n{\n    /**\n     * The textures uvs are clamped\n     * @default 33071\n     */\n    CLAMP = 33071,\n    /**\n     * The texture uvs tile and repeat\n     * @default 10497\n     */\n    REPEAT = 10497,\n    /**\n     * The texture uvs tile and repeat with mirroring\n     * @default 33648\n     */\n    MIRRORED_REPEAT = 33648,\n}\n\n/** @internal */\nexport enum GL_TYPES\n{\n    /**\n     * 8 bits per channel for gl.RGBA\n     * @default 5121\n     */\n    UNSIGNED_BYTE = 5121,\n    /** @default 5123 */\n    UNSIGNED_SHORT = 5123,\n    /**\n     * 5 red bits, 6 green bits, 5 blue bits.\n     * @default 33635\n     */\n    UNSIGNED_SHORT_5_6_5 = 33635,\n    /**\n     * 4 red bits, 4 green bits, 4 blue bits, 4 alpha bits.\n     * @default 32819\n     */\n    UNSIGNED_SHORT_4_4_4_4 = 32819,\n    /**\n     * 5 red bits, 5 green bits, 5 blue bits, 1 alpha bit.\n     * @default 32820\n     */\n    UNSIGNED_SHORT_5_5_5_1 = 32820,\n    /** @default 5125 */\n    UNSIGNED_INT = 5125,\n    /** @default 35899 */\n    UNSIGNED_INT_10F_11F_11F_REV = 35899,\n    /** @default 33640 */\n    UNSIGNED_INT_2_10_10_10_REV = 33640,\n    /** @default 34042 */\n    UNSIGNED_INT_24_8 = 34042,\n    /** @default 35902 */\n    UNSIGNED_INT_5_9_9_9_REV = 35902,\n    /** @default 5120 */\n    BYTE = 5120,\n    /** @default 5122 */\n    SHORT = 5122,\n    /** @default 5124 */\n    INT = 5124,\n    /** @default 5126 */\n    FLOAT = 5126,\n    /** @default 36269 */\n    FLOAT_32_UNSIGNED_INT_24_8_REV = 36269,\n    /** @default 36193 */\n    HALF_FLOAT = 36193,\n}\n\n", "import { GL_TYPES } from '../../texture/const';\n\nimport type { VertexFormat } from '../../../shared/geometry/const';\n\nconst infoMap = {\n    uint8x2: GL_TYPES.UNSIGNED_BYTE,\n    uint8x4: GL_TYPES.UNSIGNED_BYTE,\n    sint8x2: GL_TYPES.BYTE,\n    sint8x4: GL_TYPES.BYTE,\n    unorm8x2: GL_TYPES.UNSIGNED_BYTE,\n    unorm8x4: GL_TYPES.UNSIGNED_BYTE,\n    snorm8x2: GL_TYPES.BYTE,\n    snorm8x4: GL_TYPES.BYTE,\n    uint16x2: GL_TYPES.UNSIGNED_SHORT,\n    uint16x4: GL_TYPES.UNSIGNED_SHORT,\n    sint16x2: GL_TYPES.SHORT,\n    sint16x4: GL_TYPES.SHORT,\n    unorm16x2: GL_TYPES.UNSIGNED_SHORT,\n    unorm16x4: GL_TYPES.UNSIGNED_SHORT,\n    snorm16x2: GL_TYPES.SHORT,\n    snorm16x4: GL_TYPES.SHORT,\n    float16x2: GL_TYPES.HALF_FLOAT,\n    float16x4: GL_TYPES.HALF_FLOAT,\n    float32: GL_TYPES.FLOAT,\n    float32x2: GL_TYPES.FLOAT,\n    float32x3: GL_TYPES.FLOAT,\n    float32x4: GL_TYPES.FLOAT,\n    uint32: GL_TYPES.UNSIGNED_INT,\n    uint32x2: GL_TYPES.UNSIGNED_INT,\n    uint32x3: GL_TYPES.UNSIGNED_INT,\n    uint32x4: GL_TYPES.UNSIGNED_INT,\n    sint32: GL_TYPES.INT,\n    sint32x2: GL_TYPES.INT,\n    sint32x3: GL_TYPES.INT,\n    sint32x4: GL_TYPES.INT\n};\n\n/**\n * @param format\n * @internal\n */\nexport function getGlTypeFromFormat(format: VertexFormat): number\n{\n    return infoMap[format] ?? infoMap.float32;\n}\n", "import { ExtensionType } from '../../../../extensions/Extensions';\nimport { getAttributeInfoFromFormat } from '../../shared/geometry/utils/getAttributeInfoFromFormat';\nimport { ensureAttributes } from '../shader/program/ensureAttributes';\nimport { getGlTypeFromFormat } from './utils/getGlTypeFromFormat';\n\nimport type { Topology } from '../../shared/geometry/const';\nimport type { Geometry } from '../../shared/geometry/Geometry';\nimport type { System } from '../../shared/system/System';\nimport type { GlRenderingContext } from '../context/GlRenderingContext';\nimport type { GlProgram } from '../shader/GlProgram';\nimport type { WebGLRenderer } from '../WebGLRenderer';\n\nconst topologyToGlMap = {\n    'point-list': 0x0000,\n    'line-list': 0x0001,\n    'line-strip': 0x0003,\n    'triangle-list': 0x0004,\n    'triangle-strip': 0x0005\n};\n\n/**\n * System plugin to the renderer to manage geometry.\n * @category rendering\n * @advanced\n */\nexport class GlGeometrySystem implements System\n{\n    /** @ignore */\n    public static extension = {\n        type: [\n            ExtensionType.WebGLSystem,\n        ],\n        name: 'geometry',\n    } as const;\n\n    /**\n     * `true` if we has `*_vertex_array_object` extension.\n     * @readonly\n     */\n    public hasVao: boolean;\n\n    /**\n     * `true` if has `ANGLE_instanced_arrays` extension.\n     * @readonly\n     */\n    public hasInstance: boolean;\n\n    protected gl: GlRenderingContext;\n    protected _activeGeometry: Geometry;\n    protected _activeVao: WebGLVertexArrayObject;\n\n    protected _geometryVaoHash: Record<number, Record<string, WebGLVertexArrayObject>> = Object.create(null);\n\n    /** Renderer that owns this {@link GeometrySystem}. */\n    private _renderer: WebGLRenderer;\n\n    /** @param renderer - The renderer this System works for. */\n    constructor(renderer: WebGLRenderer)\n    {\n        this._renderer = renderer;\n        this._activeGeometry = null;\n        this._activeVao = null;\n\n        this.hasVao = true;\n        this.hasInstance = true;\n\n        this._renderer.renderableGC.addManagedHash(this, '_geometryVaoHash');\n    }\n\n    /** Sets up the renderer context and necessary buffers. */\n    protected contextChange(): void\n    {\n        const gl = this.gl = this._renderer.gl;\n\n        if (!this._renderer.context.supports.vertexArrayObject)\n        {\n            throw new Error('[PixiJS] Vertex Array Objects are not supported on this device');\n        }\n\n        const nativeVaoExtension = this._renderer.context.extensions.vertexArrayObject;\n\n        if (nativeVaoExtension)\n        {\n            gl.createVertexArray = (): WebGLVertexArrayObject =>\n                nativeVaoExtension.createVertexArrayOES();\n\n            gl.bindVertexArray = (vao): void =>\n                nativeVaoExtension.bindVertexArrayOES(vao);\n\n            gl.deleteVertexArray = (vao): void =>\n                nativeVaoExtension.deleteVertexArrayOES(vao);\n        }\n\n        const nativeInstancedExtension = this._renderer.context.extensions.vertexAttribDivisorANGLE;\n\n        if (nativeInstancedExtension)\n        {\n            gl.drawArraysInstanced = (a, b, c, d): void =>\n            {\n                nativeInstancedExtension.drawArraysInstancedANGLE(a, b, c, d);\n            };\n\n            gl.drawElementsInstanced = (a, b, c, d, e): void =>\n            {\n                nativeInstancedExtension.drawElementsInstancedANGLE(a, b, c, d, e);\n            };\n\n            gl.vertexAttribDivisor = (a, b): void =>\n                nativeInstancedExtension.vertexAttribDivisorANGLE(a, b);\n        }\n\n        this._activeGeometry = null;\n        this._activeVao = null;\n        this._geometryVaoHash = Object.create(null);\n    }\n\n    /**\n     * Binds geometry so that is can be drawn. Creating a Vao if required\n     * @param geometry - Instance of geometry to bind.\n     * @param program - Instance of program to use vao for.\n     */\n    public bind(geometry?: Geometry, program?: GlProgram): void\n    {\n        // shader ||= this.renderer.shader.shader;\n\n        const gl = this.gl;\n\n        this._activeGeometry = geometry;\n\n        const vao = this.getVao(geometry, program);\n\n        if (this._activeVao !== vao)\n        {\n            this._activeVao = vao;\n\n            gl.bindVertexArray(vao);\n        }\n\n        this.updateBuffers();\n    }\n\n    /** Reset and unbind any active VAO and geometry. */\n    public resetState(): void\n    {\n        this.unbind();\n    }\n\n    /** Update buffers of the currently bound geometry. */\n    public updateBuffers(): void\n    {\n        const geometry = this._activeGeometry;\n\n        const bufferSystem = this._renderer.buffer;\n\n        for (let i = 0; i < geometry.buffers.length; i++)\n        {\n            const buffer = geometry.buffers[i];\n\n            bufferSystem.updateBuffer(buffer);\n        }\n    }\n\n    /**\n     * Check compatibility between a geometry and a program\n     * @param geometry - Geometry instance.\n     * @param program - Program instance.\n     */\n    protected checkCompatibility(geometry: Geometry, program: GlProgram): void\n    {\n        // geometry must have at least all the attributes that the shader requires.\n        const geometryAttributes = geometry.attributes;\n        const shaderAttributes = program._attributeData;\n\n        for (const j in shaderAttributes)\n        {\n            if (!geometryAttributes[j])\n            {\n                throw new Error(`shader and geometry incompatible, geometry missing the \"${j}\" attribute`);\n            }\n        }\n    }\n\n    /**\n     * Takes a geometry and program and generates a unique signature for them.\n     * @param geometry - To get signature from.\n     * @param program - To test geometry against.\n     * @returns - Unique signature of the geometry and program\n     */\n    protected getSignature(geometry: Geometry, program: GlProgram): string\n    {\n        const attribs = geometry.attributes;\n        const shaderAttributes = program._attributeData;\n\n        const strings = ['g', geometry.uid];\n\n        for (const i in attribs)\n        {\n            if (shaderAttributes[i])\n            {\n                strings.push(i, shaderAttributes[i].location);\n            }\n        }\n\n        return strings.join('-');\n    }\n\n    protected getVao(geometry: Geometry, program: GlProgram): WebGLVertexArrayObject\n    {\n        return this._geometryVaoHash[geometry.uid]?.[program._key] || this.initGeometryVao(geometry, program);\n    }\n\n    /**\n     * Creates or gets Vao with the same structure as the geometry and stores it on the geometry.\n     * If vao is created, it is bound automatically. We use a shader to infer what and how to set up the\n     * attribute locations.\n     * @param geometry - Instance of geometry to to generate Vao for.\n     * @param program\n     * @param _incRefCount - Increment refCount of all geometry buffers.\n     */\n    protected initGeometryVao(geometry: Geometry, program: GlProgram, _incRefCount = true): WebGLVertexArrayObject\n    {\n        const gl = this._renderer.gl;\n        // const CONTEXT_UID = this.CONTEXT_UID;\n        const bufferSystem = this._renderer.buffer;\n\n        this._renderer.shader._getProgramData(program);\n\n        this.checkCompatibility(geometry, program);\n\n        const signature = this.getSignature(geometry, program);\n\n        if (!this._geometryVaoHash[geometry.uid])\n        {\n            this._geometryVaoHash[geometry.uid] = Object.create(null);\n\n            geometry.on('destroy', this.onGeometryDestroy, this);\n        }\n\n        const vaoObjectHash = this._geometryVaoHash[geometry.uid];\n\n        let vao = vaoObjectHash[signature];\n\n        if (vao)\n        {\n            // this will give us easy access to the vao\n            vaoObjectHash[program._key] = vao;\n\n            return vao;\n        }\n\n        ensureAttributes(geometry, program._attributeData);\n\n        const buffers = geometry.buffers;\n\n        // @TODO: We don't know if VAO is supported.\n        vao = gl.createVertexArray();\n\n        gl.bindVertexArray(vao);\n\n        // first update - and create the buffers!\n        // only create a gl buffer if it actually gets\n        for (let i = 0; i < buffers.length; i++)\n        {\n            const buffer = buffers[i];\n\n            bufferSystem.bind(buffer);\n        }\n\n        // TODO - maybe make this a data object?\n        // lets wait to see if we need to first!\n\n        this.activateVao(geometry, program);\n\n        // add it to the cache!\n        vaoObjectHash[program._key] = vao;\n        vaoObjectHash[signature] = vao;\n\n        gl.bindVertexArray(null);\n\n        return vao;\n    }\n\n    /**\n     * Disposes geometry.\n     * @param geometry - Geometry with buffers. Only VAO will be disposed\n     * @param [contextLost=false] - If context was lost, we suppress deleteVertexArray\n     */\n    protected onGeometryDestroy(geometry: Geometry, contextLost?: boolean): void\n    {\n        const vaoObjectHash = this._geometryVaoHash[geometry.uid];\n\n        const gl = this.gl;\n\n        if (vaoObjectHash)\n        {\n            if (contextLost)\n            {\n                for (const i in vaoObjectHash)\n                {\n                    if (this._activeVao !== vaoObjectHash[i])\n                    {\n                        this.unbind();\n                    }\n\n                    gl.deleteVertexArray(vaoObjectHash[i]);\n                }\n            }\n\n            this._geometryVaoHash[geometry.uid] = null;\n        }\n    }\n\n    /**\n     * Dispose all WebGL resources of all managed geometries.\n     * @param [contextLost=false] - If context was lost, we suppress `gl.delete` calls\n     */\n    public destroyAll(contextLost = false): void\n    {\n        const gl = this.gl;\n\n        for (const i in this._geometryVaoHash)\n        {\n            if (contextLost)\n            {\n                for (const j in this._geometryVaoHash[i])\n                {\n                    const vaoObjectHash = this._geometryVaoHash[i];\n\n                    if (this._activeVao !== vaoObjectHash)\n                    {\n                        this.unbind();\n                    }\n\n                    gl.deleteVertexArray(vaoObjectHash[j]);\n                }\n            }\n\n            this._geometryVaoHash[i] = null;\n        }\n    }\n\n    /**\n     * Activate vertex array object.\n     * @param geometry - Geometry instance.\n     * @param program - Shader program instance.\n     */\n    protected activateVao(geometry: Geometry, program: GlProgram): void\n    {\n        const gl = this._renderer.gl;\n\n        const bufferSystem = this._renderer.buffer;\n        const attributes = geometry.attributes;\n\n        if (geometry.indexBuffer)\n        {\n            // first update the index buffer if we have one..\n            bufferSystem.bind(geometry.indexBuffer);\n        }\n\n        let lastBuffer = null;\n\n        // add a new one!\n        for (const j in attributes)\n        {\n            const attribute = attributes[j];\n            const buffer = attribute.buffer;\n            const glBuffer = bufferSystem.getGlBuffer(buffer);\n            const programAttrib = program._attributeData[j];\n\n            if (programAttrib)\n            {\n                if (lastBuffer !== glBuffer)\n                {\n                    bufferSystem.bind(buffer);\n\n                    lastBuffer = glBuffer;\n                }\n\n                const location = programAttrib.location;\n\n                // TODO introduce state again\n                // we can optimise this for older devices that have no VAOs\n                gl.enableVertexAttribArray(location);\n\n                const attributeInfo = getAttributeInfoFromFormat(attribute.format);\n\n                const type = getGlTypeFromFormat(attribute.format);\n\n                if (programAttrib.format?.substring(1, 4) === 'int')\n                {\n                    gl.vertexAttribIPointer(location,\n                        attributeInfo.size,\n                        type,\n                        attribute.stride,\n                        attribute.offset);\n                }\n                else\n                {\n                    gl.vertexAttribPointer(location,\n                        attributeInfo.size,\n                        type,\n                        attributeInfo.normalised,\n                        attribute.stride,\n                        attribute.offset);\n                }\n\n                if (attribute.instance)\n                {\n                    // TODO calculate instance count based of this...\n                    if (this.hasInstance)\n                    {\n                        // Can't use truthiness check to determine if divisor is set,\n                        // since 0 is a valid value for divisor\n                        const divisor = attribute.divisor ?? 1;\n\n                        gl.vertexAttribDivisor(location, divisor);\n                    }\n                    else\n                    {\n                        throw new Error('geometry error, GPU Instancing is not supported on this device');\n                    }\n                }\n            }\n        }\n    }\n\n    /**\n     * Draws the currently bound geometry.\n     * @param topology - The type primitive to render.\n     * @param size - The number of elements to be rendered. If not specified, all vertices after the\n     *  starting vertex will be drawn.\n     * @param start - The starting vertex in the geometry to start drawing from. If not specified,\n     *  drawing will start from the first vertex.\n     * @param instanceCount - The number of instances of the set of elements to execute. If not specified,\n     *  all instances will be drawn.\n     */\n    public draw(topology?: Topology, size?: number, start?: number, instanceCount?: number): this\n    {\n        const { gl } = this._renderer;\n        const geometry = this._activeGeometry;\n\n        const glTopology = topologyToGlMap[topology || geometry.topology];\n\n        instanceCount ??= geometry.instanceCount;\n\n        if (geometry.indexBuffer)\n        {\n            const byteSize = geometry.indexBuffer.data.BYTES_PER_ELEMENT;\n            const glType = byteSize === 2 ? gl.UNSIGNED_SHORT : gl.UNSIGNED_INT;\n\n            if (instanceCount > 1)\n            {\n                /* eslint-disable max-len */\n                gl.drawElementsInstanced(glTopology, size || geometry.indexBuffer.data.length, glType, (start || 0) * byteSize, instanceCount);\n                /* eslint-enable max-len */\n            }\n            else\n            {\n                gl.drawElements(glTopology, size || geometry.indexBuffer.data.length, glType, (start || 0) * byteSize);\n            }\n        }\n        else if (instanceCount > 1)\n        {\n            // TODO need a better way to calculate size..\n            gl.drawArraysInstanced(glTopology, start || 0, size || geometry.getSize(), instanceCount);\n        }\n        else\n        {\n            gl.drawArrays(glTopology, start || 0, size || geometry.getSize());\n        }\n\n        return this;\n    }\n\n    /** Unbind/reset everything. */\n    protected unbind(): void\n    {\n        this.gl.bindVertexArray(null);\n        this._activeVao = null;\n        this._activeGeometry = null;\n    }\n\n    public destroy(): void\n    {\n        this._renderer = null;\n        this.gl = null;\n        this._activeVao = null;\n        this._activeGeometry = null;\n    }\n}\n", "import { ExtensionType } from '../../../extensions/Extensions';\nimport { warn } from '../../../utils/logging/warn';\nimport { Geometry } from '../shared/geometry/Geometry';\nimport { Shader } from '../shared/shader/Shader';\nimport { State } from '../shared/state/State';\nimport { TextureSource } from '../shared/texture/sources/TextureSource';\nimport { Texture } from '../shared/texture/Texture';\nimport { GlProgram } from './shader/GlProgram';\n\nimport type { RenderOptions } from '../shared/system/AbstractRenderer';\nimport type { System } from '../shared/system/System';\nimport type { WebGLRenderer } from './WebGLRenderer';\n\nconst bigTriangleGeometry = new Geometry({\n    attributes: {\n        aPosition: [\n            -1.0, -1.0, // Bottom left corner\n            3.0, -1.0, // Bottom right corner, extending beyond right edge\n            -1.0, 3.0 // Top left corner, extending beyond top edge\n        ],\n    },\n});\n\n/**\n * The options for the back buffer system.\n * @category rendering\n * @property {boolean} [useBackBuffer=false] - if true will use the back buffer where required\n * @property {boolean} [antialias=false] - if true will ensure the texture is antialiased\n * @advanced\n */\nexport interface GlBackBufferOptions\n{\n    /**\n     * if true will use the back buffer where required\n     * @default false\n     */\n    useBackBuffer?: boolean;\n    /** if true will ensure the texture is antialiased */\n    antialias?: boolean;\n}\n\n/**\n * For blend modes you need to know what pixels you are actually drawing to. For this to be possible in WebGL\n * we need to render to a texture and then present that texture to the screen. This system manages that process.\n *\n * As the main scene is rendered to a texture, it means we can sample it and copy its pixels,\n * something not possible on the main canvas.\n *\n * If antialiasing is set to to true and useBackBuffer is set to true, then the back buffer will be antialiased.\n * and the main gl context will not.\n *\n * You only need to activate this back buffer if you are using a blend mode that requires it.\n *\n * to activate is simple, you pass `useBackBuffer:true` to your render options\n * @category rendering\n * @advanced\n */\nexport class GlBackBufferSystem implements System<GlBackBufferOptions>\n{\n    /** @ignore */\n    public static extension = {\n        type: [\n            ExtensionType.WebGLSystem,\n        ],\n        name: 'backBuffer',\n        priority: 1\n    } as const;\n\n    /** default options for the back buffer system */\n    public static defaultOptions: GlBackBufferOptions = {\n        /** if true will use the back buffer where required */\n        useBackBuffer: false,\n    };\n\n    /** if true, the back buffer is used */\n    public useBackBuffer = false;\n\n    private _backBufferTexture: Texture;\n    private readonly _renderer: WebGLRenderer;\n    private _targetTexture: TextureSource;\n    private _useBackBufferThisRender = false;\n    private _antialias: boolean;\n    private _state: State;\n    private _bigTriangleShader: Shader;\n\n    constructor(renderer: WebGLRenderer)\n    {\n        this._renderer = renderer;\n    }\n\n    public init(options: GlBackBufferOptions = {})\n    {\n        const { useBackBuffer, antialias } = { ...GlBackBufferSystem.defaultOptions, ...options };\n\n        this.useBackBuffer = useBackBuffer;\n\n        this._antialias = antialias;\n\n        if (!this._renderer.context.supports.msaa)\n        {\n            warn('antialiasing, is not supported on when using the back buffer');\n\n            this._antialias = false;\n        }\n\n        this._state = State.for2d();\n\n        const bigTriangleProgram = new GlProgram({\n            vertex: `\n                attribute vec2 aPosition;\n                out vec2 vUv;\n\n                void main() {\n                    gl_Position = vec4(aPosition, 0.0, 1.0);\n\n                    vUv = (aPosition + 1.0) / 2.0;\n\n                    // flip dem UVs\n                    vUv.y = 1.0 - vUv.y;\n                }`,\n            fragment: `\n                in vec2 vUv;\n                out vec4 finalColor;\n\n                uniform sampler2D uTexture;\n\n                void main() {\n                    finalColor = texture(uTexture, vUv);\n                }`,\n            name: 'big-triangle',\n        });\n\n        this._bigTriangleShader = new Shader({\n            glProgram: bigTriangleProgram,\n            resources: {\n                uTexture: Texture.WHITE.source,\n            },\n        });\n    }\n\n    /**\n     * This is called before the RenderTargetSystem is started. This is where\n     * we replace the target with the back buffer if required.\n     * @param options - The options for this render.\n     */\n    protected renderStart(options: RenderOptions)\n    {\n        const renderTarget = this._renderer.renderTarget.getRenderTarget(options.target);\n\n        this._useBackBufferThisRender = this.useBackBuffer && !!renderTarget.isRoot;\n\n        if (this._useBackBufferThisRender)\n        {\n            const renderTarget = this._renderer.renderTarget.getRenderTarget(options.target);\n\n            this._targetTexture = renderTarget.colorTexture;\n\n            options.target = this._getBackBufferTexture(renderTarget.colorTexture);\n        }\n    }\n\n    protected renderEnd()\n    {\n        this._presentBackBuffer();\n    }\n\n    private _presentBackBuffer()\n    {\n        const renderer = this._renderer;\n\n        renderer.renderTarget.finishRenderPass();\n\n        if (!this._useBackBufferThisRender) return;\n\n        renderer.renderTarget.bind(this._targetTexture, false);\n\n        this._bigTriangleShader.resources.uTexture = this._backBufferTexture.source;\n\n        renderer.encoder.draw({\n            geometry: bigTriangleGeometry,\n            shader: this._bigTriangleShader,\n            state: this._state,\n        });\n    }\n\n    private _getBackBufferTexture(targetSourceTexture: TextureSource)\n    {\n        this._backBufferTexture = this._backBufferTexture || new Texture({\n            source: new TextureSource({\n                width: targetSourceTexture.width,\n                height: targetSourceTexture.height,\n                resolution: targetSourceTexture._resolution,\n                antialias: this._antialias,\n            }),\n        });\n\n        // this will not resize if its the same size already! No extra check required\n        this._backBufferTexture.source.resize(\n            targetSourceTexture.width,\n            targetSourceTexture.height,\n            targetSourceTexture._resolution,\n        );\n\n        return this._backBufferTexture;\n    }\n\n    /** destroys the back buffer */\n    public destroy()\n    {\n        if (this._backBufferTexture)\n        {\n            this._backBufferTexture.destroy();\n            this._backBufferTexture = null;\n        }\n    }\n}\n", "import { ExtensionType } from '../../../extensions/Extensions';\n\nimport type { System } from '../shared/system/System';\nimport type { WebGLRenderer } from './WebGLRenderer';\n\n/**\n * The system that handles color masking for the WebGL.\n * @category rendering\n * @advanced\n */\nexport class GlColorMaskSystem implements System\n{\n    /** @ignore */\n    public static extension = {\n        type: [\n            ExtensionType.WebGLSystem,\n        ],\n        name: 'colorMask',\n    } as const;\n\n    private readonly _renderer: WebGLRenderer;\n    private _colorMaskCache = 0b1111;\n\n    constructor(renderer: WebGLRenderer)\n    {\n        this._renderer = renderer;\n    }\n\n    public setMask(colorMask: number)\n    {\n        if (this._colorMaskCache === colorMask) return;\n        this._colorMaskCache = colorMask;\n\n        this._renderer.gl.colorMask(\n            !!(colorMask & 0b1000),\n            !!(colorMask & 0b0100),\n            !!(colorMask & 0b0010),\n            !!(colorMask & 0b0001)\n        );\n    }\n\n    public destroy?: () => void;\n}\n", "import { ExtensionType } from '../../../extensions/Extensions';\n\nimport type { Topology } from '../shared/geometry/const';\nimport type { Geometry } from '../shared/geometry/Geometry';\nimport type { Shader } from '../shared/shader/Shader';\nimport type { State } from '../shared/state/State';\nimport type { System } from '../shared/system/System';\nimport type { WebGLRenderer } from './WebGLRenderer';\n\n/**\n * The system that handles encoding commands for the WebGL.\n * @category rendering\n * @advanced\n */\nexport class GlEncoderSystem implements System\n{\n    /** @ignore */\n    public static extension = {\n        type: [\n            ExtensionType.WebGLSystem,\n        ],\n        name: 'encoder',\n    } as const;\n\n    public readonly commandFinished = Promise.resolve();\n    private readonly _renderer: WebGLRenderer;\n\n    constructor(renderer: WebGLRenderer)\n    {\n        this._renderer = renderer;\n    }\n\n    public setGeometry(geometry: Geometry, shader?: Shader)\n    {\n        this._renderer.geometry.bind(geometry, shader.glProgram);\n    }\n\n    public finishRenderPass()\n    {\n        // noop\n    }\n\n    public draw(options: {\n        geometry: Geometry,\n        shader: Shader,\n        state?: State,\n        topology?: Topology,\n        size?: number,\n        start?: number,\n        instanceCount?: number\n        skipSync?: boolean,\n    })\n    {\n        const renderer = this._renderer;\n        const { geometry, shader, state, skipSync, topology: type, size, start, instanceCount } = options;\n\n        renderer.shader.bind(shader, skipSync);\n\n        renderer.geometry.bind(geometry, renderer.shader._activeProgram);\n\n        if (state)\n        {\n            renderer.state.set(state);\n        }\n\n        renderer.geometry.draw(type, size, start, instanceCount ?? geometry.instanceCount);\n    }\n\n    public destroy()\n    {\n        (this._renderer as null) = null;\n    }\n}\n", "import { ExtensionType } from '../../../extensions/Extensions';\nimport { checkMaxIfStatementsInShader } from '../../batcher/gl/utils/checkMaxIfStatementsInShader';\nimport { type System } from '../shared/system/System';\n\nimport type { WebGLRenderer } from './WebGLRenderer';\n/**\n * The GpuLimitsSystem provides information about the capabilities and limitations of the underlying GPU.\n * These limits, such as the maximum number of textures that can be used in a shader\n * (`maxTextures`) or the maximum number of textures that can be batched together (`maxBatchableTextures`),\n * are determined by the specific graphics hardware and driver.\n *\n * The values for these limits are not available immediately upon instantiation of the class.\n * They are populated when the GL rendering context is successfully initialized and ready,\n * which occurs after the `renderer.init()` method has completed.\n * Attempting to access these properties before the context is ready will result in undefined or default values.\n *\n * This system allows the renderer to adapt its behavior and resource allocation strategies\n * to stay within the supported boundaries of the GPU, ensuring optimal performance and stability.\n * @example\n * ```ts\n * const renderer = new WebGlRenderer();\n * await renderer.init();\n *\n * console.log(renderer.limits.maxTextures);\n * ```\n * @category rendering\n * @advanced\n */\nexport class GlLimitsSystem implements System\n{\n    /** @ignore */\n    public static extension = {\n        type: [\n            ExtensionType.WebGLSystem,\n        ],\n        name: 'limits',\n    } as const;\n\n    /** The maximum number of textures that can be used by a shader */\n    public maxTextures: number;\n    /** The maximum number of batchable textures */\n    public maxBatchableTextures: number;\n\n    /** The maximum number of uniform bindings */\n    public maxUniformBindings: number;\n\n    private readonly _renderer: WebGLRenderer;\n\n    constructor(renderer: WebGLRenderer)\n    {\n        this._renderer = renderer;\n    }\n\n    public contextChange(): void\n    {\n        const gl = this._renderer.gl;\n\n        // step 1: first check max textures the GPU can handle.\n        this.maxTextures = gl.getParameter(gl.MAX_TEXTURE_IMAGE_UNITS);\n\n        // step 2: check the maximum number of if statements the shader can have too..\n        this.maxBatchableTextures = checkMaxIfStatementsInShader(this.maxTextures, gl);\n\n        this.maxUniformBindings = gl.getParameter(gl.MAX_UNIFORM_BUFFER_BINDINGS);\n    }\n\n    public destroy(): void\n    {\n        // boom!\n    }\n}\n", "import { ExtensionType } from '../../../extensions/Extensions';\nimport { GpuStencilModesToPixi } from '../gpu/state/GpuStencilModesToPixi';\nimport { STENCIL_MODES } from '../shared/state/const';\n\nimport type { RenderTarget } from '../shared/renderTarget/RenderTarget';\nimport type { System } from '../shared/system/System';\nimport type { WebGLRenderer } from './WebGLRenderer';\n\n/**\n * This manages the stencil buffer. Used primarily for masking\n * @category rendering\n * @advanced\n */\nexport class GlStencilSystem implements System\n{\n    /** @ignore */\n    public static extension = {\n        type: [\n            ExtensionType.WebGLSystem,\n        ],\n        name: 'stencil',\n    } as const;\n\n    private _gl: WebGLRenderingContext;\n\n    private readonly _stencilCache = {\n        enabled: false,\n        stencilReference: 0,\n        stencilMode: STENCIL_MODES.NONE,\n    };\n\n    private _renderTargetStencilState: Record<number, {\n        stencilMode: STENCIL_MODES;\n        stencilReference: number;\n    }> = Object.create(null);\n\n    private _stencilOpsMapping: {\n        keep: number;\n        zero: number;\n        replace: number;\n        invert: number;\n        'increment-clamp': number;\n        'decrement-clamp': number;\n        'increment-wrap': number;\n        'decrement-wrap': number;\n    };\n\n    private _comparisonFuncMapping: {\n        always: number;\n        never: number;\n        equal: number;\n        'not-equal': number;\n        less: number;\n        'less-equal': number;\n        greater: number;\n        'greater-equal': number;\n    };\n\n    private _activeRenderTarget: RenderTarget;\n\n    constructor(renderer: WebGLRenderer)\n    {\n        renderer.renderTarget.onRenderTargetChange.add(this);\n    }\n\n    protected contextChange(gl: WebGLRenderingContext)\n    {\n        // TODO - this could be declared in a gl const\n        // we know the numbers don't tend to change!\n        this._gl = gl;\n\n        this._comparisonFuncMapping = {\n            always: gl.ALWAYS,\n            never: gl.NEVER,\n            equal: gl.EQUAL,\n            'not-equal': gl.NOTEQUAL,\n            less: gl.LESS,\n            'less-equal': gl.LEQUAL,\n            greater: gl.GREATER,\n            'greater-equal': gl.GEQUAL,\n        };\n\n        this._stencilOpsMapping = {\n            keep: gl.KEEP,\n            zero: gl.ZERO,\n            replace: gl.REPLACE,\n            invert: gl.INVERT,\n            'increment-clamp': gl.INCR,\n            'decrement-clamp': gl.DECR,\n            'increment-wrap': gl.INCR_WRAP,\n            'decrement-wrap': gl.DECR_WRAP,\n        };\n\n        this.resetState();\n    }\n\n    protected onRenderTargetChange(renderTarget: RenderTarget)\n    {\n        if (this._activeRenderTarget === renderTarget) return;\n\n        this._activeRenderTarget = renderTarget;\n\n        let stencilState = this._renderTargetStencilState[renderTarget.uid];\n\n        if (!stencilState)\n        {\n            stencilState = this._renderTargetStencilState[renderTarget.uid] = {\n                stencilMode: STENCIL_MODES.DISABLED,\n                stencilReference: 0,\n            };\n        }\n\n        // restore the current render targets stencil state..\n        this.setStencilMode(stencilState.stencilMode, stencilState.stencilReference);\n    }\n\n    public resetState()\n    {\n        // reset stencil cache\n        this._stencilCache.enabled = false;\n        this._stencilCache.stencilMode = STENCIL_MODES.NONE;\n        this._stencilCache.stencilReference = 0;\n    }\n\n    public setStencilMode(stencilMode: STENCIL_MODES, stencilReference: number)\n    {\n        const stencilState = this._renderTargetStencilState[this._activeRenderTarget.uid];\n\n        const gl = this._gl;\n        const mode = GpuStencilModesToPixi[stencilMode];\n\n        const _stencilCache = this._stencilCache;\n\n        // store the stencil state for restoration later, if a render target changes\n        stencilState.stencilMode = stencilMode;\n        stencilState.stencilReference = stencilReference;\n\n        if (stencilMode === STENCIL_MODES.DISABLED)\n        {\n            if (this._stencilCache.enabled)\n            {\n                this._stencilCache.enabled = false;\n\n                gl.disable(gl.STENCIL_TEST);\n            }\n\n            return;\n        }\n\n        if (!this._stencilCache.enabled)\n        {\n            this._stencilCache.enabled = true;\n            gl.enable(gl.STENCIL_TEST);\n        }\n\n        if (stencilMode !== _stencilCache.stencilMode || _stencilCache.stencilReference !== stencilReference)\n        {\n            _stencilCache.stencilMode = stencilMode;\n            _stencilCache.stencilReference = stencilReference;\n\n            // this is pretty simple mapping.\n            // will work for pixi's simple mask cases.\n            // although a true mapping of the GPU state to webGL state should be done\n            gl.stencilFunc(this._comparisonFuncMapping[mode.stencilBack.compare], stencilReference, 0xFF);\n            gl.stencilOp(gl.KEEP, gl.KEEP, this._stencilOpsMapping[mode.stencilBack.passOp]);\n        }\n    }\n\n    public destroy?: () => void;\n}\n", "import type { UboElement, UboLayout, UniformData } from '../../../shared/shader/types';\n\n/** @internal */\nexport const WGSL_TO_STD40_SIZE: Record<string, number> = {\n    f32: 4,\n    i32: 4,\n    'vec2<f32>': 8,\n    'vec3<f32>': 12,\n    'vec4<f32>': 16,\n\n    'vec2<i32>': 8,\n    'vec3<i32>': 12,\n    'vec4<i32>': 16,\n\n    'mat2x2<f32>': 16 * 2,\n    'mat3x3<f32>': 16 * 3,\n    'mat4x4<f32>': 16 * 4,\n\n    // TODO - not essential for now but support these in the future\n    // int:      4,\n    // ivec2:    8,\n    // ivec3:    12,\n    // ivec4:    16,\n\n    // uint:     4,\n    // uvec2:    8,\n    // uvec3:    12,\n    // uvec4:    16,\n\n    // bool:     4,\n    // bvec2:    8,\n    // bvec3:    12,\n    // bvec4:    16,\n\n    // mat2:     16 * 2,\n    // mat3:     16 * 3,\n    // mat4:     16 * 4,\n};\n\n/**\n * @param uniformData\n * @internal\n */\nexport function createUboElementsSTD40(uniformData: UniformData[]): UboLayout\n{\n    const uboElements: UboElement[] = uniformData.map((data: UniformData) =>\n        ({\n            data,\n            offset: 0,\n            size: 0,\n        }));\n\n    const chunkSize = 16;\n\n    let size = 0;\n    let offset = 0;\n\n    for (let i = 0; i < uboElements.length; i++)\n    {\n        const uboElement = uboElements[i];\n\n        size = WGSL_TO_STD40_SIZE[uboElement.data.type];\n\n        if (!size)\n        {\n            throw new Error(`Unknown type ${uboElement.data.type}`);\n        }\n\n        if (uboElement.data.size > 1)\n        {\n            size = Math.max(size, chunkSize) * uboElement.data.size;\n        }\n\n        const boundary = size === 12 ? 16 : size;\n\n        uboElement.size = size;\n\n        const curOffset = offset % chunkSize;\n\n        if (curOffset > 0 && chunkSize - curOffset < boundary)\n        {\n            offset += (chunkSize - curOffset) % 16;\n        }\n        else\n        {\n            offset += (size - (curOffset % size)) % size;\n        }\n\n        uboElement.offset = offset;\n        offset += size;\n    }\n\n    offset = Math.ceil(offset / 16) * 16;\n\n    return { uboElements, size: offset };\n}\n\n", "import { WGSL_TO_STD40_SIZE } from './createUboElementsSTD40';\n\nimport type { UboElement } from '../../../shared/shader/types';\n\n/**\n * This generates a function that will sync an array to the uniform buffer\n * following the std140 layout\n * @param uboElement - the element to generate the array sync for\n * @param offsetToAdd - the offset to append at the start of the code\n * @returns - the generated code\n * @internal\n */\nexport function generateArraySyncSTD40(uboElement: UboElement, offsetToAdd: number): string\n{\n    const rowSize = Math.max(WGSL_TO_STD40_SIZE[uboElement.data.type] / 16, 1);\n    const elementSize = (uboElement.data.value as Array<number>).length / uboElement.data.size;// size / rowSize;\n\n    const remainder = (4 - (elementSize % 4)) % 4;\n    const data = uboElement.data.type.indexOf('i32') >= 0 ? 'dataInt32' : 'data';\n\n    return `\n        v = uv.${uboElement.data.name};\n        offset += ${offsetToAdd};\n\n        arrayOffset = offset;\n\n        t = 0;\n\n        for(var i=0; i < ${uboElement.data.size * rowSize}; i++)\n        {\n            for(var j = 0; j < ${elementSize}; j++)\n            {\n                ${data}[arrayOffset++] = v[t++];\n            }\n            ${remainder !== 0 ? `arrayOffset += ${remainder};` : ''}\n        }\n    `;\n}\n", "import { createUboSyncFunction } from '../../../shared/shader/utils/createUboSyncFunction';\nimport { uboSyncFunctionsSTD40 } from '../../../shared/shader/utils/uboSyncFunctions';\nimport { generateArraySyncSTD40 } from './generateArraySyncSTD40';\n\nimport type { UboElement, UniformsSyncCallback } from '../../../shared/shader/types';\n\n/**\n * @param uboElements\n * @internal\n */\nexport function createUboSyncFunctionSTD40(\n    uboElements: UboElement[],\n): UniformsSyncCallback\n{\n    return createUboSyncFunction(\n        uboElements,\n        'uboStd40',\n        generateArraySyncSTD40,\n        uboSyncFunctionsSTD40,\n    );\n}\n", "import { ExtensionType } from '../../../extensions/Extensions';\nimport { UboSystem } from '../shared/shader/UboSystem';\nimport { createUboElementsSTD40 } from './shader/utils/createUboElementsSTD40';\nimport { createUboSyncFunctionSTD40 } from './shader/utils/createUboSyncSTD40';\n\n/**\n * System plugin to the renderer to manage uniform buffers. But with an WGSL adaptor.\n * @category rendering\n * @advanced\n */\nexport class GlUboSystem extends UboSystem\n{\n    /** @ignore */\n    public static extension = {\n        type: [ExtensionType.WebGLSystem],\n        name: 'ubo',\n    } as const;\n\n    constructor()\n    {\n        super({\n            createUboElements: createUboElementsSTD40,\n            generateUboSync: createUboSyncFunctionSTD40,\n        });\n    }\n}\n", "/**\n * Represents a render target.\n * @category rendering\n * @ignore\n */\nexport class GlRenderTarget\n{\n    public width = -1;\n    public height = -1;\n    public msaa = false;\n    public framebuffer: WebGLFramebuffer;\n    public resolveTargetFramebuffer: WebGLFramebuffer;\n    public msaaRenderBuffer: WebGLRenderbuffer[] = [];\n    public depthStencilRenderBuffer: WebGLRenderbuffer;\n}\n", "import { Rectangle } from '../../../../maths/shapes/Rectangle';\nimport { warn } from '../../../../utils/logging/warn';\nimport { CanvasSource } from '../../shared/texture/sources/CanvasSource';\nimport { CLEAR } from '../const';\nimport { GlRenderTarget } from '../GlRenderTarget';\n\nimport type { RgbaArray } from '../../../../color/Color';\nimport type { RenderTarget } from '../../shared/renderTarget/RenderTarget';\nimport type { RenderTargetAdaptor, RenderTargetSystem } from '../../shared/renderTarget/RenderTargetSystem';\nimport type { Texture } from '../../shared/texture/Texture';\nimport type { CLEAR_OR_BOOL } from '../const';\nimport type { WebGLRenderer } from '../WebGLRenderer';\n\n/**\n * The WebGL adaptor for the render target system. Allows the Render Target System to be used with the WebGL renderer\n * @category rendering\n * @ignore\n */\nexport class GlRenderTargetAdaptor implements RenderTargetAdaptor<GlRenderTarget>\n{\n    private _renderTargetSystem: RenderTargetSystem<GlRenderTarget>;\n    private _renderer: WebGLRenderer<HTMLCanvasElement>;\n    private _clearColorCache: RgbaArray = [0, 0, 0, 0];\n    private _viewPortCache: Rectangle = new Rectangle();\n\n    public init(renderer: WebGLRenderer, renderTargetSystem: RenderTargetSystem<GlRenderTarget>): void\n    {\n        this._renderer = renderer;\n        this._renderTargetSystem = renderTargetSystem;\n\n        renderer.runners.contextChange.add(this);\n    }\n\n    public contextChange(): void\n    {\n        this._clearColorCache = [0, 0, 0, 0];\n        this._viewPortCache = new Rectangle();\n    }\n\n    public copyToTexture(\n        sourceRenderSurfaceTexture: RenderTarget,\n        destinationTexture: Texture,\n        originSrc: { x: number; y: number; },\n        size: { width: number; height: number; },\n        originDest: { x: number; y: number; },\n    )\n    {\n        const renderTargetSystem = this._renderTargetSystem;\n\n        const renderer = this._renderer;\n        const glRenderTarget = renderTargetSystem.getGpuRenderTarget(sourceRenderSurfaceTexture);\n        const gl = renderer.gl;\n\n        this.finishRenderPass(sourceRenderSurfaceTexture);\n\n        gl.bindFramebuffer(gl.FRAMEBUFFER, glRenderTarget.resolveTargetFramebuffer);\n\n        renderer.texture.bind(destinationTexture, 0);\n\n        gl.copyTexSubImage2D(gl.TEXTURE_2D, 0,\n            originDest.x, originDest.y,\n            originSrc.x,\n            originSrc.y,\n            size.width,\n            size.height\n        );\n\n        return destinationTexture;\n    }\n\n    public startRenderPass(\n        renderTarget: RenderTarget,\n        clear: CLEAR_OR_BOOL = true,\n        clearColor?: RgbaArray,\n        viewport?: Rectangle\n    )\n    {\n        const renderTargetSystem = this._renderTargetSystem;\n\n        const source = renderTarget.colorTexture;\n        const gpuRenderTarget = renderTargetSystem.getGpuRenderTarget(renderTarget);\n\n        let viewPortY = viewport.y;\n\n        if (renderTarget.isRoot)\n        {\n            // /TODO this is the same logic?\n            viewPortY = source.pixelHeight - viewport.height;\n        }\n\n        // unbind the current render texture..\n        renderTarget.colorTextures.forEach((texture) =>\n        {\n            this._renderer.texture.unbind(texture);\n        });\n\n        const gl = this._renderer.gl;\n\n        gl.bindFramebuffer(gl.FRAMEBUFFER, gpuRenderTarget.framebuffer);\n\n        const viewPortCache = this._viewPortCache;\n\n        if (viewPortCache.x !== viewport.x\n            || viewPortCache.y !== viewPortY\n            || viewPortCache.width !== viewport.width\n            || viewPortCache.height !== viewport.height)\n        {\n            viewPortCache.x = viewport.x;\n            viewPortCache.y = viewPortY;\n            viewPortCache.width = viewport.width;\n            viewPortCache.height = viewport.height;\n\n            gl.viewport(\n                viewport.x,\n                viewPortY,\n                viewport.width,\n                viewport.height,\n            );\n        }\n\n        // if the stencil buffer has been requested, we need to create a stencil buffer\n        if (!gpuRenderTarget.depthStencilRenderBuffer && (renderTarget.stencil || renderTarget.depth))\n        {\n            this._initStencil(gpuRenderTarget);\n        }\n\n        this.clear(renderTarget, clear, clearColor);\n    }\n\n    public finishRenderPass(renderTarget?: RenderTarget)\n    {\n        const renderTargetSystem = this._renderTargetSystem;\n\n        const glRenderTarget = renderTargetSystem.getGpuRenderTarget(renderTarget);\n\n        if (!glRenderTarget.msaa) return;\n\n        const gl = this._renderer.gl;\n\n        gl.bindFramebuffer(gl.FRAMEBUFFER, glRenderTarget.resolveTargetFramebuffer);\n        gl.bindFramebuffer(gl.READ_FRAMEBUFFER, glRenderTarget.framebuffer);\n\n        gl.blitFramebuffer(\n            0, 0, glRenderTarget.width, glRenderTarget.height,\n            0, 0, glRenderTarget.width, glRenderTarget.height,\n            gl.COLOR_BUFFER_BIT, gl.NEAREST,\n        );\n\n        gl.bindFramebuffer(gl.FRAMEBUFFER, glRenderTarget.framebuffer);\n\n        // dont think we need this anymore? keeping around just in case the wheels fall off\n        // gl.bindFramebuffer(gl.READ_FRAMEBUFFER, null);\n    }\n\n    public initGpuRenderTarget(renderTarget: RenderTarget): GlRenderTarget\n    {\n        const renderer = this._renderer;\n\n        const gl = renderer.gl;\n\n        // do single...\n\n        const glRenderTarget = new GlRenderTarget();\n\n        // we are rendering to the main canvas..\n        const colorTexture = renderTarget.colorTexture;\n\n        if (colorTexture instanceof CanvasSource)\n        {\n            this._renderer.context.ensureCanvasSize(renderTarget.colorTexture.resource);\n\n            glRenderTarget.framebuffer = null;\n\n            return glRenderTarget;\n        }\n\n        this._initColor(renderTarget, glRenderTarget);\n\n        // set up a depth texture..\n\n        gl.bindFramebuffer(gl.FRAMEBUFFER, null);\n\n        return glRenderTarget;\n    }\n\n    public destroyGpuRenderTarget(gpuRenderTarget: GlRenderTarget)\n    {\n        const gl = this._renderer.gl;\n\n        if (gpuRenderTarget.framebuffer)\n        {\n            gl.deleteFramebuffer(gpuRenderTarget.framebuffer);\n            gpuRenderTarget.framebuffer = null;\n        }\n\n        if (gpuRenderTarget.resolveTargetFramebuffer)\n        {\n            gl.deleteFramebuffer(gpuRenderTarget.resolveTargetFramebuffer);\n            gpuRenderTarget.resolveTargetFramebuffer = null;\n        }\n\n        if (gpuRenderTarget.depthStencilRenderBuffer)\n        {\n            gl.deleteRenderbuffer(gpuRenderTarget.depthStencilRenderBuffer);\n            gpuRenderTarget.depthStencilRenderBuffer = null;\n        }\n\n        gpuRenderTarget.msaaRenderBuffer.forEach((renderBuffer) =>\n        {\n            gl.deleteRenderbuffer(renderBuffer);\n        });\n\n        gpuRenderTarget.msaaRenderBuffer = null;\n    }\n\n    public clear(_renderTarget: RenderTarget, clear: CLEAR_OR_BOOL, clearColor?: RgbaArray)\n    {\n        if (!clear) return;\n\n        const renderTargetSystem = this._renderTargetSystem;\n\n        // if clear is boolean..\n        if (typeof clear === 'boolean')\n        {\n            clear = clear ? CLEAR.ALL : CLEAR.NONE;\n        }\n\n        const gl = this._renderer.gl;\n\n        if (clear & CLEAR.COLOR)\n        {\n            clearColor ??= renderTargetSystem.defaultClearColor;\n\n            const clearColorCache = this._clearColorCache;\n            const clearColorArray = clearColor as number[];\n\n            if (clearColorCache[0] !== clearColorArray[0]\n                || clearColorCache[1] !== clearColorArray[1]\n                || clearColorCache[2] !== clearColorArray[2]\n                || clearColorCache[3] !== clearColorArray[3])\n            {\n                clearColorCache[0] = clearColorArray[0];\n                clearColorCache[1] = clearColorArray[1];\n                clearColorCache[2] = clearColorArray[2];\n                clearColorCache[3] = clearColorArray[3];\n\n                gl.clearColor(clearColorArray[0], clearColorArray[1], clearColorArray[2], clearColorArray[3]);\n            }\n        }\n\n        gl.clear(clear);\n    }\n\n    public resizeGpuRenderTarget(renderTarget: RenderTarget)\n    {\n        if (renderTarget.isRoot) return;\n\n        const renderTargetSystem = this._renderTargetSystem;\n\n        const glRenderTarget = renderTargetSystem.getGpuRenderTarget(renderTarget);\n\n        this._resizeColor(renderTarget, glRenderTarget);\n\n        if (renderTarget.stencil || renderTarget.depth)\n        {\n            this._resizeStencil(glRenderTarget);\n        }\n    }\n\n    private _initColor(renderTarget: RenderTarget, glRenderTarget: GlRenderTarget)\n    {\n        const renderer = this._renderer;\n\n        const gl = renderer.gl;\n        // deal with our outputs..\n        const resolveTargetFramebuffer = gl.createFramebuffer();\n\n        glRenderTarget.resolveTargetFramebuffer = resolveTargetFramebuffer;\n\n        // set up the texture..\n        gl.bindFramebuffer(gl.FRAMEBUFFER, resolveTargetFramebuffer);\n\n        glRenderTarget.width = renderTarget.colorTexture.source.pixelWidth;\n        glRenderTarget.height = renderTarget.colorTexture.source.pixelHeight;\n\n        renderTarget.colorTextures.forEach((colorTexture, i) =>\n        {\n            const source = colorTexture.source;\n\n            if (source.antialias)\n            {\n                if (renderer.context.supports.msaa)\n                {\n                    glRenderTarget.msaa = true;\n                }\n                else\n                {\n                    warn('[RenderTexture] Antialiasing on textures is not supported in WebGL1');\n                }\n            }\n\n            // TODO bindSource could return the glTexture\n            renderer.texture.bindSource(source, 0);\n            const glSource = renderer.texture.getGlSource(source);\n\n            const glTexture = glSource.texture;\n\n            gl.framebufferTexture2D(gl.FRAMEBUFFER,\n                gl.COLOR_ATTACHMENT0 + i,\n                3553, // texture.target,\n                glTexture,\n                0);// mipLevel);\n        });\n\n        if (glRenderTarget.msaa)\n        {\n            const viewFramebuffer = gl.createFramebuffer();\n\n            glRenderTarget.framebuffer = viewFramebuffer;\n\n            gl.bindFramebuffer(gl.FRAMEBUFFER, viewFramebuffer);\n\n            renderTarget.colorTextures.forEach((_, i) =>\n            {\n                const msaaRenderBuffer = gl.createRenderbuffer();\n\n                glRenderTarget.msaaRenderBuffer[i] = msaaRenderBuffer;\n            });\n        }\n        else\n        {\n            glRenderTarget.framebuffer = resolveTargetFramebuffer;\n        }\n\n        this._resizeColor(renderTarget, glRenderTarget);\n    }\n\n    private _resizeColor(renderTarget: RenderTarget, glRenderTarget: GlRenderTarget)\n    {\n        const source = renderTarget.colorTexture.source;\n\n        glRenderTarget.width = source.pixelWidth;\n        glRenderTarget.height = source.pixelHeight;\n\n        renderTarget.colorTextures.forEach((colorTexture, i) =>\n        {\n            // nno need to resize the first texture..\n            if (i === 0) return;\n\n            colorTexture.source.resize(source.width, source.height, source._resolution);\n        });\n\n        if (glRenderTarget.msaa)\n        {\n            const renderer = this._renderer;\n            const gl = renderer.gl;\n\n            const viewFramebuffer = glRenderTarget.framebuffer;\n\n            gl.bindFramebuffer(gl.FRAMEBUFFER, viewFramebuffer);\n\n            renderTarget.colorTextures.forEach((colorTexture, i) =>\n            {\n                const source = colorTexture.source;\n\n                renderer.texture.bindSource(source, 0);\n                const glSource = renderer.texture.getGlSource(source);\n\n                const glInternalFormat = glSource.internalFormat;\n\n                const msaaRenderBuffer = glRenderTarget.msaaRenderBuffer[i];\n\n                gl.bindRenderbuffer(\n                    gl.RENDERBUFFER,\n                    msaaRenderBuffer\n                );\n\n                gl.renderbufferStorageMultisample(\n                    gl.RENDERBUFFER,\n                    4,\n                    glInternalFormat,\n                    source.pixelWidth,\n                    source.pixelHeight\n                );\n\n                gl.framebufferRenderbuffer(\n                    gl.FRAMEBUFFER,\n                    gl.COLOR_ATTACHMENT0 + i,\n                    gl.RENDERBUFFER,\n                    msaaRenderBuffer\n                );\n            });\n        }\n    }\n\n    private _initStencil(glRenderTarget: GlRenderTarget)\n    {\n        // this already exists on the default screen\n        if (glRenderTarget.framebuffer === null) return;\n\n        const gl = this._renderer.gl;\n\n        const depthStencilRenderBuffer = gl.createRenderbuffer();\n\n        glRenderTarget.depthStencilRenderBuffer = depthStencilRenderBuffer;\n\n        gl.bindRenderbuffer(\n            gl.RENDERBUFFER,\n            depthStencilRenderBuffer\n        );\n\n        gl.framebufferRenderbuffer(\n            gl.FRAMEBUFFER,\n            gl.DEPTH_STENCIL_ATTACHMENT,\n            gl.RENDERBUFFER,\n            depthStencilRenderBuffer\n        );\n\n        // TDO DO>>\n        this._resizeStencil(glRenderTarget);\n    }\n\n    private _resizeStencil(glRenderTarget: GlRenderTarget)\n    {\n        const gl = this._renderer.gl;\n\n        gl.bindRenderbuffer(\n            gl.RENDERBUFFER,\n            glRenderTarget.depthStencilRenderBuffer\n        );\n\n        if (glRenderTarget.msaa)\n        {\n            gl.renderbufferStorageMultisample(\n                gl.RENDERBUFFER,\n                4,\n                gl.DEPTH24_STENCIL8,\n                glRenderTarget.width,\n                glRenderTarget.height\n            );\n        }\n        else\n        {\n            gl.renderbufferStorage(\n                gl.RENDERBUFFER,\n                this._renderer.context.webGLVersion === 2\n                    ? gl.DEPTH24_STENCIL8\n                    : gl.DEPTH_STENCIL,\n                glRenderTarget.width,\n                glRenderTarget.height\n            );\n        }\n    }\n\n    public prerender(renderTarget: RenderTarget)\n    {\n        const resource = renderTarget.colorTexture.resource;\n\n        // if the render target is a canvas, ensure its size matches the source\n        if (this._renderer.context.multiView && CanvasSource.test(resource))\n        {\n            this._renderer.context.ensureCanvasSize(resource);\n        }\n    }\n\n    public postrender(renderTarget: RenderTarget)\n    {\n        // if multiView is not enabled, we don't need to do anything\n        if (!this._renderer.context.multiView) return;\n\n        // if the render target is a canvas, we need to copy the pixels from the gl canvas\n        // to the canvas target\n        if (CanvasSource.test(renderTarget.colorTexture.resource))\n        {\n            const contextCanvas = this._renderer.context.canvas;\n            const canvasSource = renderTarget.colorTexture as unknown as CanvasSource;\n\n            canvasSource.context2D.drawImage(\n                contextCanvas as CanvasImageSource,\n                0, canvasSource.pixelHeight - contextCanvas.height\n            );\n        }\n    }\n}\n", "import { ExtensionType } from '../../../../extensions/Extensions';\nimport { RenderTargetSystem } from '../../shared/renderTarget/RenderTargetSystem';\nimport { GlRenderTargetAdaptor } from './GlRenderTargetAdaptor';\n\nimport type { GlRenderTarget } from '../GlRenderTarget';\nimport type { WebGLRenderer } from '../WebGLRenderer';\n\n/**\n * The WebGL adaptor for the render target system. Allows the Render Target System to be used with the WebGl renderer\n * @category rendering\n * @advanced\n */\nexport class GlRenderTargetSystem extends RenderTargetSystem<GlRenderTarget>\n{\n    /** @ignore */\n    public static extension = {\n        type: [ExtensionType.WebGLSystem],\n        name: 'renderTarget',\n    } as const;\n\n    public adaptor = new GlRenderTargetAdaptor();\n\n    constructor(renderer: WebGLRenderer)\n    {\n        super(renderer);\n\n        this.adaptor.init(renderer, this);\n    }\n}\n", "import { BufferResource } from '../../shared/buffer/BufferResource';\nimport { UniformGroup } from '../../shared/shader/UniformGroup';\nimport { TextureSource } from '../../shared/texture/sources/TextureSource';\n\nimport type { Shader } from '../../shared/shader/Shader';\nimport type { GlShaderSystem, ShaderSyncFunction } from './GlShaderSystem';\n\n/**\n * Generates the a function that will efficiently sync shader resources with the GPU.\n * @param shader - The shader to generate the code for\n * @param shaderSystem - An instance of the shader system\n * @internal\n */\nexport function generateShaderSyncCode(shader: Shader, shaderSystem: GlShaderSystem): ShaderSyncFunction\n{\n    const funcFragments: string[] = [];\n\n    /**\n     * rS = renderer.shader\n     * sS = shaderSystem\n     * sD = shaderData\n     * g = shader.groups\n     * s = shader\n     * r = renderer\n     * ugS = renderer.uniformGroupSystem\n     */\n    const headerFragments: string[] = [`\n        var g = s.groups;\n        var sS = r.shader;\n        var p = s.glProgram;\n        var ugS = r.uniformGroup;\n        var resources;\n    `];\n\n    let addedTextreSystem = false;\n    let textureCount = 0;\n\n    const programData = shaderSystem._getProgramData(shader.glProgram);\n\n    for (const i in shader.groups)\n    {\n        const group = shader.groups[i];\n\n        funcFragments.push(`\n            resources = g[${i}].resources;\n        `);\n\n        for (const j in group.resources)\n        {\n            const resource = group.resources[j];\n\n            if (resource instanceof UniformGroup)\n            {\n                if (resource.ubo)\n                {\n                    const resName = shader._uniformBindMap[i][Number(j)];\n\n                    funcFragments.push(`\n                        sS.bindUniformBlock(\n                            resources[${j}],\n                            '${resName}',\n                            ${shader.glProgram._uniformBlockData[resName].index}\n                        );\n                    `);\n                }\n                else\n                {\n                    funcFragments.push(`\n                        ugS.updateUniformGroup(resources[${j}], p, sD);\n                    `);\n                }\n            }\n            else if (resource instanceof BufferResource)\n            {\n                const resName = shader._uniformBindMap[i][Number(j)];\n\n                funcFragments.push(`\n                    sS.bindUniformBlock(\n                        resources[${j}],\n                        '${resName}',\n                        ${shader.glProgram._uniformBlockData[resName].index}\n                    );\n                `);\n            }\n            else if (resource instanceof TextureSource)\n            {\n                const uniformName = shader._uniformBindMap[i as unknown as number][j as unknown as number];\n\n                const uniformData = programData.uniformData[uniformName];\n\n                if (uniformData)\n                {\n                    if (!addedTextreSystem)\n                    {\n                        addedTextreSystem = true;\n                        headerFragments.push(`\n                        var tS = r.texture;\n                        `);\n                    }\n\n                    shaderSystem._gl.uniform1i(uniformData.location, textureCount);\n\n                    funcFragments.push(`\n                        tS.bind(resources[${j}], ${textureCount});\n                    `);\n\n                    textureCount++;\n                }\n            }\n        }\n    }\n\n    const functionSource = [...headerFragments, ...funcFragments].join('\\n');\n\n    // eslint-disable-next-line no-new-func\n    return new Function('r', 's', 'sD', functionSource) as ShaderSyncFunction;\n}\n", "/** @private */\nexport class IGLUniformData\n{\n    public location: WebGLUniformLocation;\n    public value: number | boolean | Float32Array | Int32Array | Uint32Array | boolean[];\n}\n\n/**\n * Helper class to create a WebGL Program\n * @private\n */\nexport class GlProgramData\n{\n    /** The shader program. */\n    public program: WebGLProgram;\n\n    /**\n     * Holds the uniform data which contains uniform locations\n     * and current uniform values used for caching and preventing unneeded GPU commands.\n     */\n    public uniformData: Record<string, any>;\n\n    /**\n     * UniformGroups holds the various upload functions for the shader. Each uniform group\n     * and program have a unique upload function generated.\n     */\n    public uniformGroups: Record<string, any>;\n\n    /** A hash that stores where UBOs are bound to on the program. */\n    public uniformBlockBindings: Record<string, any>;\n\n    /** A hash for lazily-generated uniform uploading functions. */\n    public uniformSync: Record<string, any>;\n\n    /**\n     * A place where dirty ticks are stored for groups\n     * If a tick here does not match with the Higher level Programs tick, it means\n     * we should re upload the data.\n     */\n    public uniformDirtyGroups: Record<string, any>;\n\n    /**\n     * Makes a new Pixi program.\n     * @param program - webgl program\n     * @param uniformData - uniforms\n     */\n    constructor(program: WebGLProgram, uniformData: {[key: string]: IGLUniformData})\n    {\n        this.program = program;\n        this.uniformData = uniformData;\n        this.uniformGroups = {};\n        this.uniformDirtyGroups = {};\n        this.uniformBlockBindings = {};\n    }\n\n    /** Destroys this program. */\n    public destroy(): void\n    {\n        this.uniformData = null;\n        this.uniformGroups = null;\n        this.uniformDirtyGroups = null;\n        this.uniformBlockBindings = null;\n        this.program = null;\n    }\n}\n", "/**\n * @private\n * @param {WebGLRenderingContext} gl - The current WebGL context {WebGLProgram}\n * @param {number} type - the type, can be either VERTEX_SHADER or FRAGMENT_SHADER\n * @param {string} src - The vertex shader source as an array of strings.\n * @returns {WebGLShader} the shader\n */\nexport function compileShader(gl: WebGLRenderingContextBase, type: number, src: string): WebGLShader\n{\n    const shader = gl.createShader(type);\n\n    gl.shaderSource(shader, src);\n    gl.compileShader(shader);\n\n    return shader;\n}\n", "function booleanArray(size: number): Array<boolean>\n{\n    const array = new Array(size);\n\n    for (let i = 0; i < array.length; i++)\n    {\n        array[i] = false;\n    }\n\n    return array;\n}\n\n/**\n * @param {string} type - Type of value\n * @param {number} size\n * @private\n */\nexport function defaultValue(\n    type: string,\n    size: number\n): number | Float32Array | Int32Array | Uint32Array | boolean | boolean[]\n{\n    switch (type)\n    {\n        case 'float':\n            return 0;\n\n        case 'vec2':\n            return new Float32Array(2 * size);\n\n        case 'vec3':\n            return new Float32Array(3 * size);\n\n        case 'vec4':\n            return new Float32Array(4 * size);\n\n        case 'int':\n        case 'uint':\n        case 'sampler2D':\n        case 'sampler2DArray':\n            return 0;\n\n        case 'ivec2':\n            return new Int32Array(2 * size);\n\n        case 'ivec3':\n            return new Int32Array(3 * size);\n\n        case 'ivec4':\n            return new Int32Array(4 * size);\n\n        case 'uvec2':\n            return new Uint32Array(2 * size);\n\n        case 'uvec3':\n            return new Uint32Array(3 * size);\n\n        case 'uvec4':\n            return new Uint32Array(4 * size);\n\n        case 'bool':\n            return false;\n\n        case 'bvec2':\n\n            return booleanArray(2 * size);\n\n        case 'bvec3':\n            return booleanArray(3 * size);\n\n        case 'bvec4':\n            return booleanArray(4 * size);\n\n        case 'mat2':\n            return new Float32Array([1, 0,\n                0, 1]);\n\n        case 'mat3':\n            return new Float32Array([1, 0, 0,\n                0, 1, 0,\n                0, 0, 1]);\n\n        case 'mat4':\n            return new Float32Array([1, 0, 0, 0,\n                0, 1, 0, 0,\n                0, 0, 1, 0,\n                0, 0, 0, 1]);\n    }\n\n    return null;\n}\n", "import type { Dict } from '../../../../../utils/types';\nimport type { VertexFormat } from '../../../shared/geometry/const';\n\nlet GL_TABLE: Dict<string> = null;\n\nconst GL_TO_GLSL_TYPES: Dict<string> = {\n    FLOAT:       'float',\n    FLOAT_VEC2:  'vec2',\n    FLOAT_VEC3:  'vec3',\n    FLOAT_VEC4:  'vec4',\n\n    INT:         'int',\n    INT_VEC2:    'ivec2',\n    INT_VEC3:    'ivec3',\n    INT_VEC4:    'ivec4',\n\n    UNSIGNED_INT:         'uint',\n    UNSIGNED_INT_VEC2:    'uvec2',\n    UNSIGNED_INT_VEC3:    'uvec3',\n    UNSIGNED_INT_VEC4:    'uvec4',\n\n    BOOL:        'bool',\n    BOOL_VEC2:   'bvec2',\n    BOOL_VEC3:   'bvec3',\n    BOOL_VEC4:   'bvec4',\n\n    FLOAT_MAT2:  'mat2',\n    FLOAT_MAT3:  'mat3',\n    FLOAT_MAT4:  'mat4',\n\n    SAMPLER_2D:              'sampler2D',\n    INT_SAMPLER_2D:          'sampler2D',\n    UNSIGNED_INT_SAMPLER_2D: 'sampler2D',\n    SAMPLER_CUBE:              'samplerCube',\n    INT_SAMPLER_CUBE:          'samplerCube',\n    UNSIGNED_INT_SAMPLER_CUBE: 'samplerCube',\n    SAMPLER_2D_ARRAY:              'sampler2DArray',\n    INT_SAMPLER_2D_ARRAY:          'sampler2DArray',\n    UNSIGNED_INT_SAMPLER_2D_ARRAY: 'sampler2DArray',\n};\n\nconst GLSL_TO_VERTEX_TYPES: Record<string, VertexFormat> = {\n\n    float: 'float32',\n    vec2: 'float32x2',\n    vec3: 'float32x3',\n    vec4: 'float32x4',\n\n    int: 'sint32',\n    ivec2: 'sint32x2',\n    ivec3: 'sint32x3',\n    ivec4: 'sint32x4',\n\n    uint: 'uint32',\n    uvec2: 'uint32x2',\n    uvec3: 'uint32x3',\n    uvec4: 'uint32x4',\n\n    bool: 'uint32',\n    bvec2: 'uint32x2',\n    bvec3: 'uint32x3',\n    bvec4: 'uint32x4',\n};\n\n/**\n * @param gl\n * @param type\n * @internal\n */\nexport function mapType(gl: any, type: number): string\n{\n    if (!GL_TABLE)\n    {\n        const typeNames = Object.keys(GL_TO_GLSL_TYPES);\n\n        GL_TABLE = {};\n\n        for (let i = 0; i < typeNames.length; ++i)\n        {\n            const tn = typeNames[i];\n\n            GL_TABLE[gl[tn]] = GL_TO_GLSL_TYPES[tn];\n        }\n    }\n\n    return GL_TABLE[type];\n}\n\n/**\n * @param gl\n * @param type\n * @internal\n */\nexport function mapGlToVertexFormat(gl: any, type: number): VertexFormat\n{\n    const typeValue = mapType(gl, type);\n\n    return GLSL_TO_VERTEX_TYPES[typeValue] || 'float32';\n}\n", "import { getAttributeInfoFromFormat } from '../../../shared/geometry/utils/getAttributeInfoFromFormat';\nimport { mapGlToVertexFormat } from './mapType';\n\nimport type { Attribute } from '../../../shared/geometry/Geometry';\n\n/**\n * This interface represents the extracted attribute data from a WebGL program.\n * It extends the `Attribute` interface but omits the `buffer` property.\n * It includes an optional `location` property that indicates where the shader location is for this attribute.\n * @category rendering\n * @advanced\n */\nexport interface ExtractedAttributeData extends Omit<Attribute, 'buffer'>\n{\n    /** set where the shader location is for this attribute */\n    location?: number;\n}\n\n/**\n * returns the attribute data from the program\n * @private\n * @param {WebGLProgram} [program] - the WebGL program\n * @param {WebGLRenderingContext} [gl] - the WebGL context\n * @param sortAttributes\n * @returns {object} the attribute data for this program\n */\nexport function extractAttributesFromGlProgram(\n    program: WebGLProgram,\n    gl: WebGLRenderingContextBase,\n    sortAttributes = false\n): Record<string, ExtractedAttributeData>\n{\n    const attributes: {[key: string]: ExtractedAttributeData} = {};\n\n    const totalAttributes = gl.getProgramParameter(program, gl.ACTIVE_ATTRIBUTES);\n\n    for (let i = 0; i < totalAttributes; i++)\n    {\n        const attribData = gl.getActiveAttrib(program, i);\n\n        // ignore the default ones!\n        if (attribData.name.startsWith('gl_'))\n        {\n            continue;\n        }\n\n        const format = mapGlToVertexFormat(gl, attribData.type);\n\n        attributes[attribData.name] = {\n            location: 0, // set further down..\n            format,\n            stride: getAttributeInfoFromFormat(format).stride,\n            offset: 0,\n            instance: false,\n            start: 0,\n        };\n    }\n\n    const keys = Object.keys(attributes);\n\n    if (sortAttributes)\n    {\n        keys.sort((a, b) => (a > b) ? 1 : -1); // eslint-disable-line no-confusing-arrow\n\n        for (let i = 0; i < keys.length; i++)\n        {\n            attributes[keys[i]].location = i;\n\n            gl.bindAttribLocation(program, i, keys[i]);\n        }\n\n        gl.linkProgram(program);\n    }\n    else\n    {\n        for (let i = 0; i < keys.length; i++)\n        {\n            attributes[keys[i]].location = gl.getAttribLocation(program, keys[i]);\n        }\n    }\n\n    return attributes;\n}\n", "import type { GlUniformBlockData } from '../GlProgram';\n\n/**\n * returns the uniform block data from the program\n * @private\n * @param program - the webgl program\n * @param gl - the WebGL context\n * @returns {object} the uniform data for this program\n */\nexport function getUboData(program: WebGLProgram, gl: WebGL2RenderingContext): Record<string, GlUniformBlockData>\n{\n    // if uniform buffer data is not supported, early out\n    if (!gl.ACTIVE_UNIFORM_BLOCKS) return {};\n\n    const uniformBlocks: Record<string, GlUniformBlockData> = {};\n\n    // const totalUniforms = gl.getProgramParameter(program, gl.ACTIVE_UNIFORMS);\n\n    const totalUniformsBlocks = gl.getProgramParameter(program, gl.ACTIVE_UNIFORM_BLOCKS);\n\n    for (let i = 0; i < totalUniformsBlocks; i++)\n    {\n        const name = gl.getActiveUniformBlockName(program, i);\n        const uniformBlockIndex = gl.getUniformBlockIndex(program, name);\n\n        const size = gl.getActiveUniformBlockParameter(program, i, gl.UNIFORM_BLOCK_DATA_SIZE);\n\n        uniformBlocks[name] = {\n            name,\n            index: uniformBlockIndex,\n            size,\n        };\n    }\n\n    return uniformBlocks;\n}\n", "import { defaultValue } from './defaultValue';\nimport { mapType } from './mapType';\n\nimport type { GlUniformData } from '../GlProgram';\n\n/**\n * returns the uniform data from the program\n * @private\n * @param program - the webgl program\n * @param gl - the WebGL context\n * @returns {object} the uniform data for this program\n */\nexport function getUniformData(program: WebGLProgram, gl: WebGLRenderingContextBase): {[key: string]: GlUniformData}\n{\n    const uniforms: {[key: string]: GlUniformData} = {};\n\n    const totalUniforms = gl.getProgramParameter(program, gl.ACTIVE_UNIFORMS);\n\n    for (let i = 0; i < totalUniforms; i++)\n    {\n        const uniformData = gl.getActiveUniform(program, i);\n        const name = uniformData.name.replace(/\\[.*?\\]$/, '');\n\n        const isArray = !!(uniformData.name.match(/\\[.*?\\]$/));\n\n        const type = mapType(gl, uniformData.type);\n\n        uniforms[name] = {\n            name,\n            index: i,\n            type,\n            size: uniformData.size,\n            isArray,\n            value: defaultValue(type, uniformData.size),\n        };\n    }\n\n    return uniforms;\n}\n", "/**\n * will log a shader error highlighting the lines with the error\n * also will add numbers along the side.\n * @param gl - the WebGLContext\n * @param shader - the shader to log errors for\n */\nfunction logPrettyShaderError(gl: WebGLRenderingContext, shader: WebGLShader): void\n{\n    const shaderSrc = gl.getShaderSource(shader)\n        .split('\\n')\n        .map((line, index) => `${index}: ${line}`);\n\n    const shaderLog = gl.getShaderInfoLog(shader);\n    const splitShader = shaderLog.split('\\n');\n\n    const dedupe: Record<number, boolean> = {};\n\n    const lineNumbers = splitShader.map((line) => parseFloat(line.replace(/^ERROR\\: 0\\:([\\d]+)\\:.*$/, '$1')))\n        .filter((n) =>\n        {\n            if (n && !dedupe[n])\n            {\n                dedupe[n] = true;\n\n                return true;\n            }\n\n            return false;\n        });\n\n    const logArgs = [''];\n\n    lineNumbers.forEach((number) =>\n    {\n        shaderSrc[number - 1] = `%c${shaderSrc[number - 1]}%c`;\n        logArgs.push('background: #FF0000; color:#FFFFFF; font-size: 10px', 'font-size: 10px');\n    });\n\n    const fragmentSourceToLog = shaderSrc\n        .join('\\n');\n\n    logArgs[0] = fragmentSourceToLog;\n\n    console.error(shaderLog);\n\n    // eslint-disable-next-line no-console\n    console.groupCollapsed('click to view full shader code');\n    console.warn(...logArgs);\n    // eslint-disable-next-line no-console\n    console.groupEnd();\n}\n\n/**\n *\n * logs out any program errors\n * @param gl - The current WebGL context\n * @param program - the WebGL program to display errors for\n * @param vertexShader  - the fragment WebGL shader program\n * @param fragmentShader - the vertex WebGL shader program\n * @private\n */\nexport function logProgramError(\n    gl: WebGLRenderingContext,\n    program: WebGLProgram,\n    vertexShader: WebGLShader,\n    fragmentShader: WebGLShader\n): void\n{\n    // if linking fails, then log and cleanup\n    if (!gl.getProgramParameter(program, gl.LINK_STATUS))\n    {\n        if (!gl.getShaderParameter(vertexShader, gl.COMPILE_STATUS))\n        {\n            logPrettyShaderError(gl, vertexShader);\n        }\n\n        if (!gl.getShaderParameter(fragmentShader, gl.COMPILE_STATUS))\n        {\n            logPrettyShaderError(gl, fragmentShader);\n        }\n\n        console.error('PixiJS Error: Could not initialize shader.');\n\n        // if there is a program info log, log it\n        if (gl.getProgramInfoLog(program) !== '')\n        {\n            console.warn('PixiJS Warning: gl.getProgramInfoLog()', gl.getProgramInfoLog(program));\n        }\n    }\n}\n", "import { warn } from '../../../../../utils/logging/warn';\nimport { GlProgramData } from '../GlProgramData';\nimport { compileShader } from './compileShader';\nimport { defaultValue } from './defaultValue';\nimport { extractAttributesFromGlProgram } from './extractAttributesFromGlProgram';\nimport { getUboData } from './getUboData';\nimport { getUniformData } from './getUniformData';\nimport { logProgramError } from './logProgramError';\n\nimport type { GlRenderingContext } from '../../context/GlRenderingContext';\nimport type { GlProgram } from '../GlProgram';\nimport type { IGLUniformData } from '../GlProgramData';\n\n/**\n * generates a WebGL Program object from a high level Pixi Program.\n * @param gl - a rendering context on which to generate the program\n * @param program - the high level Pixi Program.\n * @private\n */\nexport function generateProgram(gl: GlRenderingContext, program: GlProgram): GlProgramData\n{\n    const glVertShader = compileShader(gl, gl.VERTEX_SHADER, program.vertex);\n    const glFragShader = compileShader(gl, gl.FRAGMENT_SHADER, program.fragment);\n\n    const webGLProgram = gl.createProgram();\n\n    gl.attachShader(webGLProgram, glVertShader);\n    gl.attachShader(webGLProgram, glFragShader);\n\n    const transformFeedbackVaryings = program.transformFeedbackVaryings;\n\n    if (transformFeedbackVaryings)\n    {\n        if (typeof gl.transformFeedbackVaryings !== 'function')\n        {\n            // #if _DEBUG\n            warn(`TransformFeedback is not supported but TransformFeedbackVaryings are given.`);\n            // #endif\n        }\n        else\n        {\n            gl.transformFeedbackVaryings(\n                webGLProgram,\n                transformFeedbackVaryings.names,\n                transformFeedbackVaryings.bufferMode === 'separate'\n                    ? gl.SEPARATE_ATTRIBS\n                    : gl.INTERLEAVED_ATTRIBS\n            );\n        }\n    }\n\n    gl.linkProgram(webGLProgram);\n\n    if (!gl.getProgramParameter(webGLProgram, gl.LINK_STATUS))\n    {\n        logProgramError(gl, webGLProgram, glVertShader, glFragShader);\n    }\n\n    // GLSL 1.00: bind attributes sorted by name in ascending order\n    // GLSL 3.00: don't change the attribute locations that where chosen by the compiler\n    //            or assigned by the layout specifier in the shader source code\n    program._attributeData = extractAttributesFromGlProgram(\n        webGLProgram,\n        gl,\n        !(/^[ \\t]*#[ \\t]*version[ \\t]+300[ \\t]+es[ \\t]*$/m).test(program.vertex)\n    );\n\n    program._uniformData = getUniformData(webGLProgram, gl);\n    program._uniformBlockData = getUboData(webGLProgram, gl);\n\n    gl.deleteShader(glVertShader);\n    gl.deleteShader(glFragShader);\n\n    const uniformData: {[key: string]: IGLUniformData} = {};\n\n    for (const i in program._uniformData)\n    {\n        const data = program._uniformData[i];\n\n        uniformData[i] = {\n            location: gl.getUniformLocation(webGLProgram, i),\n            value: defaultValue(data.type, data.size),\n        };\n    }\n\n    const glProgram = new GlProgramData(webGLProgram, uniformData);\n\n    return glProgram;\n}\n", "import { ExtensionType } from '../../../../extensions/Extensions';\nimport { generateShaderSyncCode } from './GenerateShaderSyncCode';\nimport { generateProgram } from './program/generateProgram';\n\nimport type { BufferResource } from '../../shared/buffer/BufferResource';\nimport type { Shader } from '../../shared/shader/Shader';\nimport type { UniformGroup } from '../../shared/shader/UniformGroup';\nimport type { GlRenderingContext } from '../context/GlRenderingContext';\nimport type { WebGLRenderer } from '../WebGLRenderer';\nimport type { GlProgram } from './GlProgram';\nimport type { GlProgramData } from './GlProgramData';\n\n/** @internal */\nexport interface ShaderSyncData\n{\n    textureCount: number;\n    blockIndex: number;\n}\n\n/** @internal */\nexport type ShaderSyncFunction = (renderer: WebGLRenderer, shader: Shader, syncData: ShaderSyncData) => void;\n\n// default sync data so we don't create a new one each time!\nconst defaultSyncData: ShaderSyncData = {\n    textureCount: 0,\n    blockIndex: 0,\n};\n\n/**\n * System plugin to the renderer to manage the shaders for WebGL.\n * @category rendering\n * @advanced\n */\nexport class GlShaderSystem\n{\n    /** @ignore */\n    public static extension = {\n        type: [\n            ExtensionType.WebGLSystem,\n        ],\n        name: 'shader',\n    } as const;\n\n    /** @internal */\n    public _activeProgram: GlProgram = null;\n\n    private _programDataHash: Record<string, GlProgramData> = Object.create(null);\n    private readonly _renderer: WebGLRenderer;\n    /** @internal */\n    public _gl: WebGL2RenderingContext;\n    private _shaderSyncFunctions: Record<string, ShaderSyncFunction> = Object.create(null);\n\n    constructor(renderer: WebGLRenderer)\n    {\n        this._renderer = renderer;\n        this._renderer.renderableGC.addManagedHash(this, '_programDataHash');\n    }\n\n    protected contextChange(gl: GlRenderingContext): void\n    {\n        this._gl = gl;\n\n        this._programDataHash = Object.create(null);\n        /**\n         * these need to also be cleared as internally some uniforms are set as an optimisation as the sync\n         * function is generated. Specifically the texture ints.\n         */\n        this._shaderSyncFunctions = Object.create(null);\n        this._activeProgram = null;\n    }\n\n    /**\n     * Changes the current shader to the one given in parameter.\n     * @param shader - the new shader\n     * @param skipSync - false if the shader should automatically sync its uniforms.\n     * @returns the glProgram that belongs to the shader.\n     */\n    public bind(shader: Shader, skipSync?: boolean): void\n    {\n        this._setProgram(shader.glProgram);\n\n        if (skipSync) return;\n\n        defaultSyncData.textureCount = 0;\n        defaultSyncData.blockIndex = 0;\n\n        let syncFunction = this._shaderSyncFunctions[shader.glProgram._key];\n\n        if (!syncFunction)\n        {\n            syncFunction = this._shaderSyncFunctions[shader.glProgram._key] = this._generateShaderSync(shader, this);\n        }\n\n        // TODO: take into account number of TF buffers. Currently works only with interleaved\n        this._renderer.buffer.nextBindBase(!!shader.glProgram.transformFeedbackVaryings);\n        syncFunction(this._renderer, shader, defaultSyncData);\n    }\n\n    /**\n     * Updates the uniform group.\n     * @param uniformGroup - the uniform group to update\n     */\n    public updateUniformGroup(uniformGroup: UniformGroup): void\n    {\n        this._renderer.uniformGroup.updateUniformGroup(uniformGroup, this._activeProgram, defaultSyncData);\n    }\n\n    /**\n     * Binds a uniform block to the shader.\n     * @param uniformGroup - the uniform group to bind\n     * @param name - the name of the uniform block\n     * @param index - the index of the uniform block\n     */\n    public bindUniformBlock(uniformGroup: UniformGroup | BufferResource, name: string, index = 0): void\n    {\n        const bufferSystem = this._renderer.buffer;\n        const programData = this._getProgramData(this._activeProgram);\n\n        const isBufferResource = (uniformGroup as BufferResource)._bufferResource;\n\n        if (!isBufferResource)\n        {\n            this._renderer.ubo.updateUniformGroup(uniformGroup as UniformGroup);\n        }\n\n        const buffer = uniformGroup.buffer;\n\n        const glBuffer = bufferSystem.updateBuffer(buffer);\n\n        const boundLocation = bufferSystem.freeLocationForBufferBase(glBuffer);\n\n        if (isBufferResource)\n        {\n            const { offset, size } = (uniformGroup as BufferResource);\n\n            // trivial case of buffer resource, can be cached\n            if (offset === 0 && size === buffer.data.byteLength)\n            {\n                bufferSystem.bindBufferBase(glBuffer, boundLocation);\n            }\n            else\n            {\n                bufferSystem.bindBufferRange(glBuffer, boundLocation, offset);\n            }\n        }\n        else if (bufferSystem.getLastBindBaseLocation(glBuffer) !== boundLocation)\n        {\n            // confirmation that buffer isn't there yet\n            bufferSystem.bindBufferBase(glBuffer, boundLocation);\n        }\n\n        const uniformBlockIndex = this._activeProgram._uniformBlockData[name].index;\n\n        if (programData.uniformBlockBindings[index] === boundLocation) return;\n        programData.uniformBlockBindings[index] = boundLocation;\n\n        this._renderer.gl.uniformBlockBinding(programData.program, uniformBlockIndex, boundLocation);\n    }\n\n    private _setProgram(program: GlProgram)\n    {\n        if (this._activeProgram === program) return;\n\n        this._activeProgram = program;\n\n        const programData = this._getProgramData(program);\n\n        this._gl.useProgram(programData.program);\n    }\n\n    /**\n     * @param program - the program to get the data for\n     * @internal\n     */\n    public _getProgramData(program: GlProgram): GlProgramData\n    {\n        return this._programDataHash[program._key] || this._createProgramData(program);\n    }\n\n    private _createProgramData(program: GlProgram): GlProgramData\n    {\n        const key = program._key;\n\n        this._programDataHash[key] = generateProgram(this._gl, program);\n\n        return this._programDataHash[key];\n    }\n\n    public destroy(): void\n    {\n        for (const key of Object.keys(this._programDataHash))\n        {\n            const programData = this._programDataHash[key];\n\n            programData.destroy();\n            this._programDataHash[key] = null;\n        }\n\n        this._programDataHash = null;\n    }\n\n    /**\n     * Creates a function that can be executed that will sync the shader as efficiently as possible.\n     * Overridden by the unsafe eval package if you don't want eval used in your project.\n     * @param shader - the shader to generate the sync function for\n     * @param shaderSystem - the shader system to use\n     * @returns - the generated sync function\n     * @ignore\n     */\n    public _generateShaderSync(shader: Shader, shaderSystem: GlShaderSystem): ShaderSyncFunction\n    {\n        return generateShaderSyncCode(shader, shaderSystem);\n    }\n\n    public resetState(): void\n    {\n        this._activeProgram = null;\n    }\n}\n", "// cu = Cached value's uniform data field\n// cv = Cached value\n// v = value to upload\n// ud = uniformData\n// uv = uniformValue\n\nimport type { UNIFORM_TYPES } from '../../../shared/shader/types';\n\n/** @internal */\nexport const UNIFORM_TO_SINGLE_SETTERS: Record<UNIFORM_TYPES | string, string> = {\n    f32: `if (cv !== v) {\n            cu.value = v;\n            gl.uniform1f(location, v);\n        }`,\n    'vec2<f32>': `if (cv[0] !== v[0] || cv[1] !== v[1]) {\n            cv[0] = v[0];\n            cv[1] = v[1];\n            gl.uniform2f(location, v[0], v[1]);\n        }`,\n    'vec3<f32>': `if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2]) {\n            cv[0] = v[0];\n            cv[1] = v[1];\n            cv[2] = v[2];\n            gl.uniform3f(location, v[0], v[1], v[2]);\n        }`,\n    'vec4<f32>': `if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2] || cv[3] !== v[3]) {\n            cv[0] = v[0];\n            cv[1] = v[1];\n            cv[2] = v[2];\n            cv[3] = v[3];\n            gl.uniform4f(location, v[0], v[1], v[2], v[3]);\n        }`,\n    i32: `if (cv !== v) {\n            cu.value = v;\n            gl.uniform1i(location, v);\n        }`,\n    'vec2<i32>': `if (cv[0] !== v[0] || cv[1] !== v[1]) {\n            cv[0] = v[0];\n            cv[1] = v[1];\n            gl.uniform2i(location, v[0], v[1]);\n        }`,\n    'vec3<i32>': `if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2]) {\n            cv[0] = v[0];\n            cv[1] = v[1];\n            cv[2] = v[2];\n            gl.uniform3i(location, v[0], v[1], v[2]);\n        }`,\n    'vec4<i32>': `if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2] || cv[3] !== v[3]) {\n            cv[0] = v[0];\n            cv[1] = v[1];\n            cv[2] = v[2];\n            cv[3] = v[3];\n            gl.uniform4i(location, v[0], v[1], v[2], v[3]);\n        }`,\n    u32: `if (cv !== v) {\n            cu.value = v;\n            gl.uniform1ui(location, v);\n        }`,\n    'vec2<u32>': `if (cv[0] !== v[0] || cv[1] !== v[1]) {\n            cv[0] = v[0];\n            cv[1] = v[1];\n            gl.uniform2ui(location, v[0], v[1]);\n        }`,\n    'vec3<u32>': `if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2]) {\n            cv[0] = v[0];\n            cv[1] = v[1];\n            cv[2] = v[2];\n            gl.uniform3ui(location, v[0], v[1], v[2]);\n        }`,\n    'vec4<u32>': `if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2] || cv[3] !== v[3]) {\n            cv[0] = v[0];\n            cv[1] = v[1];\n            cv[2] = v[2];\n            cv[3] = v[3];\n            gl.uniform4ui(location, v[0], v[1], v[2], v[3]);\n        }`,\n    bool: `if (cv !== v) {\n            cu.value = v;\n            gl.uniform1i(location, v);\n        }`,\n    'vec2<bool>': `if (cv[0] !== v[0] || cv[1] !== v[1]) {\n            cv[0] = v[0];\n            cv[1] = v[1];\n            gl.uniform2i(location, v[0], v[1]);\n        }`,\n    'vec3<bool>': `if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2]) {\n            cv[0] = v[0];\n            cv[1] = v[1];\n            cv[2] = v[2];\n            gl.uniform3i(location, v[0], v[1], v[2]);\n        }`,\n    'vec4<bool>': `if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2] || cv[3] !== v[3]) {\n            cv[0] = v[0];\n            cv[1] = v[1];\n            cv[2] = v[2];\n            cv[3] = v[3];\n            gl.uniform4i(location, v[0], v[1], v[2], v[3]);\n        }`,\n    'mat2x2<f32>': `gl.uniformMatrix2fv(location, false, v);`,\n    'mat3x3<f32>': `gl.uniformMatrix3fv(location, false, v);`,\n    'mat4x4<f32>': `gl.uniformMatrix4fv(location, false, v);`,\n};\n\n/** @internal */\nexport const UNIFORM_TO_ARRAY_SETTERS: Record<UNIFORM_TYPES | string, string> = {\n    f32: `gl.uniform1fv(location, v);`,\n    'vec2<f32>': `gl.uniform2fv(location, v);`,\n    'vec3<f32>': `gl.uniform3fv(location, v);`,\n    'vec4<f32>': `gl.uniform4fv(location, v);`,\n    'mat2x2<f32>': `gl.uniformMatrix2fv(location, false, v);`,\n    'mat3x3<f32>': `gl.uniformMatrix3fv(location, false, v);`,\n    'mat4x4<f32>': `gl.uniformMatrix4fv(location, false, v);`,\n    i32: `gl.uniform1iv(location, v);`,\n    'vec2<i32>': `gl.uniform2iv(location, v);`,\n    'vec3<i32>': `gl.uniform3iv(location, v);`,\n    'vec4<i32>': `gl.uniform4iv(location, v);`,\n    u32: `gl.uniform1iv(location, v);`,\n    'vec2<u32>': `gl.uniform2iv(location, v);`,\n    'vec3<u32>': `gl.uniform3iv(location, v);`,\n    'vec4<u32>': `gl.uniform4iv(location, v);`,\n    bool: `gl.uniform1iv(location, v);`,\n    'vec2<bool>': `gl.uniform2iv(location, v);`,\n    'vec3<bool>': `gl.uniform3iv(location, v);`,\n    'vec4<bool>': `gl.uniform4iv(location, v);`,\n};\n", "// cu = Cached value's uniform data field\n// cv = Cached value\n// v = value to upload\n// ud = uniformData\n// uv = uniformValue\n\nimport { BufferResource } from '../../../shared/buffer/BufferResource';\nimport { UniformGroup } from '../../../shared/shader/UniformGroup';\nimport { uniformParsers } from '../../../shared/shader/utils/uniformParsers';\nimport { UNIFORM_TO_ARRAY_SETTERS, UNIFORM_TO_SINGLE_SETTERS } from './generateUniformsSyncTypes';\n\nimport type { UniformsSyncCallback } from '../../../shared/shader/types';\n\n/**\n * @param group\n * @param uniformData\n * @internal\n */\nexport function generateUniformsSync(group: UniformGroup, uniformData: Record<string, any>): UniformsSyncCallback\n{\n    const funcFragments = [`\n        var v = null;\n        var cv = null;\n        var cu = null;\n        var t = 0;\n        var gl = renderer.gl;\n        var name = null;\n    `];\n\n    for (const i in group.uniforms)\n    {\n        if (!uniformData[i])\n        {\n            if (group.uniforms[i] instanceof UniformGroup)\n            {\n                if ((group.uniforms[i] as UniformGroup).ubo)\n                {\n                    funcFragments.push(`\n                        renderer.shader.bindUniformBlock(uv.${i}, \"${i}\");\n                    `);\n                }\n                else\n                {\n                    funcFragments.push(`\n                        renderer.shader.updateUniformGroup(uv.${i});\n                    `);\n                }\n            }\n            else if (group.uniforms[i] instanceof BufferResource)\n            {\n                funcFragments.push(`\n                        renderer.shader.bindBufferResource(uv.${i}, \"${i}\");\n                    `);\n            }\n\n            continue;\n        }\n\n        const uniform = group.uniformStructures[i];\n\n        let parsed = false;\n\n        for (let j = 0; j < uniformParsers.length; j++)\n        {\n            const parser = uniformParsers[j];\n\n            if (uniform.type === parser.type && parser.test(uniform))\n            {\n                funcFragments.push(`name = \"${i}\";`, uniformParsers[j].uniform);\n                parsed = true;\n\n                break;\n            }\n        }\n\n        if (!parsed)\n        {\n            const templateType = uniform.size === 1 ? UNIFORM_TO_SINGLE_SETTERS : UNIFORM_TO_ARRAY_SETTERS;\n\n            const template = templateType[uniform.type].replace('location', `ud[\"${i}\"].location`);\n\n            funcFragments.push(`\n            cu = ud[\"${i}\"];\n            cv = cu.value;\n            v = uv[\"${i}\"];\n            ${template};`);\n        }\n    }\n\n    /*\n     * the introduction of syncData is to solve an issue where textures in uniform groups are not set correctly\n     * the texture count was always starting from 0 in each group. This needs to increment each time a texture is used\n     * no matter which group is being used\n     *\n     */\n    // eslint-disable-next-line no-new-func\n    return new Function('ud', 'uv', 'renderer', 'syncData', funcFragments.join('\\n')) as UniformsSyncCallback;\n}\n", "import { ExtensionType } from '../../../../extensions/Extensions';\nimport { generateUniformsSync } from './utils/generateUniformsSync';\n\nimport type { UniformsSyncCallback } from '../../shared/shader/types';\nimport type { UniformGroup } from '../../shared/shader/UniformGroup';\nimport type { System } from '../../shared/system/System';\nimport type { GlRenderingContext } from '../context/GlRenderingContext';\nimport type { WebGLRenderer } from '../WebGLRenderer';\nimport type { GlProgram, GlUniformData } from './GlProgram';\n\n/**\n * System plugin to the renderer to manage shaders.\n * @category rendering\n * @advanced\n */\nexport class GlUniformGroupSystem implements System\n{\n    /** @ignore */\n    public static extension = {\n        type: [\n            ExtensionType.WebGLSystem,\n        ],\n        name: 'uniformGroup',\n    } as const;\n\n    /**\n     * The current WebGL rendering context.\n     * @type {WebGLRenderingContext}\n     */\n    protected gl: GlRenderingContext;\n\n    /** Cache to holds the generated functions. Stored against UniformObjects unique signature. */\n    private _cache: Record<string, UniformsSyncCallback> = {};\n    private _renderer: WebGLRenderer;\n\n    private _uniformGroupSyncHash: Record<string, Record<string, UniformsSyncCallback>> = {};\n\n    /** @param renderer - The renderer this System works for. */\n    constructor(renderer: WebGLRenderer)\n    {\n        this._renderer = renderer;\n\n        this.gl = null;\n        this._cache = {};\n    }\n\n    protected contextChange(gl: GlRenderingContext): void\n    {\n        this.gl = gl;\n    }\n\n    /**\n     * Uploads the uniforms values to the currently bound shader.\n     * @param group - the uniforms values that be applied to the current shader\n     * @param program\n     * @param syncData\n     * @param syncData.textureCount\n     */\n    public updateUniformGroup(group: UniformGroup, program: GlProgram, syncData: { textureCount: number }): void\n    {\n        const programData = this._renderer.shader._getProgramData(program);\n\n        if (!group.isStatic || group._dirtyId !== programData.uniformDirtyGroups[group.uid])\n        {\n            programData.uniformDirtyGroups[group.uid] = group._dirtyId;\n\n            const syncFunc = this._getUniformSyncFunction(group, program);\n\n            syncFunc(programData.uniformData, group.uniforms, this._renderer, syncData);\n        }\n    }\n\n    /**\n     * Overridable by the pixi.js/unsafe-eval package to use static syncUniforms instead.\n     * @param group\n     * @param program\n     */\n    private _getUniformSyncFunction(group: UniformGroup, program: GlProgram): UniformsSyncCallback\n    {\n        return this._uniformGroupSyncHash[group._signature]?.[program._key]\n            || this._createUniformSyncFunction(group, program);\n    }\n\n    private _createUniformSyncFunction(group: UniformGroup, program: GlProgram): UniformsSyncCallback\n    {\n        const uniformGroupSyncHash = this._uniformGroupSyncHash[group._signature]\n            || (this._uniformGroupSyncHash[group._signature] = {});\n\n        const id = this._getSignature(group, program._uniformData, 'u');\n\n        if (!this._cache[id])\n        {\n            this._cache[id] = this._generateUniformsSync(group, program._uniformData);\n        }\n\n        uniformGroupSyncHash[program._key] = this._cache[id];\n\n        return uniformGroupSyncHash[program._key];\n    }\n\n    private _generateUniformsSync(group: UniformGroup, uniformData: Record<string, GlUniformData>): UniformsSyncCallback\n    {\n        return generateUniformsSync(group, uniformData);\n    }\n\n    /**\n     * Takes a uniform group and data and generates a unique signature for them.\n     * @param group - The uniform group to get signature of\n     * @param group.uniforms\n     * @param uniformData - Uniform information generated by the shader\n     * @param preFix\n     * @returns Unique signature of the uniform group\n     */\n    private _getSignature(group: UniformGroup, uniformData: Record<string, any>, preFix: string): string\n    {\n        const uniforms = group.uniforms;\n\n        const strings = [`${preFix}-`];\n\n        for (const i in uniforms)\n        {\n            strings.push(i);\n\n            if (uniformData[i])\n            {\n                strings.push(uniformData[i].type);\n            }\n        }\n\n        return strings.join('-');\n    }\n\n    /** Destroys this System and removes all its textures. */\n    public destroy(): void\n    {\n        this._renderer = null;\n        this._cache = null;\n    }\n}\n", "import { DOMAdapter } from '../../../../environment/adapter';\n\nimport type { BLEND_MODES } from '../../shared/state/const';\nimport type { GlRenderingContext } from '../context/GlRenderingContext';\n\n/**\n * Maps gl blend combinations to WebGL.\n * @param gl\n * @returns {object} Map of gl blend combinations to WebGL.\n * @internal\n */\nexport function mapWebGLBlendModesToPixi(gl: GlRenderingContext): Record<BLEND_MODES, number[]>\n{\n    const blendMap: Partial<Record<BLEND_MODES, number[]>> = {};\n\n    // TODO - premultiply alpha would be different.\n    // add a boolean for that!\n    blendMap.normal = [gl.ONE, gl.ONE_MINUS_SRC_ALPHA];\n    blendMap.add = [gl.ONE, gl.ONE];\n    blendMap.multiply = [gl.DST_COLOR, gl.ONE_MINUS_SRC_ALPHA, gl.ONE, gl.ONE_MINUS_SRC_ALPHA];\n    blendMap.screen = [gl.ONE, gl.ONE_MINUS_SRC_COLOR, gl.ONE, gl.ONE_MINUS_SRC_ALPHA];\n    blendMap.none = [0, 0];\n\n    // not-premultiplied blend modes\n    blendMap['normal-npm'] = [gl.SRC_ALPHA, gl.ONE_MINUS_SRC_ALPHA, gl.ONE, gl.ONE_MINUS_SRC_ALPHA];\n    blendMap['add-npm'] = [gl.SRC_ALPHA, gl.ONE, gl.ONE, gl.ONE];\n    blendMap['screen-npm'] = [gl.SRC_ALPHA, gl.ONE_MINUS_SRC_COLOR, gl.ONE, gl.ONE_MINUS_SRC_ALPHA];\n\n    blendMap.erase = [gl.ZERO, gl.ONE_MINUS_SRC_ALPHA];\n\n    const isWebGl2 = !(gl instanceof DOMAdapter.get().getWebGLRenderingContext());\n\n    if (isWebGl2)\n    {\n        blendMap.min = [gl.ONE, gl.ONE, gl.ONE, gl.ONE, gl.MIN, gl.MIN];\n        blendMap.max = [gl.ONE, gl.ONE, gl.ONE, gl.ONE, gl.MAX, gl.MAX];\n    }\n    else\n    {\n        const ext = gl.getExtension('EXT_blend_minmax');\n\n        if (ext)\n        {\n            blendMap.min = [gl.ONE, gl.ONE, gl.ONE, gl.ONE, ext.MIN_EXT, ext.MIN_EXT];\n            blendMap.max = [gl.ONE, gl.ONE, gl.ONE, gl.ONE, ext.MAX_EXT, ext.MAX_EXT];\n        }\n    }\n\n    // TODO - implement if requested!\n    // composite operations\n    // array[BLEND_MODES.SRC_IN] = [gl.DST_ALPHA, gl.ZERO];\n    // array[BLEND_MODES.SRC_OUT] = [gl.ONE_MINUS_DST_ALPHA, gl.ZERO];\n    // array[BLEND_MODES.SRC_ATOP] = [gl.DST_ALPHA, gl.ONE_MINUS_SRC_ALPHA];\n    // array[BLEND_MODES.DST_OVER] = [gl.ONE_MINUS_DST_ALPHA, gl.ONE];\n    // array[BLEND_MODES.DST_IN] = [gl.ZERO, gl.SRC_ALPHA];\n    // array[BLEND_MODES.DST_OUT] = [gl.ZERO, gl.ONE_MINUS_SRC_ALPHA];\n    // array[BLEND_MODES.DST_ATOP] = [gl.ONE_MINUS_DST_ALPHA, gl.SRC_ALPHA];\n    // array[BLEND_MODES.XOR] = [gl.ONE_MINUS_DST_ALPHA, gl.ONE_MINUS_SRC_ALPHA];\n    // SUBTRACT from flash\n    // array[BLEND_MODES.SUBTRACT] = [gl.ONE, gl.ONE, gl.ONE, gl.ONE, gl.FUNC_REVERSE_SUBTRACT, gl.FUNC_ADD];\n\n    return blendMap as Record<BLEND_MODES, number[]>;\n}\n", "import { ExtensionType } from '../../../../extensions/Extensions';\nimport { type RenderTarget } from '../../shared/renderTarget/RenderTarget';\nimport { State } from '../../shared/state/State';\nimport { type WebGLRenderer } from '../WebGLRenderer';\nimport { mapWebGLBlendModesToPixi } from './mapWebGLBlendModesToPixi';\n\nimport type { BLEND_MODES } from '../../shared/state/const';\nimport type { System } from '../../shared/system/System';\nimport type { GlRenderingContext } from '../context/GlRenderingContext';\n\nconst BLEND = 0;\nconst OFFSET = 1;\nconst CULLING = 2;\nconst DEPTH_TEST = 3;\nconst WINDING = 4;\nconst DEPTH_MASK = 5;\n\n/**\n * System plugin to the renderer to manage WebGL state machines\n * @category rendering\n * @advanced\n */\nexport class GlStateSystem implements System\n{\n    /** @ignore */\n    public static extension = {\n        type: [\n            ExtensionType.WebGLSystem,\n        ],\n        name: 'state',\n    } as const;\n\n    /**\n     * State ID\n     * @readonly\n     */\n    public stateId: number;\n\n    /**\n     * Polygon offset\n     * @readonly\n     */\n    public polygonOffset: number;\n\n    /**\n     * Blend mode\n     * @default 'none'\n     * @readonly\n     */\n    public blendMode: BLEND_MODES;\n\n    /** Whether current blend equation is different */\n    protected _blendEq: boolean;\n\n    /**\n     * GL context\n     * @type {WebGLRenderingContext}\n     * @readonly\n     */\n    protected gl: GlRenderingContext;\n\n    protected blendModesMap: Record<BLEND_MODES, number[]>;\n\n    /**\n     * Collection of calls\n     * @type {Function[]}\n     */\n    protected readonly map: ((value: boolean) => void)[];\n\n    /**\n     * Collection of check calls\n     * @type {Function[]}\n     */\n    protected readonly checks: ((system: this, state: State) => void)[];\n\n    /**\n     * Default WebGL State\n     * @readonly\n     */\n    protected defaultState: State;\n\n    /**\n     * Whether to invert the front face when rendering\n     * This is used for render textures where the Y-coordinate is flipped\n     * @default false\n     */\n    private _invertFrontFace: boolean = false;\n    private _glFrontFace: boolean;\n    private _cullFace: boolean;\n    private _frontFaceDirty: boolean;\n    private _frontFace: boolean;\n\n    constructor(renderer: WebGLRenderer)\n    {\n        this.gl = null;\n\n        this.stateId = 0;\n        this.polygonOffset = 0;\n        this.blendMode = 'none';\n\n        this._blendEq = false;\n\n        // map functions for when we set state..\n        this.map = [];\n        this.map[BLEND] = this.setBlend;\n        this.map[OFFSET] = this.setOffset;\n        this.map[CULLING] = this.setCullFace;\n        this.map[DEPTH_TEST] = this.setDepthTest;\n        this.map[WINDING] = this.setFrontFace;\n        this.map[DEPTH_MASK] = this.setDepthMask;\n\n        this.checks = [];\n\n        this.defaultState = State.for2d();\n\n        // listen for when the renderTarget changes\n        // as rendering to textures means we need to invert the front face\n        renderer.renderTarget.onRenderTargetChange.add(this);\n    }\n\n    protected onRenderTargetChange(renderTarget: RenderTarget)\n    {\n        this._invertFrontFace = !renderTarget.isRoot;\n\n        // mini optimization to avoid setting the front face if culling is disabled\n        if (this._cullFace)\n        {\n            // need to set the front face to the requested value as it matters because of the culling is active!\n            this.setFrontFace(this._frontFace);\n        }\n        else\n        {\n            // if culling is disabled, we need to set the front face dirty\n            this._frontFaceDirty = true;\n        }\n    }\n\n    protected contextChange(gl: GlRenderingContext): void\n    {\n        this.gl = gl;\n\n        this.blendModesMap = mapWebGLBlendModesToPixi(gl);\n\n        // Reset face culling variables\n\n        this.resetState();\n    }\n\n    /**\n     * Sets the current state\n     * @param {*} state - The state to set.\n     */\n    public set(state: State): void\n    {\n        state ||= this.defaultState;\n\n        // TODO maybe to an object check? ( this.state === state )?\n        if (this.stateId !== state.data)\n        {\n            let diff = this.stateId ^ state.data;\n            let i = 0;\n\n            // order from least to most common\n            while (diff)\n            {\n                if (diff & 1)\n                {\n                    // state change!\n                    this.map[i].call(this, !!(state.data & (1 << i)));\n                }\n\n                diff >>= 1;\n                i++;\n            }\n\n            this.stateId = state.data;\n        }\n\n        // based on the above settings we check for specific modes..\n        // for example if blend is active we check and set the blend modes\n        // or of polygon offset is active we check the poly depth.\n        for (let i = 0; i < this.checks.length; i++)\n        {\n            this.checks[i](this, state);\n        }\n    }\n\n    /**\n     * Sets the state, when previous state is unknown.\n     * @param {*} state - The state to set\n     */\n    public forceState(state: State): void\n    {\n        state ||= this.defaultState;\n        for (let i = 0; i < this.map.length; i++)\n        {\n            this.map[i].call(this, !!(state.data & (1 << i)));\n        }\n        for (let i = 0; i < this.checks.length; i++)\n        {\n            this.checks[i](this, state);\n        }\n\n        this.stateId = state.data;\n    }\n\n    /**\n     * Sets whether to enable or disable blending.\n     * @param value - Turn on or off WebGl blending.\n     */\n    public setBlend(value: boolean): void\n    {\n        this._updateCheck(GlStateSystem._checkBlendMode, value);\n\n        this.gl[value ? 'enable' : 'disable'](this.gl.BLEND);\n    }\n\n    /**\n     * Sets whether to enable or disable polygon offset fill.\n     * @param value - Turn on or off webgl polygon offset testing.\n     */\n    public setOffset(value: boolean): void\n    {\n        this._updateCheck(GlStateSystem._checkPolygonOffset, value);\n\n        this.gl[value ? 'enable' : 'disable'](this.gl.POLYGON_OFFSET_FILL);\n    }\n\n    /**\n     * Sets whether to enable or disable depth test.\n     * @param value - Turn on or off webgl depth testing.\n     */\n    public setDepthTest(value: boolean): void\n    {\n        this.gl[value ? 'enable' : 'disable'](this.gl.DEPTH_TEST);\n    }\n\n    /**\n     * Sets whether to enable or disable depth mask.\n     * @param value - Turn on or off webgl depth mask.\n     */\n    public setDepthMask(value: boolean): void\n    {\n        this.gl.depthMask(value);\n    }\n\n    /**\n     * Sets whether to enable or disable cull face.\n     * @param {boolean} value - Turn on or off webgl cull face.\n     */\n    public setCullFace(value: boolean): void\n    {\n        this._cullFace = value;\n        this.gl[value ? 'enable' : 'disable'](this.gl.CULL_FACE);\n\n        if (this._cullFace && this._frontFaceDirty)\n        {\n            // need to set the front face to the requested value as it matters because of the culling is active!\n            this.setFrontFace(this._frontFace);\n        }\n    }\n\n    /**\n     * Sets the gl front face.\n     * @param {boolean} value - true is clockwise and false is counter-clockwise\n     */\n    public setFrontFace(value: boolean): void\n    {\n        this._frontFace = value;\n        this._frontFaceDirty = false;\n        // If invertFrontFace is true, we invert the face direction\n        const faceMode = this._invertFrontFace ? !value : value;\n\n        if (this._glFrontFace !== faceMode)\n        {\n            this._glFrontFace = faceMode;\n            this.gl.frontFace(this.gl[faceMode ? 'CW' : 'CCW']);\n        }\n    }\n\n    /**\n     * Sets the blend mode.\n     * @param {number} value - The blend mode to set to.\n     */\n    public setBlendMode(value: BLEND_MODES): void\n    {\n        if (!this.blendModesMap[value])\n        {\n            value = 'normal';\n        }\n\n        if (value === this.blendMode)\n        {\n            return;\n        }\n\n        this.blendMode = value;\n\n        const mode = this.blendModesMap[value];\n        const gl = this.gl;\n\n        if (mode.length === 2)\n        {\n            gl.blendFunc(mode[0], mode[1]);\n        }\n        else\n        {\n            gl.blendFuncSeparate(mode[0], mode[1], mode[2], mode[3]);\n        }\n\n        if (mode.length === 6)\n        {\n            this._blendEq = true;\n            gl.blendEquationSeparate(mode[4], mode[5]);\n        }\n        else if (this._blendEq)\n        {\n            this._blendEq = false;\n            gl.blendEquationSeparate(gl.FUNC_ADD, gl.FUNC_ADD);\n        }\n    }\n\n    /**\n     * Sets the polygon offset.\n     * @param {number} value - the polygon offset\n     * @param {number} scale - the polygon offset scale\n     */\n    public setPolygonOffset(value: number, scale: number): void\n    {\n        this.gl.polygonOffset(value, scale);\n    }\n\n    /** Resets all the logic and disables the VAOs. */\n    public resetState(): void\n    {\n        this._glFrontFace = false;\n        this._frontFace = false;\n        this._cullFace = false;\n        this._frontFaceDirty = false;\n        this._invertFrontFace = false;\n\n        this.gl.frontFace(this.gl.CCW);\n        this.gl.pixelStorei(this.gl.UNPACK_FLIP_Y_WEBGL, false);\n\n        this.forceState(this.defaultState);\n\n        this._blendEq = true;\n        // setting to '' means the blend mode will be set as soon as we set the first blend mode when rendering!\n        this.blendMode = '' as BLEND_MODES;\n        this.setBlendMode('normal');\n    }\n\n    /**\n     * Checks to see which updates should be checked based on which settings have been activated.\n     *\n     * For example, if blend is enabled then we should check the blend modes each time the state is changed\n     * or if polygon fill is activated then we need to check if the polygon offset changes.\n     * The idea is that we only check what we have too.\n     * @param func - the checking function to add or remove\n     * @param value - should the check function be added or removed.\n     */\n    private _updateCheck(func: (system: this, state: State) => void, value: boolean): void\n    {\n        const index = this.checks.indexOf(func);\n\n        if (value && index === -1)\n        {\n            this.checks.push(func);\n        }\n        else if (!value && index !== -1)\n        {\n            this.checks.splice(index, 1);\n        }\n    }\n\n    /**\n     * A private little wrapper function that we call to check the blend mode.\n     * @param system - the System to perform the state check on\n     * @param state - the state that the blendMode will pulled from\n     */\n    private static _checkBlendMode(system: GlStateSystem, state: State): void\n    {\n        system.setBlendMode(state.blendMode);\n    }\n\n    /**\n     * A private little wrapper function that we call to check the polygon offset.\n     * @param system - the System to perform the state check on\n     * @param state - the state that the blendMode will pulled from\n     */\n    private static _checkPolygonOffset(system: GlStateSystem, state: State): void\n    {\n        system.setPolygonOffset(1, state.polygonOffset);\n    }\n\n    /** @ignore */\n    public destroy(): void\n    {\n        this.gl = null;\n        this.checks.length = 0;\n    }\n}\n", "import { GL_FORMATS, GL_TARGETS, GL_TYPES } from './const';\n\n/**\n * Internal texture for WebGL context\n * @category rendering\n * @ignore\n */\nexport class GlTexture\n{\n    public target: GL_TARGETS = GL_TARGETS.TEXTURE_2D;\n\n    /** The WebGL texture. */\n    public texture: WebGLTexture;\n\n    /** Width of texture that was used in texImage2D. */\n    public width: number;\n\n    /** Height of texture that was used in texImage2D. */\n    public height: number;\n\n    /** Whether mip levels has to be generated. */\n    public mipmap: boolean;\n\n    /** Type copied from texture source. */\n    public type: number;\n\n    /** Type copied from texture source. */\n    public internalFormat: number;\n\n    /** Type of sampler corresponding to this texture. See {@link SAMPLER_TYPES} */\n    public samplerType: number;\n\n    public format: GL_FORMATS;\n\n    constructor(texture: WebGLTexture)\n    {\n        this.texture = texture;\n        this.width = -1;\n        this.height = -1;\n        this.type = GL_TYPES.UNSIGNED_BYTE;\n        this.internalFormat = GL_FORMATS.RGBA;\n        this.format = GL_FORMATS.RGBA;\n        this.samplerType = 0;\n    }\n}\n", "import type { TextureSource } from '../../../shared/texture/sources/TextureSource';\nimport type { GlRenderingContext } from '../../context/GlRenderingContext';\nimport type { GlTexture } from '../GlTexture';\nimport type { GLTextureUploader } from './GLTextureUploader';\n\n/** @internal */\nexport const glUploadBufferImageResource = {\n\n    id: 'buffer',\n\n    upload(source: TextureSource, glTexture: GlTexture, gl: GlRenderingContext)\n    {\n        if (glTexture.width === source.width || glTexture.height === source.height)\n        {\n            gl.texSubImage2D(\n                gl.TEXTURE_2D,\n                0,\n                0,\n                0,\n                source.width,\n                source.height,\n                glTexture.format,\n                glTexture.type,\n                source.resource\n            );\n        }\n        else\n        {\n            gl.texImage2D(\n                glTexture.target,\n                0,\n                glTexture.internalFormat,\n                source.width,\n                source.height,\n                0,\n                glTexture.format,\n                glTexture.type,\n                source.resource\n            );\n        }\n\n        glTexture.width = source.width;\n        glTexture.height = source.height;\n    }\n} as GLTextureUploader;\n\n", "import type { CompressedSource } from '../../../shared/texture/sources/CompressedSource';\nimport type { GlRenderingContext } from '../../context/GlRenderingContext';\nimport type { GlTexture } from '../GlTexture';\nimport type { GLTextureUploader } from './GLTextureUploader';\n\nconst compressedFormatMap: Record<string, boolean> = {\n    'bc1-rgba-unorm': true,\n    'bc1-rgba-unorm-srgb': true,\n    'bc2-rgba-unorm': true,\n    'bc2-rgba-unorm-srgb': true,\n    'bc3-rgba-unorm': true,\n    'bc3-rgba-unorm-srgb': true,\n    'bc4-r-unorm': true,\n    'bc4-r-snorm': true,\n    'bc5-rg-unorm': true,\n    'bc5-rg-snorm': true,\n    'bc6h-rgb-ufloat': true,\n    'bc6h-rgb-float': true,\n    'bc7-rgba-unorm': true,\n    'bc7-rgba-unorm-srgb': true,\n\n    // ETC2 compressed formats usable if \"texture-compression-etc2\" is both\n    // supported by the device/user agent and enabled in requestDevice.\n    'etc2-rgb8unorm': true,\n    'etc2-rgb8unorm-srgb': true,\n    'etc2-rgb8a1unorm': true,\n    'etc2-rgb8a1unorm-srgb': true,\n    'etc2-rgba8unorm': true,\n    'etc2-rgba8unorm-srgb': true,\n    'eac-r11unorm': true,\n    'eac-r11snorm': true,\n    'eac-rg11unorm': true,\n    'eac-rg11snorm': true,\n\n    // ASTC compressed formats usable if \"texture-compression-astc\" is both\n    // supported by the device/user agent and enabled in requestDevice.\n    'astc-4x4-unorm': true,\n    'astc-4x4-unorm-srgb': true,\n    'astc-5x4-unorm': true,\n    'astc-5x4-unorm-srgb': true,\n    'astc-5x5-unorm': true,\n    'astc-5x5-unorm-srgb': true,\n    'astc-6x5-unorm': true,\n    'astc-6x5-unorm-srgb': true,\n    'astc-6x6-unorm': true,\n    'astc-6x6-unorm-srgb': true,\n    'astc-8x5-unorm': true,\n    'astc-8x5-unorm-srgb': true,\n    'astc-8x6-unorm': true,\n    'astc-8x6-unorm-srgb': true,\n    'astc-8x8-unorm': true,\n    'astc-8x8-unorm-srgb': true,\n    'astc-10x5-unorm': true,\n    'astc-10x5-unorm-srgb': true,\n    'astc-10x6-unorm': true,\n    'astc-10x6-unorm-srgb': true,\n    'astc-10x8-unorm': true,\n    'astc-10x8-unorm-srgb': true,\n    'astc-10x10-unorm': true,\n    'astc-10x10-unorm-srgb': true,\n    'astc-12x10-unorm': true,\n    'astc-12x10-unorm-srgb': true,\n    'astc-12x12-unorm': true,\n    'astc-12x12-unorm-srgb': true,\n};\n\n/** @internal */\nexport const glUploadCompressedTextureResource = {\n\n    id: 'compressed',\n\n    upload(source: CompressedSource, glTexture: GlTexture, gl: GlRenderingContext)\n    {\n        gl.pixelStorei(gl.UNPACK_ALIGNMENT, 4);\n\n        let mipWidth = source.pixelWidth;\n        let mipHeight = source.pixelHeight;\n\n        const compressed = !!compressedFormatMap[source.format];\n\n        for (let i = 0; i < source.resource.length; i++)\n        {\n            const levelBuffer = source.resource[i];\n\n            if (compressed)\n            {\n                gl.compressedTexImage2D(\n                    gl.TEXTURE_2D, i, glTexture.internalFormat,\n                    mipWidth, mipHeight, 0,\n                    levelBuffer\n                );\n            }\n            else\n            {\n                gl.texImage2D(\n                    gl.TEXTURE_2D, i, glTexture.internalFormat,\n                    mipWidth, mipHeight, 0,\n                    glTexture.format, glTexture.type,\n                    levelBuffer);\n            }\n\n            mipWidth = Math.max(mipWidth >> 1, 1);\n            mipHeight = Math.max(mipHeight >> 1, 1);\n        }\n    }\n} as GLTextureUploader;\n\n", "import type { CanvasSource } from '../../../shared/texture/sources/CanvasSource';\nimport type { ImageSource } from '../../../shared/texture/sources/ImageSource';\nimport type { GlRenderingContext } from '../../context/GlRenderingContext';\nimport type { GlTexture } from '../GlTexture';\nimport type { GLTextureUploader } from './GLTextureUploader';\n\n/** @internal */\nexport const glUploadImageResource = {\n\n    id: 'image',\n\n    upload(source: ImageSource | CanvasSource, glTexture: GlTexture, gl: GlRenderingContext, webGLVersion: number)\n    {\n        const glWidth = glTexture.width;\n        const glHeight = glTexture.height;\n\n        const textureWidth = source.pixelWidth;\n        const textureHeight = source.pixelHeight;\n\n        const resourceWidth = source.resourceWidth;\n        const resourceHeight = source.resourceHeight;\n\n        if (resourceWidth < textureWidth || resourceHeight < textureHeight)\n        {\n            if (glWidth !== textureWidth || glHeight !== textureHeight)\n            {\n                gl.texImage2D(\n                    glTexture.target,\n                    0,\n                    glTexture.internalFormat,\n                    textureWidth,\n                    textureHeight,\n                    0,\n                    glTexture.format,\n                    glTexture.type,\n                    null\n                );\n            }\n\n            if (webGLVersion === 2)\n            {\n                gl.texSubImage2D(\n                    gl.TEXTURE_2D,\n                    0,\n                    0,\n                    0,\n                    resourceWidth,\n                    resourceHeight,\n                    glTexture.format,\n                    glTexture.type,\n                    source.resource as TexImageSource\n                );\n            }\n            else\n            {\n                gl.texSubImage2D(\n                    gl.TEXTURE_2D,\n                    0,\n                    0,\n                    0,\n                    glTexture.format,\n                    glTexture.type,\n                    source.resource as TexImageSource\n                );\n            }\n        }\n        else if (glWidth === textureWidth && glHeight === textureHeight)\n        {\n            gl.texSubImage2D(\n                gl.TEXTURE_2D,\n                0,\n                0,\n                0,\n                glTexture.format,\n                glTexture.type,\n                source.resource as TexImageSource\n            );\n        }\n        else if (webGLVersion === 2)\n        {\n            gl.texImage2D(\n                glTexture.target,\n                0,\n                glTexture.internalFormat,\n                textureWidth,\n                textureHeight,\n                0,\n                glTexture.format,\n                glTexture.type,\n                source.resource as TexImageSource\n            );\n        }\n        else\n        {\n            gl.texImage2D(\n                glTexture.target,\n                0,\n                glTexture.internalFormat,\n                glTexture.format,\n                glTexture.type,\n                source.resource as TexImageSource\n            );\n        }\n\n        glTexture.width = textureWidth;\n        glTexture.height = textureHeight;\n    }\n} as GLTextureUploader;\n\n", "import { glUploadImageResource } from './glUploadImageResource';\n\nimport type { VideoSource } from '../../../shared/texture/sources/VideoSource';\nimport type { GlRenderingContext } from '../../context/GlRenderingContext';\nimport type { GlTexture } from '../GlTexture';\nimport type { GLTextureUploader } from './GLTextureUploader';\n\n/** @internal */\nexport const glUploadVideoResource = {\n\n    id: 'video',\n\n    upload(source: VideoSource, glTexture: GlTexture, gl: GlRenderingContext, webGLVersion: number)\n    {\n        if (!source.isValid)\n        {\n            gl.texImage2D(\n                glTexture.target,\n                0,\n                glTexture.internalFormat,\n                1,\n                1,\n                0,\n                glTexture.format,\n                glTexture.type,\n                null\n            );\n\n            return;\n        }\n\n        glUploadImageResource.upload(source, glTexture, gl, webGLVersion);\n    }\n} as GLTextureUploader;\n\n", "/** @internal */\nexport const scaleModeToGlFilter = {\n    linear: 9729,\n    nearest: 9728,\n};\n\n/** @internal */\nexport const mipmapScaleModeToGlFilter = {\n    linear: {\n        linear: 9987,\n        nearest: 9985,\n    },\n    nearest: {\n        linear: 9986,\n        nearest: 9984,\n    }\n};\n\n/** @internal */\nexport const wrapModeToGlAddress = {\n    'clamp-to-edge': 33071,\n    repeat: 10497,\n    'mirror-repeat': 33648,\n};\n\n/** @internal */\nexport const compareModeToGlCompare = {\n    never: 512,\n    less: 513,\n    equal: 514,\n    'less-equal': 515,\n    greater: 516,\n    'not-equal': 517,\n    'greater-equal': 518,\n    always: 519,\n};\n\n", "import {\n    compareModeToGlCompare,\n    mipmapScaleModeToGlFilter,\n    scaleModeToGlFilter,\n    wrapModeToGlAddress\n} from './pixiToGlMaps';\n\nimport type { TextureStyle } from '../../../shared/texture/TextureStyle';\n\n/**\n * @param style\n * @param gl\n * @param mipmaps\n * @param anisotropicExt\n * @param glFunctionName\n * @param firstParam\n * @param forceClamp\n * @param firstCreation\n * @internal\n */\nexport function applyStyleParams(\n    style: TextureStyle,\n    gl: WebGL2RenderingContext,\n    mipmaps: boolean,\n    // eslint-disable-next-line camelcase\n    anisotropicExt: EXT_texture_filter_anisotropic,\n    glFunctionName: 'samplerParameteri' | 'texParameteri',\n    firstParam: 3553 | WebGLSampler,\n    forceClamp: boolean,\n    /** if true we can skip setting certain values if the values is the same as the default gl values */\n    firstCreation: boolean\n)\n{\n    const castParam = firstParam as 3553;\n\n    if (!firstCreation\n        || style.addressModeU !== 'repeat'\n        || style.addressModeV !== 'repeat'\n        || style.addressModeW !== 'repeat'\n    )\n    {\n        // 1. set the wrapping mode\n        const wrapModeS = wrapModeToGlAddress[forceClamp ? 'clamp-to-edge' : style.addressModeU];\n        const wrapModeT = wrapModeToGlAddress[forceClamp ? 'clamp-to-edge' : style.addressModeV];\n        const wrapModeR = wrapModeToGlAddress[forceClamp ? 'clamp-to-edge' : style.addressModeW];\n\n        gl[glFunctionName](castParam, gl.TEXTURE_WRAP_S, wrapModeS);\n        gl[glFunctionName](castParam, gl.TEXTURE_WRAP_T, wrapModeT);\n\n        // does not exist in webGL1\n        if (gl.TEXTURE_WRAP_R) gl[glFunctionName](castParam, gl.TEXTURE_WRAP_R, wrapModeR);\n    }\n\n    if (!firstCreation || style.magFilter !== 'linear')\n    {\n        // 2. set the filtering mode\n        gl[glFunctionName](castParam, gl.TEXTURE_MAG_FILTER, scaleModeToGlFilter[style.magFilter]);\n    }\n\n    // assuming the currently bound texture is the one we want to set the filter for\n    // the only smelly part of this code, WebGPU is much better here :P\n    if (mipmaps)\n    {\n        if (!firstCreation || style.mipmapFilter !== 'linear')\n        {\n            const glFilterMode = mipmapScaleModeToGlFilter[style.minFilter][style.mipmapFilter];\n\n            gl[glFunctionName](castParam, gl.TEXTURE_MIN_FILTER, glFilterMode);\n        }\n    }\n\n    else\n    {\n        gl[glFunctionName](castParam, gl.TEXTURE_MIN_FILTER, scaleModeToGlFilter[style.minFilter]);\n    }\n\n    // 3. set the anisotropy\n    if (anisotropicExt && style.maxAnisotropy > 1)\n    {\n        const level = Math.min(style.maxAnisotropy, gl.getParameter(anisotropicExt.MAX_TEXTURE_MAX_ANISOTROPY_EXT));\n\n        gl[glFunctionName](castParam, anisotropicExt.TEXTURE_MAX_ANISOTROPY_EXT, level);\n    }\n\n    // 4. set the compare mode\n    if (style.compare)\n    {\n        gl[glFunctionName](castParam, gl.TEXTURE_COMPARE_FUNC, compareModeToGlCompare[style.compare]);\n    }\n}\n", "import type { GlRenderingContext } from '../../context/GlRenderingContext';\n\n/**\n * Returns a lookup table that maps each type-format pair to a compatible internal format.\n * @function mapTypeAndFormatToInternalFormat\n * @private\n * @param {WebGLRenderingContext} gl - The rendering context.\n * @returns Lookup table.\n */\nexport function mapFormatToGlFormat(gl: GlRenderingContext): Record<string, number>\n{\n    return {\n        // 8-bit formats\n        r8unorm: gl.RED,\n        r8snorm: gl.RED,\n        r8uint: gl.RED,\n        r8sint: gl.RED,\n\n        // 16-bit formats\n        r16uint: gl.RED,\n        r16sint:    gl.RED,\n        r16float: gl.RED,\n        rg8unorm:  gl.RG,\n        rg8snorm:   gl.RG,\n        rg8uint:  gl.RG,\n        rg8sint:  gl.RG,\n\n        // 32-bit formats\n        r32uint: gl.RED,\n        r32sint: gl.RED,\n        r32float: gl.RED,\n        rg16uint:   gl.RG,\n        rg16sint:  gl.RG,\n        rg16float:  gl.RG,\n        rgba8unorm: gl.RGBA,\n        'rgba8unorm-srgb': gl.RGBA,\n\n        // Packed 32-bit formats\n        rgba8snorm: gl.RGBA,\n        rgba8uint: gl.RGBA,\n        rgba8sint: gl.RGBA,\n        bgra8unorm: gl.RGBA,\n        'bgra8unorm-srgb': gl.RGBA,\n        rgb9e5ufloat: gl.RGB,\n        rgb10a2unorm: gl.RGBA,\n        rg11b10ufloat: gl.RGB,\n\n        // 64-bit formats\n        rg32uint: gl.RG,\n        rg32sint: gl.RG,\n        rg32float:  gl.RG,\n        rgba16uint: gl.RGBA,\n        rgba16sint: gl.RGBA,\n        rgba16float: gl.RGBA,\n\n        // 128-bit formats\n        rgba32uint: gl.RGBA,\n        rgba32sint: gl.RGBA,\n        rgba32float: gl.RGBA,\n\n        // Depth/stencil formats\n        stencil8: gl.STENCIL_INDEX8,\n        depth16unorm: gl.DEPTH_COMPONENT,\n        depth24plus: gl.DEPTH_COMPONENT,\n        'depth24plus-stencil8': gl.DEPTH_STENCIL,\n        depth32float: gl.DEPTH_COMPONENT,\n        'depth32float-stencil8': gl.DEPTH_STENCIL,\n\n    };\n}\n", "import { DOMAdapter } from '../../../../../environment/adapter';\n\nimport type { GlRenderingContext } from '../../context/GlRenderingContext';\nimport type { WebGLExtensions } from '../../context/WebGLExtensions';\n\n/**\n * Returns a lookup table that maps each type-format pair to a compatible internal format.\n * @function mapTypeAndFormatToInternalFormat\n * @private\n * @param gl - The rendering context.\n * @param extensions - The WebGL extensions.\n * @returns Lookup table.\n */\nexport function mapFormatToGlInternalFormat(\n    gl: GlRenderingContext,\n    extensions: WebGLExtensions,\n): Record<string, number>\n{\n    let srgb = {};\n    let bgra8unorm: number = gl.RGBA;\n\n    if (!(gl instanceof DOMAdapter.get().getWebGLRenderingContext()))\n    {\n        srgb = {\n            'rgba8unorm-srgb': gl.SRGB8_ALPHA8,\n            'bgra8unorm-srgb': gl.SRGB8_ALPHA8,\n        };\n\n        bgra8unorm = gl.RGBA8;\n    }\n    else if (extensions.srgb)\n    {\n        srgb = {\n            'rgba8unorm-srgb': extensions.srgb.SRGB8_ALPHA8_EXT,\n            'bgra8unorm-srgb': extensions.srgb.SRGB8_ALPHA8_EXT,\n        };\n    }\n\n    return {\n        // 8-bit formats\n        r8unorm: gl.R8,\n        r8snorm: gl.R8_SNORM,\n        r8uint: gl.R8UI,\n        r8sint: gl.R8I,\n\n        // 16-bit formats\n        r16uint: gl.R16UI,\n        r16sint: gl.R16I,\n        r16float: gl.R16F,\n        rg8unorm: gl.RG8,\n        rg8snorm: gl.RG8_SNORM,\n        rg8uint: gl.RG8UI,\n        rg8sint: gl.RG8I,\n\n        // 32-bit formats\n        r32uint: gl.R32UI,\n        r32sint: gl.R32I,\n        r32float: gl.R32F,\n        rg16uint: gl.RG16UI,\n        rg16sint: gl.RG16I,\n        rg16float: gl.RG16F,\n        rgba8unorm: gl.RGBA,\n\n        ...srgb,\n\n        // Packed 32-bit formats\n        rgba8snorm: gl.RGBA8_SNORM,\n        rgba8uint: gl.RGBA8UI,\n        rgba8sint: gl.RGBA8I,\n        bgra8unorm,\n        rgb9e5ufloat: gl.RGB9_E5,\n        rgb10a2unorm: gl.RGB10_A2,\n        rg11b10ufloat: gl.R11F_G11F_B10F,\n\n        // 64-bit formats\n        rg32uint: gl.RG32UI,\n        rg32sint: gl.RG32I,\n        rg32float: gl.RG32F,\n        rgba16uint: gl.RGBA16UI,\n        rgba16sint: gl.RGBA16I,\n        rgba16float: gl.RGBA16F,\n\n        // 128-bit formats\n        rgba32uint: gl.RGBA32UI,\n        rgba32sint: gl.RGBA32I,\n        rgba32float: gl.RGBA32F,\n\n        // Depth/stencil formats\n        stencil8: gl.STENCIL_INDEX8,\n        depth16unorm: gl.DEPTH_COMPONENT16,\n        depth24plus: gl.DEPTH_COMPONENT24,\n        'depth24plus-stencil8': gl.DEPTH24_STENCIL8,\n        depth32float: gl.DEPTH_COMPONENT32F,\n        'depth32float-stencil8': gl.DEPTH32F_STENCIL8,\n\n        // Compressed formats\n        ...extensions.s3tc ? {\n            'bc1-rgba-unorm': extensions.s3tc.COMPRESSED_RGBA_S3TC_DXT1_EXT,\n            'bc2-rgba-unorm': extensions.s3tc.COMPRESSED_RGBA_S3TC_DXT3_EXT,\n            'bc3-rgba-unorm': extensions.s3tc.COMPRESSED_RGBA_S3TC_DXT5_EXT,\n        } : {},\n        ...extensions.s3tc_sRGB ? {\n            'bc1-rgba-unorm-srgb': extensions.s3tc_sRGB.COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT,\n            'bc2-rgba-unorm-srgb': extensions.s3tc_sRGB.COMPRESSED_SRGB_ALPHA_S3TC_DXT3_EXT,\n            'bc3-rgba-unorm-srgb': extensions.s3tc_sRGB.COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT,\n        } : {},\n        ...extensions.rgtc ? {\n            'bc4-r-unorm': extensions.rgtc.COMPRESSED_RED_RGTC1_EXT,\n            'bc4-r-snorm': extensions.rgtc.COMPRESSED_SIGNED_RED_RGTC1_EXT,\n            'bc5-rg-unorm': extensions.rgtc.COMPRESSED_RED_GREEN_RGTC2_EXT,\n            'bc5-rg-snorm': extensions.rgtc.COMPRESSED_SIGNED_RED_GREEN_RGTC2_EXT,\n        } : {},\n        ...extensions.bptc ? {\n            'bc6h-rgb-float': extensions.bptc.COMPRESSED_RGB_BPTC_SIGNED_FLOAT_EXT,\n            'bc6h-rgb-ufloat': extensions.bptc.COMPRESSED_RGB_BPTC_UNSIGNED_FLOAT_EXT,\n            'bc7-rgba-unorm': extensions.bptc.COMPRESSED_RGBA_BPTC_UNORM_EXT,\n            'bc7-rgba-unorm-srgb': extensions.bptc.COMPRESSED_SRGB_ALPHA_BPTC_UNORM_EXT,\n        } : {},\n        ...extensions.etc ? {\n            'etc2-rgb8unorm': extensions.etc.COMPRESSED_RGB8_ETC2,\n            'etc2-rgb8unorm-srgb': extensions.etc.COMPRESSED_SRGB8_ETC2,\n            'etc2-rgb8a1unorm': extensions.etc.COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2,\n            'etc2-rgb8a1unorm-srgb': extensions.etc.COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2,\n            'etc2-rgba8unorm': extensions.etc.COMPRESSED_RGBA8_ETC2_EAC,\n            'etc2-rgba8unorm-srgb': extensions.etc.COMPRESSED_SRGB8_ALPHA8_ETC2_EAC,\n            'eac-r11unorm': extensions.etc.COMPRESSED_R11_EAC,\n            // 'eac-r11snorm'\n            'eac-rg11unorm': extensions.etc.COMPRESSED_SIGNED_RG11_EAC,\n            // 'eac-rg11snorm'\n        } : {},\n        ...extensions.astc ? {\n            'astc-4x4-unorm': extensions.astc.COMPRESSED_RGBA_ASTC_4x4_KHR,\n            'astc-4x4-unorm-srgb': extensions.astc.COMPRESSED_SRGB8_ALPHA8_ASTC_4x4_KHR,\n            'astc-5x4-unorm': extensions.astc.COMPRESSED_RGBA_ASTC_5x4_KHR,\n            'astc-5x4-unorm-srgb': extensions.astc.COMPRESSED_SRGB8_ALPHA8_ASTC_5x4_KHR,\n            'astc-5x5-unorm': extensions.astc.COMPRESSED_RGBA_ASTC_5x5_KHR,\n            'astc-5x5-unorm-srgb': extensions.astc.COMPRESSED_SRGB8_ALPHA8_ASTC_5x5_KHR,\n            'astc-6x5-unorm': extensions.astc.COMPRESSED_RGBA_ASTC_6x5_KHR,\n            'astc-6x5-unorm-srgb': extensions.astc.COMPRESSED_SRGB8_ALPHA8_ASTC_6x5_KHR,\n            'astc-6x6-unorm': extensions.astc.COMPRESSED_RGBA_ASTC_6x6_KHR,\n            'astc-6x6-unorm-srgb': extensions.astc.COMPRESSED_SRGB8_ALPHA8_ASTC_6x6_KHR,\n            'astc-8x5-unorm': extensions.astc.COMPRESSED_RGBA_ASTC_8x5_KHR,\n            'astc-8x5-unorm-srgb': extensions.astc.COMPRESSED_SRGB8_ALPHA8_ASTC_8x5_KHR,\n            'astc-8x6-unorm':   extensions.astc.COMPRESSED_RGBA_ASTC_8x6_KHR,\n            'astc-8x6-unorm-srgb': extensions.astc.COMPRESSED_SRGB8_ALPHA8_ASTC_8x6_KHR,\n            'astc-8x8-unorm': extensions.astc.COMPRESSED_RGBA_ASTC_8x8_KHR,\n            'astc-8x8-unorm-srgb': extensions.astc.COMPRESSED_SRGB8_ALPHA8_ASTC_8x8_KHR,\n            'astc-10x5-unorm': extensions.astc.COMPRESSED_RGBA_ASTC_10x5_KHR,\n            'astc-10x5-unorm-srgb': extensions.astc.COMPRESSED_SRGB8_ALPHA8_ASTC_10x5_KHR,\n            'astc-10x6-unorm': extensions.astc.COMPRESSED_RGBA_ASTC_10x6_KHR,\n            'astc-10x6-unorm-srgb': extensions.astc.COMPRESSED_SRGB8_ALPHA8_ASTC_10x6_KHR,\n            'astc-10x8-unorm': extensions.astc.COMPRESSED_RGBA_ASTC_10x8_KHR,\n            'astc-10x8-unorm-srgb': extensions.astc.COMPRESSED_SRGB8_ALPHA8_ASTC_10x8_KHR,\n            'astc-10x10-unorm': extensions.astc.COMPRESSED_RGBA_ASTC_10x10_KHR,\n            'astc-10x10-unorm-srgb': extensions.astc.COMPRESSED_SRGB8_ALPHA8_ASTC_10x10_KHR,\n            'astc-12x10-unorm': extensions.astc.COMPRESSED_RGBA_ASTC_12x10_KHR,\n            'astc-12x10-unorm-srgb': extensions.astc.COMPRESSED_SRGB8_ALPHA8_ASTC_12x10_KHR,\n            'astc-12x12-unorm': extensions.astc.COMPRESSED_RGBA_ASTC_12x12_KHR,\n            'astc-12x12-unorm-srgb': extensions.astc.COMPRESSED_SRGB8_ALPHA8_ASTC_12x12_KHR,\n        } : {},\n    };\n}\n", "import type { GlRenderingContext } from '../../context/GlRenderingContext';\n\n/**\n * Returns a lookup table that maps each type-format pair to a compatible internal format.\n * @function mapTypeAndFormatToInternalFormat\n * @private\n * @param {WebGLRenderingContext} gl - The rendering context.\n * @returns Lookup table.\n */\nexport function mapFormatToGlType(gl: GlRenderingContext): Record<string, number>\n{\n    return {\n        // 8-bit formats\n        r8unorm: gl.UNSIGNED_BYTE,\n        r8snorm: gl.BYTE,\n        r8uint: gl.UNSIGNED_BYTE,\n        r8sint: gl.BYTE,\n\n        // 16-bit formats\n        r16uint: gl.UNSIGNED_SHORT,\n        r16sint: gl.SHORT,\n        r16float: gl.HALF_FLOAT,\n        rg8unorm: gl.UNSIGNED_BYTE,\n        rg8snorm: gl.BYTE,\n        rg8uint: gl.UNSIGNED_BYTE,\n        rg8sint: gl.BYTE,\n\n        // 32-bit formats\n        r32uint: gl.UNSIGNED_INT,\n        r32sint: gl.INT,\n        r32float: gl.FLOAT,\n        rg16uint: gl.UNSIGNED_SHORT,\n        rg16sint: gl.SHORT,\n        rg16float: gl.HALF_FLOAT,\n        rgba8unorm: gl.UNSIGNED_BYTE,\n        'rgba8unorm-srgb': gl.UNSIGNED_BYTE,\n\n        // Packed 32-bit formats\n        rgba8snorm: gl.BYTE,\n        rgba8uint: gl.UNSIGNED_BYTE,\n        rgba8sint: gl.BYTE,\n        bgra8unorm: gl.UNSIGNED_BYTE,\n        'bgra8unorm-srgb': gl.UNSIGNED_BYTE,\n        rgb9e5ufloat: gl.UNSIGNED_INT_5_9_9_9_REV,\n        rgb10a2unorm: gl.UNSIGNED_INT_2_10_10_10_REV,\n        rg11b10ufloat: gl.UNSIGNED_INT_10F_11F_11F_REV,\n\n        // 64-bit formats\n        rg32uint: gl.UNSIGNED_INT,\n        rg32sint: gl.INT,\n        rg32float: gl.FLOAT,\n        rgba16uint: gl.UNSIGNED_SHORT,\n        rgba16sint: gl.SHORT,\n        rgba16float: gl.HALF_FLOAT,\n\n        // 128-bit formats\n        rgba32uint: gl.UNSIGNED_INT,\n        rgba32sint: gl.INT,\n        rgba32float: gl.FLOAT,\n\n        // Depth/stencil formats\n        stencil8: gl.UNSIGNED_BYTE,\n        depth16unorm: gl.UNSIGNED_SHORT,\n        depth24plus: gl.UNSIGNED_INT,\n        'depth24plus-stencil8': gl.UNSIGNED_INT_24_8,\n        depth32float: gl.FLOAT,\n        'depth32float-stencil8': gl.FLOAT_32_UNSIGNED_INT_24_8_REV,\n\n    };\n}\n", "/**\n * @param pixels\n * @internal\n */\nexport function unpremultiplyAlpha(pixels: Uint8Array | Uint8ClampedArray): void\n{\n    if (pixels instanceof Uint8ClampedArray)\n    {\n        pixels = new Uint8Array(pixels.buffer);\n    }\n\n    const n = pixels.length;\n\n    for (let i = 0; i < n; i += 4)\n    {\n        const alpha = pixels[i + 3];\n\n        if (alpha !== 0)\n        {\n            const a = 255.001 / alpha;\n\n            pixels[i] = (pixels[i] * a) + 0.5;\n            pixels[i + 1] = (pixels[i + 1] * a) + 0.5;\n            pixels[i + 2] = (pixels[i + 2] * a) + 0.5;\n        }\n    }\n}\n", "import { DOMAdapter } from '../../../../environment/adapter';\nimport { ExtensionType } from '../../../../extensions/Extensions';\nimport { Texture } from '../../shared/texture/Texture';\nimport { GlTexture } from './GlTexture';\nimport { glUploadBufferImageResource } from './uploaders/glUploadBufferImageResource';\nimport { glUploadCompressedTextureResource } from './uploaders/glUploadCompressedTextureResource';\nimport { glUploadImageResource } from './uploaders/glUploadImageResource';\nimport { glUploadVideoResource } from './uploaders/glUploadVideoResource';\nimport { applyStyleParams } from './utils/applyStyleParams';\nimport { mapFormatToGlFormat } from './utils/mapFormatToGlFormat';\nimport { mapFormatToGlInternalFormat } from './utils/mapFormatToGlInternalFormat';\nimport { mapFormatToGlType } from './utils/mapFormatToGlType';\nimport { unpremultiplyAlpha } from './utils/unpremultiplyAlpha';\n\nimport type { ICanvas } from '../../../../environment/canvas/ICanvas';\nimport type { System } from '../../shared/system/System';\nimport type { CanvasGenerator, GetPixelsOutput } from '../../shared/texture/GenerateCanvas';\nimport type { TextureSource } from '../../shared/texture/sources/TextureSource';\nimport type { BindableTexture } from '../../shared/texture/Texture';\nimport type { TextureStyle } from '../../shared/texture/TextureStyle';\nimport type { GlRenderingContext } from '../context/GlRenderingContext';\nimport type { WebGLRenderer } from '../WebGLRenderer';\nimport type { GLTextureUploader } from './uploaders/GLTextureUploader';\n\nconst BYTES_PER_PIXEL = 4;\n\n/**\n * The system for managing textures in WebGL.\n * @category rendering\n * @advanced\n */\nexport class GlTextureSystem implements System, CanvasGenerator\n{\n    /** @ignore */\n    public static extension = {\n        type: [\n            ExtensionType.WebGLSystem,\n        ],\n        name: 'texture',\n    } as const;\n\n    public readonly managedTextures: TextureSource[] = [];\n\n    private readonly _renderer: WebGLRenderer;\n\n    private _glTextures: Record<number, GlTexture> = Object.create(null);\n    private _glSamplers: Record<string, WebGLSampler> = Object.create(null);\n\n    private _boundTextures: TextureSource[] = [];\n    private _activeTextureLocation = -1;\n\n    private _boundSamplers: Record<number, WebGLSampler> = Object.create(null);\n\n    private readonly _uploads: Record<string, GLTextureUploader> = {\n        image: glUploadImageResource,\n        buffer: glUploadBufferImageResource,\n        video: glUploadVideoResource,\n        compressed: glUploadCompressedTextureResource,\n    };\n\n    private _gl: GlRenderingContext;\n    private _mapFormatToInternalFormat: Record<string, number>;\n    private _mapFormatToType: Record<string, number>;\n    private _mapFormatToFormat: Record<string, number>;\n\n    private _premultiplyAlpha = false;\n\n    // TODO - separate samplers will be a cool thing to add, but not right now!\n    private readonly _useSeparateSamplers = false;\n\n    constructor(renderer: WebGLRenderer)\n    {\n        this._renderer = renderer;\n        this._renderer.renderableGC.addManagedHash(this, '_glTextures');\n        this._renderer.renderableGC.addManagedHash(this, '_glSamplers');\n    }\n\n    protected contextChange(gl: GlRenderingContext): void\n    {\n        this._gl = gl;\n\n        if (!this._mapFormatToInternalFormat)\n        {\n            this._mapFormatToInternalFormat = mapFormatToGlInternalFormat(gl, this._renderer.context.extensions);\n\n            this._mapFormatToType = mapFormatToGlType(gl);\n            this._mapFormatToFormat = mapFormatToGlFormat(gl);\n        }\n\n        this._glTextures = Object.create(null);\n        this._glSamplers = Object.create(null);\n        this._boundSamplers = Object.create(null);\n        this._premultiplyAlpha = false;\n\n        for (let i = 0; i < 16; i++)\n        {\n            this.bind(Texture.EMPTY, i);\n        }\n    }\n\n    public initSource(source: TextureSource)\n    {\n        this.bind(source);\n    }\n\n    public bind(texture: BindableTexture, location = 0)\n    {\n        const source = texture.source;\n\n        if (texture)\n        {\n            this.bindSource(source, location);\n\n            if (this._useSeparateSamplers)\n            {\n                this._bindSampler(source.style, location);\n            }\n        }\n        else\n        {\n            this.bindSource(null, location);\n\n            if (this._useSeparateSamplers)\n            {\n                this._bindSampler(null, location);\n            }\n        }\n    }\n\n    public bindSource(source: TextureSource, location = 0): void\n    {\n        const gl = this._gl;\n\n        source._touched = this._renderer.textureGC.count;\n\n        if (this._boundTextures[location] !== source)\n        {\n            this._boundTextures[location] = source;\n            this._activateLocation(location);\n\n            source ||= Texture.EMPTY.source;\n\n            // bind texture and source!\n            const glTexture = this.getGlSource(source);\n\n            gl.bindTexture(glTexture.target, glTexture.texture);\n        }\n    }\n\n    private _bindSampler(style: TextureStyle, location = 0): void\n    {\n        const gl = this._gl;\n\n        if (!style)\n        {\n            this._boundSamplers[location] = null;\n            gl.bindSampler(location, null);\n\n            return;\n        }\n\n        const sampler = this._getGlSampler(style);\n\n        if (this._boundSamplers[location] !== sampler)\n        {\n            this._boundSamplers[location] = sampler;\n            gl.bindSampler(location, sampler);\n        }\n    }\n\n    public unbind(texture: BindableTexture): void\n    {\n        const source = texture.source;\n        const boundTextures = this._boundTextures;\n        const gl = this._gl;\n\n        for (let i = 0; i < boundTextures.length; i++)\n        {\n            if (boundTextures[i] === source)\n            {\n                this._activateLocation(i);\n\n                const glTexture = this.getGlSource(source);\n\n                gl.bindTexture(glTexture.target, null);\n                boundTextures[i] = null;\n            }\n        }\n    }\n\n    private _activateLocation(location: number): void\n    {\n        if (this._activeTextureLocation !== location)\n        {\n            this._activeTextureLocation = location;\n            this._gl.activeTexture(this._gl.TEXTURE0 + location);\n        }\n    }\n\n    private _initSource(source: TextureSource): GlTexture\n    {\n        const gl = this._gl;\n\n        const glTexture = new GlTexture(gl.createTexture());\n\n        glTexture.type = this._mapFormatToType[source.format];\n        glTexture.internalFormat = this._mapFormatToInternalFormat[source.format];\n        glTexture.format = this._mapFormatToFormat[source.format];\n\n        if (source.autoGenerateMipmaps && (this._renderer.context.supports.nonPowOf2mipmaps || source.isPowerOfTwo))\n        {\n            const biggestDimension = Math.max(source.width, source.height);\n\n            source.mipLevelCount = Math.floor(Math.log2(biggestDimension)) + 1;\n        }\n\n        this._glTextures[source.uid] = glTexture;\n\n        if (!this.managedTextures.includes(source))\n        {\n            source.on('update', this.onSourceUpdate, this);\n            source.on('resize', this.onSourceUpdate, this);\n            source.on('styleChange', this.onStyleChange, this);\n            source.on('destroy', this.onSourceDestroy, this);\n            source.on('unload', this.onSourceUnload, this);\n            source.on('updateMipmaps', this.onUpdateMipmaps, this);\n\n            this.managedTextures.push(source);\n        }\n\n        this.onSourceUpdate(source);\n        this.updateStyle(source, false);\n\n        return glTexture;\n    }\n\n    protected onStyleChange(source: TextureSource): void\n    {\n        this.updateStyle(source, false);\n    }\n\n    protected updateStyle(source: TextureSource, firstCreation: boolean): void\n    {\n        const gl = this._gl;\n\n        const glTexture = this.getGlSource(source);\n\n        gl.bindTexture(gl.TEXTURE_2D, glTexture.texture);\n\n        this._boundTextures[this._activeTextureLocation] = source;\n\n        applyStyleParams(\n            source.style,\n            gl,\n            source.mipLevelCount > 1,\n            this._renderer.context.extensions.anisotropicFiltering,\n            'texParameteri',\n            gl.TEXTURE_2D,\n            // will force a clamp to edge if the texture is not a power of two\n            !this._renderer.context.supports.nonPowOf2wrapping && !source.isPowerOfTwo,\n            firstCreation,\n        );\n    }\n\n    protected onSourceUnload(source: TextureSource): void\n    {\n        const glTexture = this._glTextures[source.uid];\n\n        if (!glTexture) return;\n\n        this.unbind(source);\n        this._glTextures[source.uid] = null;\n\n        this._gl.deleteTexture(glTexture.texture);\n    }\n\n    protected onSourceUpdate(source: TextureSource): void\n    {\n        const gl = this._gl;\n\n        const glTexture = this.getGlSource(source);\n\n        gl.bindTexture(gl.TEXTURE_2D, glTexture.texture);\n\n        this._boundTextures[this._activeTextureLocation] = source;\n\n        const premultipliedAlpha = source.alphaMode === 'premultiply-alpha-on-upload';\n\n        if (this._premultiplyAlpha !== premultipliedAlpha)\n        {\n            this._premultiplyAlpha = premultipliedAlpha;\n            gl.pixelStorei(gl.UNPACK_PREMULTIPLY_ALPHA_WEBGL, premultipliedAlpha);\n        }\n\n        if (this._uploads[source.uploadMethodId])\n        {\n            this._uploads[source.uploadMethodId].upload(source, glTexture, gl, this._renderer.context.webGLVersion);\n        }\n        else\n        {\n            // eslint-disable-next-line max-len\n            gl.texImage2D(gl.TEXTURE_2D, 0, gl.RGBA, source.pixelWidth, source.pixelHeight, 0, gl.RGBA, gl.UNSIGNED_BYTE, null);\n        }\n\n        if (source.autoGenerateMipmaps && source.mipLevelCount > 1)\n        {\n            this.onUpdateMipmaps(source, false);\n        }\n    }\n\n    protected onUpdateMipmaps(source: TextureSource, bind = true): void\n    {\n        if (bind) this.bindSource(source, 0);\n\n        const glTexture = this.getGlSource(source);\n\n        this._gl.generateMipmap(glTexture.target);\n    }\n\n    protected onSourceDestroy(source: TextureSource): void\n    {\n        source.off('destroy', this.onSourceDestroy, this);\n        source.off('update', this.onSourceUpdate, this);\n        source.off('resize', this.onSourceUpdate, this);\n        source.off('unload', this.onSourceUnload, this);\n        source.off('styleChange', this.onStyleChange, this);\n        source.off('updateMipmaps', this.onUpdateMipmaps, this);\n\n        this.managedTextures.splice(this.managedTextures.indexOf(source), 1);\n\n        this.onSourceUnload(source);\n    }\n\n    private _initSampler(style: TextureStyle): WebGLSampler\n    {\n        const gl = this._gl;\n\n        const glSampler = this._gl.createSampler();\n\n        this._glSamplers[style._resourceId] = glSampler;\n\n        applyStyleParams(\n            style,\n            gl,\n            this._boundTextures[this._activeTextureLocation].mipLevelCount > 1,\n            this._renderer.context.extensions.anisotropicFiltering,\n            'samplerParameteri',\n            glSampler,\n            false,\n            true,\n        );\n\n        return this._glSamplers[style._resourceId];\n    }\n\n    private _getGlSampler(sampler: TextureStyle): WebGLSampler\n    {\n        return this._glSamplers[sampler._resourceId] || this._initSampler(sampler);\n    }\n\n    public getGlSource(source: TextureSource): GlTexture\n    {\n        return this._glTextures[source.uid] || this._initSource(source);\n    }\n\n    public generateCanvas(texture: Texture): ICanvas\n    {\n        const { pixels, width, height } = this.getPixels(texture);\n\n        const canvas = DOMAdapter.get().createCanvas();\n\n        canvas.width = width;\n        canvas.height = height;\n\n        const ctx = canvas.getContext('2d');\n\n        if (ctx)\n        {\n            const imageData = ctx.createImageData(width, height);\n\n            imageData.data.set(pixels);\n            ctx.putImageData(imageData, 0, 0);\n        }\n\n        return canvas;\n    }\n\n    public getPixels(texture: Texture): GetPixelsOutput\n    {\n        const resolution = texture.source.resolution;\n        const frame = texture.frame;\n\n        const width = Math.max(Math.round(frame.width * resolution), 1);\n        const height = Math.max(Math.round(frame.height * resolution), 1);\n        const pixels = new Uint8Array(BYTES_PER_PIXEL * width * height);\n\n        const renderer = this._renderer;\n\n        const renderTarget = renderer.renderTarget.getRenderTarget(texture);\n        const glRenterTarget = renderer.renderTarget.getGpuRenderTarget(renderTarget);\n\n        const gl = renderer.gl;\n\n        gl.bindFramebuffer(gl.FRAMEBUFFER, glRenterTarget.resolveTargetFramebuffer);\n\n        gl.readPixels(\n            Math.round(frame.x * resolution),\n            Math.round(frame.y * resolution),\n            width,\n            height,\n            gl.RGBA,\n            gl.UNSIGNED_BYTE,\n            pixels\n        );\n\n        // if (texture.source.premultiplyAlpha > 0)\n        // TODO - premultiplied alpha does not exist right now, need to add that back in!\n        // eslint-disable-next-line no-constant-condition\n        if (false)\n        {\n            unpremultiplyAlpha(pixels);\n        }\n\n        return { pixels: new Uint8ClampedArray(pixels.buffer), width, height };\n    }\n\n    public destroy(): void\n    {\n        // we copy the array as the array with a slice as onSourceDestroy\n        // will remove the source from the real managedTextures array\n        this.managedTextures\n            .slice()\n            .forEach((source) => this.onSourceDestroy(source));\n\n        (this.managedTextures as null) = null;\n\n        (this._renderer as null) = null;\n    }\n\n    public resetState(): void\n    {\n        this._activeTextureLocation = -1;\n        this._boundTextures.fill(Texture.EMPTY.source);\n        this._boundSamplers = Object.create(null);\n\n        const gl = this._gl;\n\n        this._premultiplyAlpha = false;\n\n        gl.pixelStorei(gl.UNPACK_PREMULTIPLY_ALPHA_WEBGL, this._premultiplyAlpha);\n    }\n}\n\n", "import { extensions, ExtensionType } from '../../../extensions/Extensions';\nimport { GlGraphicsAdaptor } from '../../../scene/graphics/gl/GlGraphicsAdaptor';\nimport { GlMeshAdaptor } from '../../../scene/mesh/gl/GlMeshAdaptor';\nimport { GlBatchAdaptor } from '../../batcher/gl/GlBatchAdaptor';\nimport { AbstractRenderer } from '../shared/system/AbstractRenderer';\nimport { SharedRenderPipes, SharedSystems } from '../shared/system/SharedSystems';\nimport { RendererType } from '../types';\nimport { GlBufferSystem } from './buffer/GlBufferSystem';\nimport { GlContextSystem } from './context/GlContextSystem';\nimport { GlGeometrySystem } from './geometry/GlGeometrySystem';\nimport { GlBackBufferSystem } from './GlBackBufferSystem';\nimport { GlColorMaskSystem } from './GlColorMaskSystem';\nimport { GlEncoderSystem } from './GlEncoderSystem';\nimport { GlLimitsSystem } from './GlLimitsSystem';\nimport { GlStencilSystem } from './GlStencilSystem';\nimport { GlUboSystem } from './GlUboSystem';\nimport { GlRenderTargetSystem } from './renderTarget/GlRenderTargetSystem';\nimport { GlShaderSystem } from './shader/GlShaderSystem';\nimport { GlUniformGroupSystem } from './shader/GlUniformGroupSystem';\nimport { GlStateSystem } from './state/GlStateSystem';\nimport { GlTextureSystem } from './texture/GlTextureSystem';\n\nimport type { ICanvas } from '../../../environment/canvas/ICanvas';\nimport type { PipeConstructor } from '../shared/instructions/RenderPipe';\nimport type { SharedRendererOptions } from '../shared/system/SharedSystems';\nimport type { SystemConstructor } from '../shared/system/System';\nimport type { ExtractRendererOptions, ExtractSystemTypes } from '../shared/system/utils/typeUtils';\nimport type { GlRenderingContext } from './context/GlRenderingContext';\n\nconst DefaultWebGLSystems = [\n    ...SharedSystems,\n    GlUboSystem,\n    GlBackBufferSystem,\n    GlContextSystem,\n    GlLimitsSystem,\n    GlBufferSystem,\n    GlTextureSystem,\n    GlRenderTargetSystem,\n    GlGeometrySystem,\n    GlUniformGroupSystem,\n    GlShaderSystem,\n    GlEncoderSystem,\n    GlStateSystem,\n    GlStencilSystem,\n    GlColorMaskSystem,\n];\nconst DefaultWebGLPipes = [...SharedRenderPipes];\nconst DefaultWebGLAdapters = [GlBatchAdaptor, GlMeshAdaptor, GlGraphicsAdaptor];\n\n// installed systems will bbe added to this array by the extensions manager..\nconst systems: { name: string; value: SystemConstructor }[] = [];\nconst renderPipes: { name: string; value: PipeConstructor }[] = [];\nconst renderPipeAdaptors: { name: string; value: any }[] = [];\n\nextensions.handleByNamedList(ExtensionType.WebGLSystem, systems);\nextensions.handleByNamedList(ExtensionType.WebGLPipes, renderPipes);\nextensions.handleByNamedList(ExtensionType.WebGLPipesAdaptor, renderPipeAdaptors);\n\n// add all the default systems as well as any user defined ones from the extensions\nextensions.add(...DefaultWebGLSystems, ...DefaultWebGLPipes, ...DefaultWebGLAdapters);\n\n/**\n * The default WebGL renderer, uses WebGL2 contexts.\n * @category rendering\n * @standard\n * @interface\n */\nexport type WebGLSystems = ExtractSystemTypes<typeof DefaultWebGLSystems>\n& PixiMixins.RendererSystems & PixiMixins.WebGLSystems;\n\n/**\n * The default WebGL renderer, uses WebGL2 contexts.\n * @internal\n */\nexport type WebGLPipes = ExtractSystemTypes<typeof DefaultWebGLPipes> & PixiMixins.RendererPipes & PixiMixins.WebGLPipes;\n\n/**\n * Options for WebGLRenderer.\n * @category rendering\n * @standard\n */\nexport interface WebGLOptions\n    extends\n    SharedRendererOptions,\n    ExtractRendererOptions<typeof DefaultWebGLSystems>,\n    PixiMixins.WebGLOptions {}\n\n// eslint-disable-next-line requireExport/require-export-jsdoc, requireMemberAPI/require-member-api-doc\nexport interface WebGLRenderer<T extends ICanvas = HTMLCanvasElement>\n    extends AbstractRenderer<WebGLPipes, WebGLOptions, T>,\n    WebGLSystems {}\n\n/* eslint-disable max-len */\n/**\n * The WebGL PixiJS Renderer. This renderer allows you to use the most common graphics API, WebGL (and WebGL2).\n *\n * ```ts\n * // Create a new renderer\n * const renderer = new WebGLRenderer();\n * await renderer.init();\n *\n * // Add the renderer to the stage\n * document.body.appendChild(renderer.canvas);\n *\n * // Create a new stage\n * const stage = new Container();\n *\n * // Render the stage\n * renderer.render(stage);\n * ```\n *\n * You can use {@link autoDetectRenderer} to create a renderer that will automatically detect the best\n * renderer for the environment.\n *\n *\n * ```ts\n * // Create a new renderer\n * const renderer = await rendering.autoDetectRenderer({\n *    preference:'webgl',\n * });\n * ```\n *\n * The renderer is composed of systems that manage specific tasks. The following systems are added by default\n * whenever you create a WebGL renderer:\n *\n * | WebGL Core Systems                          | Systems that are specific to the WebGL renderer                               |\n * | ------------------------------------------- | ----------------------------------------------------------------------------- |\n * | {@link GlUboSystem}               | This manages WebGL2 uniform buffer objects feature for shaders                |\n * | {@link GlBackBufferSystem}        | manages the back buffer, used so that we can pixi can pixels from the screen  |\n * | {@link GlContextSystem}           | This manages the WebGL context and its extensions                             |\n * | {@link GlBufferSystem}            | This manages buffers and their GPU resources, keeps everything in sync        |\n * | {@link GlTextureSystem}           | This manages textures and their GPU resources, keeps everything in sync       |\n * | {@link GlRenderTargetSystem}      | This manages what we render too. For example the screen, or another texture   |\n * | {@link GlGeometrySystem}          | This manages geometry, used for drawing meshes via the GPU                    |\n * | {@link GlUniformGroupSystem}      | This manages uniform groups. Syncing shader properties with the GPU           |\n * | {@link GlShaderSystem}            | This manages shaders, programs that run on the GPU to output lovely pixels    |\n * | {@link GlEncoderSystem}           | This manages encoders, a WebGPU Paradigm, use it to draw a mesh + shader      |\n * | {@link GlStateSystem}             | This manages the state of the WebGL context. eg the various flags that can be set blend modes / depthTesting etc |\n * | {@link GlStencilSystem}           | This manages the stencil buffer. Used primarily for masking                   |\n * | {@link GlColorMaskSystem}         | This manages the color mask. Used for color masking                           |\n *\n * The breadth of the API surface provided by the renderer is contained within these systems.\n * @category rendering\n * @property {GlUboSystem} ubo - UboSystem instance.\n * @property {GlBackBufferSystem} backBuffer - BackBufferSystem instance.\n * @property {GlContextSystem} context - ContextSystem instance.\n * @property {GlBufferSystem} buffer - BufferSystem instance.\n * @property {GlTextureSystem} texture - TextureSystem instance.\n * @property {GlRenderTargetSystem} renderTarget - RenderTargetSystem instance.\n * @property {GlGeometrySystem} geometry - GeometrySystem instance.\n * @property {GlUniformGroupSystem} uniformGroup - UniformGroupSystem instance.\n * @property {GlShaderSystem} shader - ShaderSystem instance.\n * @property {GlEncoderSystem} encoder - EncoderSystem instance.\n * @property {GlStateSystem} state - StateSystem instance.\n * @property {GlStencilSystem} stencil - StencilSystem instance.\n * @property {GlColorMaskSystem} colorMask - ColorMaskSystem instance.\n * @extends AbstractRenderer\n * @standard\n */\nexport class WebGLRenderer<T extends ICanvas = HTMLCanvasElement>\n    extends AbstractRenderer<WebGLPipes, WebGLOptions, T>\n    implements WebGLSystems\n{\n    public gl: GlRenderingContext;\n\n    constructor()\n    {\n        const systemConfig = {\n            name: 'webgl',\n            type: RendererType.WEBGL,\n            systems,\n            renderPipes,\n            renderPipeAdaptors,\n        };\n\n        super(systemConfig);\n    }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsBO,IAAM,oBAAN,MACP;EAWW,cAAc,UACrB;AACU,UAAA,WAAW,IAAI,aAAa;MAC9B,QAAQ,EAAE,OAAO,IAAI,aAAa,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,GAAG,MAAM,YAAY;MACnE,kBAAkB,EAAE,OAAO,IAAI,OAAO,GAAG,MAAM,cAAc;MAC7D,QAAQ,EAAE,OAAO,GAAG,MAAM,MAAM;IAAA,CACnC;AAEK,UAAA,cAAc,SAAS,OAAO;AAEpC,UAAM,YAAY,2BAA2B;MACzC,MAAM;MACN,MAAM;QACF;QACA,0BAA0B,WAAW;QACrC;QACA;MAAA;IACJ,CACH;AAEI,SAAA,SAAS,IAAI,OAAO;MACrB;MACA,WAAW;QACP,eAAe;QACf,eAAe,6BAA6B,WAAW;MAAA;IAC3D,CACH;EAAA;EAGE,QAAQ,cAA4B,YAC3C;AACI,UAAM,UAAU,WAAW;AACrB,UAAA,SAAS,QAAQ,gBAAgB,KAAK;AAC5C,UAAM,WAAW,aAAa;AAC9B,UAAM,gBAAgB,SAAS;AAEzB,UAAA;MACF;MAAS;IAAA,IACT,cAAc,qBAAqB,OAAO;AAG9C,WAAO,OAAO,CAAC,IAAI,SAAS,eAAe;AAElC,aAAA,MAAM,IAAI,aAAa,KAAK;AAE5B,aAAA,OAAO,KAAK,MAAM;AAE3B,aAAS,SAAS,KAAK,QAAQ,UAAU,OAAO,SAAS;AAEzD,UAAM,UAAU,aAAa;AAE7B,aAAS,IAAI,GAAG,IAAI,aAAa,iBAAiB,KAClD;AACU,YAAA,QAAQ,QAAQ,CAAC;AAEvB,UAAI,MAAM,MACV;AACI,iBAAS,IAAI,GAAG,IAAI,MAAM,SAAS,OAAO,KAC1C;AACI,mBAAS,QAAQ,KAAK,MAAM,SAAS,SAAS,CAAC,GAAG,CAAC;QAAA;AAGvD,iBAAS,SAAS,KAAK,MAAM,UAAU,MAAM,MAAM,MAAM,KAAK;MAAA;IAClE;EACJ;EAGG,UACP;AACS,SAAA,OAAO,QAAQ,IAAI;AACxB,SAAK,SAAS;EAAA;AAEtB;AApFa,kBAGK,YAAY;EACtB,MAAM;IACF,cAAc;EAAA;EAElB,MAAM;AACV;;;ACZG,IAAM,gBAAN,MACP;EAUW,OACP;AACI,UAAM,YAAY,2BAA2B;MACzC,MAAM;MACN,MAAM;QACF;QACA;QACA;MAAA;IACJ,CACH;AAEI,SAAA,UAAU,IAAI,OAAO;MACtB;MACA,WAAW;QACP,UAAU,QAAQ,MAAM;QACxB,iBAAiB;UACb,gBAAgB,EAAE,MAAM,eAAe,OAAO,IAAI,OAAA,EAAS;QAAA;MAC/D;IACJ,CACH;EAAA;EAGE,QAAQ,UAAoB,MACnC;AACI,UAAM,WAAW,SAAS;AAE1B,QAAI,SAAiB,KAAK;AAE1B,QAAI,CAAC,QACL;AACI,eAAS,KAAK;AAEd,YAAM,UAAU,KAAK;AACrB,YAAM,SAAS,QAAQ;AAEvB,aAAO,UAAU,WAAW;AACrB,aAAA,UAAU,WAAW,OAAO;AACnC,aAAO,UAAU,gBAAgB,SAAS,iBAAiB,QAAQ,cAAc;IAAA,WAE5E,CAAC,OAAO,WACjB;AAES,WAAA,gCAAgC,KAAK,MAAM;AAGhD;IAAA;AAKJ,WAAO,OAAO,GAAG,IAAI,SAAS,eAAe;AACtC,WAAA,OAAO,GAAG,IAAI,SAAS;AAE9B,aAAS,QAAQ,KAAK;MAClB,UAAU,KAAK;MACf;MACA,OAAO,KAAK;IAAA,CACf;EAAA;EAGE,UACP;AACS,SAAA,QAAQ,QAAQ,IAAI;AACzB,SAAK,UAAU;EAAA;AAEvB;AA5Ea,cAEK,YAAY;EACtB,MAAM;IACF,cAAc;EAAA;EAElB,MAAM;AACV;;;ACXG,IAAM,iBAAN,MACP;EADO,cAAA;AAUc,SAAA,aAAa,MAAM,MAAM;AAQ1C,SAAQ,iBAA0C,CAAA;EAAC;EAC5C,KAAK,aACZ;AACI,gBAAY,SAAS,QAAQ,cAAc,IAAI,IAAI;EAAA;EAGhD,gBACP;AACI,SAAK,iBAAiB,CAAA;EAAC;EAGpB,MAAM,WAAwB,UAAoB,QACzD;AACI,UAAM,WAAW,UAAU;AAE3B,UAAM,YAAY,KAAK,eAAe,OAAO,GAAG;AAGvC,aAAA,OAAO,KAAK,QAAQ,SAAS;AAEtC,QAAI,CAAC,WACL;AACS,WAAA,eAAe,OAAO,GAAG,IAAI;IAAA;AAGtC,aAAS,OAAO,mBAAmB,SAAS,eAAe,YAAY;AAEvE,aAAS,SAAS,KAAK,UAAU,OAAO,SAAS;EAAA;EAG9C,QAAQ,WAAwB,OACvC;AACI,UAAM,WAAW,UAAU;AAEtB,SAAA,WAAW,YAAY,MAAM;AAEzB,aAAA,MAAM,IAAI,KAAK,UAAU;AAE5B,UAAA,WAAW,MAAM,SAAS;AAEhC,aAAS,IAAI,GAAG,IAAI,MAAM,SAAS,OAAO,KAC1C;AACI,eAAS,QAAQ,KAAK,SAAS,CAAC,GAAG,CAAC;IAAA;AAGxC,aAAS,SAAS,KAAK,MAAM,UAAU,MAAM,MAAM,MAAM,KAAK;EAAA;AAEtE;AAjEa,eAGK,YAAY;EACtB,MAAM;IACF,cAAc;EAAA;EAElB,MAAM;AACV;;;ACjBQ,IAAA,eAAA,CAAAA,iBAAL;AAGHA,eAAAA,aAAA,sBAAA,IAAuB,KAAvB,IAAA;AAEAA,eAAAA,aAAA,cAAA,IAAe,KAAf,IAAA;AAEAA,eAAAA,aAAA,gBAAA,IAAiB,KAAjB,IAAA;AAPQA,SAAAA;AAAA,GAAA,eAAA,CAAA,CAAA;;;ACFL,IAAM,WAAN,MACP;EASI,YAAY,QAAqB,MACjC;AAJA,SAAO,wBAAgC;AACvC,SAAO,kBAA0B;AAI7B,SAAK,SAAS,UAAU;AACxB,SAAK,WAAW;AAChB,SAAK,aAAa;AAClB,SAAK,OAAO;EAAA;AAEpB;;;ACQO,IAAM,iBAAN,MACP;;;;EAyBI,YAAY,UACZ;AAhBQ,SAAA,cAAgD,uBAAA,OAAO,IAAI;AAG3D,SAAA,oBAAsD,uBAAA,OAAO,IAAI;AAIzE,SAAQ,mBAAmB;AAE3B,SAAQ,qBAAqB,KAAK;AAClC,SAAQ,cAAc;AAOlB,SAAK,YAAY;AAEjB,SAAK,UAAU,aAAa,eAAe,MAAM,aAAa;EAAA;;EAI3D,UACP;AACI,SAAK,YAAY;AACjB,SAAK,MAAM;AACX,SAAK,cAAc;AAClB,SAAK,oBAA6B;EAAA;;EAI7B,gBACV;AACS,SAAA,MAAM,KAAK,UAAU;AAErB,SAAA,cAAqB,uBAAA,OAAO,IAAI;AAChC,SAAA,eAAe,KAAK,UAAU,OAAO;EAAA;EAGvC,YAAY,QACnB;AACI,WAAO,KAAK,YAAY,OAAO,GAAG,KAAK,KAAK,eAAe,MAAM;EAAA;;;;;EAO9D,KAAK,QACZ;AACU,UAAA,EAAE,KAAK,GAAA,IAAO;AAEd,UAAA,WAAW,KAAK,YAAY,MAAM;AAExC,OAAG,WAAW,SAAS,MAAM,SAAS,MAAM;EAAA;;;;;;;;EAUzC,eAAe,UAAoB,OAC1C;AACU,UAAA,EAAE,KAAK,GAAA,IAAO;AAEpB,QAAI,KAAK,kBAAkB,KAAK,MAAM,UACtC;AACS,WAAA,kBAAkB,KAAK,IAAI;AAChC,eAAS,wBAAwB;AAEjC,SAAG,eAAe,GAAG,gBAAgB,OAAO,SAAS,MAAM;IAAA;EAC/D;EAGG,aAAa,sBACpB;AACS,SAAA;AACL,SAAK,mBAAmB;AACxB,QAAI,sBACJ;AACS,WAAA,kBAAkB,CAAC,IAAI;AAC5B,WAAK,mBAAmB;AACpB,UAAA,KAAK,qBAAqB,GAC9B;AACI,aAAK,qBAAqB;MAAA;IAC9B;EACJ;EAGG,0BAA0B,UACjC;AACQ,QAAA,YAAY,KAAK,wBAAwB,QAAQ;AAGjD,QAAA,aAAa,KAAK,kBACtB;AACI,eAAS,kBAAkB,KAAK;AAEzB,aAAA;IAAA;AAGX,QAAI,OAAO;AACX,QAAI,YAAY,KAAK;AAErB,WAAO,OAAO,GACd;AACQ,UAAA,aAAa,KAAK,cACtB;AACI,oBAAY,KAAK;AACjB;MAAA;AAGE,YAAA,SAAS,KAAK,kBAAkB,SAAS;AAE/C,UAAI,UAAU,OAAO,oBAAoB,KAAK,aAC9C;AACI;AACA;MAAA;AAEJ;IAAA;AAGQ,gBAAA;AACZ,SAAK,qBAAqB,YAAY;AAEtC,QAAI,QAAQ,GACZ;AAEW,aAAA;IAAA;AAGX,aAAS,kBAAkB,KAAK;AAC3B,SAAA,kBAAkB,SAAS,IAAI;AAE7B,WAAA;EAAA;EAGJ,wBAAwB,UAC/B;AACI,UAAM,QAAQ,SAAS;AAEvB,QAAI,KAAK,kBAAkB,KAAK,MAAM,UACtC;AACW,aAAA;IAAA;AAGJ,WAAA;EAAA;;;;;;;;;EAWJ,gBAAgB,UAAoB,OAAgB,QAAiB,MAC5E;AACU,UAAA,EAAE,KAAK,GAAA,IAAO;AAET,eAAA,SAAA;AACD,cAAA,QAAA;AAEL,SAAA,kBAAkB,KAAK,IAAI;AAE7B,OAAA,gBAAgB,GAAG,gBAAgB,SAAS,GAAG,SAAS,QAAQ,SAAS,KAAK,QAAQ,GAAG;EAAA;;;;;EAOzF,aAAa,QACpB;AACU,UAAA,EAAE,KAAK,GAAA,IAAO;AAEd,UAAA,WAAW,KAAK,YAAY,MAAM;AAEpC,QAAA,OAAO,cAAc,SAAS,UAClC;AACW,aAAA;IAAA;AAGX,aAAS,WAAW,OAAO;AAE3B,OAAG,WAAW,SAAS,MAAM,SAAS,MAAM;AAE5C,UAAM,OAAO,OAAO;AAEd,UAAA,WAAY,OAAO,WAAW,QAAQ,YAAY,SAAU,GAAG,cAAc,GAAG;AAEtF,QAAI,MACJ;AACQ,UAAA,SAAS,cAAc,KAAK,YAChC;AAGO,WAAA,cAAc,SAAS,MAAM,GAAG,MAAM,GAAG,OAAO,cAAc,KAAK,iBAAiB;MAAA,OAG3F;AACI,iBAAS,aAAa,KAAK;AAE3B,WAAG,WAAW,SAAS,MAAM,MAAM,QAAQ;MAAA;IAC/C,OAGJ;AACa,eAAA,aAAa,OAAO,WAAW;AACxC,SAAG,WAAW,SAAS,MAAM,SAAS,YAAY,QAAQ;IAAA;AAGvD,WAAA;EAAA;;EAIJ,aACP;AACI,UAAM,KAAK,KAAK;AAEL,eAAA,MAAM,KAAK,aACtB;AACI,SAAG,aAAa,KAAK,YAAY,EAAE,EAAE,MAAM;IAAA;AAG1C,SAAA,cAAqB,uBAAA,OAAO,IAAI;EAAA;;;;;;EAQ/B,gBAAgB,QAAgB,aAC1C;AACI,UAAM,WAAW,KAAK,YAAY,OAAO,GAAG;AAE5C,UAAM,KAAK,KAAK;AAEhB,QAAI,CAAC,aACL;AACO,SAAA,aAAa,SAAS,MAAM;IAAA;AAG9B,SAAA,YAAY,OAAO,GAAG,IAAI;EAAA;;;;;;EAQzB,eAAe,QACzB;AACU,UAAA,EAAE,KAAK,GAAA,IAAO;AAEpB,QAAI,OAAO,YAAY;AAEvB,QAAK,OAAO,WAAW,QAAQ,YAAY,OAC3C;AACI,aAAO,YAAY;IAAA,WAEb,OAAO,WAAW,QAAQ,YAAY,SAChD;AACI,aAAO,YAAY;IAAA;AAGvB,UAAM,WAAW,IAAI,SAAS,GAAG,aAAA,GAAgB,IAAI;AAEhD,SAAA,YAAY,OAAO,GAAG,IAAI;AAE/B,WAAO,GAAG,WAAW,KAAK,iBAAiB,IAAI;AAExC,WAAA;EAAA;EAGJ,aACP;AACS,SAAA,oBAA2B,uBAAA,OAAO,IAAI;EAAA;AAEnD;AAxSa,eAGK,YAAY;EACtB,MAAM;IACF,cAAc;EAAA;EAElB,MAAM;AACV;;;ACwCG,IAAM,mBAAN,MAAMC,kBACb;;EAsGI,YAAY,UACZ;AApDA,SAAO,WAAW;;MAEd,eAAe;;MAEf,qBAAqB;;MAErB,mBAAmB;;MAEnB,cAAc;;MAEd,mBAAmB;;MAEnB,MAAM;;MAEN,kBAAkB;IAAA;AAuClB,SAAK,YAAY;AAEZ,SAAA,aAAoB,uBAAA,OAAO,IAAI;AAGpC,SAAK,oBAAoB,KAAK,kBAAkB,KAAK,IAAI;AACzD,SAAK,wBAAwB,KAAK,sBAAsB,KAAK,IAAI;EAAA;;;;;EAOrE,IAAI,SACJ;AACI,WAAQ,CAAC,KAAK,MAAM,KAAK,GAAG,cAAc;EAAA;;;;;EAOpC,cAAc,IACxB;AACI,SAAK,KAAK;AACV,SAAK,UAAU,KAAK;EAAA;EAGjB,KAAK,SACZ;AACI,cAAU,EAAE,GAAGA,kBAAgB,gBAAgB,GAAG,QAAQ;AAGtD,QAAA,YAAY,KAAK,YAAY,QAAQ;AAErC,QAAA,QAAQ,WAAW,WACvB;AAEI,WAAK,+GAA+G;AAExG,kBAAA;IAAA;AAGhB,QAAI,WACJ;AACI,WAAK,SAAS,WAAW,IAAI,EACxB,aAAa,KAAK,UAAU,OAAO,OAAO,KAAK,UAAU,OAAO,MAAM;IAAA,OAG/E;AACS,WAAA,SAAS,KAAK,UAAU,KAAK;IAAA;AAKtC,QAAI,QAAQ,SACZ;AACS,WAAA,gBAAgB,QAAQ,OAAO;IAAA,OAGxC;AACI,YAAM,QAAQ,KAAK,UAAU,WAAW,QAAQ;AAC1C,YAAA,qBAAqB,QAAQ,sBAAsB;AACzD,YAAM,YAAY,QAAQ,aAAa,CAAC,KAAK,UAAU,WAAW;AAE7D,WAAA,cAAc,QAAQ,oBAAoB;QAC3C;QACA;QACA;QACA,SAAS;QACT,uBAAuB,QAAQ;QAC/B,iBAAiB,QAAQ,mBAAmB;MAAA,CAC/C;IAAA;EACL;EAGG,iBAAiB,cACxB;AACQ,QAAA,CAAC,KAAK,WACV;AACQ,UAAA,iBAAiB,KAAK,QAC1B;AACI,aAAK,gEAAgE;MAAA;AAGzE;IAAA;AAGE,UAAA,EAAE,OAAA,IAAW;AAEnB,QAAI,OAAO,QAAQ,aAAa,SAAS,OAAO,SAAS,aAAa,QACtE;AACI,aAAO,QAAQ,KAAK,IAAI,aAAa,OAAO,aAAa,KAAK;AAC9D,aAAO,SAAS,KAAK,IAAI,aAAa,QAAQ,aAAa,MAAM;IAAA;EACrE;;;;;;EAQM,gBAAgB,IAC1B;AACI,SAAK,KAAK;AAEV,SAAK,eAAe,cAAc,WAAW,IAAA,EAAM,yBAAA,IAA6B,IAAI;AAEpF,SAAK,cAAc;AAEnB,SAAK,gBAAgB,EAAE;AAEvB,SAAK,UAAU,QAAQ,cAAc,KAAK,EAAE;AAEtC,UAAA,UAAU,KAAK,UAAU,KAAK;AAEnC,YAAgB,iBAAiB,oBAAoB,KAAK,mBAAmB,KAAK;AACnF,YAAQ,iBAAiB,wBAAwB,KAAK,uBAAuB,KAAK;EAAA;;;;;;;;EAU5E,cAAc,oBAA2B,SACnD;AACQ,QAAA;AAEJ,UAAM,SAAS,KAAK;AAEpB,QAAI,uBAAuB,GAC3B;AACS,WAAA,OAAO,WAAW,UAAU,OAAO;IAAA;AAG5C,QAAI,CAAC,IACL;AACS,WAAA,OAAO,WAAW,SAAS,OAAO;AAEvC,UAAI,CAAC,IACL;AAEU,cAAA,IAAI,MAAM,oEAAoE;MAAA;IACxF;AAGJ,SAAK,KAAK;AAEL,SAAA,gBAAgB,KAAK,EAAE;EAAA;;EAItB,gBACV;AAEU,UAAA,EAAE,GAAA,IAAO;AAEf,UAAM,SAAS;MACX,sBAAsB,GAAG,aAAa,gCAAgC;MACtE,oBAAoB,GAAG,aAAa,0BAA0B;MAE9D,MAAM,GAAG,aAAa,+BAA+B;MACrD,WAAW,GAAG,aAAa,oCAAoC;;MAC/D,KAAK,GAAG,aAAa,8BAA8B;MACnD,MAAM,GAAG,aAAa,+BAA+B;MACrD,OAAO,GAAG,aAAa,gCAAgC,KAChD,GAAG,aAAa,uCAAuC;MAC9D,KAAK,GAAG,aAAa,8BAA8B;MACnD,MAAM,GAAG,aAAa,+BAA+B;MACrD,MAAM,GAAG,aAAa,8BAA8B;MACpD,MAAM,GAAG,aAAa,8BAA8B;MACpD,aAAa,GAAG,aAAa,oBAAoB;IAAA;AAGjD,QAAA,KAAK,iBAAiB,GAC1B;AACI,WAAK,aAAa;QACd,GAAG;QAEH,aAAa,GAAG,aAAa,oBAAoB;QACjD,cAAc,GAAG,aAAa,qBAAqB;QACnD,mBAAmB,GAAG,aAAa,yBAAyB,KACrD,GAAG,aAAa,6BAA6B,KAC7C,GAAG,aAAa,gCAAgC;QACvD,oBAAoB,GAAG,aAAa,wBAAwB;;QAE5D,cAAc,GAAG,aAAa,mBAAmB;QACjD,oBAAoB,GAAG,aAAa,0BAA0B;QAC9D,kBAAkB,GAAG,aAAa,wBAAwB;QAC1D,wBAAwB,GAAG,aAAa,+BAA+B;QACvE,0BAA0B,GAAG,aAAa,wBAAwB;QAClE,MAAM,GAAG,aAAa,UAAU;MAAA;IACpC,OAGJ;AACI,WAAK,aAAa;QACd,GAAG;QACH,kBAAkB,GAAG,aAAa,wBAAwB;MAAA;AAGxD,YAAA,aAAa,GAAG,aAAa,wBAAwB;AAE3D,UAAI,YACJ;AACe,mBAAA,qBAAqB,WAAW,6BAA6B;MAAA;IAC5E;EACJ;;;;;EAOM,kBAAkB,OAC5B;AACI,UAAM,eAAe;AAGrB,QAAI,KAAK,oBACT;AACI,WAAK,qBAAqB;AAE1B,iBAAW,MACX;AACQ,YAAA,KAAK,GAAG,cAAA,GACZ;AACS,eAAA,WAAW,aAAa,eAAe;QAAA;MAChD,GACD,CAAC;IAAA;EACR;;EAIM,wBACV;AACI,SAAK,cAAc;AACnB,SAAK,UAAU,QAAQ,cAAc,KAAK,KAAK,EAAE;EAAA;EAG9C,UACP;AACU,UAAA,UAAU,KAAK,UAAU,KAAK;AAEpC,SAAK,YAAY;AAGhB,YAAgB,oBAAoB,oBAAoB,KAAK,iBAAiB;AACvE,YAAA,oBAAoB,wBAAwB,KAAK,qBAAqB;AAEzE,SAAA,GAAG,WAAW,IAAI;AAElB,SAAA,WAAW,aAAa,YAAY;EAAA;;;;;;;;EAUtC,mBACP;AACS,SAAA,WAAW,aAAa,YAAY;AACzC,SAAK,qBAAqB;EAAA;;;;;EAMpB,gBAAgB,IAC1B;AACU,UAAA,aAAa,GAAG,qBAAqB;AAGvC,QAAA,cAAc,CAAC,WAAW,SAC9B;AAEI,WAAK,uFAAuF;IAAA;AAKhG,UAAM,WAAW,KAAK;AAEhB,UAAA,WAAW,KAAK,iBAAiB;AACvC,UAAMC,cAAa,KAAK;AAExB,aAAS,gBAAgB,YAAY,CAAC,CAACA,YAAW;AAClD,aAAS,sBAAsB;AAC/B,aAAS,oBAAoB,YAAY,CAAC,CAACA,YAAW;AACtD,aAAS,eAAe,YAAY,CAAC,CAACA,YAAW;AACjD,aAAS,oBAAoB;AAC7B,aAAS,mBAAmB;AAC5B,aAAS,OAAO;AAEZ,QAAA,CAAC,SAAS,eACd;AAEI,WAAK,gGAAgG;IAAA;EAEzG;AAER;AA5Za,iBAGK,YAAY;EACtB,MAAM;IACF,cAAc;EAAA;EAElB,MAAM;AACV;AARS,iBAWK,iBAAuC;;;;;EAKjD,SAAS;;;;;EAKT,oBAAoB;;;;;EAKpB,uBAAuB;;;;;EAKvB,iBAAiB;;;;;EAKjB,oBAAoB;;;;;EAKpB,WAAW;AACf;AA1CG,IAAM,kBAAN;;;ACvEK,IAAA,cAAA,CAAAC,gBAAL;AAEHA,cAAAA,YAAA,MAAA,IAAO,IAAP,IAAA;AACAA,cAAAA,YAAA,KAAA,IAAM,IAAN,IAAA;AACAA,cAAAA,YAAA,IAAA,IAAK,KAAL,IAAA;AACAA,cAAAA,YAAA,KAAA,IAAM,IAAN,IAAA;AACAA,cAAAA,YAAA,cAAA,IAAe,KAAf,IAAA;AACAA,cAAAA,YAAA,aAAA,IAAc,KAAd,IAAA;AACAA,cAAAA,YAAA,YAAA,IAAa,KAAb,IAAA;AACAA,cAAAA,YAAA,aAAA,IAAc,KAAd,IAAA;AACAA,cAAAA,YAAA,OAAA,IAAQ,IAAR,IAAA;AACAA,cAAAA,YAAA,WAAA,IAAY,IAAZ,IAAA;AACAA,cAAAA,YAAA,iBAAA,IAAkB,IAAlB,IAAA;AACAA,cAAAA,YAAA,iBAAA,IAAkB,IAAlB,IAAA;AACAA,cAAAA,YAAA,eAAA,IAAgB,KAAhB,IAAA;AAdQA,SAAAA;AAAA,GAAA,cAAA,CAAA,CAAA;AAsBA,IAAA,cAAA,CAAAC,gBAAL;AAEHA,cAAAA,YAAA,YAAA,IAAa,IAAb,IAAA;AACAA,cAAAA,YAAA,kBAAA,IAAmB,KAAnB,IAAA;AACAA,cAAAA,YAAA,kBAAA,IAAmB,KAAnB,IAAA;AACAA,cAAAA,YAAA,6BAAA,IAA8B,KAA9B,IAAA;AACAA,cAAAA,YAAA,6BAAA,IAA8B,KAA9B,IAAA;AACAA,cAAAA,YAAA,6BAAA,IAA8B,KAA9B,IAAA;AACAA,cAAAA,YAAA,6BAAA,IAA8B,KAA9B,IAAA;AACAA,cAAAA,YAAA,6BAAA,IAA8B,KAA9B,IAAA;AACAA,cAAAA,YAAA,6BAAA,IAA8B,KAA9B,IAAA;AAVQA,SAAAA;AAAA,GAAA,cAAA,CAAA,CAAA;AAyBA,IAAA,iBAAA,CAAAC,mBAAL;AAMHA,iBAAAA,eAAA,OAAA,IAAQ,KAAR,IAAA;AAKAA,iBAAAA,eAAA,QAAA,IAAS,KAAT,IAAA;AAKAA,iBAAAA,eAAA,iBAAA,IAAkB,KAAlB,IAAA;AAhBQA,SAAAA;AAAA,GAAA,iBAAA,CAAA,CAAA;AAoBA,IAAA,YAAA,CAAAC,cAAL;AAMHA,YAAAA,UAAA,eAAA,IAAgB,IAAhB,IAAA;AAEAA,YAAAA,UAAA,gBAAA,IAAiB,IAAjB,IAAA;AAKAA,YAAAA,UAAA,sBAAA,IAAuB,KAAvB,IAAA;AAKAA,YAAAA,UAAA,wBAAA,IAAyB,KAAzB,IAAA;AAKAA,YAAAA,UAAA,wBAAA,IAAyB,KAAzB,IAAA;AAEAA,YAAAA,UAAA,cAAA,IAAe,IAAf,IAAA;AAEAA,YAAAA,UAAA,8BAAA,IAA+B,KAA/B,IAAA;AAEAA,YAAAA,UAAA,6BAAA,IAA8B,KAA9B,IAAA;AAEAA,YAAAA,UAAA,mBAAA,IAAoB,KAApB,IAAA;AAEAA,YAAAA,UAAA,0BAAA,IAA2B,KAA3B,IAAA;AAEAA,YAAAA,UAAA,MAAA,IAAO,IAAP,IAAA;AAEAA,YAAAA,UAAA,OAAA,IAAQ,IAAR,IAAA;AAEAA,YAAAA,UAAA,KAAA,IAAM,IAAN,IAAA;AAEAA,YAAAA,UAAA,OAAA,IAAQ,IAAR,IAAA;AAEAA,YAAAA,UAAA,gCAAA,IAAiC,KAAjC,IAAA;AAEAA,YAAAA,UAAA,YAAA,IAAa,KAAb,IAAA;AA7CQA,SAAAA;AAAA,GAAA,YAAA,CAAA,CAAA;;;ACpEZ,IAAM,UAAU;EACZ,SAAS,SAAS;EAClB,SAAS,SAAS;EAClB,SAAS,SAAS;EAClB,SAAS,SAAS;EAClB,UAAU,SAAS;EACnB,UAAU,SAAS;EACnB,UAAU,SAAS;EACnB,UAAU,SAAS;EACnB,UAAU,SAAS;EACnB,UAAU,SAAS;EACnB,UAAU,SAAS;EACnB,UAAU,SAAS;EACnB,WAAW,SAAS;EACpB,WAAW,SAAS;EACpB,WAAW,SAAS;EACpB,WAAW,SAAS;EACpB,WAAW,SAAS;EACpB,WAAW,SAAS;EACpB,SAAS,SAAS;EAClB,WAAW,SAAS;EACpB,WAAW,SAAS;EACpB,WAAW,SAAS;EACpB,QAAQ,SAAS;EACjB,UAAU,SAAS;EACnB,UAAU,SAAS;EACnB,UAAU,SAAS;EACnB,QAAQ,SAAS;EACjB,UAAU,SAAS;EACnB,UAAU,SAAS;EACnB,UAAU,SAAS;AACvB;AAMO,SAAS,oBAAoB,QACpC;AACW,SAAA,QAAQ,MAAM,KAAK,QAAQ;AACtC;;;AChCA,IAAM,kBAAkB;EACpB,cAAc;EACd,aAAa;EACb,cAAc;EACd,iBAAiB;EACjB,kBAAkB;AACtB;AAOO,IAAM,mBAAN,MACP;;EA+BI,YAAY,UACZ;AAPU,SAAA,mBAAkF,uBAAA,OAAO,IAAI;AAQnG,SAAK,YAAY;AACjB,SAAK,kBAAkB;AACvB,SAAK,aAAa;AAElB,SAAK,SAAS;AACd,SAAK,cAAc;AAEnB,SAAK,UAAU,aAAa,eAAe,MAAM,kBAAkB;EAAA;;EAI7D,gBACV;AACI,UAAM,KAAK,KAAK,KAAK,KAAK,UAAU;AAEpC,QAAI,CAAC,KAAK,UAAU,QAAQ,SAAS,mBACrC;AACU,YAAA,IAAI,MAAM,gEAAgE;IAAA;AAGpF,UAAM,qBAAqB,KAAK,UAAU,QAAQ,WAAW;AAE7D,QAAI,oBACJ;AACO,SAAA,oBAAoB,MACnB,mBAAmB,qBAAqB;AAE5C,SAAG,kBAAkB,CAAC,QAClB,mBAAmB,mBAAmB,GAAG;AAE7C,SAAG,oBAAoB,CAAC,QACpB,mBAAmB,qBAAqB,GAAG;IAAA;AAGnD,UAAM,2BAA2B,KAAK,UAAU,QAAQ,WAAW;AAEnE,QAAI,0BACJ;AACI,SAAG,sBAAsB,CAAC,GAAG,GAAG,GAAG,MACnC;AACI,iCAAyB,yBAAyB,GAAG,GAAG,GAAG,CAAC;MAAA;AAGhE,SAAG,wBAAwB,CAAC,GAAG,GAAG,GAAG,GAAG,MACxC;AACI,iCAAyB,2BAA2B,GAAG,GAAG,GAAG,GAAG,CAAC;MAAA;AAGrE,SAAG,sBAAsB,CAAC,GAAG,MACzB,yBAAyB,yBAAyB,GAAG,CAAC;IAAA;AAG9D,SAAK,kBAAkB;AACvB,SAAK,aAAa;AACb,SAAA,mBAA0B,uBAAA,OAAO,IAAI;EAAA;;;;;;EAQvC,KAAK,UAAqB,SACjC;AAGI,UAAM,KAAK,KAAK;AAEhB,SAAK,kBAAkB;AAEvB,UAAM,MAAM,KAAK,OAAO,UAAU,OAAO;AAErC,QAAA,KAAK,eAAe,KACxB;AACI,WAAK,aAAa;AAElB,SAAG,gBAAgB,GAAG;IAAA;AAG1B,SAAK,cAAc;EAAA;;EAIhB,aACP;AACI,SAAK,OAAO;EAAA;;EAIT,gBACP;AACI,UAAM,WAAW,KAAK;AAEhB,UAAA,eAAe,KAAK,UAAU;AAEpC,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,QAAQ,KAC7C;AACU,YAAA,SAAS,SAAS,QAAQ,CAAC;AAEjC,mBAAa,aAAa,MAAM;IAAA;EACpC;;;;;;EAQM,mBAAmB,UAAoB,SACjD;AAEI,UAAM,qBAAqB,SAAS;AACpC,UAAM,mBAAmB,QAAQ;AAEjC,eAAW,KAAK,kBAChB;AACQ,UAAA,CAAC,mBAAmB,CAAC,GACzB;AACI,cAAM,IAAI,MAAM,2DAA2D,CAAC,aAAa;MAAA;IAC7F;EACJ;;;;;;;EASM,aAAa,UAAoB,SAC3C;AACI,UAAM,UAAU,SAAS;AACzB,UAAM,mBAAmB,QAAQ;AAEjC,UAAM,UAAU,CAAC,KAAK,SAAS,GAAG;AAElC,eAAW,KAAK,SAChB;AACQ,UAAA,iBAAiB,CAAC,GACtB;AACI,gBAAQ,KAAK,GAAG,iBAAiB,CAAC,EAAE,QAAQ;MAAA;IAChD;AAGG,WAAA,QAAQ,KAAK,GAAG;EAAA;EAGjB,OAAO,UAAoB,SACrC;AACW,WAAA,KAAK,iBAAiB,SAAS,GAAG,IAAI,QAAQ,IAAI,KAAK,KAAK,gBAAgB,UAAU,OAAO;EAAA;;;;;;;;;EAW9F,gBAAgB,UAAoB,SAAoB,eAAe,MACjF;AACU,UAAA,KAAK,KAAK,UAAU;AAEpB,UAAA,eAAe,KAAK,UAAU;AAE/B,SAAA,UAAU,OAAO,gBAAgB,OAAO;AAExC,SAAA,mBAAmB,UAAU,OAAO;AAEzC,UAAM,YAAY,KAAK,aAAa,UAAU,OAAO;AAErD,QAAI,CAAC,KAAK,iBAAiB,SAAS,GAAG,GACvC;AACI,WAAK,iBAAiB,SAAS,GAAG,IAAI,uBAAO,OAAO,IAAI;AAExD,eAAS,GAAG,WAAW,KAAK,mBAAmB,IAAI;IAAA;AAGvD,UAAM,gBAAgB,KAAK,iBAAiB,SAAS,GAAG;AAEpD,QAAA,MAAM,cAAc,SAAS;AAEjC,QAAI,KACJ;AAEkB,oBAAA,QAAQ,IAAI,IAAI;AAEvB,aAAA;IAAA;AAGM,qBAAA,UAAU,QAAQ,cAAc;AAEjD,UAAM,UAAU,SAAS;AAGzB,UAAM,GAAG,kBAAkB;AAE3B,OAAG,gBAAgB,GAAG;AAItB,aAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KACpC;AACU,YAAA,SAAS,QAAQ,CAAC;AAExB,mBAAa,KAAK,MAAM;IAAA;AAMvB,SAAA,YAAY,UAAU,OAAO;AAGpB,kBAAA,QAAQ,IAAI,IAAI;AAC9B,kBAAc,SAAS,IAAI;AAE3B,OAAG,gBAAgB,IAAI;AAEhB,WAAA;EAAA;;;;;;EAQD,kBAAkB,UAAoB,aAChD;AACI,UAAM,gBAAgB,KAAK,iBAAiB,SAAS,GAAG;AAExD,UAAM,KAAK,KAAK;AAEhB,QAAI,eACJ;AACI,UAAI,aACJ;AACI,mBAAW,KAAK,eAChB;AACI,cAAI,KAAK,eAAe,cAAc,CAAC,GACvC;AACI,iBAAK,OAAO;UAAA;AAGb,aAAA,kBAAkB,cAAc,CAAC,CAAC;QAAA;MACzC;AAGC,WAAA,iBAAiB,SAAS,GAAG,IAAI;IAAA;EAC1C;;;;;EAOG,WAAW,cAAc,OAChC;AACI,UAAM,KAAK,KAAK;AAEL,eAAA,KAAK,KAAK,kBACrB;AACI,UAAI,aACJ;AACI,mBAAW,KAAK,KAAK,iBAAiB,CAAC,GACvC;AACU,gBAAA,gBAAgB,KAAK,iBAAiB,CAAC;AAEzC,cAAA,KAAK,eAAe,eACxB;AACI,iBAAK,OAAO;UAAA;AAGb,aAAA,kBAAkB,cAAc,CAAC,CAAC;QAAA;MACzC;AAGC,WAAA,iBAAiB,CAAC,IAAI;IAAA;EAC/B;;;;;;EAQM,YAAY,UAAoB,SAC1C;AACU,UAAA,KAAK,KAAK,UAAU;AAEpB,UAAA,eAAe,KAAK,UAAU;AACpC,UAAM,aAAa,SAAS;AAE5B,QAAI,SAAS,aACb;AAEiB,mBAAA,KAAK,SAAS,WAAW;IAAA;AAG1C,QAAI,aAAa;AAGjB,eAAW,KAAK,YAChB;AACU,YAAA,YAAY,WAAW,CAAC;AAC9B,YAAM,SAAS,UAAU;AACnB,YAAA,WAAW,aAAa,YAAY,MAAM;AAC1C,YAAA,gBAAgB,QAAQ,eAAe,CAAC;AAE9C,UAAI,eACJ;AACI,YAAI,eAAe,UACnB;AACI,uBAAa,KAAK,MAAM;AAEX,uBAAA;QAAA;AAGjB,cAAM,WAAW,cAAc;AAI/B,WAAG,wBAAwB,QAAQ;AAE7B,cAAA,gBAAgB,2BAA2B,UAAU,MAAM;AAE3D,cAAA,OAAO,oBAAoB,UAAU,MAAM;AAEjD,YAAI,cAAc,QAAQ,UAAU,GAAG,CAAC,MAAM,OAC9C;AACO,aAAA;YAAqB;YACpB,cAAc;YACd;YACA,UAAU;YACV,UAAU;UAAA;QAAM,OAGxB;AACO,aAAA;YAAoB;YACnB,cAAc;YACd;YACA,cAAc;YACd,UAAU;YACV,UAAU;UAAA;QAAM;AAGxB,YAAI,UAAU,UACd;AAEI,cAAI,KAAK,aACT;AAGU,kBAAA,UAAU,UAAU,WAAW;AAElC,eAAA,oBAAoB,UAAU,OAAO;UAAA,OAG5C;AACU,kBAAA,IAAI,MAAM,gEAAgE;UAAA;QACpF;MACJ;IACJ;EACJ;;;;;;;;;;;EAaG,KAAK,UAAqB,MAAe,OAAgB,eAChE;AACU,UAAA,EAAE,GAAG,IAAI,KAAK;AACpB,UAAM,WAAW,KAAK;AAEtB,UAAM,aAAa,gBAAgB,YAAY,SAAS,QAAQ;AAEhE,sBAAA,gBAAkB,SAAS;AAE3B,QAAI,SAAS,aACb;AACU,YAAA,WAAW,SAAS,YAAY,KAAK;AAC3C,YAAM,SAAS,aAAa,IAAI,GAAG,iBAAiB,GAAG;AAEvD,UAAI,gBAAgB,GACpB;AAEO,WAAA,sBAAsB,YAAY,QAAQ,SAAS,YAAY,KAAK,QAAQ,SAAS,SAAS,KAAK,UAAU,aAAa;MAAA,OAIjI;AACO,WAAA,aAAa,YAAY,QAAQ,SAAS,YAAY,KAAK,QAAQ,SAAS,SAAS,KAAK,QAAQ;MAAA;IACzG,WAEK,gBAAgB,GACzB;AAEO,SAAA,oBAAoB,YAAY,SAAS,GAAG,QAAQ,SAAS,QAAA,GAAW,aAAa;IAAA,OAG5F;AACI,SAAG,WAAW,YAAY,SAAS,GAAG,QAAQ,SAAS,QAAA,CAAS;IAAA;AAG7D,WAAA;EAAA;;EAID,SACV;AACS,SAAA,GAAG,gBAAgB,IAAI;AAC5B,SAAK,aAAa;AAClB,SAAK,kBAAkB;EAAA;EAGpB,UACP;AACI,SAAK,YAAY;AACjB,SAAK,KAAK;AACV,SAAK,aAAa;AAClB,SAAK,kBAAkB;EAAA;AAE/B;AAhda,iBAGK,YAAY;EACtB,MAAM;IACF,cAAc;EAAA;EAElB,MAAM;AACV;;;ACpBJ,IAAM,sBAAsB,IAAI,SAAS;EACrC,YAAY;IACR,WAAW;MACP;MAAM;;MACN;MAAK;;MACL;MAAM;;IAAA;EACV;AAER,CAAC;AAoCM,IAAM,sBAAN,MAAMC,qBACb;EA2BI,YAAY,UACZ;AAXA,SAAO,gBAAgB;AAKvB,SAAQ,2BAA2B;AAO/B,SAAK,YAAY;EAAA;EAGd,KAAK,UAA+B,CAAA,GAC3C;AACU,UAAA,EAAE,eAAe,UAAU,IAAI,EAAE,GAAGA,qBAAmB,gBAAgB,GAAG,QAAQ;AAExF,SAAK,gBAAgB;AAErB,SAAK,aAAa;AAElB,QAAI,CAAC,KAAK,UAAU,QAAQ,SAAS,MACrC;AACI,WAAK,8DAA8D;AAEnE,WAAK,aAAa;IAAA;AAGjB,SAAA,SAAS,MAAM,MAAM;AAEpB,UAAA,qBAAqB,IAAI,UAAU;MACrC,QAAQ;;;;;;;;;;;;MAYR,UAAU;;;;;;;;;MASV,MAAM;IAAA,CACT;AAEI,SAAA,qBAAqB,IAAI,OAAO;MACjC,WAAW;MACX,WAAW;QACP,UAAU,QAAQ,MAAM;MAAA;IAC5B,CACH;EAAA;;;;;;EAQK,YAAY,SACtB;AACI,UAAM,eAAe,KAAK,UAAU,aAAa,gBAAgB,QAAQ,MAAM;AAE/E,SAAK,2BAA2B,KAAK,iBAAiB,CAAC,CAAC,aAAa;AAErE,QAAI,KAAK,0BACT;AACI,YAAMC,gBAAe,KAAK,UAAU,aAAa,gBAAgB,QAAQ,MAAM;AAE/E,WAAK,iBAAiBA,cAAa;AAEnC,cAAQ,SAAS,KAAK,sBAAsBA,cAAa,YAAY;IAAA;EACzE;EAGM,YACV;AACI,SAAK,mBAAmB;EAAA;EAGpB,qBACR;AACI,UAAM,WAAW,KAAK;AAEtB,aAAS,aAAa,iBAAiB;AAEvC,QAAI,CAAC,KAAK;AAA0B;AAEpC,aAAS,aAAa,KAAK,KAAK,gBAAgB,KAAK;AAErD,SAAK,mBAAmB,UAAU,WAAW,KAAK,mBAAmB;AAErE,aAAS,QAAQ,KAAK;MAClB,UAAU;MACV,QAAQ,KAAK;MACb,OAAO,KAAK;IAAA,CACf;EAAA;EAGG,sBAAsB,qBAC9B;AACI,SAAK,qBAAqB,KAAK,sBAAsB,IAAI,QAAQ;MAC7D,QAAQ,IAAI,cAAc;QACtB,OAAO,oBAAoB;QAC3B,QAAQ,oBAAoB;QAC5B,YAAY,oBAAoB;QAChC,WAAW,KAAK;MAAA,CACnB;IAAA,CACJ;AAGD,SAAK,mBAAmB,OAAO;MAC3B,oBAAoB;MACpB,oBAAoB;MACpB,oBAAoB;IAAA;AAGxB,WAAO,KAAK;EAAA;;EAIT,UACP;AACI,QAAI,KAAK,oBACT;AACI,WAAK,mBAAmB,QAAQ;AAChC,WAAK,qBAAqB;IAAA;EAC9B;AAER;AA9Ja,oBAGK,YAAY;EACtB,MAAM;IACF,cAAc;EAAA;EAElB,MAAM;EACN,UAAU;AACd;AATS,oBAYK,iBAAsC;;EAEhD,eAAe;AACnB;AAfG,IAAM,qBAAN;;;AC/CA,IAAM,oBAAN,MACP;EAYI,YAAY,UACZ;AAHA,SAAQ,kBAAkB;AAItB,SAAK,YAAY;EAAA;EAGd,QAAQ,WACf;AACI,QAAI,KAAK,oBAAoB;AAAW;AACxC,SAAK,kBAAkB;AAEvB,SAAK,UAAU,GAAG;MACd,CAAC,EAAE,YAAY;MACf,CAAC,EAAE,YAAY;MACf,CAAC,EAAE,YAAY;MACf,CAAC,EAAE,YAAY;IAAA;EACnB;AAIR;AAhCa,kBAGK,YAAY;EACtB,MAAM;IACF,cAAc;EAAA;EAElB,MAAM;AACV;;;ACJG,IAAM,kBAAN,MACP;EAYI,YAAY,UACZ;AAJgB,SAAA,kBAAkB,QAAQ,QAAQ;AAK9C,SAAK,YAAY;EAAA;EAGd,YAAY,UAAoB,QACvC;AACI,SAAK,UAAU,SAAS,KAAK,UAAU,OAAO,SAAS;EAAA;EAGpD,mBACP;EAAA;EAIO,KAAK,SAUZ;AACI,UAAM,WAAW,KAAK;AAChB,UAAA,EAAE,UAAU,QAAQ,OAAO,UAAU,UAAU,MAAM,MAAM,OAAO,cAAA,IAAkB;AAEjF,aAAA,OAAO,KAAK,QAAQ,QAAQ;AAErC,aAAS,SAAS,KAAK,UAAU,SAAS,OAAO,cAAc;AAE/D,QAAI,OACJ;AACa,eAAA,MAAM,IAAI,KAAK;IAAA;AAG5B,aAAS,SAAS,KAAK,MAAM,MAAM,OAAO,iBAAiB,SAAS,aAAa;EAAA;EAG9E,UACP;AACK,SAAK,YAAqB;EAAA;AAEnC;AA1Da,gBAGK,YAAY;EACtB,MAAM;IACF,cAAc;EAAA;EAElB,MAAM;AACV;;;ACMG,IAAM,iBAAN,MACP;EAmBI,YAAY,UACZ;AACI,SAAK,YAAY;EAAA;EAGd,gBACP;AACU,UAAA,KAAK,KAAK,UAAU;AAG1B,SAAK,cAAc,GAAG,aAAa,GAAG,uBAAuB;AAG7D,SAAK,uBAAuB,6BAA6B,KAAK,aAAa,EAAE;AAE7E,SAAK,qBAAqB,GAAG,aAAa,GAAG,2BAA2B;EAAA;EAGrE,UACP;EAAA;AAGJ;AA1Ca,eAGK,YAAY;EACtB,MAAM;IACF,cAAc;EAAA;EAElB,MAAM;AACV;;;ACvBG,IAAM,kBAAN,MACP;EA8CI,YAAY,UACZ;AApCA,SAAiB,gBAAgB;MAC7B,SAAS;MACT,kBAAkB;MAClB,aAAa,cAAc;IAAA;AAGvB,SAAA,4BAGI,uBAAA,OAAO,IAAI;AA4BV,aAAA,aAAa,qBAAqB,IAAI,IAAI;EAAA;EAG7C,cAAc,IACxB;AAGI,SAAK,MAAM;AAEX,SAAK,yBAAyB;MAC1B,QAAQ,GAAG;MACX,OAAO,GAAG;MACV,OAAO,GAAG;MACV,aAAa,GAAG;MAChB,MAAM,GAAG;MACT,cAAc,GAAG;MACjB,SAAS,GAAG;MACZ,iBAAiB,GAAG;IAAA;AAGxB,SAAK,qBAAqB;MACtB,MAAM,GAAG;MACT,MAAM,GAAG;MACT,SAAS,GAAG;MACZ,QAAQ,GAAG;MACX,mBAAmB,GAAG;MACtB,mBAAmB,GAAG;MACtB,kBAAkB,GAAG;MACrB,kBAAkB,GAAG;IAAA;AAGzB,SAAK,WAAW;EAAA;EAGV,qBAAqB,cAC/B;AACI,QAAI,KAAK,wBAAwB;AAAc;AAE/C,SAAK,sBAAsB;AAE3B,QAAI,eAAe,KAAK,0BAA0B,aAAa,GAAG;AAElE,QAAI,CAAC,cACL;AACI,qBAAe,KAAK,0BAA0B,aAAa,GAAG,IAAI;QAC9D,aAAa,cAAc;QAC3B,kBAAkB;MAAA;IACtB;AAIJ,SAAK,eAAe,aAAa,aAAa,aAAa,gBAAgB;EAAA;EAGxE,aACP;AAEI,SAAK,cAAc,UAAU;AACxB,SAAA,cAAc,cAAc,cAAc;AAC/C,SAAK,cAAc,mBAAmB;EAAA;EAGnC,eAAe,aAA4B,kBAClD;AACI,UAAM,eAAe,KAAK,0BAA0B,KAAK,oBAAoB,GAAG;AAEhF,UAAM,KAAK,KAAK;AACV,UAAA,OAAO,sBAAsB,WAAW;AAE9C,UAAM,gBAAgB,KAAK;AAG3B,iBAAa,cAAc;AAC3B,iBAAa,mBAAmB;AAE5B,QAAA,gBAAgB,cAAc,UAClC;AACQ,UAAA,KAAK,cAAc,SACvB;AACI,aAAK,cAAc,UAAU;AAE1B,WAAA,QAAQ,GAAG,YAAY;MAAA;AAG9B;IAAA;AAGA,QAAA,CAAC,KAAK,cAAc,SACxB;AACI,WAAK,cAAc,UAAU;AAC1B,SAAA,OAAO,GAAG,YAAY;IAAA;AAG7B,QAAI,gBAAgB,cAAc,eAAe,cAAc,qBAAqB,kBACpF;AACI,oBAAc,cAAc;AAC5B,oBAAc,mBAAmB;AAK9B,SAAA,YAAY,KAAK,uBAAuB,KAAK,YAAY,OAAO,GAAG,kBAAkB,GAAI;AACzF,SAAA,UAAU,GAAG,MAAM,GAAG,MAAM,KAAK,mBAAmB,KAAK,YAAY,MAAM,CAAC;IAAA;EACnF;AAIR;AA5Ja,gBAGK,YAAY;EACtB,MAAM;IACF,cAAc;EAAA;EAElB,MAAM;AACV;;;AClBG,IAAM,qBAA6C;EACtD,KAAK;EACL,KAAK;EACL,aAAa;EACb,aAAa;EACb,aAAa;EAEb,aAAa;EACb,aAAa;EACb,aAAa;EAEb,eAAe,KAAK;EACpB,eAAe,KAAK;EACpB,eAAe,KAAK;;;;;;;;;;;;;;;;;AAqBxB;AAMO,SAAS,uBAAuB,aACvC;AACI,QAAM,cAA4B,YAAY,IAAI,CAAC,UAC9C;IACG;IACA,QAAQ;IACR,MAAM;EAAA,EACR;AAEN,QAAM,YAAY;AAElB,MAAI,OAAO;AACX,MAAI,SAAS;AAEb,WAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KACxC;AACU,UAAA,aAAa,YAAY,CAAC;AAEzB,WAAA,mBAAmB,WAAW,KAAK,IAAI;AAE9C,QAAI,CAAC,MACL;AACI,YAAM,IAAI,MAAM,gBAAgB,WAAW,KAAK,IAAI,EAAE;IAAA;AAGtD,QAAA,WAAW,KAAK,OAAO,GAC3B;AACI,aAAO,KAAK,IAAI,MAAM,SAAS,IAAI,WAAW,KAAK;IAAA;AAGjD,UAAA,WAAW,SAAS,KAAK,KAAK;AAEpC,eAAW,OAAO;AAElB,UAAM,YAAY,SAAS;AAE3B,QAAI,YAAY,KAAK,YAAY,YAAY,UAC7C;AACI,iBAAW,YAAY,aAAa;IAAA,OAGxC;AACe,iBAAA,OAAQ,YAAY,QAAS;IAAA;AAG5C,eAAW,SAAS;AACV,cAAA;EAAA;AAGd,WAAS,KAAK,KAAK,SAAS,EAAE,IAAI;AAE3B,SAAA,EAAE,aAAa,MAAM,OAAO;AACvC;;;ACnFgB,SAAA,uBAAuB,YAAwB,aAC/D;AACU,QAAA,UAAU,KAAK,IAAI,mBAAmB,WAAW,KAAK,IAAI,IAAI,IAAI,CAAC;AACzE,QAAM,cAAe,WAAW,KAAK,MAAwB,SAAS,WAAW,KAAK;AAEhF,QAAA,aAAa,IAAK,cAAc,KAAM;AACtC,QAAA,OAAO,WAAW,KAAK,KAAK,QAAQ,KAAK,KAAK,IAAI,cAAc;AAE/D,SAAA;iBACM,WAAW,KAAK,IAAI;oBACjB,WAAW;;;;;;2BAMJ,WAAW,KAAK,OAAO,OAAO;;iCAExB,WAAW;;kBAE1B,IAAI;;cAER,cAAc,IAAI,kBAAkB,SAAS,MAAM,EAAE;;;AAGnE;;;AC3BO,SAAS,2BACZ,aAEJ;AACW,SAAA;IACH;IACA;IACA;IACA;EAAA;AAER;;;ACVO,IAAM,cAAN,cAA0B,UACjC;EAOI,cACA;AACU,UAAA;MACF,mBAAmB;MACnB,iBAAiB;IAAA,CACpB;EAAA;AAET;AAfa,YAGK,YAAY;EACtB,MAAM,CAAC,cAAc,WAAW;EAChC,MAAM;AACV;;;ACXG,IAAM,iBAAN,MACP;EADO,cAAA;AAEH,SAAO,QAAQ;AACf,SAAO,SAAS;AAChB,SAAO,OAAO;AAGd,SAAO,mBAAwC,CAAA;EAAC;AAEpD;;;ACIO,IAAM,wBAAN,MACP;EADO,cAAA;AAIH,SAAQ,mBAA8B,CAAC,GAAG,GAAG,GAAG,CAAC;AACzC,SAAA,iBAA4B,IAAI,UAAU;EAAA;EAE3C,KAAK,UAAyB,oBACrC;AACI,SAAK,YAAY;AACjB,SAAK,sBAAsB;AAElB,aAAA,QAAQ,cAAc,IAAI,IAAI;EAAA;EAGpC,gBACP;AACI,SAAK,mBAAmB,CAAC,GAAG,GAAG,GAAG,CAAC;AAC9B,SAAA,iBAAiB,IAAI,UAAU;EAAA;EAGjC,cACH,4BACA,oBACA,WACA,MACA,YAEJ;AACI,UAAM,qBAAqB,KAAK;AAEhC,UAAM,WAAW,KAAK;AAChB,UAAA,iBAAiB,mBAAmB,mBAAmB,0BAA0B;AACvF,UAAM,KAAK,SAAS;AAEpB,SAAK,iBAAiB,0BAA0B;AAEhD,OAAG,gBAAgB,GAAG,aAAa,eAAe,wBAAwB;AAEjE,aAAA,QAAQ,KAAK,oBAAoB,CAAC;AAExC,OAAA;MAAkB,GAAG;MAAY;MAChC,WAAW;MAAG,WAAW;MACzB,UAAU;MACV,UAAU;MACV,KAAK;MACL,KAAK;IAAA;AAGF,WAAA;EAAA;EAGJ,gBACH,cACA,QAAuB,MACvB,YACA,UAEJ;AACI,UAAM,qBAAqB,KAAK;AAEhC,UAAM,SAAS,aAAa;AACtB,UAAA,kBAAkB,mBAAmB,mBAAmB,YAAY;AAE1E,QAAI,YAAY,SAAS;AAEzB,QAAI,aAAa,QACjB;AAEgB,kBAAA,OAAO,cAAc,SAAS;IAAA;AAIjC,iBAAA,cAAc,QAAQ,CAAC,YACpC;AACS,WAAA,UAAU,QAAQ,OAAO,OAAO;IAAA,CACxC;AAEK,UAAA,KAAK,KAAK,UAAU;AAE1B,OAAG,gBAAgB,GAAG,aAAa,gBAAgB,WAAW;AAE9D,UAAM,gBAAgB,KAAK;AAE3B,QAAI,cAAc,MAAM,SAAS,KAC1B,cAAc,MAAM,aACpB,cAAc,UAAU,SAAS,SACjC,cAAc,WAAW,SAAS,QACzC;AACI,oBAAc,IAAI,SAAS;AAC3B,oBAAc,IAAI;AAClB,oBAAc,QAAQ,SAAS;AAC/B,oBAAc,SAAS,SAAS;AAE7B,SAAA;QACC,SAAS;QACT;QACA,SAAS;QACT,SAAS;MAAA;IACb;AAIJ,QAAI,CAAC,gBAAgB,6BAA6B,aAAa,WAAW,aAAa,QACvF;AACI,WAAK,aAAa,eAAe;IAAA;AAGhC,SAAA,MAAM,cAAc,OAAO,UAAU;EAAA;EAGvC,iBAAiB,cACxB;AACI,UAAM,qBAAqB,KAAK;AAE1B,UAAA,iBAAiB,mBAAmB,mBAAmB,YAAY;AAEzE,QAAI,CAAC,eAAe;AAAM;AAEpB,UAAA,KAAK,KAAK,UAAU;AAE1B,OAAG,gBAAgB,GAAG,aAAa,eAAe,wBAAwB;AAC1E,OAAG,gBAAgB,GAAG,kBAAkB,eAAe,WAAW;AAE/D,OAAA;MACC;MAAG;MAAG,eAAe;MAAO,eAAe;MAC3C;MAAG;MAAG,eAAe;MAAO,eAAe;MAC3C,GAAG;MAAkB,GAAG;IAAA;AAG5B,OAAG,gBAAgB,GAAG,aAAa,eAAe,WAAW;EAAA;EAM1D,oBAAoB,cAC3B;AACI,UAAM,WAAW,KAAK;AAEtB,UAAM,KAAK,SAAS;AAId,UAAA,iBAAiB,IAAI,eAAe;AAG1C,UAAM,eAAe,aAAa;AAElC,QAAI,wBAAwB,cAC5B;AACI,WAAK,UAAU,QAAQ,iBAAiB,aAAa,aAAa,QAAQ;AAE1E,qBAAe,cAAc;AAEtB,aAAA;IAAA;AAGN,SAAA,WAAW,cAAc,cAAc;AAIzC,OAAA,gBAAgB,GAAG,aAAa,IAAI;AAEhC,WAAA;EAAA;EAGJ,uBAAuB,iBAC9B;AACU,UAAA,KAAK,KAAK,UAAU;AAE1B,QAAI,gBAAgB,aACpB;AACO,SAAA,kBAAkB,gBAAgB,WAAW;AAChD,sBAAgB,cAAc;IAAA;AAGlC,QAAI,gBAAgB,0BACpB;AACO,SAAA,kBAAkB,gBAAgB,wBAAwB;AAC7D,sBAAgB,2BAA2B;IAAA;AAG/C,QAAI,gBAAgB,0BACpB;AACO,SAAA,mBAAmB,gBAAgB,wBAAwB;AAC9D,sBAAgB,2BAA2B;IAAA;AAG/B,oBAAA,iBAAiB,QAAQ,CAAC,iBAC1C;AACI,SAAG,mBAAmB,YAAY;IAAA,CACrC;AAED,oBAAgB,mBAAmB;EAAA;EAGhC,MAAM,eAA6B,OAAsB,YAChE;AACI,QAAI,CAAC;AAAO;AAEZ,UAAM,qBAAqB,KAAK;AAG5B,QAAA,OAAO,UAAU,WACrB;AACY,cAAA,QAAQ,MAAM,MAAM,MAAM;IAAA;AAGhC,UAAA,KAAK,KAAK,UAAU;AAEtB,QAAA,QAAQ,MAAM,OAClB;AACI,qBAAA,aAAe,mBAAmB;AAElC,YAAM,kBAAkB,KAAK;AAC7B,YAAM,kBAAkB;AAEpB,UAAA,gBAAgB,CAAC,MAAM,gBAAgB,CAAC,KACrC,gBAAgB,CAAC,MAAM,gBAAgB,CAAC,KACxC,gBAAgB,CAAC,MAAM,gBAAgB,CAAC,KACxC,gBAAgB,CAAC,MAAM,gBAAgB,CAAC,GAC/C;AACoB,wBAAA,CAAC,IAAI,gBAAgB,CAAC;AACtB,wBAAA,CAAC,IAAI,gBAAgB,CAAC;AACtB,wBAAA,CAAC,IAAI,gBAAgB,CAAC;AACtB,wBAAA,CAAC,IAAI,gBAAgB,CAAC;AAEtC,WAAG,WAAW,gBAAgB,CAAC,GAAG,gBAAgB,CAAC,GAAG,gBAAgB,CAAC,GAAG,gBAAgB,CAAC,CAAC;MAAA;IAChG;AAGJ,OAAG,MAAM,KAAK;EAAA;EAGX,sBAAsB,cAC7B;AACI,QAAI,aAAa;AAAQ;AAEzB,UAAM,qBAAqB,KAAK;AAE1B,UAAA,iBAAiB,mBAAmB,mBAAmB,YAAY;AAEpE,SAAA,aAAa,cAAc,cAAc;AAE1C,QAAA,aAAa,WAAW,aAAa,OACzC;AACI,WAAK,eAAe,cAAc;IAAA;EACtC;EAGI,WAAW,cAA4B,gBAC/C;AACI,UAAM,WAAW,KAAK;AAEtB,UAAM,KAAK,SAAS;AAEd,UAAA,2BAA2B,GAAG,kBAAkB;AAEtD,mBAAe,2BAA2B;AAGvC,OAAA,gBAAgB,GAAG,aAAa,wBAAwB;AAE5C,mBAAA,QAAQ,aAAa,aAAa,OAAO;AACzC,mBAAA,SAAS,aAAa,aAAa,OAAO;AAEzD,iBAAa,cAAc,QAAQ,CAAC,cAAc,MAClD;AACI,YAAM,SAAS,aAAa;AAE5B,UAAI,OAAO,WACX;AACQ,YAAA,SAAS,QAAQ,SAAS,MAC9B;AACI,yBAAe,OAAO;QAAA,OAG1B;AACI,eAAK,qEAAqE;QAAA;MAC9E;AAIK,eAAA,QAAQ,WAAW,QAAQ,CAAC;AACrC,YAAM,WAAW,SAAS,QAAQ,YAAY,MAAM;AAEpD,YAAM,YAAY,SAAS;AAExB,SAAA;QAAqB,GAAG;QACvB,GAAG,oBAAoB;QACvB;;QACA;QACA;MAAA;IAAC,CACR;AAED,QAAI,eAAe,MACnB;AACU,YAAA,kBAAkB,GAAG,kBAAkB;AAE7C,qBAAe,cAAc;AAE1B,SAAA,gBAAgB,GAAG,aAAa,eAAe;AAElD,mBAAa,cAAc,QAAQ,CAAC,GAAG,MACvC;AACU,cAAA,mBAAmB,GAAG,mBAAmB;AAEhC,uBAAA,iBAAiB,CAAC,IAAI;MAAA,CACxC;IAAA,OAGL;AACI,qBAAe,cAAc;IAAA;AAG5B,SAAA,aAAa,cAAc,cAAc;EAAA;EAG1C,aAAa,cAA4B,gBACjD;AACU,UAAA,SAAS,aAAa,aAAa;AAEzC,mBAAe,QAAQ,OAAO;AAC9B,mBAAe,SAAS,OAAO;AAE/B,iBAAa,cAAc,QAAQ,CAAC,cAAc,MAClD;AAEI,UAAI,MAAM;AAAG;AAEb,mBAAa,OAAO,OAAO,OAAO,OAAO,OAAO,QAAQ,OAAO,WAAW;IAAA,CAC7E;AAED,QAAI,eAAe,MACnB;AACI,YAAM,WAAW,KAAK;AACtB,YAAM,KAAK,SAAS;AAEpB,YAAM,kBAAkB,eAAe;AAEpC,SAAA,gBAAgB,GAAG,aAAa,eAAe;AAElD,mBAAa,cAAc,QAAQ,CAAC,cAAc,MAClD;AACI,cAAMC,UAAS,aAAa;AAEnB,iBAAA,QAAQ,WAAWA,SAAQ,CAAC;AACrC,cAAM,WAAW,SAAS,QAAQ,YAAYA,OAAM;AAEpD,cAAM,mBAAmB,SAAS;AAE5B,cAAA,mBAAmB,eAAe,iBAAiB,CAAC;AAEvD,WAAA;UACC,GAAG;UACH;QAAA;AAGD,WAAA;UACC,GAAG;UACH;UACA;UACAA,QAAO;UACPA,QAAO;QAAA;AAGR,WAAA;UACC,GAAG;UACH,GAAG,oBAAoB;UACvB,GAAG;UACH;QAAA;MACJ,CACH;IAAA;EACL;EAGI,aAAa,gBACrB;AAEI,QAAI,eAAe,gBAAgB;AAAM;AAEnC,UAAA,KAAK,KAAK,UAAU;AAEpB,UAAA,2BAA2B,GAAG,mBAAmB;AAEvD,mBAAe,2BAA2B;AAEvC,OAAA;MACC,GAAG;MACH;IAAA;AAGD,OAAA;MACC,GAAG;MACH,GAAG;MACH,GAAG;MACH;IAAA;AAIJ,SAAK,eAAe,cAAc;EAAA;EAG9B,eAAe,gBACvB;AACU,UAAA,KAAK,KAAK,UAAU;AAEvB,OAAA;MACC,GAAG;MACH,eAAe;IAAA;AAGnB,QAAI,eAAe,MACnB;AACO,SAAA;QACC,GAAG;QACH;QACA,GAAG;QACH,eAAe;QACf,eAAe;MAAA;IACnB,OAGJ;AACO,SAAA;QACC,GAAG;QACH,KAAK,UAAU,QAAQ,iBAAiB,IAClC,GAAG,mBACH,GAAG;QACT,eAAe;QACf,eAAe;MAAA;IACnB;EACJ;EAGG,UAAU,cACjB;AACU,UAAA,WAAW,aAAa,aAAa;AAG3C,QAAI,KAAK,UAAU,QAAQ,aAAa,aAAa,KAAK,QAAQ,GAClE;AACS,WAAA,UAAU,QAAQ,iBAAiB,QAAQ;IAAA;EACpD;EAGG,WAAW,cAClB;AAEQ,QAAA,CAAC,KAAK,UAAU,QAAQ;AAAW;AAIvC,QAAI,aAAa,KAAK,aAAa,aAAa,QAAQ,GACxD;AACU,YAAA,gBAAgB,KAAK,UAAU,QAAQ;AAC7C,YAAM,eAAe,aAAa;AAElC,mBAAa,UAAU;QACnB;QACA;QAAG,aAAa,cAAc,cAAc;MAAA;IAChD;EACJ;AAER;;;ACvdO,IAAM,uBAAN,cAAmC,mBAC1C;EASI,YAAY,UACZ;AACI,UAAM,QAAQ;AAJX,SAAA,UAAU,IAAI,sBAAsB;AAMlC,SAAA,QAAQ,KAAK,UAAU,IAAI;EAAA;AAExC;AAhBa,qBAGK,YAAY;EACtB,MAAM,CAAC,cAAc,WAAW;EAChC,MAAM;AACV;;;ACLY,SAAA,uBAAuB,QAAgB,cACvD;AACI,QAAM,gBAA0B,CAAA;AAWhC,QAAM,kBAA4B,CAAC;;;;;;KAMlC;AAED,MAAI,oBAAoB;AACxB,MAAI,eAAe;AAEnB,QAAM,cAAc,aAAa,gBAAgB,OAAO,SAAS;AAEtD,aAAA,KAAK,OAAO,QACvB;AACU,UAAA,QAAQ,OAAO,OAAO,CAAC;AAE7B,kBAAc,KAAK;4BACC,CAAC;SACpB;AAEU,eAAA,KAAK,MAAM,WACtB;AACU,YAAA,WAAW,MAAM,UAAU,CAAC;AAElC,UAAI,oBAAoB,cACxB;AACI,YAAI,SAAS,KACb;AACI,gBAAM,UAAU,OAAO,gBAAgB,CAAC,EAAE,OAAO,CAAC,CAAC;AAEnD,wBAAc,KAAK;;wCAEC,CAAC;+BACV,OAAO;8BACR,OAAO,UAAU,kBAAkB,OAAO,EAAE,KAAK;;qBAE1D;QAAA,OAGL;AACI,wBAAc,KAAK;2DACoB,CAAC;qBACvC;QAAA;MACL,WAEK,oBAAoB,gBAC7B;AACI,cAAM,UAAU,OAAO,gBAAgB,CAAC,EAAE,OAAO,CAAC,CAAC;AAEnD,sBAAc,KAAK;;oCAEC,CAAC;2BACV,OAAO;0BACR,OAAO,UAAU,kBAAkB,OAAO,EAAE,KAAK;;iBAE1D;MAAA,WAEI,oBAAoB,eAC7B;AACI,cAAM,cAAc,OAAO,gBAAgB,CAAsB,EAAE,CAAsB;AAEnF,cAAA,cAAc,YAAY,YAAY,WAAW;AAEvD,YAAI,aACJ;AACI,cAAI,CAAC,mBACL;AACwB,gCAAA;AACpB,4BAAgB,KAAK;;yBAEpB;UAAA;AAGL,uBAAa,IAAI,UAAU,YAAY,UAAU,YAAY;AAE7D,wBAAc,KAAK;4CACK,CAAC,MAAM,YAAY;qBAC1C;AAED;QAAA;MACJ;IACJ;EACJ;AAGE,QAAA,iBAAiB,CAAC,GAAG,iBAAiB,GAAG,aAAa,EAAE,KAAK,IAAI;AAGvE,SAAO,IAAI,SAAS,KAAK,KAAK,MAAM,cAAc;AACtD;;;ACnHO,IAAM,iBAAN,MACP;AAGA;AAMO,IAAM,gBAAN,MACP;;;;;;EAkCI,YAAY,SAAuB,aACnC;AACI,SAAK,UAAU;AACf,SAAK,cAAc;AACnB,SAAK,gBAAgB,CAAA;AACrB,SAAK,qBAAqB,CAAA;AAC1B,SAAK,uBAAuB,CAAA;EAAC;;EAI1B,UACP;AACI,SAAK,cAAc;AACnB,SAAK,gBAAgB;AACrB,SAAK,qBAAqB;AAC1B,SAAK,uBAAuB;AAC5B,SAAK,UAAU;EAAA;AAEvB;;;ACzDgB,SAAA,cAAc,IAA+B,MAAc,KAC3E;AACU,QAAA,SAAS,GAAG,aAAa,IAAI;AAEhC,KAAA,aAAa,QAAQ,GAAG;AAC3B,KAAG,cAAc,MAAM;AAEhB,SAAA;AACX;;;ACfA,SAAS,aAAa,MACtB;AACU,QAAA,QAAQ,IAAI,MAAM,IAAI;AAE5B,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAClC;AACI,UAAM,CAAC,IAAI;EAAA;AAGR,SAAA;AACX;AAOgB,SAAA,aACZ,MACA,MAEJ;AACI,UAAQ,MACR;IACI,KAAK;AACM,aAAA;IAEX,KAAK;AACM,aAAA,IAAI,aAAa,IAAI,IAAI;IAEpC,KAAK;AACM,aAAA,IAAI,aAAa,IAAI,IAAI;IAEpC,KAAK;AACM,aAAA,IAAI,aAAa,IAAI,IAAI;IAEpC,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;AACM,aAAA;IAEX,KAAK;AACM,aAAA,IAAI,WAAW,IAAI,IAAI;IAElC,KAAK;AACM,aAAA,IAAI,WAAW,IAAI,IAAI;IAElC,KAAK;AACM,aAAA,IAAI,WAAW,IAAI,IAAI;IAElC,KAAK;AACM,aAAA,IAAI,YAAY,IAAI,IAAI;IAEnC,KAAK;AACM,aAAA,IAAI,YAAY,IAAI,IAAI;IAEnC,KAAK;AACM,aAAA,IAAI,YAAY,IAAI,IAAI;IAEnC,KAAK;AACM,aAAA;IAEX,KAAK;AAEM,aAAA,aAAa,IAAI,IAAI;IAEhC,KAAK;AACM,aAAA,aAAa,IAAI,IAAI;IAEhC,KAAK;AACM,aAAA,aAAa,IAAI,IAAI;IAEhC,KAAK;AACD,aAAO,IAAI,aAAa;QAAC;QAAG;QACxB;QAAG;MAAA,CAAE;IAEb,KAAK;AACD,aAAO,IAAI,aAAa;QAAC;QAAG;QAAG;QAC3B;QAAG;QAAG;QACN;QAAG;QAAG;MAAA,CAAE;IAEhB,KAAK;AACD,aAAO,IAAI,aAAa;QAAC;QAAG;QAAG;QAAG;QAC9B;QAAG;QAAG;QAAG;QACT;QAAG;QAAG;QAAG;QACT;QAAG;QAAG;QAAG;MAAA,CAAE;EAAA;AAGhB,SAAA;AACX;;;ACvFA,IAAI,WAAyB;AAE7B,IAAM,mBAAiC;EACnC,OAAa;EACb,YAAa;EACb,YAAa;EACb,YAAa;EAEb,KAAa;EACb,UAAa;EACb,UAAa;EACb,UAAa;EAEb,cAAsB;EACtB,mBAAsB;EACtB,mBAAsB;EACtB,mBAAsB;EAEtB,MAAa;EACb,WAAa;EACb,WAAa;EACb,WAAa;EAEb,YAAa;EACb,YAAa;EACb,YAAa;EAEb,YAAyB;EACzB,gBAAyB;EACzB,yBAAyB;EACzB,cAA2B;EAC3B,kBAA2B;EAC3B,2BAA2B;EAC3B,kBAA+B;EAC/B,sBAA+B;EAC/B,+BAA+B;AACnC;AAEA,IAAM,uBAAqD;EAEvD,OAAO;EACP,MAAM;EACN,MAAM;EACN,MAAM;EAEN,KAAK;EACL,OAAO;EACP,OAAO;EACP,OAAO;EAEP,MAAM;EACN,OAAO;EACP,OAAO;EACP,OAAO;EAEP,MAAM;EACN,OAAO;EACP,OAAO;EACP,OAAO;AACX;AAOgB,SAAA,QAAQ,IAAS,MACjC;AACI,MAAI,CAAC,UACL;AACU,UAAA,YAAY,OAAO,KAAK,gBAAgB;AAE9C,eAAW,CAAA;AAEX,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,EAAE,GACxC;AACU,YAAA,KAAK,UAAU,CAAC;AAEtB,eAAS,GAAG,EAAE,CAAC,IAAI,iBAAiB,EAAE;IAAA;EAC1C;AAGJ,SAAO,SAAS,IAAI;AACxB;AAOgB,SAAA,oBAAoB,IAAS,MAC7C;AACU,QAAA,YAAY,QAAQ,IAAI,IAAI;AAE3B,SAAA,qBAAqB,SAAS,KAAK;AAC9C;;;ACxEO,SAAS,+BACZ,SACA,IACA,iBAAiB,OAErB;AACI,QAAM,aAAsD,CAAA;AAE5D,QAAM,kBAAkB,GAAG,oBAAoB,SAAS,GAAG,iBAAiB;AAE5E,WAAS,IAAI,GAAG,IAAI,iBAAiB,KACrC;AACI,UAAM,aAAa,GAAG,gBAAgB,SAAS,CAAC;AAGhD,QAAI,WAAW,KAAK,WAAW,KAAK,GACpC;AACI;IAAA;AAGJ,UAAM,SAAS,oBAAoB,IAAI,WAAW,IAAI;AAE3C,eAAA,WAAW,IAAI,IAAI;MAC1B,UAAU;;MACV;MACA,QAAQ,2BAA2B,MAAM,EAAE;MAC3C,QAAQ;MACR,UAAU;MACV,OAAO;IAAA;EACX;AAGE,QAAA,OAAO,OAAO,KAAK,UAAU;AAEnC,MAAI,gBACJ;AACI,SAAK,KAAK,CAAC,GAAG,MAAO,IAAI,IAAK,IAAI,EAAE;AAEpC,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KACjC;AACI,iBAAW,KAAK,CAAC,CAAC,EAAE,WAAW;AAE/B,SAAG,mBAAmB,SAAS,GAAG,KAAK,CAAC,CAAC;IAAA;AAG7C,OAAG,YAAY,OAAO;EAAA,OAG1B;AACI,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KACjC;AACe,iBAAA,KAAK,CAAC,CAAC,EAAE,WAAW,GAAG,kBAAkB,SAAS,KAAK,CAAC,CAAC;IAAA;EACxE;AAGG,SAAA;AACX;;;ACzEgB,SAAA,WAAW,SAAuB,IAClD;AAEI,MAAI,CAAC,GAAG;AAAuB,WAAO,CAAA;AAEtC,QAAM,gBAAoD,CAAA;AAI1D,QAAM,sBAAsB,GAAG,oBAAoB,SAAS,GAAG,qBAAqB;AAEpF,WAAS,IAAI,GAAG,IAAI,qBAAqB,KACzC;AACI,UAAM,OAAO,GAAG,0BAA0B,SAAS,CAAC;AACpD,UAAM,oBAAoB,GAAG,qBAAqB,SAAS,IAAI;AAE/D,UAAM,OAAO,GAAG,+BAA+B,SAAS,GAAG,GAAG,uBAAuB;AAErF,kBAAc,IAAI,IAAI;MAClB;MACA,OAAO;MACP;IAAA;EACJ;AAGG,SAAA;AACX;;;ACvBgB,SAAA,eAAe,SAAuB,IACtD;AACI,QAAM,WAA2C,CAAA;AAEjD,QAAM,gBAAgB,GAAG,oBAAoB,SAAS,GAAG,eAAe;AAExE,WAAS,IAAI,GAAG,IAAI,eAAe,KACnC;AACI,UAAM,cAAc,GAAG,iBAAiB,SAAS,CAAC;AAClD,UAAM,OAAO,YAAY,KAAK,QAAQ,YAAY,EAAE;AAEpD,UAAM,UAAU,CAAC,CAAE,YAAY,KAAK,MAAM,UAAU;AAEpD,UAAM,OAAO,QAAQ,IAAI,YAAY,IAAI;AAEzC,aAAS,IAAI,IAAI;MACb;MACA,OAAO;MACP;MACA,MAAM,YAAY;MAClB;MACA,OAAO,aAAa,MAAM,YAAY,IAAI;IAAA;EAC9C;AAGG,SAAA;AACX;;;AChCA,SAAS,qBAAqB,IAA2B,QACzD;AACI,QAAM,YAAY,GAAG,gBAAgB,MAAM,EACtC,MAAM,IAAI,EACV,IAAI,CAAC,MAAM,UAAU,GAAG,KAAK,KAAK,IAAI,EAAE;AAEvC,QAAA,YAAY,GAAG,iBAAiB,MAAM;AACtC,QAAA,cAAc,UAAU,MAAM,IAAI;AAExC,QAAM,SAAkC,CAAA;AAExC,QAAM,cAAc,YAAY,IAAI,CAAC,SAAS,WAAW,KAAK,QAAQ,4BAA4B,IAAI,CAAC,CAAC,EACnG,OAAO,CAAC,MACT;AACI,QAAI,KAAK,CAAC,OAAO,CAAC,GAClB;AACI,aAAO,CAAC,IAAI;AAEL,aAAA;IAAA;AAGJ,WAAA;EAAA,CACV;AAEC,QAAA,UAAU,CAAC,EAAE;AAEP,cAAA,QAAQ,CAAC,WACrB;AACI,cAAU,SAAS,CAAC,IAAI,KAAK,UAAU,SAAS,CAAC,CAAC;AAC1C,YAAA,KAAK,uDAAuD,iBAAiB;EAAA,CACxF;AAEK,QAAA,sBAAsB,UACvB,KAAK,IAAI;AAEd,UAAQ,CAAC,IAAI;AAEb,UAAQ,MAAM,SAAS;AAGvB,UAAQ,eAAe,gCAAgC;AAC/C,UAAA,KAAK,GAAG,OAAO;AAEvB,UAAQ,SAAS;AACrB;AAWO,SAAS,gBACZ,IACA,SACA,cACA,gBAEJ;AAEI,MAAI,CAAC,GAAG,oBAAoB,SAAS,GAAG,WAAW,GACnD;AACI,QAAI,CAAC,GAAG,mBAAmB,cAAc,GAAG,cAAc,GAC1D;AACI,2BAAqB,IAAI,YAAY;IAAA;AAGzC,QAAI,CAAC,GAAG,mBAAmB,gBAAgB,GAAG,cAAc,GAC5D;AACI,2BAAqB,IAAI,cAAc;IAAA;AAG3C,YAAQ,MAAM,4CAA4C;AAG1D,QAAI,GAAG,kBAAkB,OAAO,MAAM,IACtC;AACI,cAAQ,KAAK,0CAA0C,GAAG,kBAAkB,OAAO,CAAC;IAAA;EACxF;AAER;;;ACtEgB,SAAA,gBAAgB,IAAwB,SACxD;AACI,QAAM,eAAe,cAAc,IAAI,GAAG,eAAe,QAAQ,MAAM;AACvE,QAAM,eAAe,cAAc,IAAI,GAAG,iBAAiB,QAAQ,QAAQ;AAErE,QAAA,eAAe,GAAG,cAAc;AAEnC,KAAA,aAAa,cAAc,YAAY;AACvC,KAAA,aAAa,cAAc,YAAY;AAE1C,QAAM,4BAA4B,QAAQ;AAE1C,MAAI,2BACJ;AACQ,QAAA,OAAO,GAAG,8BAA8B,YAC5C;AAEI,WAAK,6EAA6E;IAAA,OAItF;AACO,SAAA;QACC;QACA,0BAA0B;QAC1B,0BAA0B,eAAe,aACnC,GAAG,mBACH,GAAG;MAAA;IACb;EACJ;AAGJ,KAAG,YAAY,YAAY;AAE3B,MAAI,CAAC,GAAG,oBAAoB,cAAc,GAAG,WAAW,GACxD;AACoB,oBAAA,IAAI,cAAc,cAAc,YAAY;EAAA;AAMhE,UAAQ,iBAAiB;IACrB;IACA;IACA,CAAE,iDAAkD,KAAK,QAAQ,MAAM;EAAA;AAGnE,UAAA,eAAe,eAAe,cAAc,EAAE;AAC9C,UAAA,oBAAoB,WAAW,cAAc,EAAE;AAEvD,KAAG,aAAa,YAAY;AAC5B,KAAG,aAAa,YAAY;AAE5B,QAAM,cAA+C,CAAA;AAE1C,aAAA,KAAK,QAAQ,cACxB;AACU,UAAA,OAAO,QAAQ,aAAa,CAAC;AAEnC,gBAAY,CAAC,IAAI;MACb,UAAU,GAAG,mBAAmB,cAAc,CAAC;MAC/C,OAAO,aAAa,KAAK,MAAM,KAAK,IAAI;IAAA;EAC5C;AAGJ,QAAM,YAAY,IAAI,cAAc,cAAc,WAAW;AAEtD,SAAA;AACX;;;ACjEA,IAAM,kBAAkC;EACpC,cAAc;EACd,YAAY;AAChB;AAOO,IAAM,iBAAN,MACP;EAkBI,YAAY,UACZ;AATA,SAAO,iBAA4B;AAE3B,SAAA,mBAAyD,uBAAA,OAAO,IAAI;AAIpE,SAAA,uBAAkE,uBAAA,OAAO,IAAI;AAIjF,SAAK,YAAY;AACjB,SAAK,UAAU,aAAa,eAAe,MAAM,kBAAkB;EAAA;EAG7D,cAAc,IACxB;AACI,SAAK,MAAM;AAEN,SAAA,mBAA0B,uBAAA,OAAO,IAAI;AAKrC,SAAA,uBAA8B,uBAAA,OAAO,IAAI;AAC9C,SAAK,iBAAiB;EAAA;;;;;;;EASnB,KAAK,QAAgB,UAC5B;AACS,SAAA,YAAY,OAAO,SAAS;AAE7B,QAAA;AAAU;AAEd,oBAAgB,eAAe;AAC/B,oBAAgB,aAAa;AAE7B,QAAI,eAAe,KAAK,qBAAqB,OAAO,UAAU,IAAI;AAElE,QAAI,CAAC,cACL;AACmB,qBAAA,KAAK,qBAAqB,OAAO,UAAU,IAAI,IAAI,KAAK,oBAAoB,QAAQ,IAAI;IAAA;AAI3G,SAAK,UAAU,OAAO,aAAa,CAAC,CAAC,OAAO,UAAU,yBAAyB;AAClE,iBAAA,KAAK,WAAW,QAAQ,eAAe;EAAA;;;;;EAOjD,mBAAmB,cAC1B;AACI,SAAK,UAAU,aAAa,mBAAmB,cAAc,KAAK,gBAAgB,eAAe;EAAA;;;;;;;EAS9F,iBAAiB,cAA6C,MAAc,QAAQ,GAC3F;AACU,UAAA,eAAe,KAAK,UAAU;AACpC,UAAM,cAAc,KAAK,gBAAgB,KAAK,cAAc;AAE5D,UAAM,mBAAoB,aAAgC;AAE1D,QAAI,CAAC,kBACL;AACS,WAAA,UAAU,IAAI,mBAAmB,YAA4B;IAAA;AAGtE,UAAM,SAAS,aAAa;AAEtB,UAAA,WAAW,aAAa,aAAa,MAAM;AAE3C,UAAA,gBAAgB,aAAa,0BAA0B,QAAQ;AAErE,QAAI,kBACJ;AACU,YAAA,EAAE,QAAQ,KAAA,IAAU;AAG1B,UAAI,WAAW,KAAK,SAAS,OAAO,KAAK,YACzC;AACiB,qBAAA,eAAe,UAAU,aAAa;MAAA,OAGvD;AACiB,qBAAA,gBAAgB,UAAU,eAAe,MAAM;MAAA;IAChE,WAEK,aAAa,wBAAwB,QAAQ,MAAM,eAC5D;AAEiB,mBAAA,eAAe,UAAU,aAAa;IAAA;AAGvD,UAAM,oBAAoB,KAAK,eAAe,kBAAkB,IAAI,EAAE;AAElE,QAAA,YAAY,qBAAqB,KAAK,MAAM;AAAe;AACnD,gBAAA,qBAAqB,KAAK,IAAI;AAE1C,SAAK,UAAU,GAAG,oBAAoB,YAAY,SAAS,mBAAmB,aAAa;EAAA;EAGvF,YAAY,SACpB;AACI,QAAI,KAAK,mBAAmB;AAAS;AAErC,SAAK,iBAAiB;AAEhB,UAAA,cAAc,KAAK,gBAAgB,OAAO;AAE3C,SAAA,IAAI,WAAW,YAAY,OAAO;EAAA;;;;;EAOpC,gBAAgB,SACvB;AACI,WAAO,KAAK,iBAAiB,QAAQ,IAAI,KAAK,KAAK,mBAAmB,OAAO;EAAA;EAGzE,mBAAmB,SAC3B;AACI,UAAM,MAAM,QAAQ;AAEpB,SAAK,iBAAiB,GAAG,IAAI,gBAAgB,KAAK,KAAK,OAAO;AAEvD,WAAA,KAAK,iBAAiB,GAAG;EAAA;EAG7B,UACP;AACI,eAAW,OAAO,OAAO,KAAK,KAAK,gBAAgB,GACnD;AACU,YAAA,cAAc,KAAK,iBAAiB,GAAG;AAE7C,kBAAY,QAAQ;AACf,WAAA,iBAAiB,GAAG,IAAI;IAAA;AAGjC,SAAK,mBAAmB;EAAA;;;;;;;;;EAWrB,oBAAoB,QAAgB,cAC3C;AACW,WAAA,uBAAuB,QAAQ,YAAY;EAAA;EAG/C,aACP;AACI,SAAK,iBAAiB;EAAA;AAE9B;AAzLa,eAGK,YAAY;EACtB,MAAM;IACF,cAAc;EAAA;EAElB,MAAM;AACV;;;AChCG,IAAM,4BAAoE;EAC7E,KAAK;;;;EAIL,aAAa;;;;;EAKb,aAAa;;;;;;EAMb,aAAa;;;;;;;EAOb,KAAK;;;;EAIL,aAAa;;;;;EAKb,aAAa;;;;;;EAMb,aAAa;;;;;;;EAOb,KAAK;;;;EAIL,aAAa;;;;;EAKb,aAAa;;;;;;EAMb,aAAa;;;;;;;EAOb,MAAM;;;;EAIN,cAAc;;;;;EAKd,cAAc;;;;;;EAMd,cAAc;;;;;;;EAOd,eAAe;EACf,eAAe;EACf,eAAe;AACnB;AAGO,IAAM,2BAAmE;EAC5E,KAAK;EACL,aAAa;EACb,aAAa;EACb,aAAa;EACb,eAAe;EACf,eAAe;EACf,eAAe;EACf,KAAK;EACL,aAAa;EACb,aAAa;EACb,aAAa;EACb,KAAK;EACL,aAAa;EACb,aAAa;EACb,aAAa;EACb,MAAM;EACN,cAAc;EACd,cAAc;EACd,cAAc;AAClB;;;AC1GgB,SAAA,qBAAqB,OAAqB,aAC1D;AACI,QAAM,gBAAgB,CAAC;;;;;;;KAOtB;AAEU,aAAA,KAAK,MAAM,UACtB;AACQ,QAAA,CAAC,YAAY,CAAC,GAClB;AACI,UAAI,MAAM,SAAS,CAAC,aAAa,cACjC;AACI,YAAK,MAAM,SAAS,CAAC,EAAmB,KACxC;AACI,wBAAc,KAAK;8DACuB,CAAC,MAAM,CAAC;qBACjD;QAAA,OAGL;AACI,wBAAc,KAAK;gEACyB,CAAC;qBAC5C;QAAA;MACL,WAEK,MAAM,SAAS,CAAC,aAAa,gBACtC;AACI,sBAAc,KAAK;gEAC6B,CAAC,MAAM,CAAC;qBACnD;MAAA;AAGT;IAAA;AAGE,UAAA,UAAU,MAAM,kBAAkB,CAAC;AAEzC,QAAI,SAAS;AAEb,aAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,KAC3C;AACU,YAAA,SAAS,eAAe,CAAC;AAE/B,UAAI,QAAQ,SAAS,OAAO,QAAQ,OAAO,KAAK,OAAO,GACvD;AACI,sBAAc,KAAK,WAAW,CAAC,MAAM,eAAe,CAAC,EAAE,OAAO;AACrD,iBAAA;AAET;MAAA;IACJ;AAGJ,QAAI,CAAC,QACL;AACI,YAAM,eAAe,QAAQ,SAAS,IAAI,4BAA4B;AAEhE,YAAA,WAAW,aAAa,QAAQ,IAAI,EAAE,QAAQ,YAAY,OAAO,CAAC,aAAa;AAErF,oBAAc,KAAK;uBACR,CAAC;;sBAEF,CAAC;cACT,QAAQ,GAAG;IAAA;EACjB;AAUG,SAAA,IAAI,SAAS,MAAM,MAAM,YAAY,YAAY,cAAc,KAAK,IAAI,CAAC;AACpF;;;AClFO,IAAM,uBAAN,MACP;;EAsBI,YAAY,UACZ;AAPA,SAAQ,SAA+C,CAAA;AAGvD,SAAQ,wBAA8E,CAAA;AAKlF,SAAK,YAAY;AAEjB,SAAK,KAAK;AACV,SAAK,SAAS,CAAA;EAAC;EAGT,cAAc,IACxB;AACI,SAAK,KAAK;EAAA;;;;;;;;EAUP,mBAAmB,OAAqB,SAAoB,UACnE;AACI,UAAM,cAAc,KAAK,UAAU,OAAO,gBAAgB,OAAO;AAE7D,QAAA,CAAC,MAAM,YAAY,MAAM,aAAa,YAAY,mBAAmB,MAAM,GAAG,GAClF;AACI,kBAAY,mBAAmB,MAAM,GAAG,IAAI,MAAM;AAElD,YAAM,WAAW,KAAK,wBAAwB,OAAO,OAAO;AAE5D,eAAS,YAAY,aAAa,MAAM,UAAU,KAAK,WAAW,QAAQ;IAAA;EAC9E;;;;;;EAQI,wBAAwB,OAAqB,SACrD;AACW,WAAA,KAAK,sBAAsB,MAAM,UAAU,IAAI,QAAQ,IAAI,KAC3D,KAAK,2BAA2B,OAAO,OAAO;EAAA;EAGjD,2BAA2B,OAAqB,SACxD;AACU,UAAA,uBAAuB,KAAK,sBAAsB,MAAM,UAAU,MAChE,KAAK,sBAAsB,MAAM,UAAU,IAAI,CAAA;AAEvD,UAAM,KAAK,KAAK,cAAc,OAAO,QAAQ,cAAc,GAAG;AAE9D,QAAI,CAAC,KAAK,OAAO,EAAE,GACnB;AACI,WAAK,OAAO,EAAE,IAAI,KAAK,sBAAsB,OAAO,QAAQ,YAAY;IAAA;AAG5E,yBAAqB,QAAQ,IAAI,IAAI,KAAK,OAAO,EAAE;AAE5C,WAAA,qBAAqB,QAAQ,IAAI;EAAA;EAGpC,sBAAsB,OAAqB,aACnD;AACW,WAAA,qBAAqB,OAAO,WAAW;EAAA;;;;;;;;;EAW1C,cAAc,OAAqB,aAAkC,QAC7E;AACI,UAAM,WAAW,MAAM;AAEvB,UAAM,UAAU,CAAC,GAAG,MAAM,GAAG;AAE7B,eAAW,KAAK,UAChB;AACI,cAAQ,KAAK,CAAC;AAEV,UAAA,YAAY,CAAC,GACjB;AACI,gBAAQ,KAAK,YAAY,CAAC,EAAE,IAAI;MAAA;IACpC;AAGG,WAAA,QAAQ,KAAK,GAAG;EAAA;;EAIpB,UACP;AACI,SAAK,YAAY;AACjB,SAAK,SAAS;EAAA;AAEtB;AA3Ha,qBAGK,YAAY;EACtB,MAAM;IACF,cAAc;EAAA;EAElB,MAAM;AACV;;;ACZG,SAAS,yBAAyB,IACzC;AACI,QAAM,WAAmD,CAAA;AAIzD,WAAS,SAAS,CAAC,GAAG,KAAK,GAAG,mBAAmB;AACjD,WAAS,MAAM,CAAC,GAAG,KAAK,GAAG,GAAG;AACrB,WAAA,WAAW,CAAC,GAAG,WAAW,GAAG,qBAAqB,GAAG,KAAK,GAAG,mBAAmB;AAChF,WAAA,SAAS,CAAC,GAAG,KAAK,GAAG,qBAAqB,GAAG,KAAK,GAAG,mBAAmB;AACxE,WAAA,OAAO,CAAC,GAAG,CAAC;AAGZ,WAAA,YAAY,IAAI,CAAC,GAAG,WAAW,GAAG,qBAAqB,GAAG,KAAK,GAAG,mBAAmB;AACrF,WAAA,SAAS,IAAI,CAAC,GAAG,WAAW,GAAG,KAAK,GAAG,KAAK,GAAG,GAAG;AAClD,WAAA,YAAY,IAAI,CAAC,GAAG,WAAW,GAAG,qBAAqB,GAAG,KAAK,GAAG,mBAAmB;AAE9F,WAAS,QAAQ,CAAC,GAAG,MAAM,GAAG,mBAAmB;AAEjD,QAAM,WAAW,EAAE,cAAc,WAAW,IAAA,EAAM,yBAAyB;AAE3E,MAAI,UACJ;AACI,aAAS,MAAM,CAAC,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,GAAG;AAC9D,aAAS,MAAM,CAAC,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,GAAG;EAAA,OAGlE;AACU,UAAA,MAAM,GAAG,aAAa,kBAAkB;AAE9C,QAAI,KACJ;AACI,eAAS,MAAM,CAAC,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,IAAI,SAAS,IAAI,OAAO;AACxE,eAAS,MAAM,CAAC,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,IAAI,SAAS,IAAI,OAAO;IAAA;EAC5E;AAgBG,SAAA;AACX;;;ACpDA,IAAM,QAAQ;AACd,IAAM,SAAS;AACf,IAAM,UAAU;AAChB,IAAM,aAAa;AACnB,IAAM,UAAU;AAChB,IAAM,aAAa;AAOZ,IAAM,iBAAN,MAAMC,gBACb;EAqEI,YAAY,UACZ;AAPA,SAAQ,mBAA4B;AAQhC,SAAK,KAAK;AAEV,SAAK,UAAU;AACf,SAAK,gBAAgB;AACrB,SAAK,YAAY;AAEjB,SAAK,WAAW;AAGhB,SAAK,MAAM,CAAA;AACN,SAAA,IAAI,KAAK,IAAI,KAAK;AAClB,SAAA,IAAI,MAAM,IAAI,KAAK;AACnB,SAAA,IAAI,OAAO,IAAI,KAAK;AACpB,SAAA,IAAI,UAAU,IAAI,KAAK;AACvB,SAAA,IAAI,OAAO,IAAI,KAAK;AACpB,SAAA,IAAI,UAAU,IAAI,KAAK;AAE5B,SAAK,SAAS,CAAA;AAET,SAAA,eAAe,MAAM,MAAM;AAIvB,aAAA,aAAa,qBAAqB,IAAI,IAAI;EAAA;EAG7C,qBAAqB,cAC/B;AACS,SAAA,mBAAmB,CAAC,aAAa;AAGtC,QAAI,KAAK,WACT;AAES,WAAA,aAAa,KAAK,UAAU;IAAA,OAGrC;AAEI,WAAK,kBAAkB;IAAA;EAC3B;EAGM,cAAc,IACxB;AACI,SAAK,KAAK;AAEL,SAAA,gBAAgB,yBAAyB,EAAE;AAIhD,SAAK,WAAW;EAAA;;;;;EAOb,IAAI,OACX;AACI,cAAA,QAAU,KAAK;AAGX,QAAA,KAAK,YAAY,MAAM,MAC3B;AACQ,UAAA,OAAO,KAAK,UAAU,MAAM;AAChC,UAAI,IAAI;AAGR,aAAO,MACP;AACI,YAAI,OAAO,GACX;AAES,eAAA,IAAI,CAAC,EAAE,KAAK,MAAM,CAAC,EAAE,MAAM,OAAQ,KAAK,EAAG;QAAA;AAG3C,iBAAA;AACT;MAAA;AAGJ,WAAK,UAAU,MAAM;IAAA;AAMzB,aAAS,IAAI,GAAG,IAAI,KAAK,OAAO,QAAQ,KACxC;AACI,WAAK,OAAO,CAAC,EAAE,MAAM,KAAK;IAAA;EAC9B;;;;;EAOG,WAAW,OAClB;AACI,cAAA,QAAU,KAAK;AACf,aAAS,IAAI,GAAG,IAAI,KAAK,IAAI,QAAQ,KACrC;AACS,WAAA,IAAI,CAAC,EAAE,KAAK,MAAM,CAAC,EAAE,MAAM,OAAQ,KAAK,EAAG;IAAA;AAEpD,aAAS,IAAI,GAAG,IAAI,KAAK,OAAO,QAAQ,KACxC;AACI,WAAK,OAAO,CAAC,EAAE,MAAM,KAAK;IAAA;AAG9B,SAAK,UAAU,MAAM;EAAA;;;;;EAOlB,SAAS,OAChB;AACS,SAAA,aAAaA,gBAAc,iBAAiB,KAAK;AAEtD,SAAK,GAAG,QAAQ,WAAW,SAAS,EAAE,KAAK,GAAG,KAAK;EAAA;;;;;EAOhD,UAAU,OACjB;AACS,SAAA,aAAaA,gBAAc,qBAAqB,KAAK;AAE1D,SAAK,GAAG,QAAQ,WAAW,SAAS,EAAE,KAAK,GAAG,mBAAmB;EAAA;;;;;EAO9D,aAAa,OACpB;AACI,SAAK,GAAG,QAAQ,WAAW,SAAS,EAAE,KAAK,GAAG,UAAU;EAAA;;;;;EAOrD,aAAa,OACpB;AACS,SAAA,GAAG,UAAU,KAAK;EAAA;;;;;EAOpB,YAAY,OACnB;AACI,SAAK,YAAY;AACjB,SAAK,GAAG,QAAQ,WAAW,SAAS,EAAE,KAAK,GAAG,SAAS;AAEnD,QAAA,KAAK,aAAa,KAAK,iBAC3B;AAES,WAAA,aAAa,KAAK,UAAU;IAAA;EACrC;;;;;EAOG,aAAa,OACpB;AACI,SAAK,aAAa;AAClB,SAAK,kBAAkB;AAEvB,UAAM,WAAW,KAAK,mBAAmB,CAAC,QAAQ;AAE9C,QAAA,KAAK,iBAAiB,UAC1B;AACI,WAAK,eAAe;AACpB,WAAK,GAAG,UAAU,KAAK,GAAG,WAAW,OAAO,KAAK,CAAC;IAAA;EACtD;;;;;EAOG,aAAa,OACpB;AACI,QAAI,CAAC,KAAK,cAAc,KAAK,GAC7B;AACY,cAAA;IAAA;AAGR,QAAA,UAAU,KAAK,WACnB;AACI;IAAA;AAGJ,SAAK,YAAY;AAEX,UAAA,OAAO,KAAK,cAAc,KAAK;AACrC,UAAM,KAAK,KAAK;AAEZ,QAAA,KAAK,WAAW,GACpB;AACI,SAAG,UAAU,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;IAAA,OAGjC;AACI,SAAG,kBAAkB,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;IAAA;AAGvD,QAAA,KAAK,WAAW,GACpB;AACI,WAAK,WAAW;AAChB,SAAG,sBAAsB,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;IAAA,WAEpC,KAAK,UACd;AACI,WAAK,WAAW;AAChB,SAAG,sBAAsB,GAAG,UAAU,GAAG,QAAQ;IAAA;EACrD;;;;;;EAQG,iBAAiB,OAAe,OACvC;AACS,SAAA,GAAG,cAAc,OAAO,KAAK;EAAA;;EAI/B,aACP;AACI,SAAK,eAAe;AACpB,SAAK,aAAa;AAClB,SAAK,YAAY;AACjB,SAAK,kBAAkB;AACvB,SAAK,mBAAmB;AAExB,SAAK,GAAG,UAAU,KAAK,GAAG,GAAG;AAC7B,SAAK,GAAG,YAAY,KAAK,GAAG,qBAAqB,KAAK;AAEjD,SAAA,WAAW,KAAK,YAAY;AAEjC,SAAK,WAAW;AAEhB,SAAK,YAAY;AACjB,SAAK,aAAa,QAAQ;EAAA;;;;;;;;;;EAYtB,aAAa,MAA4C,OACjE;AACI,UAAM,QAAQ,KAAK,OAAO,QAAQ,IAAI;AAElC,QAAA,SAAS,UAAU,IACvB;AACS,WAAA,OAAO,KAAK,IAAI;IAAA,WAEhB,CAAC,SAAS,UAAU,IAC7B;AACS,WAAA,OAAO,OAAO,OAAO,CAAC;IAAA;EAC/B;;;;;;EAQJ,OAAe,gBAAgB,QAAuB,OACtD;AACW,WAAA,aAAa,MAAM,SAAS;EAAA;;;;;;EAQvC,OAAe,oBAAoB,QAAuB,OAC1D;AACW,WAAA,iBAAiB,GAAG,MAAM,aAAa;EAAA;;EAI3C,UACP;AACI,SAAK,KAAK;AACV,SAAK,OAAO,SAAS;EAAA;AAE7B;AA3Xa,eAGK,YAAY;EACtB,MAAM;IACF,cAAc;EAAA;EAElB,MAAM;AACV;AARG,IAAM,gBAAN;;;ACfA,IAAM,YAAN,MACP;EA0BI,YAAY,SACZ;AA1BA,SAAO,SAAqB,WAAW;AA2BnC,SAAK,UAAU;AACf,SAAK,QAAQ;AACb,SAAK,SAAS;AACd,SAAK,OAAO,SAAS;AACrB,SAAK,iBAAiB,WAAW;AACjC,SAAK,SAAS,WAAW;AACzB,SAAK,cAAc;EAAA;AAE3B;;;ACtCO,IAAM,8BAA8B;EAEvC,IAAI;EAEJ,OAAO,QAAuB,WAAsB,IACpD;AACI,QAAI,UAAU,UAAU,OAAO,SAAS,UAAU,WAAW,OAAO,QACpE;AACO,SAAA;QACC,GAAG;QACH;QACA;QACA;QACA,OAAO;QACP,OAAO;QACP,UAAU;QACV,UAAU;QACV,OAAO;MAAA;IACX,OAGJ;AACO,SAAA;QACC,UAAU;QACV;QACA,UAAU;QACV,OAAO;QACP,OAAO;QACP;QACA,UAAU;QACV,UAAU;QACV,OAAO;MAAA;IACX;AAGJ,cAAU,QAAQ,OAAO;AACzB,cAAU,SAAS,OAAO;EAAA;AAElC;;;ACvCA,IAAM,sBAA+C;EACjD,kBAAkB;EAClB,uBAAuB;EACvB,kBAAkB;EAClB,uBAAuB;EACvB,kBAAkB;EAClB,uBAAuB;EACvB,eAAe;EACf,eAAe;EACf,gBAAgB;EAChB,gBAAgB;EAChB,mBAAmB;EACnB,kBAAkB;EAClB,kBAAkB;EAClB,uBAAuB;;;EAIvB,kBAAkB;EAClB,uBAAuB;EACvB,oBAAoB;EACpB,yBAAyB;EACzB,mBAAmB;EACnB,wBAAwB;EACxB,gBAAgB;EAChB,gBAAgB;EAChB,iBAAiB;EACjB,iBAAiB;;;EAIjB,kBAAkB;EAClB,uBAAuB;EACvB,kBAAkB;EAClB,uBAAuB;EACvB,kBAAkB;EAClB,uBAAuB;EACvB,kBAAkB;EAClB,uBAAuB;EACvB,kBAAkB;EAClB,uBAAuB;EACvB,kBAAkB;EAClB,uBAAuB;EACvB,kBAAkB;EAClB,uBAAuB;EACvB,kBAAkB;EAClB,uBAAuB;EACvB,mBAAmB;EACnB,wBAAwB;EACxB,mBAAmB;EACnB,wBAAwB;EACxB,mBAAmB;EACnB,wBAAwB;EACxB,oBAAoB;EACpB,yBAAyB;EACzB,oBAAoB;EACpB,yBAAyB;EACzB,oBAAoB;EACpB,yBAAyB;AAC7B;AAGO,IAAM,oCAAoC;EAE7C,IAAI;EAEJ,OAAO,QAA0B,WAAsB,IACvD;AACO,OAAA,YAAY,GAAG,kBAAkB,CAAC;AAErC,QAAI,WAAW,OAAO;AACtB,QAAI,YAAY,OAAO;AAEvB,UAAM,aAAa,CAAC,CAAC,oBAAoB,OAAO,MAAM;AAEtD,aAAS,IAAI,GAAG,IAAI,OAAO,SAAS,QAAQ,KAC5C;AACU,YAAA,cAAc,OAAO,SAAS,CAAC;AAErC,UAAI,YACJ;AACO,WAAA;UACC,GAAG;UAAY;UAAG,UAAU;UAC5B;UAAU;UAAW;UACrB;QAAA;MACJ,OAGJ;AACO,WAAA;UACC,GAAG;UAAY;UAAG,UAAU;UAC5B;UAAU;UAAW;UACrB,UAAU;UAAQ,UAAU;UAC5B;QAAA;MAAW;AAGnB,iBAAW,KAAK,IAAI,YAAY,GAAG,CAAC;AACpC,kBAAY,KAAK,IAAI,aAAa,GAAG,CAAC;IAAA;EAC1C;AAER;;;AClGO,IAAM,wBAAwB;EAEjC,IAAI;EAEJ,OAAO,QAAoC,WAAsB,IAAwB,cACzF;AACI,UAAM,UAAU,UAAU;AAC1B,UAAM,WAAW,UAAU;AAE3B,UAAM,eAAe,OAAO;AAC5B,UAAM,gBAAgB,OAAO;AAE7B,UAAM,gBAAgB,OAAO;AAC7B,UAAM,iBAAiB,OAAO;AAE1B,QAAA,gBAAgB,gBAAgB,iBAAiB,eACrD;AACQ,UAAA,YAAY,gBAAgB,aAAa,eAC7C;AACO,WAAA;UACC,UAAU;UACV;UACA,UAAU;UACV;UACA;UACA;UACA,UAAU;UACV,UAAU;UACV;QAAA;MACJ;AAGJ,UAAI,iBAAiB,GACrB;AACO,WAAA;UACC,GAAG;UACH;UACA;UACA;UACA;UACA;UACA,UAAU;UACV,UAAU;UACV,OAAO;QAAA;MACX,OAGJ;AACO,WAAA;UACC,GAAG;UACH;UACA;UACA;UACA,UAAU;UACV,UAAU;UACV,OAAO;QAAA;MACX;IACJ,WAEK,YAAY,gBAAgB,aAAa,eAClD;AACO,SAAA;QACC,GAAG;QACH;QACA;QACA;QACA,UAAU;QACV,UAAU;QACV,OAAO;MAAA;IACX,WAEK,iBAAiB,GAC1B;AACO,SAAA;QACC,UAAU;QACV;QACA,UAAU;QACV;QACA;QACA;QACA,UAAU;QACV,UAAU;QACV,OAAO;MAAA;IACX,OAGJ;AACO,SAAA;QACC,UAAU;QACV;QACA,UAAU;QACV,UAAU;QACV,UAAU;QACV,OAAO;MAAA;IACX;AAGJ,cAAU,QAAQ;AAClB,cAAU,SAAS;EAAA;AAE3B;;;ACnGO,IAAM,wBAAwB;EAEjC,IAAI;EAEJ,OAAO,QAAqB,WAAsB,IAAwB,cAC1E;AACQ,QAAA,CAAC,OAAO,SACZ;AACO,SAAA;QACC,UAAU;QACV;QACA,UAAU;QACV;QACA;QACA;QACA,UAAU;QACV,UAAU;QACV;MAAA;AAGJ;IAAA;AAGJ,0BAAsB,OAAO,QAAQ,WAAW,IAAI,YAAY;EAAA;AAExE;;;AChCO,IAAM,sBAAsB;EAC/B,QAAQ;EACR,SAAS;AACb;AAGO,IAAM,4BAA4B;EACrC,QAAQ;IACJ,QAAQ;IACR,SAAS;EAAA;EAEb,SAAS;IACL,QAAQ;IACR,SAAS;EAAA;AAEjB;AAGO,IAAM,sBAAsB;EAC/B,iBAAiB;EACjB,QAAQ;EACR,iBAAiB;AACrB;AAGO,IAAM,yBAAyB;EAClC,OAAO;EACP,MAAM;EACN,OAAO;EACP,cAAc;EACd,SAAS;EACT,aAAa;EACb,iBAAiB;EACjB,QAAQ;AACZ;;;ACfgB,SAAA,iBACZ,OACA,IACA,SAEA,gBACA,gBACA,YACA,YAEA,eAEJ;AACI,QAAM,YAAY;AAEd,MAAA,CAAC,iBACE,MAAM,iBAAiB,YACvB,MAAM,iBAAiB,YACvB,MAAM,iBAAiB,UAE9B;AAEI,UAAM,YAAY,oBAAoB,aAAa,kBAAkB,MAAM,YAAY;AACvF,UAAM,YAAY,oBAAoB,aAAa,kBAAkB,MAAM,YAAY;AACvF,UAAM,YAAY,oBAAoB,aAAa,kBAAkB,MAAM,YAAY;AAEvF,OAAG,cAAc,EAAE,WAAW,GAAG,gBAAgB,SAAS;AAC1D,OAAG,cAAc,EAAE,WAAW,GAAG,gBAAgB,SAAS;AAG1D,QAAI,GAAG;AAAgB,SAAG,cAAc,EAAE,WAAW,GAAG,gBAAgB,SAAS;EAAA;AAGrF,MAAI,CAAC,iBAAiB,MAAM,cAAc,UAC1C;AAEO,OAAA,cAAc,EAAE,WAAW,GAAG,oBAAoB,oBAAoB,MAAM,SAAS,CAAC;EAAA;AAK7F,MAAI,SACJ;AACI,QAAI,CAAC,iBAAiB,MAAM,iBAAiB,UAC7C;AACI,YAAM,eAAe,0BAA0B,MAAM,SAAS,EAAE,MAAM,YAAY;AAElF,SAAG,cAAc,EAAE,WAAW,GAAG,oBAAoB,YAAY;IAAA;EACrE,OAIJ;AACO,OAAA,cAAc,EAAE,WAAW,GAAG,oBAAoB,oBAAoB,MAAM,SAAS,CAAC;EAAA;AAIzF,MAAA,kBAAkB,MAAM,gBAAgB,GAC5C;AACU,UAAA,QAAQ,KAAK,IAAI,MAAM,eAAe,GAAG,aAAa,eAAe,8BAA8B,CAAC;AAE1G,OAAG,cAAc,EAAE,WAAW,eAAe,4BAA4B,KAAK;EAAA;AAIlF,MAAI,MAAM,SACV;AACO,OAAA,cAAc,EAAE,WAAW,GAAG,sBAAsB,uBAAuB,MAAM,OAAO,CAAC;EAAA;AAEpG;;;AChFO,SAAS,oBAAoB,IACpC;AACW,SAAA;;IAEH,SAAS,GAAG;IACZ,SAAS,GAAG;IACZ,QAAQ,GAAG;IACX,QAAQ,GAAG;;IAGX,SAAS,GAAG;IACZ,SAAY,GAAG;IACf,UAAU,GAAG;IACb,UAAW,GAAG;IACd,UAAY,GAAG;IACf,SAAU,GAAG;IACb,SAAU,GAAG;;IAGb,SAAS,GAAG;IACZ,SAAS,GAAG;IACZ,UAAU,GAAG;IACb,UAAY,GAAG;IACf,UAAW,GAAG;IACd,WAAY,GAAG;IACf,YAAY,GAAG;IACf,mBAAmB,GAAG;;IAGtB,YAAY,GAAG;IACf,WAAW,GAAG;IACd,WAAW,GAAG;IACd,YAAY,GAAG;IACf,mBAAmB,GAAG;IACtB,cAAc,GAAG;IACjB,cAAc,GAAG;IACjB,eAAe,GAAG;;IAGlB,UAAU,GAAG;IACb,UAAU,GAAG;IACb,WAAY,GAAG;IACf,YAAY,GAAG;IACf,YAAY,GAAG;IACf,aAAa,GAAG;;IAGhB,YAAY,GAAG;IACf,YAAY,GAAG;IACf,aAAa,GAAG;;IAGhB,UAAU,GAAG;IACb,cAAc,GAAG;IACjB,aAAa,GAAG;IAChB,wBAAwB,GAAG;IAC3B,cAAc,GAAG;IACjB,yBAAyB,GAAG;EAAA;AAGpC;;;ACxDgB,SAAA,4BACZ,IACAC,aAEJ;AACI,MAAI,OAAO,CAAA;AACX,MAAI,aAAqB,GAAG;AAE5B,MAAI,EAAE,cAAc,WAAW,IAAI,EAAE,yBAAA,IACrC;AACW,WAAA;MACH,mBAAmB,GAAG;MACtB,mBAAmB,GAAG;IAAA;AAG1B,iBAAa,GAAG;EAAA,WAEXA,YAAW,MACpB;AACW,WAAA;MACH,mBAAmBA,YAAW,KAAK;MACnC,mBAAmBA,YAAW,KAAK;IAAA;EACvC;AAGG,SAAA;;IAEH,SAAS,GAAG;IACZ,SAAS,GAAG;IACZ,QAAQ,GAAG;IACX,QAAQ,GAAG;;IAGX,SAAS,GAAG;IACZ,SAAS,GAAG;IACZ,UAAU,GAAG;IACb,UAAU,GAAG;IACb,UAAU,GAAG;IACb,SAAS,GAAG;IACZ,SAAS,GAAG;;IAGZ,SAAS,GAAG;IACZ,SAAS,GAAG;IACZ,UAAU,GAAG;IACb,UAAU,GAAG;IACb,UAAU,GAAG;IACb,WAAW,GAAG;IACd,YAAY,GAAG;IAEf,GAAG;;IAGH,YAAY,GAAG;IACf,WAAW,GAAG;IACd,WAAW,GAAG;IACd;IACA,cAAc,GAAG;IACjB,cAAc,GAAG;IACjB,eAAe,GAAG;;IAGlB,UAAU,GAAG;IACb,UAAU,GAAG;IACb,WAAW,GAAG;IACd,YAAY,GAAG;IACf,YAAY,GAAG;IACf,aAAa,GAAG;;IAGhB,YAAY,GAAG;IACf,YAAY,GAAG;IACf,aAAa,GAAG;;IAGhB,UAAU,GAAG;IACb,cAAc,GAAG;IACjB,aAAa,GAAG;IAChB,wBAAwB,GAAG;IAC3B,cAAc,GAAG;IACjB,yBAAyB,GAAG;;IAG5B,GAAGA,YAAW,OAAO;MACjB,kBAAkBA,YAAW,KAAK;MAClC,kBAAkBA,YAAW,KAAK;MAClC,kBAAkBA,YAAW,KAAK;IAAA,IAClC,CAAA;IACJ,GAAGA,YAAW,YAAY;MACtB,uBAAuBA,YAAW,UAAU;MAC5C,uBAAuBA,YAAW,UAAU;MAC5C,uBAAuBA,YAAW,UAAU;IAAA,IAC5C,CAAA;IACJ,GAAGA,YAAW,OAAO;MACjB,eAAeA,YAAW,KAAK;MAC/B,eAAeA,YAAW,KAAK;MAC/B,gBAAgBA,YAAW,KAAK;MAChC,gBAAgBA,YAAW,KAAK;IAAA,IAChC,CAAA;IACJ,GAAGA,YAAW,OAAO;MACjB,kBAAkBA,YAAW,KAAK;MAClC,mBAAmBA,YAAW,KAAK;MACnC,kBAAkBA,YAAW,KAAK;MAClC,uBAAuBA,YAAW,KAAK;IAAA,IACvC,CAAA;IACJ,GAAGA,YAAW,MAAM;MAChB,kBAAkBA,YAAW,IAAI;MACjC,uBAAuBA,YAAW,IAAI;MACtC,oBAAoBA,YAAW,IAAI;MACnC,yBAAyBA,YAAW,IAAI;MACxC,mBAAmBA,YAAW,IAAI;MAClC,wBAAwBA,YAAW,IAAI;MACvC,gBAAgBA,YAAW,IAAI;;MAE/B,iBAAiBA,YAAW,IAAI;;IAAA,IAEhC,CAAA;IACJ,GAAGA,YAAW,OAAO;MACjB,kBAAkBA,YAAW,KAAK;MAClC,uBAAuBA,YAAW,KAAK;MACvC,kBAAkBA,YAAW,KAAK;MAClC,uBAAuBA,YAAW,KAAK;MACvC,kBAAkBA,YAAW,KAAK;MAClC,uBAAuBA,YAAW,KAAK;MACvC,kBAAkBA,YAAW,KAAK;MAClC,uBAAuBA,YAAW,KAAK;MACvC,kBAAkBA,YAAW,KAAK;MAClC,uBAAuBA,YAAW,KAAK;MACvC,kBAAkBA,YAAW,KAAK;MAClC,uBAAuBA,YAAW,KAAK;MACvC,kBAAoBA,YAAW,KAAK;MACpC,uBAAuBA,YAAW,KAAK;MACvC,kBAAkBA,YAAW,KAAK;MAClC,uBAAuBA,YAAW,KAAK;MACvC,mBAAmBA,YAAW,KAAK;MACnC,wBAAwBA,YAAW,KAAK;MACxC,mBAAmBA,YAAW,KAAK;MACnC,wBAAwBA,YAAW,KAAK;MACxC,mBAAmBA,YAAW,KAAK;MACnC,wBAAwBA,YAAW,KAAK;MACxC,oBAAoBA,YAAW,KAAK;MACpC,yBAAyBA,YAAW,KAAK;MACzC,oBAAoBA,YAAW,KAAK;MACpC,yBAAyBA,YAAW,KAAK;MACzC,oBAAoBA,YAAW,KAAK;MACpC,yBAAyBA,YAAW,KAAK;IAAA,IACzC,CAAA;EAAC;AAEb;;;ACxJO,SAAS,kBAAkB,IAClC;AACW,SAAA;;IAEH,SAAS,GAAG;IACZ,SAAS,GAAG;IACZ,QAAQ,GAAG;IACX,QAAQ,GAAG;;IAGX,SAAS,GAAG;IACZ,SAAS,GAAG;IACZ,UAAU,GAAG;IACb,UAAU,GAAG;IACb,UAAU,GAAG;IACb,SAAS,GAAG;IACZ,SAAS,GAAG;;IAGZ,SAAS,GAAG;IACZ,SAAS,GAAG;IACZ,UAAU,GAAG;IACb,UAAU,GAAG;IACb,UAAU,GAAG;IACb,WAAW,GAAG;IACd,YAAY,GAAG;IACf,mBAAmB,GAAG;;IAGtB,YAAY,GAAG;IACf,WAAW,GAAG;IACd,WAAW,GAAG;IACd,YAAY,GAAG;IACf,mBAAmB,GAAG;IACtB,cAAc,GAAG;IACjB,cAAc,GAAG;IACjB,eAAe,GAAG;;IAGlB,UAAU,GAAG;IACb,UAAU,GAAG;IACb,WAAW,GAAG;IACd,YAAY,GAAG;IACf,YAAY,GAAG;IACf,aAAa,GAAG;;IAGhB,YAAY,GAAG;IACf,YAAY,GAAG;IACf,aAAa,GAAG;;IAGhB,UAAU,GAAG;IACb,cAAc,GAAG;IACjB,aAAa,GAAG;IAChB,wBAAwB,GAAG;IAC3B,cAAc,GAAG;IACjB,yBAAyB,GAAG;EAAA;AAGpC;;;ACjEO,SAASC,oBAAmB,QACnC;AACI,MAAI,kBAAkB,mBACtB;AACa,aAAA,IAAI,WAAW,OAAO,MAAM;EAAA;AAGzC,QAAM,IAAI,OAAO;AAEjB,WAAS,IAAI,GAAG,IAAI,GAAG,KAAK,GAC5B;AACU,UAAA,QAAQ,OAAO,IAAI,CAAC;AAE1B,QAAI,UAAU,GACd;AACI,YAAM,IAAI,UAAU;AAEpB,aAAO,CAAC,IAAK,OAAO,CAAC,IAAI,IAAK;AAC9B,aAAO,IAAI,CAAC,IAAK,OAAO,IAAI,CAAC,IAAI,IAAK;AACtC,aAAO,IAAI,CAAC,IAAK,OAAO,IAAI,CAAC,IAAI,IAAK;IAAA;EAC1C;AAER;;;ACFA,IAAM,kBAAkB;AAOjB,IAAM,kBAAN,MACP;EAsCI,YAAY,UACZ;AA9BA,SAAgB,kBAAmC,CAAA;AAI3C,SAAA,cAAgD,uBAAA,OAAO,IAAI;AAC3D,SAAA,cAAmD,uBAAA,OAAO,IAAI;AAEtE,SAAQ,iBAAkC,CAAA;AAC1C,SAAQ,yBAAyB;AAEzB,SAAA,iBAAsD,uBAAA,OAAO,IAAI;AAEzE,SAAiB,WAA8C;MAC3D,OAAO;MACP,QAAQ;MACR,OAAO;MACP,YAAY;IAAA;AAQhB,SAAQ,oBAAoB;AAG5B,SAAiB,uBAAuB;AAIpC,SAAK,YAAY;AACjB,SAAK,UAAU,aAAa,eAAe,MAAM,aAAa;AAC9D,SAAK,UAAU,aAAa,eAAe,MAAM,aAAa;EAAA;EAGxD,cAAc,IACxB;AACI,SAAK,MAAM;AAEP,QAAA,CAAC,KAAK,4BACV;AACI,WAAK,6BAA6B,4BAA4B,IAAI,KAAK,UAAU,QAAQ,UAAU;AAE9F,WAAA,mBAAmB,kBAAkB,EAAE;AACvC,WAAA,qBAAqB,oBAAoB,EAAE;IAAA;AAG/C,SAAA,cAAqB,uBAAA,OAAO,IAAI;AAChC,SAAA,cAAqB,uBAAA,OAAO,IAAI;AAChC,SAAA,iBAAwB,uBAAA,OAAO,IAAI;AACxC,SAAK,oBAAoB;AAEzB,aAAS,IAAI,GAAG,IAAI,IAAI,KACxB;AACS,WAAA,KAAK,QAAQ,OAAO,CAAC;IAAA;EAC9B;EAGG,WAAW,QAClB;AACI,SAAK,KAAK,MAAM;EAAA;EAGb,KAAK,SAA0B,WAAW,GACjD;AACI,UAAM,SAAS,QAAQ;AAEvB,QAAI,SACJ;AACS,WAAA,WAAW,QAAQ,QAAQ;AAEhC,UAAI,KAAK,sBACT;AACS,aAAA,aAAa,OAAO,OAAO,QAAQ;MAAA;IAC5C,OAGJ;AACS,WAAA,WAAW,MAAM,QAAQ;AAE9B,UAAI,KAAK,sBACT;AACS,aAAA,aAAa,MAAM,QAAQ;MAAA;IACpC;EACJ;EAGG,WAAW,QAAuB,WAAW,GACpD;AACI,UAAM,KAAK,KAAK;AAET,WAAA,WAAW,KAAK,UAAU,UAAU;AAE3C,QAAI,KAAK,eAAe,QAAQ,MAAM,QACtC;AACS,WAAA,eAAe,QAAQ,IAAI;AAChC,WAAK,kBAAkB,QAAQ;AAE/B,iBAAA,SAAW,QAAQ,MAAM;AAGnB,YAAA,YAAY,KAAK,YAAY,MAAM;AAEzC,SAAG,YAAY,UAAU,QAAQ,UAAU,OAAO;IAAA;EACtD;EAGI,aAAa,OAAqB,WAAW,GACrD;AACI,UAAM,KAAK,KAAK;AAEhB,QAAI,CAAC,OACL;AACS,WAAA,eAAe,QAAQ,IAAI;AAC7B,SAAA,YAAY,UAAU,IAAI;AAE7B;IAAA;AAGE,UAAA,UAAU,KAAK,cAAc,KAAK;AAExC,QAAI,KAAK,eAAe,QAAQ,MAAM,SACtC;AACS,WAAA,eAAe,QAAQ,IAAI;AAC7B,SAAA,YAAY,UAAU,OAAO;IAAA;EACpC;EAGG,OAAO,SACd;AACI,UAAM,SAAS,QAAQ;AACvB,UAAM,gBAAgB,KAAK;AAC3B,UAAM,KAAK,KAAK;AAEhB,aAAS,IAAI,GAAG,IAAI,cAAc,QAAQ,KAC1C;AACQ,UAAA,cAAc,CAAC,MAAM,QACzB;AACI,aAAK,kBAAkB,CAAC;AAElB,cAAA,YAAY,KAAK,YAAY,MAAM;AAEtC,WAAA,YAAY,UAAU,QAAQ,IAAI;AACrC,sBAAc,CAAC,IAAI;MAAA;IACvB;EACJ;EAGI,kBAAkB,UAC1B;AACQ,QAAA,KAAK,2BAA2B,UACpC;AACI,WAAK,yBAAyB;AAC9B,WAAK,IAAI,cAAc,KAAK,IAAI,WAAW,QAAQ;IAAA;EACvD;EAGI,YAAY,QACpB;AACI,UAAM,KAAK,KAAK;AAEhB,UAAM,YAAY,IAAI,UAAU,GAAG,cAAA,CAAe;AAElD,cAAU,OAAO,KAAK,iBAAiB,OAAO,MAAM;AACpD,cAAU,iBAAiB,KAAK,2BAA2B,OAAO,MAAM;AACxE,cAAU,SAAS,KAAK,mBAAmB,OAAO,MAAM;AAEpD,QAAA,OAAO,wBAAwB,KAAK,UAAU,QAAQ,SAAS,oBAAoB,OAAO,eAC9F;AACI,YAAM,mBAAmB,KAAK,IAAI,OAAO,OAAO,OAAO,MAAM;AAE7D,aAAO,gBAAgB,KAAK,MAAM,KAAK,KAAK,gBAAgB,CAAC,IAAI;IAAA;AAGhE,SAAA,YAAY,OAAO,GAAG,IAAI;AAE/B,QAAI,CAAC,KAAK,gBAAgB,SAAS,MAAM,GACzC;AACI,aAAO,GAAG,UAAU,KAAK,gBAAgB,IAAI;AAC7C,aAAO,GAAG,UAAU,KAAK,gBAAgB,IAAI;AAC7C,aAAO,GAAG,eAAe,KAAK,eAAe,IAAI;AACjD,aAAO,GAAG,WAAW,KAAK,iBAAiB,IAAI;AAC/C,aAAO,GAAG,UAAU,KAAK,gBAAgB,IAAI;AAC7C,aAAO,GAAG,iBAAiB,KAAK,iBAAiB,IAAI;AAEhD,WAAA,gBAAgB,KAAK,MAAM;IAAA;AAGpC,SAAK,eAAe,MAAM;AACrB,SAAA,YAAY,QAAQ,KAAK;AAEvB,WAAA;EAAA;EAGD,cAAc,QACxB;AACS,SAAA,YAAY,QAAQ,KAAK;EAAA;EAGxB,YAAY,QAAuB,eAC7C;AACI,UAAM,KAAK,KAAK;AAEV,UAAA,YAAY,KAAK,YAAY,MAAM;AAEzC,OAAG,YAAY,GAAG,YAAY,UAAU,OAAO;AAE1C,SAAA,eAAe,KAAK,sBAAsB,IAAI;AAEnD;MACI,OAAO;MACP;MACA,OAAO,gBAAgB;MACvB,KAAK,UAAU,QAAQ,WAAW;MAClC;MACA,GAAG;;MAEH,CAAC,KAAK,UAAU,QAAQ,SAAS,qBAAqB,CAAC,OAAO;MAC9D;IAAA;EACJ;EAGM,eAAe,QACzB;AACI,UAAM,YAAY,KAAK,YAAY,OAAO,GAAG;AAE7C,QAAI,CAAC;AAAW;AAEhB,SAAK,OAAO,MAAM;AACb,SAAA,YAAY,OAAO,GAAG,IAAI;AAE1B,SAAA,IAAI,cAAc,UAAU,OAAO;EAAA;EAGlC,eAAe,QACzB;AACI,UAAM,KAAK,KAAK;AAEV,UAAA,YAAY,KAAK,YAAY,MAAM;AAEzC,OAAG,YAAY,GAAG,YAAY,UAAU,OAAO;AAE1C,SAAA,eAAe,KAAK,sBAAsB,IAAI;AAE7C,UAAA,qBAAqB,OAAO,cAAc;AAE5C,QAAA,KAAK,sBAAsB,oBAC/B;AACI,WAAK,oBAAoB;AACtB,SAAA,YAAY,GAAG,gCAAgC,kBAAkB;IAAA;AAGxE,QAAI,KAAK,SAAS,OAAO,cAAc,GACvC;AACS,WAAA,SAAS,OAAO,cAAc,EAAE,OAAO,QAAQ,WAAW,IAAI,KAAK,UAAU,QAAQ,YAAY;IAAA,OAG1G;AAEI,SAAG,WAAW,GAAG,YAAY,GAAG,GAAG,MAAM,OAAO,YAAY,OAAO,aAAa,GAAG,GAAG,MAAM,GAAG,eAAe,IAAI;IAAA;AAGtH,QAAI,OAAO,uBAAuB,OAAO,gBAAgB,GACzD;AACS,WAAA,gBAAgB,QAAQ,KAAK;IAAA;EACtC;EAGM,gBAAgB,QAAuB,OAAO,MACxD;AACQ,QAAA;AAAW,WAAA,WAAW,QAAQ,CAAC;AAE7B,UAAA,YAAY,KAAK,YAAY,MAAM;AAEpC,SAAA,IAAI,eAAe,UAAU,MAAM;EAAA;EAGlC,gBAAgB,QAC1B;AACI,WAAO,IAAI,WAAW,KAAK,iBAAiB,IAAI;AAChD,WAAO,IAAI,UAAU,KAAK,gBAAgB,IAAI;AAC9C,WAAO,IAAI,UAAU,KAAK,gBAAgB,IAAI;AAC9C,WAAO,IAAI,UAAU,KAAK,gBAAgB,IAAI;AAC9C,WAAO,IAAI,eAAe,KAAK,eAAe,IAAI;AAClD,WAAO,IAAI,iBAAiB,KAAK,iBAAiB,IAAI;AAEtD,SAAK,gBAAgB,OAAO,KAAK,gBAAgB,QAAQ,MAAM,GAAG,CAAC;AAEnE,SAAK,eAAe,MAAM;EAAA;EAGtB,aAAa,OACrB;AACI,UAAM,KAAK,KAAK;AAEV,UAAA,YAAY,KAAK,IAAI,cAAc;AAEpC,SAAA,YAAY,MAAM,WAAW,IAAI;AAEtC;MACI;MACA;MACA,KAAK,eAAe,KAAK,sBAAsB,EAAE,gBAAgB;MACjE,KAAK,UAAU,QAAQ,WAAW;MAClC;MACA;MACA;MACA;IAAA;AAGG,WAAA,KAAK,YAAY,MAAM,WAAW;EAAA;EAGrC,cAAc,SACtB;AACI,WAAO,KAAK,YAAY,QAAQ,WAAW,KAAK,KAAK,aAAa,OAAO;EAAA;EAGtE,YAAY,QACnB;AACI,WAAO,KAAK,YAAY,OAAO,GAAG,KAAK,KAAK,YAAY,MAAM;EAAA;EAG3D,eAAe,SACtB;AACI,UAAM,EAAE,QAAQ,OAAO,OAAA,IAAW,KAAK,UAAU,OAAO;AAExD,UAAM,SAAS,WAAW,IAAI,EAAE,aAAa;AAE7C,WAAO,QAAQ;AACf,WAAO,SAAS;AAEV,UAAA,MAAM,OAAO,WAAW,IAAI;AAElC,QAAI,KACJ;AACI,YAAM,YAAY,IAAI,gBAAgB,OAAO,MAAM;AAEzC,gBAAA,KAAK,IAAI,MAAM;AACrB,UAAA,aAAa,WAAW,GAAG,CAAC;IAAA;AAG7B,WAAA;EAAA;EAGJ,UAAU,SACjB;AACU,UAAA,aAAa,QAAQ,OAAO;AAClC,UAAM,QAAQ,QAAQ;AAEhB,UAAA,QAAQ,KAAK,IAAI,KAAK,MAAM,MAAM,QAAQ,UAAU,GAAG,CAAC;AACxD,UAAA,SAAS,KAAK,IAAI,KAAK,MAAM,MAAM,SAAS,UAAU,GAAG,CAAC;AAChE,UAAM,SAAS,IAAI,WAAW,kBAAkB,QAAQ,MAAM;AAE9D,UAAM,WAAW,KAAK;AAEtB,UAAM,eAAe,SAAS,aAAa,gBAAgB,OAAO;AAClE,UAAM,iBAAiB,SAAS,aAAa,mBAAmB,YAAY;AAE5E,UAAM,KAAK,SAAS;AAEpB,OAAG,gBAAgB,GAAG,aAAa,eAAe,wBAAwB;AAEvE,OAAA;MACC,KAAK,MAAM,MAAM,IAAI,UAAU;MAC/B,KAAK,MAAM,MAAM,IAAI,UAAU;MAC/B;MACA;MACA,GAAG;MACH,GAAG;MACH;IAAA;AAMJ,QAAI,OACJ;AACI,yBAAmB,MAAM;IAAA;AAGtB,WAAA,EAAE,QAAQ,IAAI,kBAAkB,OAAO,MAAM,GAAG,OAAO,OAAO;EAAA;EAGlE,UACP;AAGS,SAAA,gBACA,MAAA,EACA,QAAQ,CAAC,WAAW,KAAK,gBAAgB,MAAM,CAAC;AAEpD,SAAK,kBAA2B;AAEhC,SAAK,YAAqB;EAAA;EAGxB,aACP;AACI,SAAK,yBAAyB;AAC9B,SAAK,eAAe,KAAK,QAAQ,MAAM,MAAM;AACxC,SAAA,iBAAwB,uBAAA,OAAO,IAAI;AAExC,UAAM,KAAK,KAAK;AAEhB,SAAK,oBAAoB;AAEzB,OAAG,YAAY,GAAG,gCAAgC,KAAK,iBAAiB;EAAA;AAEhF;AApaa,gBAGK,YAAY;EACtB,MAAM;IACF,cAAc;EAAA;EAElB,MAAM;AACV;;;ACVJ,IAAM,sBAAsB;EACxB,GAAG;EACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACJ;AACA,IAAM,oBAAoB,CAAC,GAAG,iBAAiB;AAC/C,IAAM,uBAAuB,CAAC,gBAAgB,eAAe,iBAAiB;AAG9E,IAAM,UAAwD,CAAA;AAC9D,IAAM,cAA0D,CAAA;AAChE,IAAM,qBAAqD,CAAA;AAE3D,WAAW,kBAAkB,cAAc,aAAa,OAAO;AAC/D,WAAW,kBAAkB,cAAc,YAAY,WAAW;AAClE,WAAW,kBAAkB,cAAc,mBAAmB,kBAAkB;AAGhF,WAAW,IAAI,GAAG,qBAAqB,GAAG,mBAAmB,GAAG,oBAAoB;AAoG7E,IAAM,gBAAN,cACK,iBAEZ;EAGI,cACA;AACI,UAAM,eAAe;MACjB,MAAM;MACN,MAAM,aAAa;MACnB;MACA;MACA;IAAA;AAGJ,UAAM,YAAY;EAAA;AAE1B;", "names": ["BUFFER_TYPE", "_GlContextSystem", "extensions", "GL_FORMATS", "GL_TARGETS", "GL_WRAP_MODES", "GL_TYPES", "_GlBackBufferSystem", "renderTarget", "source", "_GlStateSystem", "extensions", "unpremultiplyAlpha"]}