{"version": 3, "sources": ["../../pixi.js/src/scene/graphics/gpu/GpuGraphicsAdaptor.ts", "../../pixi.js/src/scene/mesh/gpu/GpuMeshAdapter.ts", "../../pixi.js/src/rendering/batcher/gpu/GpuBatchAdaptor.ts", "../../pixi.js/src/rendering/renderers/gpu/BindGroupSystem.ts", "../../pixi.js/src/rendering/renderers/gpu/buffer/GpuBufferSystem.ts", "../../pixi.js/src/rendering/renderers/gpu/GpuColorMaskSystem.ts", "../../pixi.js/src/rendering/renderers/gpu/GpuDeviceSystem.ts", "../../pixi.js/src/rendering/renderers/gpu/GpuEncoderSystem.ts", "../../pixi.js/src/rendering/renderers/gpu/GpuLimitsSystem.ts", "../../pixi.js/src/rendering/renderers/gpu/GpuStencilSystem.ts", "../../pixi.js/src/rendering/renderers/gpu/shader/utils/createUboElementsWGSL.ts", "../../pixi.js/src/rendering/renderers/gpu/shader/utils/generateArraySyncWGSL.ts", "../../pixi.js/src/rendering/renderers/gpu/shader/utils/createUboSyncFunctionWGSL.ts", "../../pixi.js/src/rendering/renderers/gpu/GpuUboSystem.ts", "../../pixi.js/src/rendering/renderers/gpu/buffer/UboBatch.ts", "../../pixi.js/src/rendering/renderers/gpu/GpuUniformBatchPipe.ts", "../../pixi.js/src/rendering/renderers/gpu/pipeline/PipelineSystem.ts", "../../pixi.js/src/rendering/renderers/gpu/renderTarget/GpuRenderTarget.ts", "../../pixi.js/src/rendering/renderers/gpu/renderTarget/GpuRenderTargetAdaptor.ts", "../../pixi.js/src/rendering/renderers/gpu/renderTarget/GpuRenderTargetSystem.ts", "../../pixi.js/src/rendering/renderers/gpu/shader/GpuShaderSystem.ts", "../../pixi.js/src/rendering/renderers/gpu/state/GpuBlendModesToPixi.ts", "../../pixi.js/src/rendering/renderers/gpu/state/GpuStateSystem.ts", "../../pixi.js/src/rendering/renderers/gpu/texture/uploaders/gpuUploadBufferImageResource.ts", "../../pixi.js/src/rendering/renderers/gpu/texture/uploaders/gpuUploadCompressedTextureResource.ts", "../../pixi.js/src/rendering/renderers/gpu/texture/uploaders/gpuUploadImageSource.ts", "../../pixi.js/src/rendering/renderers/gpu/texture/uploaders/gpuUploadVideoSource.ts", "../../pixi.js/src/rendering/renderers/gpu/texture/utils/GpuMipmapGenerator.ts", "../../pixi.js/src/rendering/renderers/gpu/texture/GpuTextureSystem.ts", "../../pixi.js/src/rendering/renderers/gpu/WebGPURenderer.ts"], "sourcesContent": ["import { ExtensionType } from '../../../extensions/Extensions';\nimport { Matrix } from '../../../maths/matrix/Matrix';\nimport { getTextureBatchBindGroup } from '../../../rendering/batcher/gpu/getTextureBatchBindGroup';\nimport { compileHighShaderGpuProgram } from '../../../rendering/high-shader/compileHighShaderToProgram';\nimport { colorBit } from '../../../rendering/high-shader/shader-bits/colorBit';\nimport { generateTextureBatchBit } from '../../../rendering/high-shader/shader-bits/generateTextureBatchBit';\nimport { localUniformBitGroup2 } from '../../../rendering/high-shader/shader-bits/localUniformBit';\nimport { roundPixelsBit } from '../../../rendering/high-shader/shader-bits/roundPixelsBit';\nimport { Shader } from '../../../rendering/renderers/shared/shader/Shader';\nimport { UniformGroup } from '../../../rendering/renderers/shared/shader/UniformGroup';\nimport { type Renderer } from '../../../rendering/renderers/types';\n\nimport type { Batch } from '../../../rendering/batcher/shared/Batcher';\nimport type { GpuEncoderSystem } from '../../../rendering/renderers/gpu/GpuEncoderSystem';\nimport type { WebGPURenderer } from '../../../rendering/renderers/gpu/WebGPURenderer';\nimport type { Topology } from '../../../rendering/renderers/shared/geometry/const';\nimport type { Graphics } from '../shared/Graphics';\nimport type { GraphicsAdaptor, GraphicsPipe } from '../shared/GraphicsPipe';\n\n/**\n * A GraphicsAdaptor that uses the GPU to render graphics.\n * @category rendering\n * @ignore\n */\nexport class GpuGraphicsAdaptor implements GraphicsAdaptor\n{\n    /** @ignore */\n    public static extension = {\n        type: [\n            ExtensionType.WebGPUPipesAdaptor,\n        ],\n        name: 'graphics',\n    } as const;\n\n    public shader: Shader;\n\n    private _maxTextures = 0;\n\n    public contextChange(renderer: Renderer): void\n    {\n        const localUniforms = new UniformGroup({\n            uTransformMatrix: { value: new Matrix(), type: 'mat3x3<f32>' },\n            uColor: { value: new Float32Array([1, 1, 1, 1]), type: 'vec4<f32>' },\n            uRound: { value: 0, type: 'f32' },\n        });\n\n        this._maxTextures = renderer.limits.maxBatchableTextures;\n\n        const gpuProgram = compileHighShaderGpuProgram({\n            name: 'graphics',\n            bits: [\n                colorBit,\n                generateTextureBatchBit(this._maxTextures),\n\n                localUniformBitGroup2,\n                roundPixelsBit\n            ]\n        });\n\n        this.shader = new Shader({\n            gpuProgram,\n            resources: {\n                // added on the fly!\n                localUniforms,\n            },\n        });\n    }\n\n    public execute(graphicsPipe: GraphicsPipe, renderable: Graphics): void\n    {\n        const context = renderable.context;\n        const shader = context.customShader || this.shader;\n        const renderer = graphicsPipe.renderer as WebGPURenderer;\n        const contextSystem = renderer.graphicsContext;\n\n        const {\n            batcher, instructions\n        } = contextSystem.getContextRenderData(context);\n\n        // WebGPU specific...\n\n        // TODO perf test this a bit...\n        const encoder = renderer.encoder as GpuEncoderSystem;\n\n        encoder.setGeometry(batcher.geometry, shader.gpuProgram);\n\n        const globalUniformsBindGroup = renderer.globalUniforms.bindGroup;\n\n        encoder.setBindGroup(0, globalUniformsBindGroup, shader.gpuProgram);\n\n        const localBindGroup = (renderer as WebGPURenderer)\n            .renderPipes.uniformBatch.getUniformBindGroup(shader.resources.localUniforms, true);\n\n        encoder.setBindGroup(2, localBindGroup, shader.gpuProgram);\n\n        const batches = instructions.instructions as Batch[];\n\n        let topology: Topology = null;\n\n        for (let i = 0; i < instructions.instructionSize; i++)\n        {\n            const batch = batches[i];\n\n            if (batch.topology !== topology)\n            {\n                topology = batch.topology;\n\n                encoder.setPipelineFromGeometryProgramAndState(\n                    batcher.geometry,\n                    shader.gpuProgram,\n                    graphicsPipe.state,\n                    batch.topology\n                );\n            }\n\n            shader.groups[1] = batch.bindGroup;\n\n            if (!batch.gpuBindGroup)\n            {\n                const textureBatch = batch.textures;\n\n                batch.bindGroup = getTextureBatchBindGroup(\n                    textureBatch.textures,\n                    textureBatch.count,\n                    this._maxTextures\n                );\n\n                batch.gpuBindGroup = renderer.bindGroup.getBindGroup(\n                    batch.bindGroup, shader.gpuProgram, 1\n                );\n            }\n\n            encoder.setBindGroup(1, batch.bindGroup, shader.gpuProgram);\n\n            encoder.renderPassEncoder.drawIndexed(batch.size, 1, batch.start);\n        }\n    }\n\n    public destroy(): void\n    {\n        this.shader.destroy(true);\n        this.shader = null;\n    }\n}\n", "import { ExtensionType } from '../../../extensions/Extensions';\nimport { Matrix } from '../../../maths/matrix/Matrix';\nimport { compileHighShaderGpuProgram } from '../../../rendering/high-shader/compileHighShaderToProgram';\nimport { localUniformBit } from '../../../rendering/high-shader/shader-bits/localUniformBit';\nimport { roundPixelsBit } from '../../../rendering/high-shader/shader-bits/roundPixelsBit';\nimport { textureBit } from '../../../rendering/high-shader/shader-bits/textureBit';\nimport { Shader } from '../../../rendering/renderers/shared/shader/Shader';\nimport { Texture } from '../../../rendering/renderers/shared/texture/Texture';\nimport { warn } from '../../../utils/logging/warn';\n\nimport type { WebGPURenderer } from '../../../rendering/renderers/gpu/WebGPURenderer';\nimport type { Mesh } from '../shared/Mesh';\nimport type { MeshAdaptor, MeshPipe } from '../shared/MeshPipe';\n\n/**\n * The WebGL adaptor for the mesh system. Allows the Mesh System to be used with the WebGl renderer\n * @category rendering\n * @ignore\n */\nexport class GpuMeshAdapter implements MeshAdaptor\n{\n    /** @ignore */\n    public static extension = {\n        type: [\n            ExtensionType.WebGPUPipesAdaptor,\n        ],\n        name: 'mesh',\n    } as const;\n\n    private _shader: Shader;\n\n    public init(): void\n    {\n        const gpuProgram = compileHighShaderGpuProgram({\n            name: 'mesh',\n            bits: [\n                localUniformBit,\n                textureBit,\n                roundPixelsBit,\n            ]\n        });\n\n        this._shader = new Shader({\n            gpuProgram,\n            resources: {\n                uTexture: Texture.EMPTY._source,\n                uSampler: Texture.EMPTY._source.style,\n                textureUniforms: {\n                    uTextureMatrix: { type: 'mat3x3<f32>', value: new Matrix() },\n                }\n            }\n        });\n    }\n\n    public execute(meshPipe: MeshPipe, mesh: Mesh)\n    {\n        const renderer = meshPipe.renderer as WebGPURenderer;\n\n        let shader: Shader = mesh._shader;\n\n        if (!shader)\n        {\n            shader = this._shader;\n\n            shader.groups[2] = renderer.texture.getTextureBindGroup(mesh.texture);\n        }\n        else if (!shader.gpuProgram)\n        {\n            // #if _DEBUG\n            warn('Mesh shader has no gpuProgram', mesh.shader);\n            // #endif\n\n            return;\n        }\n\n        const gpuProgram = shader.gpuProgram;\n        // GPU..\n\n        if (gpuProgram.autoAssignGlobalUniforms)\n        {\n            shader.groups[0] = renderer.globalUniforms.bindGroup;\n        }\n\n        if (gpuProgram.autoAssignLocalUniforms)\n        {\n            const localUniforms = meshPipe.localUniforms;\n\n            shader.groups[1] = (renderer as WebGPURenderer)\n                .renderPipes.uniformBatch.getUniformBindGroup(localUniforms, true);\n        }\n\n        renderer.encoder.draw({\n            geometry: mesh._geometry,\n            shader,\n            state: mesh.state\n        });\n    }\n\n    public destroy(): void\n    {\n        this._shader.destroy(true);\n        this._shader = null;\n    }\n}\n", "import { ExtensionType } from '../../../extensions/Extensions';\nimport { State } from '../../renderers/shared/state/State';\nimport { getTextureBatchBindGroup } from './getTextureBatchBindGroup';\n\nimport type { GpuEncoderSystem } from '../../renderers/gpu/GpuEncoderSystem';\nimport type { WebGPURenderer } from '../../renderers/gpu/WebGPURenderer';\nimport type { Geometry } from '../../renderers/shared/geometry/Geometry';\nimport type { Shader } from '../../renderers/shared/shader/Shader';\nimport type { Batch } from '../shared/Batcher';\nimport type { BatcherAdaptor, BatcherPipe } from '../shared/BatcherPipe';\n\nconst tempState = State.for2d();\n\n/**\n * A BatcherAdaptor that uses the GPU to render batches.\n * @category rendering\n * @ignore\n */\nexport class GpuBatchAdaptor implements BatcherAdaptor\n{\n    /** @ignore */\n    public static extension = {\n        type: [\n            ExtensionType.WebGPUPipesAdaptor,\n        ],\n        name: 'batch',\n    } as const;\n\n    private _shader: Shader;\n    private _geometry: Geometry;\n\n    public start(batchPipe: BatcherPipe, geometry: Geometry, shader: Shader): void\n    {\n        const renderer = batchPipe.renderer as WebGPURenderer;\n        const encoder = renderer.encoder as GpuEncoderSystem;\n        const program = shader.gpuProgram;\n\n        this._shader = shader;\n        this._geometry = geometry;\n\n        encoder.setGeometry(geometry, program);\n\n        tempState.blendMode = 'normal';\n\n        // this just initiates the pipeline, so we can then set bind groups on it\n        renderer.pipeline.getPipeline(\n            geometry,\n            program,\n            tempState\n        );\n\n        const globalUniformsBindGroup = renderer.globalUniforms.bindGroup;\n\n        // low level - we need to reset the bind group at location 1 to null\n        // this is because we directly manipulate the bound buffer in the execute function for\n        // performance reasons.\n        // setting it to null ensures that the next bind group we set at location 1 will\n        // be the one we want.\n        encoder.resetBindGroup(1);\n\n        encoder.setBindGroup(0, globalUniformsBindGroup, program);\n    }\n\n    public execute(batchPipe: BatcherPipe, batch: Batch): void\n    {\n        const program = this._shader.gpuProgram;\n        const renderer = batchPipe.renderer as WebGPURenderer;\n        const encoder = renderer.encoder as GpuEncoderSystem;\n\n        if (!batch.bindGroup)\n        {\n            const textureBatch = batch.textures;\n\n            batch.bindGroup = getTextureBatchBindGroup(\n                textureBatch.textures,\n                textureBatch.count,\n                renderer.limits.maxBatchableTextures\n            );\n        }\n\n        tempState.blendMode = batch.blendMode;\n\n        const gpuBindGroup = renderer.bindGroup.getBindGroup(\n            batch.bindGroup, program, 1\n        );\n\n        const pipeline = renderer.pipeline.getPipeline(\n            this._geometry,\n            program,\n            tempState,\n            batch.topology\n        );\n\n        batch.bindGroup._touch(renderer.textureGC.count);\n\n        encoder.setPipeline(pipeline);\n\n        encoder.renderPassEncoder.setBindGroup(1, gpuBindGroup);\n        encoder.renderPassEncoder.drawIndexed(batch.size, 1, batch.start);\n    }\n}\n", "import { ExtensionType } from '../../../extensions/Extensions';\n\nimport type { Buffer } from '../shared/buffer/Buffer';\nimport type { BufferResource } from '../shared/buffer/BufferResource';\nimport type { UniformGroup } from '../shared/shader/UniformGroup';\nimport type { System } from '../shared/system/System';\nimport type { TextureSource } from '../shared/texture/sources/TextureSource';\nimport type { TextureStyle } from '../shared/texture/TextureStyle';\nimport type { GPU } from './GpuDeviceSystem';\nimport type { BindGroup } from './shader/BindGroup';\nimport type { BindResource } from './shader/BindResource';\nimport type { GpuProgram } from './shader/GpuProgram';\nimport type { WebGPURenderer } from './WebGPURenderer';\n\n/**\n * This manages the WebGPU bind groups. this is how data is bound to a shader when rendering\n * @category rendering\n * @advanced\n */\nexport class BindGroupSystem implements System\n{\n    /** @ignore */\n    public static extension = {\n        type: [\n            ExtensionType.WebGPUSystem,\n        ],\n        name: 'bindGroup',\n    } as const;\n\n    private readonly _renderer: WebGPURenderer;\n\n    private _hash: Record<string, GPUBindGroup> = Object.create(null);\n    private _gpu: GPU;\n\n    constructor(renderer: WebGPURenderer)\n    {\n        this._renderer = renderer;\n        this._renderer.renderableGC.addManagedHash(this, '_hash');\n    }\n\n    protected contextChange(gpu: GPU): void\n    {\n        this._gpu = gpu;\n    }\n\n    public getBindGroup(bindGroup: BindGroup, program: GpuProgram, groupIndex: number): GPUBindGroup\n    {\n        bindGroup._updateKey();\n\n        const gpuBindGroup = this._hash[bindGroup._key] || this._createBindGroup(bindGroup, program, groupIndex);\n\n        return gpuBindGroup;\n    }\n\n    private _createBindGroup(group: BindGroup, program: GpuProgram, groupIndex: number): GPUBindGroup\n    {\n        const device = this._gpu.device;\n        const groupLayout = program.layout[groupIndex];\n        const entries: GPUBindGroupEntry[] = [];\n        const renderer = this._renderer;\n\n        for (const j in groupLayout)\n        {\n            const resource: BindResource = group.resources[j] ?? group.resources[groupLayout[j]];\n            let gpuResource: GPUSampler | GPUTextureView | GPUExternalTexture | GPUBufferBinding;\n            // TODO make this dynamic..\n\n            if (resource._resourceType === 'uniformGroup')\n            {\n                const uniformGroup = resource as UniformGroup;\n\n                renderer.ubo.updateUniformGroup(uniformGroup as UniformGroup);\n\n                const buffer = uniformGroup.buffer;\n\n                gpuResource = {\n                    buffer: renderer.buffer.getGPUBuffer(buffer),\n                    offset: 0,\n                    size: buffer.descriptor.size,\n                };\n            }\n            else if (resource._resourceType === 'buffer')\n            {\n                const buffer = resource as Buffer;\n\n                gpuResource = {\n                    buffer: renderer.buffer.getGPUBuffer(buffer),\n                    offset: 0,\n                    size: buffer.descriptor.size,\n                };\n            }\n            else if (resource._resourceType === 'bufferResource')\n            {\n                const bufferResource = resource as BufferResource;\n\n                gpuResource = {\n                    buffer: renderer.buffer.getGPUBuffer(bufferResource.buffer),\n                    offset: bufferResource.offset,\n                    size: bufferResource.size,\n                };\n            }\n            else if (resource._resourceType === 'textureSampler')\n            {\n                const sampler = resource as TextureStyle;\n\n                gpuResource = renderer.texture.getGpuSampler(sampler);\n            }\n            else if (resource._resourceType === 'textureSource')\n            {\n                const texture = resource as TextureSource;\n\n                gpuResource = renderer.texture.getGpuSource(texture).createView({\n\n                });\n            }\n\n            entries.push({\n                binding: groupLayout[j],\n                resource: gpuResource,\n            });\n        }\n\n        const layout = renderer.shader.getProgramData(program).bindGroups[groupIndex];\n\n        const gpuBindGroup = device.createBindGroup({\n            layout,\n            entries,\n        });\n\n        this._hash[group._key] = gpuBindGroup;\n\n        return gpuBindGroup;\n    }\n\n    public destroy(): void\n    {\n        for (const key of Object.keys(this._hash))\n        {\n            this._hash[key] = null;\n        }\n\n        this._hash = null;\n\n        (this._renderer as null) = null;\n    }\n}\n", "import { ExtensionType } from '../../../../extensions/Extensions';\nimport { fastCopy } from '../../shared/buffer/utils/fastCopy';\n\nimport type { Buffer } from '../../shared/buffer/Buffer';\nimport type { System } from '../../shared/system/System';\nimport type { GPU } from '../GpuDeviceSystem';\nimport type { WebGPURenderer } from '../WebGPURenderer';\n\n/**\n * System plugin to the renderer to manage buffers.\n * @category rendering\n * @advanced\n */\nexport class GpuBufferSystem implements System\n{\n    /** @ignore */\n    public static extension = {\n        type: [\n            ExtensionType.WebGPUSystem,\n        ],\n        name: 'buffer',\n    } as const;\n\n    protected CONTEXT_UID: number;\n    private _gpuBuffers: { [key: number]: GPUBuffer } = Object.create(null);\n    private readonly _managedBuffers: Buffer[] = [];\n\n    private _gpu: GPU;\n\n    constructor(renderer: WebGPURenderer)\n    {\n        renderer.renderableGC.addManagedHash(this, '_gpuBuffers');\n    }\n\n    protected contextChange(gpu: GPU): void\n    {\n        this._gpu = gpu;\n    }\n\n    public getGPUBuffer(buffer: Buffer): GPUBuffer\n    {\n        return this._gpuBuffers[buffer.uid] || this.createGPUBuffer(buffer);\n    }\n\n    public updateBuffer(buffer: Buffer): GPUBuffer\n    {\n        const gpuBuffer = this._gpuBuffers[buffer.uid] || this.createGPUBuffer(buffer);\n\n        const data = buffer.data;\n\n        // TODO this can be better...\n        if (buffer._updateID && data)\n        {\n            buffer._updateID = 0;\n\n            // make sure\n            this._gpu.device.queue.writeBuffer(\n                gpuBuffer, 0, data.buffer, 0,\n                // round to the nearest 4 bytes\n                ((buffer._updateSize || data.byteLength) + 3) & ~3\n            );\n        }\n\n        return gpuBuffer;\n    }\n\n    /** dispose all WebGL resources of all managed buffers */\n    public destroyAll(): void\n    {\n        for (const id in this._gpuBuffers)\n        {\n            this._gpuBuffers[id].destroy();\n        }\n\n        this._gpuBuffers = {};\n    }\n\n    public createGPUBuffer(buffer: Buffer): GPUBuffer\n    {\n        if (!this._gpuBuffers[buffer.uid])\n        {\n            buffer.on('update', this.updateBuffer, this);\n            buffer.on('change', this.onBufferChange, this);\n            buffer.on('destroy', this.onBufferDestroy, this);\n\n            this._managedBuffers.push(buffer);\n        }\n\n        const gpuBuffer = this._gpu.device.createBuffer(buffer.descriptor);\n\n        buffer._updateID = 0;\n\n        if (buffer.data)\n        {\n            // TODO if data is static, this can be mapped at creation\n            fastCopy(buffer.data.buffer, gpuBuffer.getMappedRange());\n\n            gpuBuffer.unmap();\n        }\n\n        this._gpuBuffers[buffer.uid] = gpuBuffer;\n\n        return gpuBuffer;\n    }\n\n    protected onBufferChange(buffer: Buffer)\n    {\n        const gpuBuffer = this._gpuBuffers[buffer.uid];\n\n        gpuBuffer.destroy();\n        buffer._updateID = 0;\n        this._gpuBuffers[buffer.uid] = this.createGPUBuffer(buffer);\n    }\n\n    /**\n     * Disposes buffer\n     * @param buffer - buffer with data\n     */\n    protected onBufferDestroy(buffer: Buffer): void\n    {\n        this._managedBuffers.splice(this._managedBuffers.indexOf(buffer), 1);\n\n        this._destroyBuffer(buffer);\n    }\n\n    public destroy(): void\n    {\n        this._managedBuffers.forEach((buffer) => this._destroyBuffer(buffer));\n\n        (this._managedBuffers as null) = null;\n\n        this._gpuBuffers = null;\n    }\n\n    private _destroyBuffer(buffer: Buffer): void\n    {\n        const gpuBuffer = this._gpuBuffers[buffer.uid];\n\n        gpuBuffer.destroy();\n\n        buffer.off('update', this.updateBuffer, this);\n        buffer.off('change', this.onBufferChange, this);\n        buffer.off('destroy', this.onBufferDestroy, this);\n\n        this._gpuBuffers[buffer.uid] = null;\n    }\n}\n\n", "import { ExtensionType } from '../../../extensions/Extensions';\n\nimport type { System } from '../shared/system/System';\nimport type { WebGPURenderer } from './WebGPURenderer';\n\n/**\n * The system that handles color masking for the GPU.\n * @category rendering\n * @advanced\n */\nexport class GpuColorMaskSystem implements System\n{\n    /** @ignore */\n    public static extension = {\n        type: [\n            ExtensionType.WebGPUSystem,\n        ],\n        name: 'colorMask',\n    } as const;\n\n    private readonly _renderer: WebGPURenderer;\n\n    private _colorMaskCache = 0b1111;\n\n    constructor(renderer: WebGPURenderer)\n    {\n        this._renderer = renderer;\n    }\n\n    public setMask(colorMask: number)\n    {\n        if (this._colorMaskCache === colorMask) return;\n        this._colorMaskCache = colorMask;\n\n        this._renderer.pipeline.setColorMask(colorMask);\n    }\n\n    public destroy()\n    {\n        (this._renderer as null) = null;\n        this._colorMaskCache = null;\n    }\n}\n", "import { DOMAdapter } from '../../../environment/adapter';\nimport { ExtensionType } from '../../../extensions/Extensions';\n\nimport type { System } from '../shared/system/System';\nimport type { GpuPowerPreference } from '../types';\nimport type { WebGPURenderer } from './WebGPURenderer';\n\n/**\n * The GPU object.\n * Contains the GPU adapter and device.\n * @category rendering\n * @advanced\n */\nexport interface GPU\n{\n    /** The GPU adapter */\n    adapter: GPUAdapter;\n    /** The GPU device */\n    device: GPUDevice;\n}\n\n/**\n * Options for the WebGPU context.\n * @property {GpuPowerPreference} [powerPreference=default] - An optional hint indicating what configuration of GPU\n * is suitable for the WebGPU context, can be `'high-performance'` or `'low-power'`.\n * Setting to `'high-performance'` will prioritize rendering performance over power consumption,\n * while setting to `'low-power'` will prioritize power saving over rendering performance.\n * @property {boolean} [forceFallbackAdapter=false] - Force the use of the fallback adapter\n * @category rendering\n * @advanced\n */\nexport interface GpuContextOptions\n{\n    /**\n     * An optional hint indicating what configuration of GPU is suitable for the WebGPU context,\n     * can be `'high-performance'` or `'low-power'`.\n     * Setting to `'high-performance'` will prioritize rendering performance over power consumption,\n     * while setting to `'low-power'` will prioritize power saving over rendering performance.\n     * @default undefined\n     */\n    powerPreference?: GpuPowerPreference;\n    /**\n     * Force the use of the fallback adapter\n     * @default false\n     */\n    forceFallbackAdapter: boolean;\n}\n\n/**\n * System plugin to the renderer to manage the context.\n * @class\n * @category rendering\n * @advanced\n */\nexport class GpuDeviceSystem implements System<GpuContextOptions>\n{\n    /** @ignore */\n    public static extension = {\n        type: [\n            ExtensionType.WebGPUSystem,\n        ],\n        name: 'device',\n    } as const;\n\n    /** The default options for the GpuDeviceSystem. */\n    public static defaultOptions: GpuContextOptions = {\n        /**\n         * {@link WebGPUOptions.powerPreference}\n         * @default default\n         */\n        powerPreference: undefined,\n        /**\n         * Force the use of the fallback adapter\n         * @default false\n         */\n        forceFallbackAdapter: false,\n    };\n\n    /** The GPU device */\n    public gpu: GPU;\n\n    private _renderer: WebGPURenderer;\n    private _initPromise: Promise<void>;\n\n    /**\n     * @param {WebGPURenderer} renderer - The renderer this System works for.\n     */\n    constructor(renderer: WebGPURenderer)\n    {\n        this._renderer = renderer;\n    }\n\n    public async init(options: GpuContextOptions): Promise<void>\n    {\n        if (this._initPromise) return this._initPromise;\n\n        this._initPromise = this._createDeviceAndAdaptor(options)\n            .then((gpu) =>\n            {\n                this.gpu = gpu;\n\n                this._renderer.runners.contextChange.emit(this.gpu);\n            });\n\n        return this._initPromise;\n    }\n\n    /**\n     * Handle the context change event\n     * @param gpu\n     */\n    protected contextChange(gpu: GPU): void\n    {\n        this._renderer.gpu = gpu;\n    }\n\n    /**\n     * Helper class to create a WebGL Context\n     * @param {object} options - An options object that gets passed in to the canvas element containing the\n     *    context attributes\n     * @see https://developer.mozilla.org/en/docs/Web/API/HTMLCanvasElement/getContext\n     * @returns {WebGLRenderingContext} the WebGL context\n     */\n    private async _createDeviceAndAdaptor(options: GpuContextOptions): Promise<GPU>\n    {\n        // TODO we only need one of these..\n        const adapter = await DOMAdapter.get().getNavigator().gpu.requestAdapter({\n            powerPreference: options.powerPreference,\n            forceFallbackAdapter: options.forceFallbackAdapter,\n        });\n\n        const requiredFeatures = [\n            'texture-compression-bc',\n            'texture-compression-astc',\n            'texture-compression-etc2',\n        ].filter((feature) => adapter.features.has(feature)) as GPUFeatureName[];\n\n        // TODO and one of these!\n        const device = await adapter.requestDevice({\n            requiredFeatures\n        });\n\n        return { adapter, device };\n    }\n\n    public destroy(): void\n    {\n        this.gpu = null;\n        this._renderer = null;\n    }\n}\n", "import { ExtensionType } from '../../../extensions/Extensions';\n\nimport type { Rectangle } from '../../../maths/shapes/Rectangle';\nimport type { Buffer } from '../shared/buffer/Buffer';\nimport type { Topology } from '../shared/geometry/const';\nimport type { Geometry } from '../shared/geometry/Geometry';\nimport type { Shader } from '../shared/shader/Shader';\nimport type { UniformGroup } from '../shared/shader/UniformGroup';\nimport type { State } from '../shared/state/State';\nimport type { System } from '../shared/system/System';\nimport type { GPU } from './GpuDeviceSystem';\nimport type { GpuRenderTarget } from './renderTarget/GpuRenderTarget';\nimport type { GpuRenderTargetAdaptor } from './renderTarget/GpuRenderTargetAdaptor';\nimport type { BindGroup } from './shader/BindGroup';\nimport type { GpuProgram } from './shader/GpuProgram';\nimport type { WebGPURenderer } from './WebGPURenderer';\n\n/**\n * The system that handles encoding commands for the GPU.\n * @category rendering\n * @advanced\n */\nexport class GpuEncoderSystem implements System\n{\n    /** @ignore */\n    public static extension = {\n        type: [ExtensionType.WebGPUSystem],\n        name: 'encoder',\n        priority: 1\n    } as const;\n\n    public commandEncoder: GPUCommandEncoder;\n    public renderPassEncoder: GPURenderPassEncoder;\n    public commandFinished: Promise<void>;\n\n    private _resolveCommandFinished: (value: void) => void;\n\n    private _gpu: GPU;\n    private _boundBindGroup: Record<number, BindGroup> = Object.create(null);\n    private _boundVertexBuffer: Record<number, Buffer> = Object.create(null);\n    private _boundIndexBuffer: Buffer;\n    private _boundPipeline: GPURenderPipeline;\n\n    private readonly _renderer: WebGPURenderer;\n\n    constructor(renderer: WebGPURenderer)\n    {\n        this._renderer = renderer;\n    }\n\n    public renderStart(): void\n    {\n        this.commandFinished = new Promise((resolve) =>\n        {\n            this._resolveCommandFinished = resolve;\n        });\n\n        // generate a render pass description..\n        // create an encoder..\n        this.commandEncoder = this._renderer.gpu.device.createCommandEncoder();\n    }\n\n    public beginRenderPass(gpuRenderTarget: GpuRenderTarget)\n    {\n        this.endRenderPass();\n\n        this._clearCache();\n\n        this.renderPassEncoder = this.commandEncoder.beginRenderPass(gpuRenderTarget.descriptor);\n    }\n\n    public endRenderPass()\n    {\n        if (this.renderPassEncoder)\n        {\n            this.renderPassEncoder.end();\n        }\n\n        this.renderPassEncoder = null;\n    }\n\n    public setViewport(viewport: Rectangle): void\n    {\n        this.renderPassEncoder.setViewport(viewport.x, viewport.y, viewport.width, viewport.height, 0, 1);\n    }\n\n    public setPipelineFromGeometryProgramAndState(\n        geometry: Geometry,\n        program: GpuProgram,\n        state: any,\n        topology?: Topology,\n    ): void\n    {\n        const pipeline = this._renderer.pipeline.getPipeline(geometry, program, state, topology);\n\n        this.setPipeline(pipeline);\n    }\n\n    public setPipeline(pipeline: GPURenderPipeline)\n    {\n        if (this._boundPipeline === pipeline) return;\n        this._boundPipeline = pipeline;\n\n        this.renderPassEncoder.setPipeline(pipeline);\n    }\n\n    private _setVertexBuffer(index: number, buffer: Buffer)\n    {\n        if (this._boundVertexBuffer[index] === buffer) return;\n\n        this._boundVertexBuffer[index] = buffer;\n\n        this.renderPassEncoder.setVertexBuffer(index, this._renderer.buffer.updateBuffer(buffer));\n    }\n\n    private _setIndexBuffer(buffer: Buffer)\n    {\n        if (this._boundIndexBuffer === buffer) return;\n\n        this._boundIndexBuffer = buffer;\n\n        const indexFormat = buffer.data.BYTES_PER_ELEMENT === 2 ? 'uint16' : 'uint32';\n\n        this.renderPassEncoder.setIndexBuffer(this._renderer.buffer.updateBuffer(buffer), indexFormat);\n    }\n\n    public resetBindGroup(index: number)\n    {\n        this._boundBindGroup[index] = null;\n    }\n\n    public setBindGroup(index: number, bindGroup: BindGroup, program: GpuProgram)\n    {\n        if (this._boundBindGroup[index] === bindGroup) return;\n        this._boundBindGroup[index] = bindGroup;\n\n        bindGroup._touch(this._renderer.textureGC.count);\n\n        // TODO getting the bind group works as it looks at th e assets and generates a key\n        // should this just be hidden behind a dirty flag?\n        const gpuBindGroup = this._renderer.bindGroup.getBindGroup(bindGroup, program, index);\n\n        // mark each item as having been used..\n        this.renderPassEncoder.setBindGroup(index, gpuBindGroup);\n    }\n\n    public setGeometry(geometry: Geometry, program: GpuProgram)\n    {\n        // when binding a buffers for geometry, there is no need to bind a buffer more than once if it is interleaved.\n        // which is often the case for Pixi. This is a performance optimisation.\n        // Instead of looping through the attributes, we instead call getBufferNamesToBind\n        // which returns a list of buffer names that need to be bound.\n        // we can then loop through this list and bind the buffers.\n        // essentially only binding a single time for any buffers that are interleaved.\n        const buffersToBind = this._renderer.pipeline.getBufferNamesToBind(geometry, program);\n\n        for (const i in buffersToBind)\n        {\n            this._setVertexBuffer(i as any as number, geometry.attributes[buffersToBind[i]].buffer);\n        }\n\n        if (geometry.indexBuffer)\n        {\n            this._setIndexBuffer(geometry.indexBuffer);\n        }\n    }\n\n    private _setShaderBindGroups(shader: Shader, skipSync?: boolean)\n    {\n        for (const i in shader.groups)\n        {\n            const bindGroup = shader.groups[i] as BindGroup;\n\n            // update any uniforms?\n            if (!skipSync)\n            {\n                this._syncBindGroup(bindGroup);\n            }\n\n            this.setBindGroup(i as unknown as number, bindGroup, shader.gpuProgram);\n        }\n    }\n\n    private _syncBindGroup(bindGroup: BindGroup)\n    {\n        for (const j in bindGroup.resources)\n        {\n            const resource = bindGroup.resources[j];\n\n            if ((resource as UniformGroup).isUniformGroup)\n            {\n                this._renderer.ubo.updateUniformGroup(resource as UniformGroup);\n            }\n        }\n    }\n\n    public draw(options: {\n        geometry: Geometry;\n        shader: Shader;\n        state?: State;\n        topology?: Topology;\n        size?: number;\n        start?: number;\n        instanceCount?: number;\n        skipSync?: boolean;\n    })\n    {\n        const { geometry, shader, state, topology, size, start, instanceCount, skipSync } = options;\n\n        this.setPipelineFromGeometryProgramAndState(geometry, shader.gpuProgram, state, topology);\n        this.setGeometry(geometry, shader.gpuProgram);\n        this._setShaderBindGroups(shader, skipSync);\n\n        if (geometry.indexBuffer)\n        {\n            this.renderPassEncoder.drawIndexed(\n                size || geometry.indexBuffer.data.length,\n                instanceCount ?? geometry.instanceCount,\n                start || 0\n            );\n        }\n        else\n        {\n            this.renderPassEncoder.draw(size || geometry.getSize(), instanceCount ?? geometry.instanceCount, start || 0);\n        }\n    }\n\n    public finishRenderPass()\n    {\n        if (this.renderPassEncoder)\n        {\n            this.renderPassEncoder.end();\n            this.renderPassEncoder = null;\n        }\n    }\n\n    public postrender()\n    {\n        this.finishRenderPass();\n\n        this._gpu.device.queue.submit([this.commandEncoder.finish()]);\n\n        this._resolveCommandFinished();\n\n        this.commandEncoder = null;\n    }\n\n    // restores a render pass if finishRenderPass was called\n    // not optimised as really used for debugging!\n    // used when we want to stop drawing and log a texture..\n    public restoreRenderPass()\n    {\n        const descriptor = (this._renderer.renderTarget.adaptor as GpuRenderTargetAdaptor).getDescriptor(\n            this._renderer.renderTarget.renderTarget,\n            false,\n            [0, 0, 0, 1],\n        );\n\n        this.renderPassEncoder = this.commandEncoder.beginRenderPass(descriptor);\n\n        const boundPipeline = this._boundPipeline;\n        const boundVertexBuffer = { ...this._boundVertexBuffer };\n        const boundIndexBuffer = this._boundIndexBuffer;\n        const boundBindGroup = { ...this._boundBindGroup };\n\n        this._clearCache();\n\n        const viewport = this._renderer.renderTarget.viewport;\n\n        this.renderPassEncoder.setViewport(viewport.x, viewport.y, viewport.width, viewport.height, 0, 1);\n\n        // reinstate the cache...\n\n        this.setPipeline(boundPipeline);\n\n        for (const i in boundVertexBuffer)\n        {\n            this._setVertexBuffer(i as unknown as number, boundVertexBuffer[i]);\n        }\n\n        for (const i in boundBindGroup)\n        {\n            this.setBindGroup(i as unknown as number, boundBindGroup[i], null);\n        }\n\n        this._setIndexBuffer(boundIndexBuffer);\n    }\n\n    private _clearCache()\n    {\n        for (let i = 0; i < 16; i++)\n        {\n            this._boundBindGroup[i] = null;\n            this._boundVertexBuffer[i] = null;\n        }\n\n        this._boundIndexBuffer = null;\n        this._boundPipeline = null;\n    }\n\n    public destroy()\n    {\n        (this._renderer as null) = null;\n        this._gpu = null;\n        this._boundBindGroup = null;\n        this._boundVertexBuffer = null;\n        this._boundIndexBuffer = null;\n        this._boundPipeline = null;\n    }\n\n    protected contextChange(gpu: GPU): void\n    {\n        this._gpu = gpu;\n    }\n}\n", "import { ExtensionType } from '../../../extensions/Extensions';\nimport { type System } from '../shared/system/System';\nimport { type WebGPURenderer } from './WebGPURenderer';\n\n/**\n * The GpuLimitsSystem provides information about the capabilities and limitations of the underlying GPU.\n * These limits, such as the maximum number of textures that can be used in a shader\n * (`maxTextures`) or the maximum number of textures that can be batched together (`maxBatchableTextures`),\n * are determined by the specific graphics hardware and driver.\n *\n * The values for these limits are not available immediately upon instantiation of the class.\n * They are populated when the WebGPU Device rendering context is successfully initialized and ready,\n * which occurs after the `renderer.init()` method has completed.\n * Attempting to access these properties before the context is ready will result in undefined or default values.\n *\n * This system allows the renderer to adapt its behavior and resource allocation strategies\n * to stay within the supported boundaries of the GPU, ensuring optimal performance and stability.\n * @example\n * ```ts\n * const renderer = new WebGPURenderer();\n * await renderer.init(); // GPU limits are populated after this call\n *\n * console.log(renderer.limits.maxTextures);\n * console.log(renderer.limits.maxBatchableTextures);\n * ```\n * @category rendering\n * @advanced\n */\nexport class GpuLimitsSystem implements System\n{\n    /** @ignore */\n    public static extension = {\n        type: [\n            ExtensionType.WebGPUSystem,\n        ],\n        name: 'limits',\n    } as const;\n\n    /** The maximum number of textures that can be used by a shader */\n    public maxTextures: number;\n    /** The maximum number of batchable textures */\n    public maxBatchableTextures: number;\n\n    private readonly _renderer: WebGPURenderer;\n\n    constructor(renderer: WebGPURenderer)\n    {\n        this._renderer = renderer;\n    }\n\n    public contextChange(): void\n    {\n        this.maxTextures = this._renderer.device.gpu.device.limits.maxSampledTexturesPerShaderStage;\n        this.maxBatchableTextures = this.maxTextures;\n    }\n\n    public destroy(): void\n    {\n        // boom!\n    }\n}\n", "import { ExtensionType } from '../../../extensions/Extensions';\nimport { STENCIL_MODES } from '../shared/state/const';\n\nimport type { RenderTarget } from '../shared/renderTarget/RenderTarget';\nimport type { System } from '../shared/system/System';\nimport type { WebGPURenderer } from './WebGPURenderer';\n\n/**\n * This manages the stencil buffer. Used primarily for masking\n * @category rendering\n * @advanced\n */\nexport class GpuStencilSystem implements System\n{\n    /** @ignore */\n    public static extension = {\n        type: [\n            ExtensionType.WebGPUSystem,\n        ],\n        name: 'stencil',\n    } as const;\n\n    private readonly _renderer: WebGPURenderer;\n\n    private _renderTargetStencilState: Record<number, {\n        stencilMode: STENCIL_MODES;\n        stencilReference: number;\n    }> = Object.create(null);\n\n    private _activeRenderTarget: RenderTarget;\n\n    constructor(renderer: WebGPURenderer)\n    {\n        this._renderer = renderer;\n\n        renderer.renderTarget.onRenderTargetChange.add(this);\n    }\n\n    protected onRenderTargetChange(renderTarget: RenderTarget)\n    {\n        let stencilState = this._renderTargetStencilState[renderTarget.uid];\n\n        if (!stencilState)\n        {\n            stencilState = this._renderTargetStencilState[renderTarget.uid] = {\n                stencilMode: STENCIL_MODES.DISABLED,\n                stencilReference: 0,\n            };\n        }\n\n        this._activeRenderTarget = renderTarget;\n\n        this.setStencilMode(stencilState.stencilMode, stencilState.stencilReference);\n    }\n\n    public setStencilMode(stencilMode: STENCIL_MODES, stencilReference: number)\n    {\n        const stencilState = this._renderTargetStencilState[this._activeRenderTarget.uid];\n\n        stencilState.stencilMode = stencilMode;\n        stencilState.stencilReference = stencilReference;\n\n        const renderer = this._renderer;\n\n        renderer.pipeline.setStencilMode(stencilMode);\n        renderer.encoder.renderPassEncoder.setStencilReference(stencilReference);\n    }\n\n    public destroy()\n    {\n        this._renderer.renderTarget.onRenderTargetChange.remove(this);\n\n        (this._renderer as null) = null;\n\n        this._activeRenderTarget = null;\n        this._renderTargetStencilState = null;\n    }\n}\n", "import type { UboElement, UboLayout, UNIFORM_TYPES, UniformData } from '../../../shared/shader/types';\n\n/** @internal */\nexport const WGSL_ALIGN_SIZE_DATA: Record<UNIFORM_TYPES | string, {align: number, size: number}> = {\n    i32: { align: 4, size: 4 },\n    u32: { align: 4, size: 4 },\n    f32: { align: 4, size: 4 },\n    f16: { align: 2, size: 2 },\n    'vec2<i32>': { align: 8, size: 8 },\n    'vec2<u32>': { align: 8, size: 8 },\n    'vec2<f32>': { align: 8, size: 8 },\n    'vec2<f16>': { align: 4, size: 4 },\n    'vec3<i32>': { align: 16, size: 12 },\n    'vec3<u32>': { align: 16, size: 12 },\n    'vec3<f32>': { align: 16, size: 12 },\n    'vec3<f16>': { align: 8, size: 6 },\n    'vec4<i32>': { align: 16, size: 16 },\n    'vec4<u32>': { align: 16, size: 16 },\n    'vec4<f32>': { align: 16, size: 16 },\n    'vec4<f16>': { align: 8, size: 8 },\n    'mat2x2<f32>': { align: 8, size: 16 },\n    'mat2x2<f16>': { align: 4, size: 8 },\n    'mat3x2<f32>': { align: 8, size: 24 },\n    'mat3x2<f16>': { align: 4, size: 12 },\n    'mat4x2<f32>': { align: 8, size: 32 },\n    'mat4x2<f16>': { align: 4, size: 16 },\n    'mat2x3<f32>': { align: 16, size: 32 },\n    'mat2x3<f16>': { align: 8, size: 16 },\n    'mat3x3<f32>': { align: 16, size: 48 },\n    'mat3x3<f16>': { align: 8, size: 24 },\n    'mat4x3<f32>': { align: 16, size: 64 },\n    'mat4x3<f16>': { align: 8, size: 32 },\n    'mat2x4<f32>': { align: 16, size: 32 },\n    'mat2x4<f16>': { align: 8, size: 16 },\n    'mat3x4<f32>': { align: 16, size: 48 },\n    'mat3x4<f16>': { align: 8, size: 24 },\n    'mat4x4<f32>': { align: 16, size: 64 },\n    'mat4x4<f16>': { align: 8, size: 32 },\n};\n\n/**\n * @param uniformData\n * @internal\n */\nexport function createUboElementsWGSL(uniformData: UniformData[]): UboLayout\n{\n    const uboElements: UboElement[] = uniformData.map((data: UniformData) =>\n        ({\n            data,\n            offset: 0,\n            size: 0,\n        }));\n\n    let offset = 0;\n\n    for (let i = 0; i < uboElements.length; i++)\n    {\n        const uboElement = uboElements[i];\n\n        let size = WGSL_ALIGN_SIZE_DATA[uboElement.data.type].size;\n        const align = WGSL_ALIGN_SIZE_DATA[uboElement.data.type].align;\n\n        if (!WGSL_ALIGN_SIZE_DATA[uboElement.data.type])\n        {\n            throw new Error(`[Pixi.js] WebGPU UniformBuffer: Unknown type ${uboElement.data.type}`);\n        }\n\n        if (uboElement.data.size > 1)\n        {\n            size = Math.max(size, align) * uboElement.data.size;\n        }\n\n        offset = Math.ceil((offset) / align) * align;\n\n        // TODO deal with Arrays\n        uboElement.size = size;\n\n        uboElement.offset = offset;\n\n        offset += size;\n    }\n\n    // must align to 16 bits!\n    offset = Math.ceil(offset / 16) * 16;\n\n    return { uboElements, size: offset };\n}\n\n", "import { WGSL_ALIGN_SIZE_DATA } from './createUboElementsWGSL';\n\nimport type { UboElement } from '../../../shared/shader/types';\n\n/**\n * This generates a function that will sync an array to the uniform buffer\n * following the wgsl layout\n * @param uboElement - the element to generate the array sync for\n * @param offsetToAdd - the offset to append at the start of the code\n * @returns - the generated code\n * @internal\n */\nexport function generateArraySyncWGSL(uboElement: UboElement, offsetToAdd: number): string\n{\n    // this is in byte..\n    const { size, align } = WGSL_ALIGN_SIZE_DATA[uboElement.data.type];\n\n    const remainder = (align - size) / 4;\n    const data = uboElement.data.type.indexOf('i32') >= 0 ? 'dataInt32' : 'data';\n\n    return `\n         v = uv.${uboElement.data.name};\n         ${offsetToAdd !== 0 ? `offset += ${offsetToAdd};` : ''}\n\n         arrayOffset = offset;\n\n         t = 0;\n\n         for(var i=0; i < ${uboElement.data.size * (size / 4)}; i++)\n         {\n             for(var j = 0; j < ${size / 4}; j++)\n             {\n                 ${data}[arrayOffset++] = v[t++];\n             }\n             ${remainder !== 0 ? `arrayOffset += ${remainder};` : ''}\n         }\n     `;\n}\n", "import { createUboSyncFunction } from '../../../shared/shader/utils/createUboSyncFunction';\nimport { uboSyncFunctionsWGSL } from '../../../shared/shader/utils/uboSyncFunctions';\nimport { generateArraySyncWGSL } from './generateArraySyncWGSL';\n\nimport type { UboElement, UniformsSyncCallback } from '../../../shared/shader/types';\n\n/**\n * @param uboElements\n * @internal\n */\nexport function createUboSyncFunctionWGSL(\n    uboElements: UboElement[],\n): UniformsSyncCallback\n{\n    return createUboSyncFunction(\n        uboElements,\n        'uboWgsl',\n        generateArraySyncWGSL,\n        uboSyncFunctionsWGSL,\n    );\n}\n", "import { ExtensionType } from '../../../extensions/Extensions';\nimport { UboSystem } from '../shared/shader/UboSystem';\nimport { createUboElementsWGSL } from './shader/utils/createUboElementsWGSL';\nimport { createUboSyncFunctionWGSL } from './shader/utils/createUboSyncFunctionWGSL';\n\n/**\n * System plugin to the renderer to manage uniform buffers. With a WGSL twist!\n * @category rendering\n * @advanced\n */\nexport class GpuUboSystem extends UboSystem\n{\n    /** @ignore */\n    public static extension = {\n        type: [ExtensionType.WebGPUSystem],\n        name: 'ubo',\n    } as const;\n\n    constructor()\n    {\n        super({\n            createUboElements: createUboElementsWGSL,\n            generateUboSync: createUboSyncFunctionWGSL,\n        });\n    }\n}\n", "/** @internal */\nexport class UboBatch\n{\n    public data: Float32Array;\n    private readonly _minUniformOffsetAlignment: number = 256;\n\n    public byteIndex = 0;\n\n    constructor({ minUniformOffsetAlignment }: {minUniformOffsetAlignment: number})\n    {\n        this._minUniformOffsetAlignment = minUniformOffsetAlignment;\n        this.data = new Float32Array(65535);\n    }\n\n    public clear(): void\n    {\n        this.byteIndex = 0;\n    }\n\n    public addEmptyGroup(size: number): number\n    {\n        // update the buffer.. only float32 for now!\n        if (size > this._minUniformOffsetAlignment / 4)\n        {\n            throw new Error(`UniformBufferBatch: array is too large: ${size * 4}`);\n        }\n\n        const start = this.byteIndex;\n\n        let newSize = start + (size * 4);\n\n        newSize = Math.ceil(newSize / this._minUniformOffsetAlignment) * this._minUniformOffsetAlignment;\n\n        if (newSize > this.data.length * 4)\n        {\n            // TODO push a new buffer\n            throw new Error('UniformBufferBatch: ubo batch got too big');\n        }\n\n        this.byteIndex = newSize;\n\n        return start;\n    }\n\n    public addGroup(array: Float32Array): number\n    {\n        const offset = this.addEmptyGroup(array.length);\n\n        for (let i = 0; i < array.length; i++)\n        {\n            this.data[(offset / 4) + i] = array[i];\n        }\n\n        return offset;\n    }\n\n    public destroy()\n    {\n        this.data = null;\n    }\n}\n", "import { ExtensionType } from '../../../extensions/Extensions';\nimport { Buffer } from '../shared/buffer/Buffer';\nimport { BufferResource } from '../shared/buffer/BufferResource';\nimport { BufferUsage } from '../shared/buffer/const';\nimport { UboBatch } from './buffer/UboBatch';\nimport { BindGroup } from './shader/BindGroup';\n\nimport type { UniformGroup } from '../shared/shader/UniformGroup';\nimport type { WebGPURenderer } from './WebGPURenderer';\n\nconst minUniformOffsetAlignment = 128;// 256 / 2;\n\n/** @internal */\nexport class GpuUniformBatchPipe\n{\n    /** @ignore */\n    public static extension = {\n        type: [\n            ExtensionType.WebGPUPipes,\n        ],\n        name: 'uniformBatch',\n    } as const;\n\n    private _renderer: WebGPURenderer;\n\n    private _bindGroupHash: Record<number, BindGroup> = Object.create(null);\n    private readonly _batchBuffer: UboBatch;\n\n    // number of buffers..\n    private _buffers: Buffer[] = [];\n\n    private _bindGroups: BindGroup[] = [];\n    private _bufferResources: BufferResource[] = [];\n\n    constructor(renderer: WebGPURenderer)\n    {\n        this._renderer = renderer;\n        this._renderer.renderableGC.addManagedHash(this, '_bindGroupHash');\n\n        this._batchBuffer = new UboBatch({ minUniformOffsetAlignment });\n\n        const totalBuffers = (256 / minUniformOffsetAlignment);\n\n        for (let i = 0; i < totalBuffers; i++)\n        {\n            let usage = BufferUsage.UNIFORM | BufferUsage.COPY_DST;\n\n            if (i === 0) usage |= BufferUsage.COPY_SRC;\n\n            this._buffers.push(new Buffer({\n                data: this._batchBuffer.data,\n                usage\n            }));\n        }\n    }\n\n    public renderEnd()\n    {\n        this._uploadBindGroups();\n        this._resetBindGroups();\n    }\n\n    private _resetBindGroups()\n    {\n        for (const i in this._bindGroupHash)\n        {\n            this._bindGroupHash[i] = null;\n        }\n\n        this._batchBuffer.clear();\n    }\n\n    // just works for single bind groups for now\n    public getUniformBindGroup(group: UniformGroup<any>, duplicate: boolean): BindGroup\n    {\n        if (!duplicate && this._bindGroupHash[group.uid])\n        {\n            return this._bindGroupHash[group.uid];\n        }\n\n        this._renderer.ubo.ensureUniformGroup(group);\n\n        const data = group.buffer.data as Float32Array;\n\n        const offset = this._batchBuffer.addEmptyGroup(data.length);\n\n        this._renderer.ubo.syncUniformGroup(group, this._batchBuffer.data, offset / 4);\n\n        this._bindGroupHash[group.uid] = this._getBindGroup(offset / minUniformOffsetAlignment);\n\n        return this._bindGroupHash[group.uid];\n    }\n\n    public getUboResource(group: UniformGroup<any>): BufferResource\n    {\n        this._renderer.ubo.updateUniformGroup(group);\n\n        const data = group.buffer.data as Float32Array;\n\n        const offset = this._batchBuffer.addGroup(data);\n\n        return this._getBufferResource(offset / minUniformOffsetAlignment);\n    }\n\n    public getArrayBindGroup(data: Float32Array): BindGroup\n    {\n        const offset = this._batchBuffer.addGroup(data);\n\n        return this._getBindGroup(offset / minUniformOffsetAlignment);\n    }\n\n    public getArrayBufferResource(data: Float32Array): BufferResource\n    {\n        const offset = this._batchBuffer.addGroup(data);\n\n        const index = offset / minUniformOffsetAlignment;\n\n        return this._getBufferResource(index);\n    }\n\n    private _getBufferResource(index: number): BufferResource\n    {\n        if (!this._bufferResources[index])\n        {\n            const buffer = this._buffers[index % 2];\n\n            this._bufferResources[index] = new BufferResource({\n                buffer,\n                offset: ((index / 2) | 0) * 256,\n                size: minUniformOffsetAlignment\n            });\n        }\n\n        return this._bufferResources[index];\n    }\n\n    private _getBindGroup(index: number): BindGroup\n    {\n        if (!this._bindGroups[index])\n        {\n            // even!\n            const bindGroup = new BindGroup({\n                0: this._getBufferResource(index),\n            });\n\n            this._bindGroups[index] = bindGroup;\n        }\n\n        return this._bindGroups[index];\n    }\n\n    private _uploadBindGroups()\n    {\n        const bufferSystem = this._renderer.buffer;\n\n        const firstBuffer = this._buffers[0];\n\n        firstBuffer.update(this._batchBuffer.byteIndex);\n\n        bufferSystem.updateBuffer(firstBuffer);\n\n        const commandEncoder = this._renderer.gpu.device.createCommandEncoder();\n\n        for (let i = 1; i < this._buffers.length; i++)\n        {\n            const buffer = this._buffers[i];\n\n            commandEncoder.copyBufferToBuffer(\n                bufferSystem.getGPUBuffer(firstBuffer),\n                minUniformOffsetAlignment,\n                bufferSystem.getGPUBuffer(buffer),\n                0,\n                this._batchBuffer.byteIndex\n            );\n        }\n\n        // TODO make a system that will que up all commands in to one array?\n        this._renderer.gpu.device.queue.submit([commandEncoder.finish()]);\n    }\n\n    public destroy()\n    {\n        for (let i = 0; i < this._bindGroups.length; i++)\n        {\n            this._bindGroups[i].destroy();\n        }\n\n        this._bindGroups = null;\n        this._bindGroupHash = null;\n\n        for (let i = 0; i < this._buffers.length; i++)\n        {\n            this._buffers[i].destroy();\n        }\n        this._buffers = null;\n\n        for (let i = 0; i < this._bufferResources.length; i++)\n        {\n            this._bufferResources[i].destroy();\n        }\n\n        this._bufferResources = null;\n\n        this._batchBuffer.destroy();\n        this._bindGroupHash = null;\n\n        this._renderer = null;\n    }\n}\n", "import { ExtensionType } from '../../../../extensions/Extensions';\nimport { warn } from '../../../../utils/logging/warn';\nimport { ensureAttributes } from '../../gl/shader/program/ensureAttributes';\nimport { STENCIL_MODES } from '../../shared/state/const';\nimport { createIdFromString } from '../../shared/utils/createIdFromString';\nimport { GpuStencilModesToPixi } from '../state/GpuStencilModesToPixi';\n\nimport type { Topology } from '../../shared/geometry/const';\nimport type { Geometry } from '../../shared/geometry/Geometry';\nimport type { State } from '../../shared/state/State';\nimport type { System } from '../../shared/system/System';\nimport type { GPU } from '../GpuDeviceSystem';\nimport type { GpuRenderTarget } from '../renderTarget/GpuRenderTarget';\nimport type { GpuProgram } from '../shader/GpuProgram';\nimport type { StencilState } from '../state/GpuStencilModesToPixi';\nimport type { WebGPURenderer } from '../WebGPURenderer';\n\nconst topologyStringToId = {\n    'point-list': 0,\n    'line-list': 1,\n    'line-strip': 2,\n    'triangle-list': 3,\n    'triangle-strip': 4,\n};\n\n// geometryLayouts = 256; // 8 bits // 256 states // value 0-255;\n// shaderKeys = 256; // 8 bits // 256 states // value 0-255;\n// state = 64; // 6 bits // 64 states // value 0-63;\n// blendMode = 32; // 5 bits // 32 states // value 0-31;\n// topology = 8; // 3 bits // 8 states // value 0-7;\nfunction getGraphicsStateKey(\n    geometryLayout: number,\n    shaderKey: number,\n    state: number,\n    blendMode: number,\n    topology: number,\n): number\n{\n    return (geometryLayout << 24) // Allocate the 8 bits for geometryLayouts at the top\n         | (shaderKey << 16) // Next 8 bits for shaderKeys\n         | (state << 10) // 6 bits for state\n         | (blendMode << 5) // 5 bits for blendMode\n         | topology; // And 3 bits for topology at the least significant position\n}\n\n// colorMask = 16;// 4 bits // 16 states // value 0-15;\n// stencilState = 8; // 3 bits // 8 states // value 0-7;\n// renderTarget = 1; // 2 bit // 3 states // value 0-3; // none, stencil, depth, depth-stencil\n// multiSampleCount = 1; // 1 bit // 2 states // value 0-1;\nfunction getGlobalStateKey(\n    stencilStateId: number,\n    multiSampleCount: number,\n    colorMask: number,\n    renderTarget: number,\n): number\n{\n    return (colorMask << 6) // Allocate the 4 bits for colorMask at the top\n         | (stencilStateId << 3) // Next 3 bits for stencilStateId\n         | (renderTarget << 1) // 2 bits for renderTarget\n         | multiSampleCount; // And 1 bit for multiSampleCount at the least significant position\n}\n\ntype PipeHash = Record<number, GPURenderPipeline>;\n\n/**\n * A system that creates and manages the GPU pipelines.\n *\n * Caching Mechanism: At its core, the system employs a two-tiered caching strategy to minimize\n * the redundant creation of GPU pipelines (or \"pipes\"). This strategy is based on generating unique\n * keys that represent the state of the graphics settings and the specific requirements of the\n * item being rendered. By caching these pipelines, subsequent draw calls with identical configurations\n * can reuse existing pipelines instead of generating new ones.\n *\n * State Management: The system differentiates between \"global\" state properties (like color masks\n * and stencil masks, which do not change frequently) and properties that may vary between draw calls\n * (such as geometry, shaders, and blend modes). Unique keys are generated for both these categories\n * using getStateKey for global state and getGraphicsStateKey for draw-specific settings. These keys are\n * then then used to caching the pipe. The next time we need a pipe we can check\n * the cache by first looking at the state cache and then the pipe cache.\n * @category rendering\n * @advanced\n */\nexport class PipelineSystem implements System\n{\n    /** @ignore */\n    public static extension = {\n        type: [ExtensionType.WebGPUSystem],\n        name: 'pipeline',\n    } as const;\n    private readonly _renderer: WebGPURenderer;\n\n    protected CONTEXT_UID: number;\n\n    private _moduleCache: Record<string, GPUShaderModule> = Object.create(null);\n    private _bufferLayoutsCache: Record<number, GPUVertexBufferLayout[]> = Object.create(null);\n    private readonly _bindingNamesCache: Record<string, Record<string, string>> = Object.create(null);\n\n    private _pipeCache: PipeHash = Object.create(null);\n    private readonly _pipeStateCaches: Record<number, PipeHash> = Object.create(null);\n\n    private _gpu: GPU;\n    private _stencilState: StencilState;\n\n    private _stencilMode: STENCIL_MODES;\n    private _colorMask = 0b1111;\n    private _multisampleCount = 1;\n    private _depthStencilAttachment: 0 | 1;\n\n    constructor(renderer: WebGPURenderer)\n    {\n        this._renderer = renderer;\n    }\n\n    protected contextChange(gpu: GPU): void\n    {\n        this._gpu = gpu;\n        this.setStencilMode(STENCIL_MODES.DISABLED);\n\n        this._updatePipeHash();\n    }\n\n    public setMultisampleCount(multisampleCount: number): void\n    {\n        if (this._multisampleCount === multisampleCount) return;\n\n        this._multisampleCount = multisampleCount;\n\n        this._updatePipeHash();\n    }\n\n    public setRenderTarget(renderTarget: GpuRenderTarget)\n    {\n        this._multisampleCount = renderTarget.msaaSamples;\n        this._depthStencilAttachment = renderTarget.descriptor.depthStencilAttachment ? 1 : 0;\n\n        this._updatePipeHash();\n    }\n\n    public setColorMask(colorMask: number): void\n    {\n        if (this._colorMask === colorMask) return;\n\n        this._colorMask = colorMask;\n\n        this._updatePipeHash();\n    }\n\n    public setStencilMode(stencilMode: STENCIL_MODES): void\n    {\n        if (this._stencilMode === stencilMode) return;\n\n        this._stencilMode = stencilMode;\n        this._stencilState = GpuStencilModesToPixi[stencilMode];\n\n        this._updatePipeHash();\n    }\n\n    public setPipeline(geometry: Geometry, program: GpuProgram, state: State, passEncoder: GPURenderPassEncoder): void\n    {\n        const pipeline = this.getPipeline(geometry, program, state);\n\n        passEncoder.setPipeline(pipeline);\n    }\n\n    public getPipeline(\n        geometry: Geometry,\n        program: GpuProgram,\n        state: State,\n        topology?: Topology,\n    ): GPURenderPipeline\n    {\n        if (!geometry._layoutKey)\n        {\n            ensureAttributes(geometry, program.attributeData);\n\n            // prepare the geometry for the pipeline\n            this._generateBufferKey(geometry);\n        }\n\n        topology ||= geometry.topology;\n\n        // now we have set the Ids - the key is different...\n        const key = getGraphicsStateKey(\n            geometry._layoutKey,\n            program._layoutKey,\n            state.data,\n            state._blendModeId,\n            topologyStringToId[topology],\n        );\n\n        if (this._pipeCache[key]) return this._pipeCache[key];\n\n        this._pipeCache[key] = this._createPipeline(geometry, program, state, topology);\n\n        return this._pipeCache[key];\n    }\n\n    private _createPipeline(geometry: Geometry, program: GpuProgram, state: State, topology: Topology): GPURenderPipeline\n    {\n        const device = this._gpu.device;\n\n        const buffers = this._createVertexBufferLayouts(geometry, program);\n\n        const blendModes = this._renderer.state.getColorTargets(state);\n\n        blendModes[0].writeMask = this._stencilMode === STENCIL_MODES.RENDERING_MASK_ADD ? 0 : this._colorMask;\n\n        const layout = this._renderer.shader.getProgramData(program).pipeline;\n\n        const descriptor: GPURenderPipelineDescriptor = {\n            // TODO later check if its helpful to create..\n            // layout,\n            vertex: {\n                module: this._getModule(program.vertex.source),\n                entryPoint: program.vertex.entryPoint,\n                // geometry..\n                buffers,\n            },\n            fragment: {\n                module: this._getModule(program.fragment.source),\n                entryPoint: program.fragment.entryPoint,\n                targets: blendModes,\n            },\n            primitive: {\n                topology,\n                cullMode: state.cullMode,\n            },\n            layout,\n            multisample: {\n                count: this._multisampleCount,\n            },\n            // depthStencil,\n            label: `PIXI Pipeline`,\n        };\n\n        // only apply if the texture has stencil or depth\n        if (this._depthStencilAttachment)\n        {\n            // mask states..\n            descriptor.depthStencil = {\n                ...this._stencilState,\n                format: 'depth24plus-stencil8',\n                depthWriteEnabled: state.depthTest,\n                depthCompare: state.depthTest ? 'less' : 'always',\n            };\n        }\n\n        const pipeline = device.createRenderPipeline(descriptor);\n\n        return pipeline;\n    }\n\n    private _getModule(code: string): GPUShaderModule\n    {\n        return this._moduleCache[code] || this._createModule(code);\n    }\n\n    private _createModule(code: string): GPUShaderModule\n    {\n        const device = this._gpu.device;\n\n        this._moduleCache[code] = device.createShaderModule({\n            code,\n        });\n\n        return this._moduleCache[code];\n    }\n\n    private _generateBufferKey(geometry: Geometry): number\n    {\n        const keyGen = [];\n        let index = 0;\n        // generate a key..\n\n        const attributeKeys = Object.keys(geometry.attributes).sort();\n\n        for (let i = 0; i < attributeKeys.length; i++)\n        {\n            const attribute = geometry.attributes[attributeKeys[i]];\n\n            keyGen[index++] = attribute.offset;\n            keyGen[index++] = attribute.format;\n            keyGen[index++] = attribute.stride;\n            keyGen[index++] = attribute.instance;\n        }\n\n        const stringKey = keyGen.join('|');\n\n        geometry._layoutKey = createIdFromString(stringKey, 'geometry');\n\n        return geometry._layoutKey;\n    }\n\n    private _generateAttributeLocationsKey(program: GpuProgram): number\n    {\n        const keyGen = [];\n        let index = 0;\n        // generate a key..\n\n        const attributeKeys = Object.keys(program.attributeData).sort();\n\n        for (let i = 0; i < attributeKeys.length; i++)\n        {\n            const attribute = program.attributeData[attributeKeys[i]];\n\n            keyGen[index++] = attribute.location;\n        }\n\n        const stringKey = keyGen.join('|');\n\n        program._attributeLocationsKey = createIdFromString(stringKey, 'programAttributes');\n\n        return program._attributeLocationsKey;\n    }\n\n    /**\n     * Returns a hash of buffer names mapped to bind locations.\n     * This is used to bind the correct buffer to the correct location in the shader.\n     * @param geometry - The geometry where to get the buffer names\n     * @param program - The program where to get the buffer names\n     * @returns An object of buffer names mapped to the bind location.\n     */\n    public getBufferNamesToBind(geometry: Geometry, program: GpuProgram): Record<string, string>\n    {\n        const key = (geometry._layoutKey << 16) | program._attributeLocationsKey;\n\n        if (this._bindingNamesCache[key]) return this._bindingNamesCache[key];\n\n        const data = this._createVertexBufferLayouts(geometry, program);\n\n        // now map the data to the buffers..\n        const bufferNamesToBind: Record<string, string> = Object.create(null);\n\n        const attributeData = program.attributeData;\n\n        for (let i = 0; i < data.length; i++)\n        {\n            const attributes = Object.values(data[i].attributes);\n\n            const shaderLocation = attributes[0].shaderLocation;\n\n            for (const j in attributeData)\n            {\n                if (attributeData[j].location === shaderLocation)\n                {\n                    bufferNamesToBind[i] = j;\n                    break;\n                }\n            }\n        }\n\n        this._bindingNamesCache[key] = bufferNamesToBind;\n\n        return bufferNamesToBind;\n    }\n\n    private _createVertexBufferLayouts(geometry: Geometry, program: GpuProgram): GPUVertexBufferLayout[]\n    {\n        if (!program._attributeLocationsKey) this._generateAttributeLocationsKey(program);\n\n        const key = (geometry._layoutKey << 16) | program._attributeLocationsKey;\n\n        if (this._bufferLayoutsCache[key])\n        {\n            return this._bufferLayoutsCache[key];\n        }\n\n        const vertexBuffersLayout: GPUVertexBufferLayout[] = [];\n\n        geometry.buffers.forEach((buffer) =>\n        {\n            const bufferEntry: GPUVertexBufferLayout = {\n                arrayStride: 0,\n                stepMode: 'vertex',\n                attributes: [],\n            };\n\n            const bufferEntryAttributes = bufferEntry.attributes as GPUVertexAttribute[];\n\n            for (const i in program.attributeData)\n            {\n                const attribute = geometry.attributes[i];\n\n                if ((attribute.divisor ?? 1) !== 1)\n                {\n                    // TODO: Maybe emulate divisor with storage_buffers/float_textures?\n                    // For now just issue a warning\n                    warn(`Attribute ${i} has an invalid divisor value of '${attribute.divisor}'. `\n                        + 'WebGPU only supports a divisor value of 1');\n                }\n\n                if (attribute.buffer === buffer)\n                {\n                    bufferEntry.arrayStride = attribute.stride;\n                    bufferEntry.stepMode = attribute.instance ? 'instance' : 'vertex';\n\n                    bufferEntryAttributes.push({\n                        shaderLocation: program.attributeData[i].location,\n                        offset: attribute.offset,\n                        format: attribute.format,\n                    });\n                }\n            }\n\n            if (bufferEntryAttributes.length)\n            {\n                vertexBuffersLayout.push(bufferEntry);\n            }\n        });\n\n        this._bufferLayoutsCache[key] = vertexBuffersLayout;\n\n        return vertexBuffersLayout;\n    }\n\n    private _updatePipeHash(): void\n    {\n        const key = getGlobalStateKey(\n            this._stencilMode,\n            this._multisampleCount,\n            this._colorMask,\n            this._depthStencilAttachment\n        );\n\n        if (!this._pipeStateCaches[key])\n        {\n            this._pipeStateCaches[key] = Object.create(null);\n        }\n\n        this._pipeCache = this._pipeStateCaches[key];\n    }\n\n    public destroy(): void\n    {\n        (this._renderer as null) = null;\n        this._bufferLayoutsCache = null;\n    }\n}\n", "import type { TextureSource } from '../../shared/texture/sources/TextureSource';\n\n/**\n * A class which holds the canvas contexts and textures for a render target.\n * @category rendering\n * @ignore\n */\nexport class GpuRenderTarget\n{\n    public contexts: GPUCanvasContext[] = [];\n    public msaaTextures: TextureSource[] = [];\n    public msaa: boolean;\n    public msaaSamples = 1;\n    public width: number;\n    public height: number;\n    public descriptor: GPURenderPassDescriptor;\n}\n", "import { <PERSON><PERSON><PERSON> } from '../../gl/const';\nimport { CanvasSource } from '../../shared/texture/sources/CanvasSource';\nimport { TextureSource } from '../../shared/texture/sources/TextureSource';\nimport { GpuRenderTarget } from './GpuRenderTarget';\n\nimport type { RgbaArray } from '../../../../color/Color';\nimport type { Rectangle } from '../../../../maths/shapes/Rectangle';\nimport type { CLEAR_OR_BOOL } from '../../gl/const';\nimport type { RenderTarget } from '../../shared/renderTarget/RenderTarget';\nimport type { RenderTargetAdaptor, RenderTargetSystem } from '../../shared/renderTarget/RenderTargetSystem';\nimport type { Texture } from '../../shared/texture/Texture';\nimport type { WebGPURenderer } from '../WebGPURenderer';\n\n/**\n * The WebGPU adaptor for the render target system. Allows the Render Target System to\n * be used with the WebGPU renderer\n * @category rendering\n * @ignore\n */\nexport class GpuRenderTargetAdaptor implements RenderTargetAdaptor<GpuRenderTarget>\n{\n    private _renderTargetSystem: RenderTargetSystem<GpuRenderTarget>;\n    private _renderer: WebGPURenderer<HTMLCanvasElement>;\n\n    public init(renderer: WebGPURenderer, renderTargetSystem: RenderTargetSystem<GpuRenderTarget>): void\n    {\n        this._renderer = renderer;\n        this._renderTargetSystem = renderTargetSystem;\n    }\n\n    public copyToTexture(\n        sourceRenderSurfaceTexture: RenderTarget,\n        destinationTexture: Texture,\n        originSrc: { x: number; y: number; },\n        size: { width: number; height: number; },\n        originDest: { x: number; y: number; },\n    )\n    {\n        const renderer = this._renderer;\n\n        const baseGpuTexture = this._getGpuColorTexture(\n            sourceRenderSurfaceTexture\n        );\n\n        const backGpuTexture = renderer.texture.getGpuSource(\n            destinationTexture.source\n        );\n\n        renderer.encoder.commandEncoder.copyTextureToTexture(\n            {\n                texture: baseGpuTexture,\n                origin: originSrc,\n            },\n            {\n                texture: backGpuTexture,\n                origin: originDest,\n            },\n            size\n        );\n\n        return destinationTexture;\n    }\n\n    public startRenderPass(\n        renderTarget: RenderTarget,\n        clear: CLEAR_OR_BOOL = true,\n        clearColor?: RgbaArray,\n        viewport?: Rectangle\n    )\n    {\n        const renderTargetSystem = this._renderTargetSystem;\n\n        const gpuRenderTarget = renderTargetSystem.getGpuRenderTarget(renderTarget);\n\n        const descriptor = this.getDescriptor(renderTarget, clear, clearColor);\n\n        gpuRenderTarget.descriptor = descriptor;\n\n        // TODO we should not finish a render pass each time we bind\n        // for example filters - we would want to push / pop render targets\n        this._renderer.pipeline.setRenderTarget(gpuRenderTarget);\n        this._renderer.encoder.beginRenderPass(gpuRenderTarget);\n        this._renderer.encoder.setViewport(viewport);\n    }\n\n    public finishRenderPass()\n    {\n        this._renderer.encoder.endRenderPass();\n    }\n\n    /**\n     * returns the gpu texture for the first color texture in the render target\n     * mainly used by the filter manager to get copy the texture for blending\n     * @param renderTarget\n     * @returns a gpu texture\n     */\n    private _getGpuColorTexture(renderTarget: RenderTarget): GPUTexture\n    {\n        const gpuRenderTarget = this._renderTargetSystem.getGpuRenderTarget(renderTarget);\n\n        if (gpuRenderTarget.contexts[0])\n        {\n            return gpuRenderTarget.contexts[0].getCurrentTexture();\n        }\n\n        return this._renderer.texture.getGpuSource(\n            renderTarget.colorTextures[0].source\n        );\n    }\n\n    public getDescriptor(\n        renderTarget: RenderTarget,\n        clear: CLEAR_OR_BOOL,\n        clearValue: RgbaArray\n    ): GPURenderPassDescriptor\n    {\n        if (typeof clear === 'boolean')\n        {\n            clear = clear ? CLEAR.ALL : CLEAR.NONE;\n        }\n\n        const renderTargetSystem = this._renderTargetSystem;\n\n        const gpuRenderTarget = renderTargetSystem.getGpuRenderTarget(renderTarget);\n\n        const colorAttachments = renderTarget.colorTextures.map(\n            (texture, i) =>\n            {\n                const context = gpuRenderTarget.contexts[i];\n\n                let view: GPUTextureView;\n                let resolveTarget: GPUTextureView;\n\n                if (context)\n                {\n                    const currentTexture = context.getCurrentTexture();\n\n                    const canvasTextureView = currentTexture.createView();\n\n                    view = canvasTextureView;\n                }\n                else\n                {\n                    view = this._renderer.texture.getGpuSource(texture).createView({\n                        mipLevelCount: 1,\n                    });\n                }\n\n                if (gpuRenderTarget.msaaTextures[i])\n                {\n                    resolveTarget = view;\n                    view = this._renderer.texture.getTextureView(\n                        gpuRenderTarget.msaaTextures[i]\n                    );\n                }\n\n                const loadOp = ((clear as CLEAR) & CLEAR.COLOR ? 'clear' : 'load') as GPULoadOp;\n\n                clearValue ??= renderTargetSystem.defaultClearColor;\n\n                return {\n                    view,\n                    resolveTarget,\n                    clearValue,\n                    storeOp: 'store',\n                    loadOp\n                };\n            }\n        ) as GPURenderPassColorAttachment[];\n\n        let depthStencilAttachment: GPURenderPassDepthStencilAttachment;\n\n        // if we have a depth or stencil buffer, we need to ensure we have a texture for it\n        // this is WebGPU specific - as WebGL does not require textures to run a depth / stencil buffer\n        if ((renderTarget.stencil || renderTarget.depth) && !renderTarget.depthStencilTexture)\n        {\n            renderTarget.ensureDepthStencilTexture();\n            renderTarget.depthStencilTexture.source.sampleCount = gpuRenderTarget.msaa ? 4 : 1;\n        }\n\n        if (renderTarget.depthStencilTexture)\n        {\n            const stencilLoadOp = (clear & CLEAR.STENCIL ? 'clear' : 'load') as GPULoadOp;\n            const depthLoadOp = (clear & CLEAR.DEPTH ? 'clear' : 'load') as GPULoadOp;\n\n            depthStencilAttachment = {\n                view: this._renderer.texture\n                    .getGpuSource(renderTarget.depthStencilTexture.source)\n                    .createView(),\n                stencilStoreOp: 'store',\n                stencilLoadOp,\n                depthClearValue: 1.0,\n                depthLoadOp,\n                depthStoreOp: 'store',\n            };\n        }\n\n        const descriptor: GPURenderPassDescriptor = {\n            colorAttachments,\n            depthStencilAttachment,\n        };\n\n        return descriptor;\n    }\n\n    public clear(renderTarget: RenderTarget, clear: CLEAR_OR_BOOL = true, clearColor?: RgbaArray, viewport?: Rectangle)\n    {\n        if (!clear) return;\n\n        const { gpu, encoder } = this._renderer;\n\n        const device = gpu.device;\n\n        const standAlone = encoder.commandEncoder === null;\n\n        if (standAlone)\n        {\n            const commandEncoder = device.createCommandEncoder();\n            const renderPassDescriptor = this.getDescriptor(renderTarget, clear, clearColor);\n\n            const passEncoder = commandEncoder.beginRenderPass(renderPassDescriptor);\n\n            passEncoder.setViewport(viewport.x, viewport.y, viewport.width, viewport.height, 0, 1);\n\n            passEncoder.end();\n\n            const gpuCommands = commandEncoder.finish();\n\n            device.queue.submit([gpuCommands]);\n        }\n        else\n        {\n            this.startRenderPass(renderTarget, clear, clearColor, viewport);\n        }\n    }\n\n    public initGpuRenderTarget(renderTarget: RenderTarget): GpuRenderTarget\n    {\n        // always true for WebGPU\n        renderTarget.isRoot = true;\n\n        const gpuRenderTarget = new GpuRenderTarget();\n\n        // create a context...\n        // is a canvas...\n        renderTarget.colorTextures.forEach((colorTexture, i) =>\n        {\n            if (colorTexture instanceof CanvasSource)\n            {\n                const context = colorTexture.resource.getContext(\n                    'webgpu'\n                ) as unknown as GPUCanvasContext;\n\n                const alphaMode = (colorTexture as CanvasSource).transparent ? 'premultiplied' : 'opaque';\n\n                try\n                {\n                    context.configure({\n                        device: this._renderer.gpu.device,\n                        usage: GPUTextureUsage.TEXTURE_BINDING\n                            | GPUTextureUsage.COPY_DST\n                            | GPUTextureUsage.RENDER_ATTACHMENT\n                            | GPUTextureUsage.COPY_SRC,\n                        format: 'bgra8unorm',\n                        alphaMode,\n                    });\n                }\n                catch (e)\n                {\n                    console.error(e);\n                }\n\n                gpuRenderTarget.contexts[i] = context;\n            }\n\n            gpuRenderTarget.msaa = colorTexture.source.antialias;\n\n            if (colorTexture.source.antialias)\n            {\n                const msaaTexture = new TextureSource({\n                    width: 0,\n                    height: 0,\n                    sampleCount: 4,\n                });\n\n                gpuRenderTarget.msaaTextures[i] = msaaTexture;\n            }\n        });\n\n        if (gpuRenderTarget.msaa)\n        {\n            gpuRenderTarget.msaaSamples = 4;\n\n            if (renderTarget.depthStencilTexture)\n            {\n                renderTarget.depthStencilTexture.source.sampleCount = 4;\n            }\n        }\n\n        return gpuRenderTarget;\n    }\n\n    public destroyGpuRenderTarget(gpuRenderTarget: GpuRenderTarget)\n    {\n        gpuRenderTarget.contexts.forEach((context) =>\n        {\n            context.unconfigure();\n        });\n\n        gpuRenderTarget.msaaTextures.forEach((texture) =>\n        {\n            texture.destroy();\n        });\n\n        gpuRenderTarget.msaaTextures.length = 0;\n        gpuRenderTarget.contexts.length = 0;\n    }\n\n    public ensureDepthStencilTexture(renderTarget: RenderTarget)\n    {\n        // TODO This function will be more useful once we cache the descriptors\n        const gpuRenderTarget = this._renderTargetSystem.getGpuRenderTarget(renderTarget);\n\n        if (renderTarget.depthStencilTexture && gpuRenderTarget.msaa)\n        {\n            renderTarget.depthStencilTexture.source.sampleCount = 4;\n        }\n    }\n\n    public resizeGpuRenderTarget(renderTarget: RenderTarget)\n    {\n        const gpuRenderTarget = this._renderTargetSystem.getGpuRenderTarget(renderTarget);\n\n        gpuRenderTarget.width = renderTarget.width;\n        gpuRenderTarget.height = renderTarget.height;\n\n        if (gpuRenderTarget.msaa)\n        {\n            renderTarget.colorTextures.forEach((colorTexture, i) =>\n            {\n                const msaaTexture = gpuRenderTarget.msaaTextures[i];\n\n                msaaTexture?.resize(\n                    colorTexture.source.width,\n                    colorTexture.source.height,\n                    colorTexture.source._resolution\n                );\n            });\n        }\n    }\n}\n", "import { ExtensionType } from '../../../../extensions/Extensions';\nimport { RenderTargetSystem } from '../../shared/renderTarget/RenderTargetSystem';\nimport { GpuRenderTargetAdaptor } from './GpuRenderTargetAdaptor';\n\nimport type { WebGPURenderer } from '../WebGPURenderer';\nimport type { GpuRenderTarget } from './GpuRenderTarget';\n\n/**\n * The WebGL adaptor for the render target system. Allows the Render Target System to be used with the WebGl renderer\n * @category rendering\n * @advanced\n */\nexport class GpuRenderTargetSystem extends RenderTargetSystem<GpuRenderTarget>\n{\n    /** @ignore */\n    public static extension = {\n        type: [ExtensionType.WebGPUSystem],\n        name: 'renderTarget',\n    } as const;\n\n    public adaptor = new GpuRenderTargetAdaptor();\n\n    constructor(renderer: WebGPURenderer)\n    {\n        super(renderer);\n\n        this.adaptor.init(renderer, this);\n    }\n}\n", "import { ExtensionType } from '../../../../extensions/Extensions';\n\nimport type { GPU } from '../GpuDeviceSystem';\nimport type { GpuProgram } from './GpuProgram';\n\n/**\n * Data structure for GPU program layout.\n * Contains bind group layouts and pipeline layout.\n * @category rendering\n * @advanced\n */\nexport interface GPUProgramData\n{\n    bindGroups: GPUBindGroupLayout[]\n    pipeline: GPUPipelineLayout\n}\n\n/**\n * A system that manages the rendering of GpuPrograms.\n * @category rendering\n * @advanced\n */\nexport class GpuShaderSystem\n{\n    /** @ignore */\n    public static extension = {\n        type: [\n            ExtensionType.WebGPUSystem,\n        ],\n        name: 'shader',\n    } as const;\n\n    private _gpu: GPU;\n\n    private readonly _gpuProgramData: Record<number, GPUProgramData> = Object.create(null);\n\n    protected contextChange(gpu: GPU): void\n    {\n        this._gpu = gpu;\n    }\n\n    public getProgramData(program: GpuProgram)\n    {\n        return this._gpuProgramData[program._layoutKey] || this._createGPUProgramData(program);\n    }\n\n    private _createGPUProgramData(program: GpuProgram)\n    {\n        const device = this._gpu.device;\n\n        const bindGroups = program.gpuLayout.map((group) => device.createBindGroupLayout({ entries: group }));\n\n        const pipelineLayoutDesc = { bindGroupLayouts: bindGroups };\n\n        this._gpuProgramData[program._layoutKey] = {\n            bindGroups,\n            pipeline: device.createPipelineLayout(pipelineLayoutDesc),\n        };\n\n        // generally we avoid having to make this automatically\n        // keeping this for a reminder, if any issues popup\n        // program._gpuLayout = {\n        //     bindGroups: null,\n        //     pipeline: 'auto',\n        // };\n\n        return this._gpuProgramData[program._layoutKey];\n    }\n\n    public destroy(): void\n    {\n        // TODO destroy the _gpuProgramData\n        this._gpu = null;\n        (this._gpuProgramData as null) = null;\n    }\n}\n", "import type { BLEND_MODES } from '../../shared/state/const';\n\n/** @internal */\nexport const GpuBlendModesToPixi: Partial<Record<BLEND_MODES, GPUBlendState>> = {};\n\nGpuBlendModesToPixi.normal = {\n    alpha: {\n        srcFactor: 'one',\n        dstFactor: 'one-minus-src-alpha',\n        operation: 'add',\n    },\n    color: {\n        srcFactor: 'one',\n        dstFactor: 'one-minus-src-alpha',\n        operation: 'add',\n    },\n};\n\nGpuBlendModesToPixi.add = {\n    alpha: {\n        srcFactor: 'src-alpha',\n        dstFactor: 'one-minus-src-alpha',\n        operation: 'add',\n    },\n    color: {\n        srcFactor: 'one',\n        dstFactor: 'one',\n        operation: 'add',\n    },\n};\n\nGpuBlendModesToPixi.multiply = {\n    alpha: {\n        srcFactor: 'one',\n        dstFactor: 'one-minus-src-alpha',\n        operation: 'add',\n    },\n    color: {\n        srcFactor: 'dst',\n        dstFactor: 'one-minus-src-alpha',\n        operation: 'add',\n    },\n};\n\nGpuBlendModesToPixi.screen = {\n    alpha: {\n        srcFactor: 'one',\n        dstFactor: 'one-minus-src-alpha',\n        operation: 'add',\n    },\n    color: {\n        srcFactor: 'one',\n        dstFactor: 'one-minus-src',\n        operation: 'add',\n    },\n};\n\nGpuBlendModesToPixi.overlay = {\n    alpha: {\n        srcFactor: 'one',\n        dstFactor: 'one-minus-src-alpha',\n        operation: 'add',\n    },\n    color: {\n        srcFactor: 'one',\n        dstFactor: 'one-minus-src',\n        operation: 'add',\n    },\n};\n\nGpuBlendModesToPixi.none = {\n    alpha: {\n        srcFactor: 'one',\n        dstFactor: 'one-minus-src-alpha',\n        operation: 'add',\n    },\n    color: {\n        srcFactor: 'zero',\n        dstFactor: 'zero',\n        operation: 'add',\n    },\n};\n\n// not-premultiplied blend modes\nGpuBlendModesToPixi['normal-npm'] = {\n    alpha: {\n        srcFactor: 'one',\n        dstFactor: 'one-minus-src-alpha',\n        operation: 'add',\n    },\n    color: {\n        srcFactor: 'src-alpha',\n        dstFactor: 'one-minus-src-alpha',\n        operation: 'add',\n    },\n};\n\nGpuBlendModesToPixi['add-npm'] = {\n    alpha: {\n        srcFactor: 'one',\n        dstFactor: 'one',\n        operation: 'add',\n    },\n    color: {\n        srcFactor: 'src-alpha',\n        dstFactor: 'one',\n        operation: 'add',\n    },\n};\n\nGpuBlendModesToPixi['screen-npm'] = {\n    alpha: {\n        srcFactor: 'one',\n        dstFactor: 'one-minus-src-alpha',\n        operation: 'add',\n    },\n    color: {\n        srcFactor: 'src-alpha',\n        dstFactor: 'one-minus-src',\n        operation: 'add',\n    },\n};\n\nGpuBlendModesToPixi.erase = {\n    alpha: {\n        srcFactor: 'zero',\n        dstFactor: 'one-minus-src-alpha',\n        operation: 'add',\n    },\n    color: {\n        srcFactor: 'zero',\n        dstFactor: 'one-minus-src',\n        operation: 'add',\n    },\n};\n\nGpuBlendModesToPixi.min = {\n    alpha: {\n        srcFactor: 'one',\n        dstFactor: 'one',\n        operation: 'min',\n    },\n    color: {\n        srcFactor: 'one',\n        dstFactor: 'one',\n        operation: 'min',\n    },\n};\n\nGpuBlendModesToPixi.max = {\n    alpha: {\n        srcFactor: 'one',\n        dstFactor: 'one',\n        operation: 'max',\n    },\n    color: {\n        srcFactor: 'one',\n        dstFactor: 'one',\n        operation: 'max',\n    },\n};\n\n// composite operations\n// GpuBlendModesToPixi[BLEND_MODES.SRC_IN] = {\n//     alpha: {\n//         srcFactor: 'src-alpha',\n//         dstFactor: 'one-minus-src-alpha',\n//         operation: 'add',\n//     },\n//     color: {\n//         srcFactor: 'dst-alpha',\n//         dstFactor: 'zero',\n//         operation: 'add',\n//     },\n// };\n\n// GpuBlendModesToPixi[BLEND_MODES.SRC_OUT] = {\n//     alpha: {\n//         srcFactor: 'src-alpha',\n//         dstFactor: 'one-minus-src-alpha',\n//         operation: 'add',\n//     },\n//     color: {\n//         srcFactor: 'one-minus-dst-alpha',\n//         dstFactor: 'zero',\n//         operation: 'add',\n//     },\n// };\n\n// GpuBlendModesToPixi[BLEND_MODES.SRC_ATOP] = {\n//     alpha: {\n//         srcFactor: 'src-alpha',\n//         dstFactor: 'one-minus-src-alpha',\n//         operation: 'add',\n//     },\n//     color: {\n//         srcFactor: 'dst-alpha',\n//         dstFactor: 'one-minus-src-alpha',\n//         operation: 'add',\n//     },\n// };\n\n// GpuBlendModesToPixi[BLEND_MODES.DST_OVER] = {\n//     alpha: {\n//         srcFactor: 'src-alpha',\n//         dstFactor: 'one-minus-src-alpha',\n//         operation: 'add',\n//     },\n//     color: {\n//         srcFactor: 'one-minus-dst-alpha',\n//         dstFactor: 'one',\n//         operation: 'add',\n//     },\n// };\n\n// GpuBlendModesToPixi[BLEND_MODES.DST_IN] = {\n//     alpha: {\n//         srcFactor: 'src-alpha',\n//         dstFactor: 'one-minus-src-alpha',\n//         operation: 'add',\n//     },\n//     color: {\n//         srcFactor: 'zero',\n//         dstFactor: 'src-alpha',\n//         operation: 'add',\n//     },\n// };\n\n// GpuBlendModesToPixi[BLEND_MODES.DST_OUT] = {\n//     alpha: {\n//         srcFactor: 'src-alpha',\n//         dstFactor: 'one-minus-src-alpha',\n//         operation: 'add',\n//     },\n//     color: {\n//         srcFactor: 'zero',\n//         dstFactor: 'one-minus-src-alpha',\n//         operation: 'add',\n//     },\n// };\n\n// GpuBlendModesToPixi[BLEND_MODES.DST_ATOP] = {\n//     alpha: {\n//         srcFactor: 'src-alpha',\n//         dstFactor: 'one-minus-src-alpha',\n//         operation: 'add',\n//     },\n//     color: {\n//         srcFactor: 'one-minus-dst-alpha',\n//         dstFactor: 'src-alpha',\n//         operation: 'add',\n//     },\n// };\n\n// GpuBlendModesToPixi[BLEND_MODES.XOR] = {\n//     alpha: {\n//         srcFactor: 'src-alpha',\n//         dstFactor: 'one-minus-src-alpha',\n//         operation: 'add',\n//     },\n//     color: {\n//         srcFactor: 'one-minus-dst-alpha',\n//         dstFactor: 'one-minus-src-alpha',\n//         operation: 'add',\n//     },\n// };\n\n// TODO - fix me\n// GLBlendModesToPixi[BLEND_MODES.SUBTRACT] = {\n//     alpha: {\n//         srcFactor: 'one',\n//         dstFactor: 'one-minus-src-alpha',\n//         operation: 'add',\n//     },\n//     color: {\n//         srcFactor: 'one-minus-dst-alpha',\n//         dstFactor: 'one-minus-src-alpha',\n//         operation: 'add',\n//     },\n// };\n", "import { ExtensionType } from '../../../../extensions/Extensions';\nimport { State } from '../../shared/state/State';\nimport { GpuBlendModesToPixi } from './GpuBlendModesToPixi';\n\nimport type { BLEND_MODES } from '../../shared/state/const';\nimport type { System } from '../../shared/system/System';\nimport type { GPU } from '../GpuDeviceSystem';\n\n/**\n * System plugin to the renderer to manage WebGL state machines.\n * @category rendering\n * @advanced\n */\nexport class GpuStateSystem implements System\n{\n    /** @ignore */\n    public static extension = {\n        type: [\n            ExtensionType.WebGPUSystem,\n        ],\n        name: 'state',\n    } as const;\n    /**\n     * State ID\n     * @readonly\n     */\n    public stateId: number;\n\n    /**\n     * Polygon offset\n     * @readonly\n     */\n    public polygonOffset: number;\n\n    /**\n     * Blend mode\n     * @default 'none'\n     * @readonly\n     */\n    public blendMode: BLEND_MODES;\n\n    /** Whether current blend equation is different */\n    protected _blendEq: boolean;\n\n    /**\n     * GL context\n     * @type {WebGLRenderingContext}\n     * @readonly\n     */\n    protected gpu: GPU;\n\n    /**\n     * Default WebGL State\n     * @readonly\n     */\n    protected defaultState: State;\n\n    constructor()\n    {\n        this.defaultState = new State();\n        this.defaultState.blend = true;\n    }\n\n    protected contextChange(gpu: GPU): void\n    {\n        this.gpu = gpu;\n    }\n\n    /**\n     * Gets the blend mode data for the current state\n     * @param state - The state to get the blend mode from\n     */\n    public getColorTargets(state: State): GPUColorTargetState[]\n    {\n        const blend = GpuBlendModesToPixi[state.blendMode] || GpuBlendModesToPixi.normal;\n\n        return [\n            {\n                format: 'bgra8unorm',\n                writeMask: 0,\n                blend,\n            },\n        ];\n    }\n\n    public destroy(): void\n    {\n        this.gpu = null;\n    }\n}\n", "import type { BufferImageSource } from '../../../shared/texture/sources/BufferImageSource';\nimport type { GPU } from '../../GpuDeviceSystem';\nimport type { GpuTextureUploader } from './GpuTextureUploader';\n\n/** @internal */\nexport const gpuUploadBufferImageResource = {\n\n    type: 'image',\n\n    upload(source: BufferImageSource, gpuTexture: GPUTexture, gpu: GPU)\n    {\n        const resource = source.resource;\n\n        const total = (source.pixelWidth | 0) * (source.pixelHeight | 0);\n\n        const bytesPerPixel = resource.byteLength / total;\n\n        gpu.device.queue.writeTexture(\n            { texture: gpuTexture },\n            resource,\n            {\n                offset: 0,\n                rowsPerImage: source.pixelHeight,\n                bytesPerRow: source.pixelHeight * bytesPerPixel,\n            },\n            {\n                width: source.pixelWidth,\n                height: source.pixelHeight,\n                depthOrArrayLayers: 1,\n            }\n        );\n    }\n} as GpuTextureUploader<BufferImageSource>;\n\n", "import type { CompressedSource } from '../../../shared/texture/sources/CompressedSource';\nimport type { GPU } from '../../GpuDeviceSystem';\nimport type { GpuTextureUploader } from './GpuTextureUploader';\n\n/** @internal */\nexport const blockDataMap: Record<string, {blockBytes: number, blockWidth: number, blockHeight: number}> = {\n    'bc1-rgba-unorm': { blockBytes: 8, blockWidth: 4, blockHeight: 4 },\n    'bc2-rgba-unorm': { blockBytes: 16, blockWidth: 4, blockHeight: 4 },\n    'bc3-rgba-unorm': { blockBytes: 16, blockWidth: 4, blockHeight: 4 },\n    'bc7-rgba-unorm': { blockBytes: 16, blockWidth: 4, blockHeight: 4 },\n    'etc1-rgb-unorm': { blockBytes: 8, blockWidth: 4, blockHeight: 4 },\n    'etc2-rgba8unorm': { blockBytes: 16, blockWidth: 4, blockHeight: 4 },\n    'astc-4x4-unorm': { blockBytes: 16, blockWidth: 4, blockHeight: 4 },\n};\n\nconst defaultBlockData = { blockBytes: 4, blockWidth: 1, blockHeight: 1 };\n\n/** @internal */\nexport const gpuUploadCompressedTextureResource = {\n\n    type: 'compressed',\n\n    upload(source: CompressedSource, gpuTexture: GPUTexture, gpu: GPU)\n    {\n        let mipWidth = source.pixelWidth;\n        let mipHeight = source.pixelHeight;\n\n        const blockData = blockDataMap[source.format] || defaultBlockData;\n\n        for (let i = 0; i < source.resource.length; i++)\n        {\n            const levelBuffer = source.resource[i];\n\n            const bytesPerRow = Math.ceil(mipWidth / blockData.blockWidth) * blockData.blockBytes;\n\n            gpu.device.queue.writeTexture(\n                {\n                    texture: gpuTexture,\n                    mipLevel: i\n                },\n                levelBuffer,\n                {\n                    offset: 0,\n                    bytesPerRow,\n                },\n                {\n                    width: Math.ceil(mipWidth / blockData.blockWidth) * blockData.blockWidth,\n                    height: Math.ceil(mipHeight / blockData.blockHeight) * blockData.blockHeight,\n                    depthOrArrayLayers: 1,\n                }\n            );\n\n            mipWidth = Math.max(mipWidth >> 1, 1);\n            mipHeight = Math.max(mipHeight >> 1, 1);\n        }\n    }\n} as GpuTextureUploader<CompressedSource>;\n\n", "import { DOMAdapter } from '../../../../../environment/adapter';\nimport { warn } from '../../../../../utils/logging/warn';\n\nimport type { TextureSource } from '../../../shared/texture/sources/TextureSource';\nimport type { GPU } from '../../GpuDeviceSystem';\nimport type { GpuTextureUploader } from './GpuTextureUploader';\n\n/** @internal */\nexport const gpuUploadImageResource = {\n\n    type: 'image',\n\n    upload(source: TextureSource, gpuTexture: GPUTexture, gpu: GPU)\n    {\n        const resource = source.resource as ImageBitmap | HTMLCanvasElement | OffscreenCanvas | HTMLImageElement;\n\n        if (!resource) return;\n\n        // WebGPU does not support HTMLImageElement\n        // so we need to convert it to a canvas\n        if (globalThis.HTMLImageElement && resource instanceof HTMLImageElement)\n        {\n            const canvas = DOMAdapter.get().createCanvas(resource.width, resource.height);\n            const context = canvas.getContext('2d');\n\n            context.drawImage(resource, 0, 0, resource.width, resource.height);\n\n            // replace with the canvas - for future uploads\n            source.resource = canvas;\n\n            // #if _DEBUG\n            warn('ImageSource: Image element passed, converting to canvas and replacing resource.');\n            // #endif\n        }\n\n        const width = Math.min(gpuTexture.width, source.resourceWidth || source.pixelWidth);\n        const height = Math.min(gpuTexture.height, source.resourceHeight || source.pixelHeight);\n\n        const premultipliedAlpha = source.alphaMode === 'premultiply-alpha-on-upload';\n\n        gpu.device.queue.copyExternalImageToTexture(\n            { source: resource },\n            { texture: gpuTexture, premultipliedAlpha },\n            {\n                width,\n                height,\n            }\n        );\n    }\n} as GpuTextureUploader<TextureSource>;\n\n", "import { gpuUploadImageResource } from './gpuUploadImageSource';\n\nimport type { VideoSource } from '../../../shared/texture/sources/VideoSource';\nimport type { GPU } from '../../GpuDeviceSystem';\nimport type { GpuTextureUploader } from './GpuTextureUploader';\n\n/** @internal */\nexport const gpuUploadVideoResource = {\n\n    type: 'video',\n\n    upload(source: VideoSource, gpuTexture: GPUTexture, gpu: GPU)\n    {\n        gpuUploadImageResource.upload(source, gpuTexture, gpu);\n    }\n} as GpuTextureUploader<VideoSource>;\n\n", "/**\n * A class which generates mipmaps for a GPUTexture.\n * Thanks to toji for the original implementation\n * https://github.com/toji/web-texture-tool/blob/main/src/webgpu-mipmap-generator.js\n * @category rendering\n * @ignore\n */\nexport class GpuMipmapGenerator\n{\n    public device: GPUDevice;\n    public sampler: GPUSampler;\n    public pipelines: Record<string, GPURenderPipeline>;\n\n    public mipmapShaderModule: any;\n\n    constructor(device: GPUDevice)\n    {\n        this.device = device;\n        this.sampler = device.createSampler({ minFilter: 'linear' });\n        // We'll need a new pipeline for every texture format used.\n        this.pipelines = {};\n    }\n\n    private _getMipmapPipeline(format: GPUTextureFormat)\n    {\n        let pipeline = this.pipelines[format];\n\n        if (!pipeline)\n        {\n            // Shader modules is shared between all pipelines, so only create once.\n            if (!this.mipmapShaderModule)\n            {\n                this.mipmapShaderModule = this.device.createShaderModule({\n                    code: /* wgsl */ `\n                        var<private> pos : array<vec2<f32>, 3> = array<vec2<f32>, 3>(\n                        vec2<f32>(-1.0, -1.0), vec2<f32>(-1.0, 3.0), vec2<f32>(3.0, -1.0));\n\n                        struct VertexOutput {\n                        @builtin(position) position : vec4<f32>,\n                        @location(0) texCoord : vec2<f32>,\n                        };\n\n                        @vertex\n                        fn vertexMain(@builtin(vertex_index) vertexIndex : u32) -> VertexOutput {\n                        var output : VertexOutput;\n                        output.texCoord = pos[vertexIndex] * vec2<f32>(0.5, -0.5) + vec2<f32>(0.5);\n                        output.position = vec4<f32>(pos[vertexIndex], 0.0, 1.0);\n                        return output;\n                        }\n\n                        @group(0) @binding(0) var imgSampler : sampler;\n                        @group(0) @binding(1) var img : texture_2d<f32>;\n\n                        @fragment\n                        fn fragmentMain(@location(0) texCoord : vec2<f32>) -> @location(0) vec4<f32> {\n                        return textureSample(img, imgSampler, texCoord);\n                        }\n                    `,\n                });\n            }\n\n            pipeline = this.device.createRenderPipeline({\n                layout: 'auto',\n                vertex: {\n                    module: this.mipmapShaderModule,\n                    entryPoint: 'vertexMain',\n                },\n                fragment: {\n                    module: this.mipmapShaderModule,\n                    entryPoint: 'fragmentMain',\n                    targets: [{ format }],\n                }\n            });\n\n            this.pipelines[format] = pipeline;\n        }\n\n        return pipeline;\n    }\n\n    /**\n     * Generates mipmaps for the given GPUTexture from the data in level 0.\n     * @param {module:External.GPUTexture} texture - Texture to generate mipmaps for.\n     * @returns {module:External.GPUTexture} - The originally passed texture\n     */\n    public generateMipmap(texture: GPUTexture)\n    {\n        const pipeline = this._getMipmapPipeline(texture.format);\n\n        if (texture.dimension === '3d' || texture.dimension === '1d')\n        {\n            throw new Error('Generating mipmaps for non-2d textures is currently unsupported!');\n        }\n\n        let mipTexture = texture;\n        const arrayLayerCount = texture.depthOrArrayLayers || 1; // Only valid for 2D textures.\n\n        // If the texture was created with RENDER_ATTACHMENT usage we can render directly between mip levels.\n        const renderToSource = texture.usage & GPUTextureUsage.RENDER_ATTACHMENT;\n\n        if (!renderToSource)\n        {\n            // Otherwise we have to use a separate texture to render into. It can be one mip level smaller than the source\n            // texture, since we already have the top level.\n            const mipTextureDescriptor = {\n                size: {\n                    width: Math.ceil(texture.width / 2),\n                    height: Math.ceil(texture.height / 2),\n                    depthOrArrayLayers: arrayLayerCount,\n                },\n                format: texture.format,\n                usage: GPUTextureUsage.TEXTURE_BINDING | GPUTextureUsage.COPY_SRC | GPUTextureUsage.RENDER_ATTACHMENT,\n                mipLevelCount: texture.mipLevelCount - 1,\n            };\n\n            mipTexture = this.device.createTexture(mipTextureDescriptor);\n        }\n\n        const commandEncoder = this.device.createCommandEncoder({});\n        // TODO: Consider making this static.\n        const bindGroupLayout = pipeline.getBindGroupLayout(0);\n\n        for (let arrayLayer = 0; arrayLayer < arrayLayerCount; ++arrayLayer)\n        {\n            let srcView = texture.createView({\n                baseMipLevel: 0,\n                mipLevelCount: 1,\n                dimension: '2d',\n                baseArrayLayer: arrayLayer,\n                arrayLayerCount: 1,\n            });\n\n            let dstMipLevel = renderToSource ? 1 : 0;\n\n            for (let i = 1; i < texture.mipLevelCount; ++i)\n            {\n                const dstView = mipTexture.createView({\n                    baseMipLevel: dstMipLevel++,\n                    mipLevelCount: 1,\n                    dimension: '2d',\n                    baseArrayLayer: arrayLayer,\n                    arrayLayerCount: 1,\n                });\n\n                const passEncoder = commandEncoder.beginRenderPass({\n                    colorAttachments: [{\n                        view: dstView,\n                        storeOp: 'store',\n                        loadOp: 'clear',\n                        clearValue: { r: 0, g: 0, b: 0, a: 0 },\n                    }],\n                });\n\n                const bindGroup = this.device.createBindGroup({\n                    layout: bindGroupLayout,\n                    entries: [{\n                        binding: 0,\n                        resource: this.sampler,\n                    }, {\n                        binding: 1,\n                        resource: srcView,\n                    }],\n                });\n\n                passEncoder.setPipeline(pipeline);\n                passEncoder.setBindGroup(0, bindGroup);\n                passEncoder.draw(3, 1, 0, 0);\n\n                passEncoder.end();\n\n                srcView = dstView;\n            }\n        }\n\n        // If we didn't render to the source texture, finish by copying the mip results from the temporary mipmap texture\n        // to the source.\n        if (!renderToSource)\n        {\n            const mipLevelSize = {\n                width: Math.ceil(texture.width / 2),\n                height: Math.ceil(texture.height / 2),\n                depthOrArrayLayers: arrayLayerCount,\n            };\n\n            for (let i = 1; i < texture.mipLevelCount; ++i)\n            {\n                commandEncoder.copyTextureToTexture({\n                    texture: mipTexture,\n                    mipLevel: i - 1,\n                }, {\n                    texture,\n                    mipLevel: i,\n                }, mipLevelSize);\n\n                mipLevelSize.width = Math.ceil(mipLevelSize.width / 2);\n                mipLevelSize.height = Math.ceil(mipLevelSize.height / 2);\n            }\n        }\n\n        this.device.queue.submit([commandEncoder.finish()]);\n\n        if (!renderToSource)\n        {\n            mipTexture.destroy();\n        }\n\n        return texture;\n    }\n}\n", "import { DOMAdapter } from '../../../../environment/adapter';\nimport { ExtensionType } from '../../../../extensions/Extensions';\nimport { UniformGroup } from '../../shared/shader/UniformGroup';\nimport { CanvasPool } from '../../shared/texture/CanvasPool';\nimport { BindGroup } from '../shader/BindGroup';\nimport { gpuUploadBufferImageResource } from './uploaders/gpuUploadBufferImageResource';\nimport { blockDataMap, gpuUploadCompressedTextureResource } from './uploaders/gpuUploadCompressedTextureResource';\nimport { gpuUploadImageResource } from './uploaders/gpuUploadImageSource';\nimport { gpuUploadVideoResource } from './uploaders/gpuUploadVideoSource';\nimport { GpuMipmapGenerator } from './utils/GpuMipmapGenerator';\n\nimport type { ICanvas } from '../../../../environment/canvas/ICanvas';\nimport type { System } from '../../shared/system/System';\nimport type { CanvasGenerator, GetPixelsOutput } from '../../shared/texture/GenerateCanvas';\nimport type { TextureSource } from '../../shared/texture/sources/TextureSource';\nimport type { BindableTexture, Texture } from '../../shared/texture/Texture';\nimport type { TextureStyle } from '../../shared/texture/TextureStyle';\nimport type { GPU } from '../GpuDeviceSystem';\nimport type { WebGPURenderer } from '../WebGPURenderer';\nimport type { GpuTextureUploader } from './uploaders/GpuTextureUploader';\n\n/**\n * The system that handles textures for the GPU.\n * @category rendering\n * @advanced\n */\nexport class GpuTextureSystem implements System, CanvasGenerator\n{\n    /** @ignore */\n    public static extension = {\n        type: [\n            ExtensionType.WebGPUSystem,\n        ],\n        name: 'texture',\n    } as const;\n\n    public readonly managedTextures: TextureSource[] = [];\n\n    protected CONTEXT_UID: number;\n    private _gpuSources: Record<number, GPUTexture> = Object.create(null);\n    private _gpuSamplers: Record<string, GPUSampler> = Object.create(null);\n    private _bindGroupHash: Record<string, BindGroup> = Object.create(null);\n    private _textureViewHash: Record<string, GPUTextureView> = Object.create(null);\n\n    private readonly _uploads: Record<string, GpuTextureUploader> = {\n        image: gpuUploadImageResource,\n        buffer: gpuUploadBufferImageResource,\n        video: gpuUploadVideoResource,\n        compressed: gpuUploadCompressedTextureResource\n    };\n\n    private _gpu: GPU;\n    private _mipmapGenerator?: GpuMipmapGenerator;\n\n    private readonly _renderer: WebGPURenderer;\n\n    constructor(renderer: WebGPURenderer)\n    {\n        this._renderer = renderer;\n        renderer.renderableGC.addManagedHash(this, '_gpuSources');\n        renderer.renderableGC.addManagedHash(this, '_gpuSamplers');\n        renderer.renderableGC.addManagedHash(this, '_bindGroupHash');\n        renderer.renderableGC.addManagedHash(this, '_textureViewHash');\n    }\n\n    protected contextChange(gpu: GPU): void\n    {\n        this._gpu = gpu;\n    }\n\n    public initSource(source: TextureSource): GPUTexture\n    {\n        if (source.autoGenerateMipmaps)\n        {\n            const biggestDimension = Math.max(source.pixelWidth, source.pixelHeight);\n\n            source.mipLevelCount = Math.floor(Math.log2(biggestDimension)) + 1;\n        }\n\n        let usage = GPUTextureUsage.TEXTURE_BINDING | GPUTextureUsage.COPY_DST;\n\n        if (source.uploadMethodId !== 'compressed')\n        {\n            usage |= GPUTextureUsage.RENDER_ATTACHMENT;\n            usage |= GPUTextureUsage.COPY_SRC;\n        }\n\n        const blockData = blockDataMap[source.format] || { blockBytes: 4, blockWidth: 1, blockHeight: 1 };\n\n        const width = Math.ceil(source.pixelWidth / blockData.blockWidth) * blockData.blockWidth;\n        const height = Math.ceil(source.pixelHeight / blockData.blockHeight) * blockData.blockHeight;\n\n        const textureDescriptor: GPUTextureDescriptor = {\n            label: source.label,\n            size: { width, height },\n            format: source.format,\n            sampleCount: source.sampleCount,\n            mipLevelCount: source.mipLevelCount,\n            dimension: source.dimension,\n            usage\n        };\n\n        const gpuTexture = this._gpu.device.createTexture(textureDescriptor);\n\n        this._gpuSources[source.uid] = gpuTexture;\n\n        if (!this.managedTextures.includes(source))\n        {\n            source.on('update', this.onSourceUpdate, this);\n            source.on('resize', this.onSourceResize, this);\n            source.on('destroy', this.onSourceDestroy, this);\n            source.on('unload', this.onSourceUnload, this);\n            source.on('updateMipmaps', this.onUpdateMipmaps, this);\n\n            this.managedTextures.push(source);\n        }\n\n        this.onSourceUpdate(source);\n\n        return gpuTexture;\n    }\n\n    protected onSourceUpdate(source: TextureSource): void\n    {\n        const gpuTexture = this.getGpuSource(source);\n\n        // destroyed!\n        if (!gpuTexture) return;\n\n        if (this._uploads[source.uploadMethodId])\n        {\n            this._uploads[source.uploadMethodId].upload(source, gpuTexture, this._gpu);\n        }\n\n        if (source.autoGenerateMipmaps && source.mipLevelCount > 1)\n        {\n            this.onUpdateMipmaps(source);\n        }\n    }\n\n    protected onSourceUnload(source: TextureSource): void\n    {\n        const gpuTexture = this._gpuSources[source.uid];\n\n        if (gpuTexture)\n        {\n            this._gpuSources[source.uid] = null;\n\n            gpuTexture.destroy();\n        }\n    }\n\n    protected onUpdateMipmaps(source: TextureSource): void\n    {\n        if (!this._mipmapGenerator)\n        {\n            this._mipmapGenerator = new GpuMipmapGenerator(this._gpu.device);\n        }\n\n        const gpuTexture = this.getGpuSource(source);\n\n        this._mipmapGenerator.generateMipmap(gpuTexture);\n    }\n\n    protected onSourceDestroy(source: TextureSource): void\n    {\n        source.off('update', this.onSourceUpdate, this);\n        source.off('unload', this.onSourceUnload, this);\n        source.off('destroy', this.onSourceDestroy, this);\n        source.off('resize', this.onSourceResize, this);\n        source.off('updateMipmaps', this.onUpdateMipmaps, this);\n\n        this.managedTextures.splice(this.managedTextures.indexOf(source), 1);\n\n        this.onSourceUnload(source);\n    }\n\n    protected onSourceResize(source: TextureSource): void\n    {\n        const gpuTexture = this._gpuSources[source.uid];\n\n        if (!gpuTexture)\n        {\n            this.initSource(source);\n        }\n        else if (gpuTexture.width !== source.pixelWidth || gpuTexture.height !== source.pixelHeight)\n        {\n            this._textureViewHash[source.uid] = null;\n            this._bindGroupHash[source.uid] = null;\n\n            this.onSourceUnload(source);\n            this.initSource(source);\n        }\n    }\n\n    private _initSampler(sampler: TextureStyle): GPUSampler\n    {\n        this._gpuSamplers[sampler._resourceId] = this._gpu.device.createSampler(sampler);\n\n        return this._gpuSamplers[sampler._resourceId];\n    }\n\n    public getGpuSampler(sampler: TextureStyle): GPUSampler\n    {\n        return this._gpuSamplers[sampler._resourceId] || this._initSampler(sampler);\n    }\n\n    public getGpuSource(source: TextureSource): GPUTexture\n    {\n        return this._gpuSources[source.uid] || this.initSource(source);\n    }\n\n    /**\n     * this returns s bind group for a specific texture, the bind group contains\n     * - the texture source\n     * - the texture style\n     * - the texture matrix\n     * This is cached so the bind group should only be created once per texture\n     * @param texture - the texture you want the bindgroup for\n     * @returns the bind group for the texture\n     */\n    public getTextureBindGroup(texture: Texture)\n    {\n        return this._bindGroupHash[texture.uid] ?? this._createTextureBindGroup(texture);\n    }\n\n    private _createTextureBindGroup(texture: Texture)\n    {\n        const source = texture.source;\n\n        this._bindGroupHash[texture.uid] = new BindGroup({\n            0: source,\n            1: source.style,\n            2: new UniformGroup({\n                uTextureMatrix: { type: 'mat3x3<f32>', value: texture.textureMatrix.mapCoord },\n            })\n        });\n\n        return this._bindGroupHash[texture.uid];\n    }\n\n    public getTextureView(texture: BindableTexture)\n    {\n        const source = texture.source;\n\n        return this._textureViewHash[source.uid] ?? this._createTextureView(source);\n    }\n\n    private _createTextureView(texture: TextureSource)\n    {\n        this._textureViewHash[texture.uid] = this.getGpuSource(texture).createView();\n\n        return this._textureViewHash[texture.uid];\n    }\n\n    public generateCanvas(texture: Texture): ICanvas\n    {\n        const renderer = this._renderer;\n\n        const commandEncoder = renderer.gpu.device.createCommandEncoder();\n\n        // create canvas\n        const canvas = DOMAdapter.get().createCanvas();\n\n        canvas.width = texture.source.pixelWidth;\n        canvas.height = texture.source.pixelHeight;\n\n        const context = canvas.getContext('webgpu') as unknown as GPUCanvasContext;\n\n        context.configure({\n            device: renderer.gpu.device,\n\n            usage: GPUTextureUsage.COPY_DST | GPUTextureUsage.COPY_SRC,\n            format: DOMAdapter.get().getNavigator().gpu.getPreferredCanvasFormat(),\n            alphaMode: 'premultiplied',\n        });\n\n        commandEncoder.copyTextureToTexture({\n            texture: renderer.texture.getGpuSource(texture.source),\n            origin: {\n                x: 0,\n                y: 0,\n            },\n        }, {\n            texture: context.getCurrentTexture(),\n        }, {\n            width: canvas.width,\n            height: canvas.height,\n        });\n\n        renderer.gpu.device.queue.submit([commandEncoder.finish()]);\n\n        return canvas;\n    }\n\n    public getPixels(texture: Texture): GetPixelsOutput\n    {\n        const webGPUCanvas = this.generateCanvas(texture);\n\n        const canvasAndContext = CanvasPool.getOptimalCanvasAndContext(webGPUCanvas.width, webGPUCanvas.height);\n\n        const context = canvasAndContext.context;\n\n        context.drawImage(webGPUCanvas, 0, 0);\n\n        const { width, height } = webGPUCanvas;\n\n        const imageData = context.getImageData(0, 0, width, height);\n\n        const pixels = new Uint8ClampedArray(imageData.data.buffer);\n\n        CanvasPool.returnCanvasAndContext(canvasAndContext);\n\n        return { pixels, width, height };\n    }\n\n    public destroy(): void\n    {\n        // we copy the array as the aarry with a slice as onSourceDestroy\n        // will remove the source from the real managedTextures array\n        this.managedTextures\n            .slice()\n            .forEach((source) => this.onSourceDestroy(source));\n\n        (this.managedTextures as null) = null;\n\n        for (const k of Object.keys(this._bindGroupHash))\n        {\n            const key = Number(k);\n            const bindGroup = this._bindGroupHash[key];\n\n            bindGroup?.destroy();\n            this._bindGroupHash[key] = null;\n        }\n\n        this._gpu = null;\n        this._mipmapGenerator = null;\n        this._gpuSources = null;\n        this._bindGroupHash = null;\n        this._textureViewHash = null;\n        this._gpuSamplers = null;\n    }\n}\n", "import { extensions, ExtensionType } from '../../../extensions/Extensions';\nimport { GpuGraphicsAdaptor } from '../../../scene/graphics/gpu/GpuGraphicsAdaptor';\nimport { GpuMeshAdapter } from '../../../scene/mesh/gpu/GpuMeshAdapter';\nimport { GpuBatchAdaptor } from '../../batcher/gpu/GpuBatchAdaptor';\nimport { AbstractRenderer } from '../shared/system/AbstractRenderer';\nimport { SharedRenderPipes, SharedSystems } from '../shared/system/SharedSystems';\nimport { RendererType } from '../types';\nimport { BindGroupSystem } from './BindGroupSystem';\nimport { GpuBufferSystem } from './buffer/GpuBufferSystem';\nimport { GpuColorMaskSystem } from './GpuColorMaskSystem';\nimport { type GPU, GpuDeviceSystem } from './GpuDeviceSystem';\nimport { GpuEncoderSystem } from './GpuEncoderSystem';\nimport { GpuLimitsSystem } from './GpuLimitsSystem';\nimport { GpuStencilSystem } from './GpuStencilSystem';\nimport { GpuUboSystem } from './GpuUboSystem';\nimport { GpuUniformBatchPipe } from './GpuUniformBatchPipe';\nimport { PipelineSystem } from './pipeline/PipelineSystem';\nimport { GpuRenderTargetSystem } from './renderTarget/GpuRenderTargetSystem';\nimport { GpuShaderSystem } from './shader/GpuShaderSystem';\nimport { GpuStateSystem } from './state/GpuStateSystem';\nimport { GpuTextureSystem } from './texture/GpuTextureSystem';\n\nimport type { ICanvas } from '../../../environment/canvas/ICanvas';\nimport type { PipeConstructor } from '../shared/instructions/RenderPipe';\nimport type { SharedRendererOptions } from '../shared/system/SharedSystems';\nimport type { SystemConstructor } from '../shared/system/System';\nimport type { ExtractRendererOptions, ExtractSystemTypes } from '../shared/system/utils/typeUtils';\n\nconst DefaultWebGPUSystems = [\n    ...SharedSystems,\n    GpuUboSystem,\n    GpuEncoderSystem,\n    GpuDeviceSystem,\n    GpuLimitsSystem,\n    GpuBufferSystem,\n    GpuTextureSystem,\n    GpuRenderTargetSystem,\n    GpuShaderSystem,\n    GpuStateSystem,\n    PipelineSystem,\n    GpuColorMaskSystem,\n    GpuStencilSystem,\n    BindGroupSystem,\n];\nconst DefaultWebGPUPipes = [...SharedRenderPipes, GpuUniformBatchPipe];\nconst DefaultWebGPUAdapters = [GpuBatchAdaptor, GpuMeshAdapter, GpuGraphicsAdaptor];\n\n// installed systems will bbe added to this array by the extensions manager..\nconst systems: { name: string; value: SystemConstructor }[] = [];\nconst renderPipes: { name: string; value: PipeConstructor }[] = [];\nconst renderPipeAdaptors: { name: string; value: any }[] = [];\n\nextensions.handleByNamedList(ExtensionType.WebGPUSystem, systems);\nextensions.handleByNamedList(ExtensionType.WebGPUPipes, renderPipes);\nextensions.handleByNamedList(ExtensionType.WebGPUPipesAdaptor, renderPipeAdaptors);\n\n// add all the default systems as well as any user defined ones from the extensions\nextensions.add(...DefaultWebGPUSystems, ...DefaultWebGPUPipes, ...DefaultWebGPUAdapters);\n\n/**\n * The default WebGPU systems. These are the systems that are added by default to the WebGPURenderer.\n * @category rendering\n * @standard\n * @interface\n */\nexport type WebGPUSystems = ExtractSystemTypes<typeof DefaultWebGPUSystems> &\nPixiMixins.RendererSystems &\nPixiMixins.WebGPUSystems;\n\n/**\n * The WebGPU renderer pipes. These are used to render the scene.\n * @see {@link WebGPURenderer}\n * @internal\n */\nexport type WebGPUPipes = ExtractSystemTypes<typeof DefaultWebGPUPipes> &\nPixiMixins.RendererPipes &\nPixiMixins.WebGPUPipes;\n\n/**\n * Options for WebGPURenderer.\n * @category rendering\n * @standard\n */\nexport interface WebGPUOptions extends\n    SharedRendererOptions,\n    ExtractRendererOptions<typeof DefaultWebGPUSystems>,\n    PixiMixins.WebGPUOptions{}\n\n// eslint-disable-next-line requireExport/require-export-jsdoc, requireMemberAPI/require-member-api-doc\nexport interface WebGPURenderer<T extends ICanvas = HTMLCanvasElement>\n    extends AbstractRenderer<WebGPUPipes, WebGPUOptions, T>,\n    WebGPUSystems {}\n\n/* eslint-disable max-len */\n/**\n * The WebGPU PixiJS Renderer. This renderer allows you to use the next-generation graphics API, WebGPU.\n * ```ts\n * // Create a new renderer\n * const renderer = new WebGPURenderer();\n * await renderer.init();\n *\n * // Add the renderer to the stage\n * document.body.appendChild(renderer.canvas);\n *\n * // Create a new stage\n * const stage = new Container();\n *\n * // Render the stage\n * renderer.render(stage);\n * ```\n *\n * You can use {@link autoDetectRenderer} to create a renderer that will automatically detect the best\n * renderer for the environment.\n * ```ts\n * import { autoDetectRenderer } from 'pixi.js';\n * // Create a new renderer\n * const renderer = await autoDetectRenderer();\n * ```\n *\n * The renderer is composed of systems that manage specific tasks. The following systems are added by default\n * whenever you create a WebGPU renderer:\n *\n * | WebGPU Core Systems                      | Systems that are specific to the WebGL renderer                               |\n * | ---------------------------------------- | ----------------------------------------------------------------------------- |\n * | {@link GpuUboSystem}           | This manages WebGPU uniform buffer objects feature for shaders                |\n * | {@link GpuEncoderSystem}       | This manages the WebGPU command encoder                                       |\n * | {@link GpuDeviceSystem}        | This manages the WebGPU Device and its extensions                             |\n * | {@link GpuBufferSystem}        | This manages buffers and their GPU resources, keeps everything in sync        |\n * | {@link GpuTextureSystem}       | This manages textures and their GPU resources, keeps everything in sync       |\n * | {@link GpuRenderTargetSystem}  | This manages what we render too. For example the screen, or another texture   |\n * | {@link GpuShaderSystem}        | This manages shaders, programs that run on the GPU to output lovely pixels    |\n * | {@link GpuStateSystem}         | This manages the state of the WebGPU Pipelines. eg the various flags that can be set blend modes / depthTesting etc |\n * | {@link PipelineSystem}         | This manages the WebGPU pipelines, used for rendering                         |\n * | {@link GpuColorMaskSystem}     | This manages the color mask. Used for color masking                           |\n * | {@link GpuStencilSystem}       | This manages the stencil buffer. Used primarily for masking                   |\n * | {@link BindGroupSystem}        | This manages the WebGPU bind groups. this is how data is bound to a shader when rendering |\n *\n * The breadth of the API surface provided by the renderer is contained within these systems.\n * @category rendering\n * @standard\n * @property {GpuUboSystem} ubo - UboSystem instance.\n * @property {GpuEncoderSystem} encoder - EncoderSystem instance.\n * @property {GpuDeviceSystem} device - DeviceSystem instance.\n * @property {GpuBufferSystem} buffer - BufferSystem instance.\n * @property {GpuTextureSystem} texture - TextureSystem instance.\n * @property {GpuRenderTargetSystem} renderTarget - RenderTargetSystem instance.\n * @property {GpuShaderSystem} shader - ShaderSystem instance.\n * @property {GpuStateSystem} state - StateSystem instance.\n * @property {PipelineSystem} pipeline - PipelineSystem instance.\n * @property {GpuColorMaskSystem} colorMask - ColorMaskSystem instance.\n * @property {GpuStencilSystem} stencil - StencilSystem instance.\n * @property {BindGroupSystem} bindGroup - BindGroupSystem instance.\n * @extends AbstractRenderer\n */\nexport class WebGPURenderer<T extends ICanvas = HTMLCanvasElement>\n    extends AbstractRenderer<WebGPUPipes, WebGPUOptions, T>\n    implements WebGPUSystems\n{\n    /** The WebGPU Device. */\n    public gpu: GPU;\n\n    constructor()\n    {\n        const systemConfig = {\n            name: 'webgpu',\n            type: RendererType.WEBGPU,\n            systems,\n            renderPipes,\n            renderPipeAdaptors,\n        };\n\n        super(systemConfig);\n    }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwBO,IAAM,qBAAN,MACP;EADO,cAAA;AAYH,SAAQ,eAAe;EAAA;EAEhB,cAAc,UACrB;AACU,UAAA,gBAAgB,IAAI,aAAa;MACnC,kBAAkB,EAAE,OAAO,IAAI,OAAO,GAAG,MAAM,cAAc;MAC7D,QAAQ,EAAE,OAAO,IAAI,aAAa,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,GAAG,MAAM,YAAY;MACnE,QAAQ,EAAE,OAAO,GAAG,MAAM,MAAM;IAAA,CACnC;AAEI,SAAA,eAAe,SAAS,OAAO;AAEpC,UAAM,aAAa,4BAA4B;MAC3C,MAAM;MACN,MAAM;QACF;QACA,wBAAwB,KAAK,YAAY;QAEzC;QACA;MAAA;IACJ,CACH;AAEI,SAAA,SAAS,IAAI,OAAO;MACrB;MACA,WAAW;;QAEP;MAAA;IACJ,CACH;EAAA;EAGE,QAAQ,cAA4B,YAC3C;AACI,UAAM,UAAU,WAAW;AACrB,UAAA,SAAS,QAAQ,gBAAgB,KAAK;AAC5C,UAAM,WAAW,aAAa;AAC9B,UAAM,gBAAgB,SAAS;AAEzB,UAAA;MACF;MAAS;IAAA,IACT,cAAc,qBAAqB,OAAO;AAK9C,UAAM,UAAU,SAAS;AAEzB,YAAQ,YAAY,QAAQ,UAAU,OAAO,UAAU;AAEjD,UAAA,0BAA0B,SAAS,eAAe;AAExD,YAAQ,aAAa,GAAG,yBAAyB,OAAO,UAAU;AAE5D,UAAA,iBAAkB,SACnB,YAAY,aAAa,oBAAoB,OAAO,UAAU,eAAe,IAAI;AAEtF,YAAQ,aAAa,GAAG,gBAAgB,OAAO,UAAU;AAEzD,UAAM,UAAU,aAAa;AAE7B,QAAI,WAAqB;AAEzB,aAAS,IAAI,GAAG,IAAI,aAAa,iBAAiB,KAClD;AACU,YAAA,QAAQ,QAAQ,CAAC;AAEnB,UAAA,MAAM,aAAa,UACvB;AACI,mBAAW,MAAM;AAET,gBAAA;UACJ,QAAQ;UACR,OAAO;UACP,aAAa;UACb,MAAM;QAAA;MACV;AAGG,aAAA,OAAO,CAAC,IAAI,MAAM;AAErB,UAAA,CAAC,MAAM,cACX;AACI,cAAM,eAAe,MAAM;AAE3B,cAAM,YAAY;UACd,aAAa;UACb,aAAa;UACb,KAAK;QAAA;AAGH,cAAA,eAAe,SAAS,UAAU;UACpC,MAAM;UAAW,OAAO;UAAY;QAAA;MACxC;AAGJ,cAAQ,aAAa,GAAG,MAAM,WAAW,OAAO,UAAU;AAE1D,cAAQ,kBAAkB,YAAY,MAAM,MAAM,GAAG,MAAM,KAAK;IAAA;EACpE;EAGG,UACP;AACS,SAAA,OAAO,QAAQ,IAAI;AACxB,SAAK,SAAS;EAAA;AAEtB;AAvHa,mBAGK,YAAY;EACtB,MAAM;IACF,cAAc;EAAA;EAElB,MAAM;AACV;;;ACbG,IAAM,iBAAN,MACP;EAWW,OACP;AACI,UAAM,aAAa,4BAA4B;MAC3C,MAAM;MACN,MAAM;QACF;QACA;QACA;MAAA;IACJ,CACH;AAEI,SAAA,UAAU,IAAI,OAAO;MACtB;MACA,WAAW;QACP,UAAU,QAAQ,MAAM;QACxB,UAAU,QAAQ,MAAM,QAAQ;QAChC,iBAAiB;UACb,gBAAgB,EAAE,MAAM,eAAe,OAAO,IAAI,OAAA,EAAS;QAAA;MAC/D;IACJ,CACH;EAAA;EAGE,QAAQ,UAAoB,MACnC;AACI,UAAM,WAAW,SAAS;AAE1B,QAAI,SAAiB,KAAK;AAE1B,QAAI,CAAC,QACL;AACI,eAAS,KAAK;AAEd,aAAO,OAAO,CAAC,IAAI,SAAS,QAAQ,oBAAoB,KAAK,OAAO;IAAA,WAE/D,CAAC,OAAO,YACjB;AAES,WAAA,iCAAiC,KAAK,MAAM;AAGjD;IAAA;AAGJ,UAAM,aAAa,OAAO;AAG1B,QAAI,WAAW,0BACf;AACI,aAAO,OAAO,CAAC,IAAI,SAAS,eAAe;IAAA;AAG/C,QAAI,WAAW,yBACf;AACI,YAAM,gBAAgB,SAAS;AAExB,aAAA,OAAO,CAAC,IAAK,SACf,YAAY,aAAa,oBAAoB,eAAe,IAAI;IAAA;AAGzE,aAAS,QAAQ,KAAK;MAClB,UAAU,KAAK;MACf;MACA,OAAO,KAAK;IAAA,CACf;EAAA;EAGE,UACP;AACS,SAAA,QAAQ,QAAQ,IAAI;AACzB,SAAK,UAAU;EAAA;AAEvB;AApFa,eAGK,YAAY;EACtB,MAAM;IACF,cAAc;EAAA;EAElB,MAAM;AACV;;;AChBJ,IAAM,YAAY,MAAM,MAAM;AAOvB,IAAM,kBAAN,MACP;EAYW,MAAM,WAAwB,UAAoB,QACzD;AACI,UAAM,WAAW,UAAU;AAC3B,UAAM,UAAU,SAAS;AACzB,UAAM,UAAU,OAAO;AAEvB,SAAK,UAAU;AACf,SAAK,YAAY;AAET,YAAA,YAAY,UAAU,OAAO;AAErC,cAAU,YAAY;AAGtB,aAAS,SAAS;MACd;MACA;MACA;IAAA;AAGE,UAAA,0BAA0B,SAAS,eAAe;AAOxD,YAAQ,eAAe,CAAC;AAEhB,YAAA,aAAa,GAAG,yBAAyB,OAAO;EAAA;EAGrD,QAAQ,WAAwB,OACvC;AACU,UAAA,UAAU,KAAK,QAAQ;AAC7B,UAAM,WAAW,UAAU;AAC3B,UAAM,UAAU,SAAS;AAErB,QAAA,CAAC,MAAM,WACX;AACI,YAAM,eAAe,MAAM;AAE3B,YAAM,YAAY;QACd,aAAa;QACb,aAAa;QACb,SAAS,OAAO;MAAA;IACpB;AAGJ,cAAU,YAAY,MAAM;AAEtB,UAAA,eAAe,SAAS,UAAU;MACpC,MAAM;MAAW;MAAS;IAAA;AAGxB,UAAA,WAAW,SAAS,SAAS;MAC/B,KAAK;MACL;MACA;MACA,MAAM;IAAA;AAGV,UAAM,UAAU,OAAO,SAAS,UAAU,KAAK;AAE/C,YAAQ,YAAY,QAAQ;AAEpB,YAAA,kBAAkB,aAAa,GAAG,YAAY;AACtD,YAAQ,kBAAkB,YAAY,MAAM,MAAM,GAAG,MAAM,KAAK;EAAA;AAExE;AAlFa,gBAGK,YAAY;EACtB,MAAM;IACF,cAAc;EAAA;EAElB,MAAM;AACV;;;ACPG,IAAM,kBAAN,MACP;EAcI,YAAY,UACZ;AAJQ,SAAA,QAA6C,uBAAA,OAAO,IAAI;AAK5D,SAAK,YAAY;AACjB,SAAK,UAAU,aAAa,eAAe,MAAM,OAAO;EAAA;EAGlD,cAAc,KACxB;AACI,SAAK,OAAO;EAAA;EAGT,aAAa,WAAsB,SAAqB,YAC/D;AACI,cAAU,WAAW;AAEf,UAAA,eAAe,KAAK,MAAM,UAAU,IAAI,KAAK,KAAK,iBAAiB,WAAW,SAAS,UAAU;AAEhG,WAAA;EAAA;EAGH,iBAAiB,OAAkB,SAAqB,YAChE;AACU,UAAA,SAAS,KAAK,KAAK;AACnB,UAAA,cAAc,QAAQ,OAAO,UAAU;AAC7C,UAAM,UAA+B,CAAA;AACrC,UAAM,WAAW,KAAK;AAEtB,eAAW,KAAK,aAChB;AACU,YAAA,WAAyB,MAAM,UAAU,CAAC,KAAK,MAAM,UAAU,YAAY,CAAC,CAAC;AAC/E,UAAA;AAGA,UAAA,SAAS,kBAAkB,gBAC/B;AACI,cAAM,eAAe;AAEZ,iBAAA,IAAI,mBAAmB,YAA4B;AAE5D,cAAM,SAAS,aAAa;AAEd,sBAAA;UACV,QAAQ,SAAS,OAAO,aAAa,MAAM;UAC3C,QAAQ;UACR,MAAM,OAAO,WAAW;QAAA;MAC5B,WAEK,SAAS,kBAAkB,UACpC;AACI,cAAM,SAAS;AAED,sBAAA;UACV,QAAQ,SAAS,OAAO,aAAa,MAAM;UAC3C,QAAQ;UACR,MAAM,OAAO,WAAW;QAAA;MAC5B,WAEK,SAAS,kBAAkB,kBACpC;AACI,cAAM,iBAAiB;AAET,sBAAA;UACV,QAAQ,SAAS,OAAO,aAAa,eAAe,MAAM;UAC1D,QAAQ,eAAe;UACvB,MAAM,eAAe;QAAA;MACzB,WAEK,SAAS,kBAAkB,kBACpC;AACI,cAAM,UAAU;AAEF,sBAAA,SAAS,QAAQ,cAAc,OAAO;MAAA,WAE/C,SAAS,kBAAkB,iBACpC;AACI,cAAM,UAAU;AAEhB,sBAAc,SAAS,QAAQ,aAAa,OAAO,EAAE,WAAW,CAAA,CAE/D;MAAA;AAGL,cAAQ,KAAK;QACT,SAAS,YAAY,CAAC;QACtB,UAAU;MAAA,CACb;IAAA;AAGL,UAAM,SAAS,SAAS,OAAO,eAAe,OAAO,EAAE,WAAW,UAAU;AAEtE,UAAA,eAAe,OAAO,gBAAgB;MACxC;MACA;IAAA,CACH;AAEI,SAAA,MAAM,MAAM,IAAI,IAAI;AAElB,WAAA;EAAA;EAGJ,UACP;AACI,eAAW,OAAO,OAAO,KAAK,KAAK,KAAK,GACxC;AACS,WAAA,MAAM,GAAG,IAAI;IAAA;AAGtB,SAAK,QAAQ;AAEZ,SAAK,YAAqB;EAAA;AAEnC;AA9Ha,gBAGK,YAAY;EACtB,MAAM;IACF,cAAc;EAAA;EAElB,MAAM;AACV;;;ACdG,IAAM,kBAAN,MACP;EAeI,YAAY,UACZ;AANQ,SAAA,cAAmD,uBAAA,OAAO,IAAI;AACtE,SAAiB,kBAA4B,CAAA;AAMhC,aAAA,aAAa,eAAe,MAAM,aAAa;EAAA;EAGlD,cAAc,KACxB;AACI,SAAK,OAAO;EAAA;EAGT,aAAa,QACpB;AACI,WAAO,KAAK,YAAY,OAAO,GAAG,KAAK,KAAK,gBAAgB,MAAM;EAAA;EAG/D,aAAa,QACpB;AACU,UAAA,YAAY,KAAK,YAAY,OAAO,GAAG,KAAK,KAAK,gBAAgB,MAAM;AAE7E,UAAM,OAAO,OAAO;AAGhB,QAAA,OAAO,aAAa,MACxB;AACI,aAAO,YAAY;AAGd,WAAA,KAAK,OAAO,MAAM;QACnB;QAAW;QAAG,KAAK;QAAQ;;SAEzB,OAAO,eAAe,KAAK,cAAc,IAAK,CAAC;MAAA;IACrD;AAGG,WAAA;EAAA;;EAIJ,aACP;AACe,eAAA,MAAM,KAAK,aACtB;AACS,WAAA,YAAY,EAAE,EAAE,QAAQ;IAAA;AAGjC,SAAK,cAAc,CAAA;EAAC;EAGjB,gBAAgB,QACvB;AACI,QAAI,CAAC,KAAK,YAAY,OAAO,GAAG,GAChC;AACI,aAAO,GAAG,UAAU,KAAK,cAAc,IAAI;AAC3C,aAAO,GAAG,UAAU,KAAK,gBAAgB,IAAI;AAC7C,aAAO,GAAG,WAAW,KAAK,iBAAiB,IAAI;AAE1C,WAAA,gBAAgB,KAAK,MAAM;IAAA;AAGpC,UAAM,YAAY,KAAK,KAAK,OAAO,aAAa,OAAO,UAAU;AAEjE,WAAO,YAAY;AAEnB,QAAI,OAAO,MACX;AAEI,eAAS,OAAO,KAAK,QAAQ,UAAU,eAAA,CAAgB;AAEvD,gBAAU,MAAM;IAAA;AAGf,SAAA,YAAY,OAAO,GAAG,IAAI;AAExB,WAAA;EAAA;EAGD,eAAe,QACzB;AACI,UAAM,YAAY,KAAK,YAAY,OAAO,GAAG;AAE7C,cAAU,QAAQ;AAClB,WAAO,YAAY;AACnB,SAAK,YAAY,OAAO,GAAG,IAAI,KAAK,gBAAgB,MAAM;EAAA;;;;;EAOpD,gBAAgB,QAC1B;AACI,SAAK,gBAAgB,OAAO,KAAK,gBAAgB,QAAQ,MAAM,GAAG,CAAC;AAEnE,SAAK,eAAe,MAAM;EAAA;EAGvB,UACP;AACI,SAAK,gBAAgB,QAAQ,CAAC,WAAW,KAAK,eAAe,MAAM,CAAC;AAEnE,SAAK,kBAA2B;AAEjC,SAAK,cAAc;EAAA;EAGf,eAAe,QACvB;AACI,UAAM,YAAY,KAAK,YAAY,OAAO,GAAG;AAE7C,cAAU,QAAQ;AAElB,WAAO,IAAI,UAAU,KAAK,cAAc,IAAI;AAC5C,WAAO,IAAI,UAAU,KAAK,gBAAgB,IAAI;AAC9C,WAAO,IAAI,WAAW,KAAK,iBAAiB,IAAI;AAE3C,SAAA,YAAY,OAAO,GAAG,IAAI;EAAA;AAEvC;AArIa,gBAGK,YAAY;EACtB,MAAM;IACF,cAAc;EAAA;EAElB,MAAM;AACV;;;ACXG,IAAM,qBAAN,MACP;EAaI,YAAY,UACZ;AAHA,SAAQ,kBAAkB;AAItB,SAAK,YAAY;EAAA;EAGd,QAAQ,WACf;AACI,QAAI,KAAK,oBAAoB;AAAW;AACxC,SAAK,kBAAkB;AAElB,SAAA,UAAU,SAAS,aAAa,SAAS;EAAA;EAG3C,UACP;AACK,SAAK,YAAqB;AAC3B,SAAK,kBAAkB;EAAA;AAE/B;AAhCa,mBAGK,YAAY;EACtB,MAAM;IACF,cAAc;EAAA;EAElB,MAAM;AACV;;;ACoCG,IAAM,kBAAN,MACP;;;;EAgCI,YAAY,UACZ;AACI,SAAK,YAAY;EAAA;EAGrB,MAAa,KAAK,SAClB;AACI,QAAI,KAAK;AAAc,aAAO,KAAK;AAEnC,SAAK,eAAe,KAAK,wBAAwB,OAAO,EACnD,KAAK,CAAC,QACP;AACI,WAAK,MAAM;AAEX,WAAK,UAAU,QAAQ,cAAc,KAAK,KAAK,GAAG;IAAA,CACrD;AAEL,WAAO,KAAK;EAAA;;;;;EAON,cAAc,KACxB;AACI,SAAK,UAAU,MAAM;EAAA;;;;;;;;EAUzB,MAAc,wBAAwB,SACtC;AAEU,UAAA,UAAU,MAAM,WAAW,IAAA,EAAM,aAAa,EAAE,IAAI,eAAe;MACrE,iBAAiB,QAAQ;MACzB,sBAAsB,QAAQ;IAAA,CACjC;AAED,UAAM,mBAAmB;MACrB;MACA;MACA;IAAA,EACF,OAAO,CAAC,YAAY,QAAQ,SAAS,IAAI,OAAO,CAAC;AAG7C,UAAA,SAAS,MAAM,QAAQ,cAAc;MACvC;IAAA,CACH;AAEM,WAAA,EAAE,SAAS,OAAO;EAAA;EAGtB,UACP;AACI,SAAK,MAAM;AACX,SAAK,YAAY;EAAA;AAEzB;AAhGa,gBAGK,YAAY;EACtB,MAAM;IACF,cAAc;EAAA;EAElB,MAAM;AACV;AARS,gBAWK,iBAAoC;;;;;EAK9C,iBAAiB;;;;;EAKjB,sBAAsB;AAC1B;;;ACtDG,IAAM,mBAAN,MACP;EAsBI,YAAY,UACZ;AARQ,SAAA,kBAAoD,uBAAA,OAAO,IAAI;AAC/D,SAAA,qBAAoD,uBAAA,OAAO,IAAI;AAQnE,SAAK,YAAY;EAAA;EAGd,cACP;AACI,SAAK,kBAAkB,IAAI,QAAQ,CAAC,YACpC;AACI,WAAK,0BAA0B;IAAA,CAClC;AAID,SAAK,iBAAiB,KAAK,UAAU,IAAI,OAAO,qBAAqB;EAAA;EAGlE,gBAAgB,iBACvB;AACI,SAAK,cAAc;AAEnB,SAAK,YAAY;AAEjB,SAAK,oBAAoB,KAAK,eAAe,gBAAgB,gBAAgB,UAAU;EAAA;EAGpF,gBACP;AACI,QAAI,KAAK,mBACT;AACI,WAAK,kBAAkB,IAAI;IAAA;AAG/B,SAAK,oBAAoB;EAAA;EAGtB,YAAY,UACnB;AACS,SAAA,kBAAkB,YAAY,SAAS,GAAG,SAAS,GAAG,SAAS,OAAO,SAAS,QAAQ,GAAG,CAAC;EAAA;EAG7F,uCACH,UACA,SACA,OACA,UAEJ;AACU,UAAA,WAAW,KAAK,UAAU,SAAS,YAAY,UAAU,SAAS,OAAO,QAAQ;AAEvF,SAAK,YAAY,QAAQ;EAAA;EAGtB,YAAY,UACnB;AACI,QAAI,KAAK,mBAAmB;AAAU;AACtC,SAAK,iBAAiB;AAEjB,SAAA,kBAAkB,YAAY,QAAQ;EAAA;EAGvC,iBAAiB,OAAe,QACxC;AACQ,QAAA,KAAK,mBAAmB,KAAK,MAAM;AAAQ;AAE1C,SAAA,mBAAmB,KAAK,IAAI;AAE5B,SAAA,kBAAkB,gBAAgB,OAAO,KAAK,UAAU,OAAO,aAAa,MAAM,CAAC;EAAA;EAGpF,gBAAgB,QACxB;AACI,QAAI,KAAK,sBAAsB;AAAQ;AAEvC,SAAK,oBAAoB;AAEzB,UAAM,cAAc,OAAO,KAAK,sBAAsB,IAAI,WAAW;AAEhE,SAAA,kBAAkB,eAAe,KAAK,UAAU,OAAO,aAAa,MAAM,GAAG,WAAW;EAAA;EAG1F,eAAe,OACtB;AACS,SAAA,gBAAgB,KAAK,IAAI;EAAA;EAG3B,aAAa,OAAe,WAAsB,SACzD;AACQ,QAAA,KAAK,gBAAgB,KAAK,MAAM;AAAW;AAC1C,SAAA,gBAAgB,KAAK,IAAI;AAE9B,cAAU,OAAO,KAAK,UAAU,UAAU,KAAK;AAI/C,UAAM,eAAe,KAAK,UAAU,UAAU,aAAa,WAAW,SAAS,KAAK;AAG/E,SAAA,kBAAkB,aAAa,OAAO,YAAY;EAAA;EAGpD,YAAY,UAAoB,SACvC;AAOI,UAAM,gBAAgB,KAAK,UAAU,SAAS,qBAAqB,UAAU,OAAO;AAEpF,eAAW,KAAK,eAChB;AACS,WAAA,iBAAiB,GAAoB,SAAS,WAAW,cAAc,CAAC,CAAC,EAAE,MAAM;IAAA;AAG1F,QAAI,SAAS,aACb;AACS,WAAA,gBAAgB,SAAS,WAAW;IAAA;EAC7C;EAGI,qBAAqB,QAAgB,UAC7C;AACe,eAAA,KAAK,OAAO,QACvB;AACU,YAAA,YAAY,OAAO,OAAO,CAAC;AAGjC,UAAI,CAAC,UACL;AACI,aAAK,eAAe,SAAS;MAAA;AAGjC,WAAK,aAAa,GAAwB,WAAW,OAAO,UAAU;IAAA;EAC1E;EAGI,eAAe,WACvB;AACe,eAAA,KAAK,UAAU,WAC1B;AACU,YAAA,WAAW,UAAU,UAAU,CAAC;AAEtC,UAAK,SAA0B,gBAC/B;AACS,aAAA,UAAU,IAAI,mBAAmB,QAAwB;MAAA;IAClE;EACJ;EAGG,KAAK,SAUZ;AACU,UAAA,EAAE,UAAU,QAAQ,OAAO,UAAU,MAAM,OAAO,eAAe,SAAA,IAAa;AAEpF,SAAK,uCAAuC,UAAU,OAAO,YAAY,OAAO,QAAQ;AACnF,SAAA,YAAY,UAAU,OAAO,UAAU;AACvC,SAAA,qBAAqB,QAAQ,QAAQ;AAE1C,QAAI,SAAS,aACb;AACI,WAAK,kBAAkB;QACnB,QAAQ,SAAS,YAAY,KAAK;QAClC,iBAAiB,SAAS;QAC1B,SAAS;MAAA;IACb,OAGJ;AACS,WAAA,kBAAkB,KAAK,QAAQ,SAAS,QAAA,GAAW,iBAAiB,SAAS,eAAe,SAAS,CAAC;IAAA;EAC/G;EAGG,mBACP;AACI,QAAI,KAAK,mBACT;AACI,WAAK,kBAAkB,IAAI;AAC3B,WAAK,oBAAoB;IAAA;EAC7B;EAGG,aACP;AACI,SAAK,iBAAiB;AAEjB,SAAA,KAAK,OAAO,MAAM,OAAO,CAAC,KAAK,eAAe,OAAO,CAAC,CAAC;AAE5D,SAAK,wBAAwB;AAE7B,SAAK,iBAAiB;EAAA;;;;EAMnB,oBACP;AACI,UAAM,aAAc,KAAK,UAAU,aAAa,QAAmC;MAC/E,KAAK,UAAU,aAAa;MAC5B;MACA,CAAC,GAAG,GAAG,GAAG,CAAC;IAAA;AAGf,SAAK,oBAAoB,KAAK,eAAe,gBAAgB,UAAU;AAEvE,UAAM,gBAAgB,KAAK;AAC3B,UAAM,oBAAoB,EAAE,GAAG,KAAK,mBAAmB;AACvD,UAAM,mBAAmB,KAAK;AAC9B,UAAM,iBAAiB,EAAE,GAAG,KAAK,gBAAgB;AAEjD,SAAK,YAAY;AAEX,UAAA,WAAW,KAAK,UAAU,aAAa;AAExC,SAAA,kBAAkB,YAAY,SAAS,GAAG,SAAS,GAAG,SAAS,OAAO,SAAS,QAAQ,GAAG,CAAC;AAIhG,SAAK,YAAY,aAAa;AAE9B,eAAW,KAAK,mBAChB;AACI,WAAK,iBAAiB,GAAwB,kBAAkB,CAAC,CAAC;IAAA;AAGtE,eAAW,KAAK,gBAChB;AACI,WAAK,aAAa,GAAwB,eAAe,CAAC,GAAG,IAAI;IAAA;AAGrE,SAAK,gBAAgB,gBAAgB;EAAA;EAGjC,cACR;AACI,aAAS,IAAI,GAAG,IAAI,IAAI,KACxB;AACS,WAAA,gBAAgB,CAAC,IAAI;AACrB,WAAA,mBAAmB,CAAC,IAAI;IAAA;AAGjC,SAAK,oBAAoB;AACzB,SAAK,iBAAiB;EAAA;EAGnB,UACP;AACK,SAAK,YAAqB;AAC3B,SAAK,OAAO;AACZ,SAAK,kBAAkB;AACvB,SAAK,qBAAqB;AAC1B,SAAK,oBAAoB;AACzB,SAAK,iBAAiB;EAAA;EAGhB,cAAc,KACxB;AACI,SAAK,OAAO;EAAA;AAEpB;AApSa,iBAGK,YAAY;EACtB,MAAM,CAAC,cAAc,YAAY;EACjC,MAAM;EACN,UAAU;AACd;;;ACDG,IAAM,kBAAN,MACP;EAgBI,YAAY,UACZ;AACI,SAAK,YAAY;EAAA;EAGd,gBACP;AACI,SAAK,cAAc,KAAK,UAAU,OAAO,IAAI,OAAO,OAAO;AAC3D,SAAK,uBAAuB,KAAK;EAAA;EAG9B,UACP;EAAA;AAGJ;AAhCa,gBAGK,YAAY;EACtB,MAAM;IACF,cAAc;EAAA;EAElB,MAAM;AACV;;;ACxBG,IAAM,mBAAN,MACP;EAkBI,YAAY,UACZ;AARQ,SAAA,4BAGI,uBAAA,OAAO,IAAI;AAMnB,SAAK,YAAY;AAER,aAAA,aAAa,qBAAqB,IAAI,IAAI;EAAA;EAG7C,qBAAqB,cAC/B;AACI,QAAI,eAAe,KAAK,0BAA0B,aAAa,GAAG;AAElE,QAAI,CAAC,cACL;AACI,qBAAe,KAAK,0BAA0B,aAAa,GAAG,IAAI;QAC9D,aAAa,cAAc;QAC3B,kBAAkB;MAAA;IACtB;AAGJ,SAAK,sBAAsB;AAE3B,SAAK,eAAe,aAAa,aAAa,aAAa,gBAAgB;EAAA;EAGxE,eAAe,aAA4B,kBAClD;AACI,UAAM,eAAe,KAAK,0BAA0B,KAAK,oBAAoB,GAAG;AAEhF,iBAAa,cAAc;AAC3B,iBAAa,mBAAmB;AAEhC,UAAM,WAAW,KAAK;AAEb,aAAA,SAAS,eAAe,WAAW;AACnC,aAAA,QAAQ,kBAAkB,oBAAoB,gBAAgB;EAAA;EAGpE,UACP;AACI,SAAK,UAAU,aAAa,qBAAqB,OAAO,IAAI;AAE3D,SAAK,YAAqB;AAE3B,SAAK,sBAAsB;AAC3B,SAAK,4BAA4B;EAAA;AAEzC;AAjEa,iBAGK,YAAY;EACtB,MAAM;IACF,cAAc;EAAA;EAElB,MAAM;AACV;;;ACjBG,IAAM,uBAAsF;EAC/F,KAAK,EAAE,OAAO,GAAG,MAAM,EAAE;EACzB,KAAK,EAAE,OAAO,GAAG,MAAM,EAAE;EACzB,KAAK,EAAE,OAAO,GAAG,MAAM,EAAE;EACzB,KAAK,EAAE,OAAO,GAAG,MAAM,EAAE;EACzB,aAAa,EAAE,OAAO,GAAG,MAAM,EAAE;EACjC,aAAa,EAAE,OAAO,GAAG,MAAM,EAAE;EACjC,aAAa,EAAE,OAAO,GAAG,MAAM,EAAE;EACjC,aAAa,EAAE,OAAO,GAAG,MAAM,EAAE;EACjC,aAAa,EAAE,OAAO,IAAI,MAAM,GAAG;EACnC,aAAa,EAAE,OAAO,IAAI,MAAM,GAAG;EACnC,aAAa,EAAE,OAAO,IAAI,MAAM,GAAG;EACnC,aAAa,EAAE,OAAO,GAAG,MAAM,EAAE;EACjC,aAAa,EAAE,OAAO,IAAI,MAAM,GAAG;EACnC,aAAa,EAAE,OAAO,IAAI,MAAM,GAAG;EACnC,aAAa,EAAE,OAAO,IAAI,MAAM,GAAG;EACnC,aAAa,EAAE,OAAO,GAAG,MAAM,EAAE;EACjC,eAAe,EAAE,OAAO,GAAG,MAAM,GAAG;EACpC,eAAe,EAAE,OAAO,GAAG,MAAM,EAAE;EACnC,eAAe,EAAE,OAAO,GAAG,MAAM,GAAG;EACpC,eAAe,EAAE,OAAO,GAAG,MAAM,GAAG;EACpC,eAAe,EAAE,OAAO,GAAG,MAAM,GAAG;EACpC,eAAe,EAAE,OAAO,GAAG,MAAM,GAAG;EACpC,eAAe,EAAE,OAAO,IAAI,MAAM,GAAG;EACrC,eAAe,EAAE,OAAO,GAAG,MAAM,GAAG;EACpC,eAAe,EAAE,OAAO,IAAI,MAAM,GAAG;EACrC,eAAe,EAAE,OAAO,GAAG,MAAM,GAAG;EACpC,eAAe,EAAE,OAAO,IAAI,MAAM,GAAG;EACrC,eAAe,EAAE,OAAO,GAAG,MAAM,GAAG;EACpC,eAAe,EAAE,OAAO,IAAI,MAAM,GAAG;EACrC,eAAe,EAAE,OAAO,GAAG,MAAM,GAAG;EACpC,eAAe,EAAE,OAAO,IAAI,MAAM,GAAG;EACrC,eAAe,EAAE,OAAO,GAAG,MAAM,GAAG;EACpC,eAAe,EAAE,OAAO,IAAI,MAAM,GAAG;EACrC,eAAe,EAAE,OAAO,GAAG,MAAM,GAAG;AACxC;AAMO,SAAS,sBAAsB,aACtC;AACI,QAAM,cAA4B,YAAY,IAAI,CAAC,UAC9C;IACG;IACA,QAAQ;IACR,MAAM;EAAA,EACR;AAEN,MAAI,SAAS;AAEb,WAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KACxC;AACU,UAAA,aAAa,YAAY,CAAC;AAEhC,QAAI,OAAO,qBAAqB,WAAW,KAAK,IAAI,EAAE;AACtD,UAAM,QAAQ,qBAAqB,WAAW,KAAK,IAAI,EAAE;AAEzD,QAAI,CAAC,qBAAqB,WAAW,KAAK,IAAI,GAC9C;AACI,YAAM,IAAI,MAAM,gDAAgD,WAAW,KAAK,IAAI,EAAE;IAAA;AAGtF,QAAA,WAAW,KAAK,OAAO,GAC3B;AACI,aAAO,KAAK,IAAI,MAAM,KAAK,IAAI,WAAW,KAAK;IAAA;AAGnD,aAAS,KAAK,KAAM,SAAU,KAAK,IAAI;AAGvC,eAAW,OAAO;AAElB,eAAW,SAAS;AAEV,cAAA;EAAA;AAId,WAAS,KAAK,KAAK,SAAS,EAAE,IAAI;AAE3B,SAAA,EAAE,aAAa,MAAM,OAAO;AACvC;;;AC1EgB,SAAA,sBAAsB,YAAwB,aAC9D;AAEI,QAAM,EAAE,MAAM,MAAA,IAAU,qBAAqB,WAAW,KAAK,IAAI;AAE3D,QAAA,aAAa,QAAQ,QAAQ;AAC7B,QAAA,OAAO,WAAW,KAAK,KAAK,QAAQ,KAAK,KAAK,IAAI,cAAc;AAE/D,SAAA;kBACO,WAAW,KAAK,IAAI;WAC3B,gBAAgB,IAAI,aAAa,WAAW,MAAM,EAAE;;;;;;4BAMnC,WAAW,KAAK,QAAQ,OAAO,EAAE;;kCAE3B,OAAO,CAAC;;mBAEvB,IAAI;;eAER,cAAc,IAAI,kBAAkB,SAAS,MAAM,EAAE;;;AAGpE;;;AC3BO,SAAS,0BACZ,aAEJ;AACW,SAAA;IACH;IACA;IACA;IACA;EAAA;AAER;;;ACVO,IAAM,eAAN,cAA2B,UAClC;EAOI,cACA;AACU,UAAA;MACF,mBAAmB;MACnB,iBAAiB;IAAA,CACpB;EAAA;AAET;AAfa,aAGK,YAAY;EACtB,MAAM,CAAC,cAAc,YAAY;EACjC,MAAM;AACV;;;ACfG,IAAM,WAAN,MACP;EAMI,YAAY,EAAE,2BAAAA,2BAAA,GACd;AALA,SAAiB,6BAAqC;AAEtD,SAAO,YAAY;AAIf,SAAK,6BAA6BA;AAC7B,SAAA,OAAO,IAAI,aAAa,KAAK;EAAA;EAG/B,QACP;AACI,SAAK,YAAY;EAAA;EAGd,cAAc,MACrB;AAEQ,QAAA,OAAO,KAAK,6BAA6B,GAC7C;AACI,YAAM,IAAI,MAAM,2CAA2C,OAAO,CAAC,EAAE;IAAA;AAGzE,UAAM,QAAQ,KAAK;AAEf,QAAA,UAAU,QAAS,OAAO;AAE9B,cAAU,KAAK,KAAK,UAAU,KAAK,0BAA0B,IAAI,KAAK;AAEtE,QAAI,UAAU,KAAK,KAAK,SAAS,GACjC;AAEU,YAAA,IAAI,MAAM,2CAA2C;IAAA;AAG/D,SAAK,YAAY;AAEV,WAAA;EAAA;EAGJ,SAAS,OAChB;AACI,UAAM,SAAS,KAAK,cAAc,MAAM,MAAM;AAE9C,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAClC;AACI,WAAK,KAAM,SAAS,IAAK,CAAC,IAAI,MAAM,CAAC;IAAA;AAGlC,WAAA;EAAA;EAGJ,UACP;AACI,SAAK,OAAO;EAAA;AAEpB;;;AClDA,IAAM,4BAA4B;AAG3B,IAAM,sBAAN,MACP;EAoBI,YAAY,UACZ;AAVQ,SAAA,iBAAmD,uBAAA,OAAO,IAAI;AAItE,SAAQ,WAAqB,CAAA;AAE7B,SAAQ,cAA2B,CAAA;AACnC,SAAQ,mBAAqC,CAAA;AAIzC,SAAK,YAAY;AACjB,SAAK,UAAU,aAAa,eAAe,MAAM,gBAAgB;AAEjE,SAAK,eAAe,IAAI,SAAS,EAAE,0BAAA,CAA2B;AAE9D,UAAM,eAAgB,MAAM;AAE5B,aAAS,IAAI,GAAG,IAAI,cAAc,KAClC;AACQ,UAAA,QAAQ,YAAY,UAAU,YAAY;AAE9C,UAAI,MAAM;AAAG,iBAAS,YAAY;AAE7B,WAAA,SAAS,KAAK,IAAI,OAAO;QAC1B,MAAM,KAAK,aAAa;QACxB;MAAA,CACH,CAAC;IAAA;EACN;EAGG,YACP;AACI,SAAK,kBAAkB;AACvB,SAAK,iBAAiB;EAAA;EAGlB,mBACR;AACe,eAAA,KAAK,KAAK,gBACrB;AACS,WAAA,eAAe,CAAC,IAAI;IAAA;AAG7B,SAAK,aAAa,MAAM;EAAA;;EAIrB,oBAAoB,OAA0B,WACrD;AACI,QAAI,CAAC,aAAa,KAAK,eAAe,MAAM,GAAG,GAC/C;AACW,aAAA,KAAK,eAAe,MAAM,GAAG;IAAA;AAGnC,SAAA,UAAU,IAAI,mBAAmB,KAAK;AAErC,UAAA,OAAO,MAAM,OAAO;AAE1B,UAAM,SAAS,KAAK,aAAa,cAAc,KAAK,MAAM;AAErD,SAAA,UAAU,IAAI,iBAAiB,OAAO,KAAK,aAAa,MAAM,SAAS,CAAC;AAE7E,SAAK,eAAe,MAAM,GAAG,IAAI,KAAK,cAAc,SAAS,yBAAyB;AAE/E,WAAA,KAAK,eAAe,MAAM,GAAG;EAAA;EAGjC,eAAe,OACtB;AACS,SAAA,UAAU,IAAI,mBAAmB,KAAK;AAErC,UAAA,OAAO,MAAM,OAAO;AAE1B,UAAM,SAAS,KAAK,aAAa,SAAS,IAAI;AAEvC,WAAA,KAAK,mBAAmB,SAAS,yBAAyB;EAAA;EAG9D,kBAAkB,MACzB;AACI,UAAM,SAAS,KAAK,aAAa,SAAS,IAAI;AAEvC,WAAA,KAAK,cAAc,SAAS,yBAAyB;EAAA;EAGzD,uBAAuB,MAC9B;AACI,UAAM,SAAS,KAAK,aAAa,SAAS,IAAI;AAE9C,UAAM,QAAQ,SAAS;AAEhB,WAAA,KAAK,mBAAmB,KAAK;EAAA;EAGhC,mBAAmB,OAC3B;AACI,QAAI,CAAC,KAAK,iBAAiB,KAAK,GAChC;AACI,YAAM,SAAS,KAAK,SAAS,QAAQ,CAAC;AAEtC,WAAK,iBAAiB,KAAK,IAAI,IAAI,eAAe;QAC9C;QACA,SAAU,QAAQ,IAAK,KAAK;QAC5B,MAAM;MAAA,CACT;IAAA;AAGE,WAAA,KAAK,iBAAiB,KAAK;EAAA;EAG9B,cAAc,OACtB;AACI,QAAI,CAAC,KAAK,YAAY,KAAK,GAC3B;AAEU,YAAA,YAAY,IAAI,UAAU;QAC5B,GAAG,KAAK,mBAAmB,KAAK;MAAA,CACnC;AAEI,WAAA,YAAY,KAAK,IAAI;IAAA;AAGvB,WAAA,KAAK,YAAY,KAAK;EAAA;EAGzB,oBACR;AACU,UAAA,eAAe,KAAK,UAAU;AAE9B,UAAA,cAAc,KAAK,SAAS,CAAC;AAEvB,gBAAA,OAAO,KAAK,aAAa,SAAS;AAE9C,iBAAa,aAAa,WAAW;AAErC,UAAM,iBAAiB,KAAK,UAAU,IAAI,OAAO,qBAAqB;AAEtE,aAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,KAC1C;AACU,YAAA,SAAS,KAAK,SAAS,CAAC;AAEf,qBAAA;QACX,aAAa,aAAa,WAAW;QACrC;QACA,aAAa,aAAa,MAAM;QAChC;QACA,KAAK,aAAa;MAAA;IACtB;AAIC,SAAA,UAAU,IAAI,OAAO,MAAM,OAAO,CAAC,eAAe,OAAO,CAAC,CAAC;EAAA;EAG7D,UACP;AACI,aAAS,IAAI,GAAG,IAAI,KAAK,YAAY,QAAQ,KAC7C;AACS,WAAA,YAAY,CAAC,EAAE,QAAQ;IAAA;AAGhC,SAAK,cAAc;AACnB,SAAK,iBAAiB;AAEtB,aAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,KAC1C;AACS,WAAA,SAAS,CAAC,EAAE,QAAQ;IAAA;AAE7B,SAAK,WAAW;AAEhB,aAAS,IAAI,GAAG,IAAI,KAAK,iBAAiB,QAAQ,KAClD;AACS,WAAA,iBAAiB,CAAC,EAAE,QAAQ;IAAA;AAGrC,SAAK,mBAAmB;AAExB,SAAK,aAAa,QAAQ;AAC1B,SAAK,iBAAiB;AAEtB,SAAK,YAAY;EAAA;AAEzB;AAnMa,oBAGK,YAAY;EACtB,MAAM;IACF,cAAc;EAAA;EAElB,MAAM;AACV;;;ACJJ,IAAM,qBAAqB;EACvB,cAAc;EACd,aAAa;EACb,cAAc;EACd,iBAAiB;EACjB,kBAAkB;AACtB;AAOA,SAAS,oBACL,gBACA,WACA,OACA,WACA,UAEJ;AACI,SAAQ,kBAAkB,KAClB,aAAa,KACb,SAAS,KACT,aAAa,IACd;AACX;AAMA,SAAS,kBACL,gBACA,kBACA,WACA,cAEJ;AACI,SAAQ,aAAa,IACb,kBAAkB,IAClB,gBAAgB,IACjB;AACX;AAsBO,IAAM,iBAAN,MACP;EAyBI,YAAY,UACZ;AAhBQ,SAAA,eAAuD,uBAAA,OAAO,IAAI;AAClE,SAAA,sBAAsE,uBAAA,OAAO,IAAI;AACxE,SAAA,qBAAoE,uBAAA,OAAO,IAAI;AAExF,SAAA,aAA8B,uBAAA,OAAO,IAAI;AAChC,SAAA,mBAAoD,uBAAA,OAAO,IAAI;AAMhF,SAAQ,aAAa;AACrB,SAAQ,oBAAoB;AAKxB,SAAK,YAAY;EAAA;EAGX,cAAc,KACxB;AACI,SAAK,OAAO;AACP,SAAA,eAAe,cAAc,QAAQ;AAE1C,SAAK,gBAAgB;EAAA;EAGlB,oBAAoB,kBAC3B;AACI,QAAI,KAAK,sBAAsB;AAAkB;AAEjD,SAAK,oBAAoB;AAEzB,SAAK,gBAAgB;EAAA;EAGlB,gBAAgB,cACvB;AACI,SAAK,oBAAoB,aAAa;AACtC,SAAK,0BAA0B,aAAa,WAAW,yBAAyB,IAAI;AAEpF,SAAK,gBAAgB;EAAA;EAGlB,aAAa,WACpB;AACI,QAAI,KAAK,eAAe;AAAW;AAEnC,SAAK,aAAa;AAElB,SAAK,gBAAgB;EAAA;EAGlB,eAAe,aACtB;AACI,QAAI,KAAK,iBAAiB;AAAa;AAEvC,SAAK,eAAe;AACf,SAAA,gBAAgB,sBAAsB,WAAW;AAEtD,SAAK,gBAAgB;EAAA;EAGlB,YAAY,UAAoB,SAAqB,OAAc,aAC1E;AACI,UAAM,WAAW,KAAK,YAAY,UAAU,SAAS,KAAK;AAE1D,gBAAY,YAAY,QAAQ;EAAA;EAG7B,YACH,UACA,SACA,OACA,UAEJ;AACQ,QAAA,CAAC,SAAS,YACd;AACqB,uBAAA,UAAU,QAAQ,aAAa;AAGhD,WAAK,mBAAmB,QAAQ;IAAA;AAGpC,iBAAA,WAAa,SAAS;AAGtB,UAAM,MAAM;MACR,SAAS;MACT,QAAQ;MACR,MAAM;MACN,MAAM;MACN,mBAAmB,QAAQ;IAAA;AAG3B,QAAA,KAAK,WAAW,GAAG;AAAU,aAAA,KAAK,WAAW,GAAG;AAE/C,SAAA,WAAW,GAAG,IAAI,KAAK,gBAAgB,UAAU,SAAS,OAAO,QAAQ;AAEvE,WAAA,KAAK,WAAW,GAAG;EAAA;EAGtB,gBAAgB,UAAoB,SAAqB,OAAc,UAC/E;AACU,UAAA,SAAS,KAAK,KAAK;AAEzB,UAAM,UAAU,KAAK,2BAA2B,UAAU,OAAO;AAEjE,UAAM,aAAa,KAAK,UAAU,MAAM,gBAAgB,KAAK;AAElD,eAAA,CAAC,EAAE,YAAY,KAAK,iBAAiB,cAAc,qBAAqB,IAAI,KAAK;AAE5F,UAAM,SAAS,KAAK,UAAU,OAAO,eAAe,OAAO,EAAE;AAE7D,UAAM,aAA0C;;;MAG5C,QAAQ;QACJ,QAAQ,KAAK,WAAW,QAAQ,OAAO,MAAM;QAC7C,YAAY,QAAQ,OAAO;;QAE3B;MAAA;MAEJ,UAAU;QACN,QAAQ,KAAK,WAAW,QAAQ,SAAS,MAAM;QAC/C,YAAY,QAAQ,SAAS;QAC7B,SAAS;MAAA;MAEb,WAAW;QACP;QACA,UAAU,MAAM;MAAA;MAEpB;MACA,aAAa;QACT,OAAO,KAAK;MAAA;;MAGhB,OAAO;IAAA;AAIX,QAAI,KAAK,yBACT;AAEI,iBAAW,eAAe;QACtB,GAAG,KAAK;QACR,QAAQ;QACR,mBAAmB,MAAM;QACzB,cAAc,MAAM,YAAY,SAAS;MAAA;IAC7C;AAGE,UAAA,WAAW,OAAO,qBAAqB,UAAU;AAEhD,WAAA;EAAA;EAGH,WAAW,MACnB;AACI,WAAO,KAAK,aAAa,IAAI,KAAK,KAAK,cAAc,IAAI;EAAA;EAGrD,cAAc,MACtB;AACU,UAAA,SAAS,KAAK,KAAK;AAEzB,SAAK,aAAa,IAAI,IAAI,OAAO,mBAAmB;MAChD;IAAA,CACH;AAEM,WAAA,KAAK,aAAa,IAAI;EAAA;EAGzB,mBAAmB,UAC3B;AACI,UAAM,SAAS,CAAA;AACf,QAAI,QAAQ;AAGZ,UAAM,gBAAgB,OAAO,KAAK,SAAS,UAAU,EAAE,KAAK;AAE5D,aAAS,IAAI,GAAG,IAAI,cAAc,QAAQ,KAC1C;AACI,YAAM,YAAY,SAAS,WAAW,cAAc,CAAC,CAAC;AAE/C,aAAA,OAAO,IAAI,UAAU;AACrB,aAAA,OAAO,IAAI,UAAU;AACrB,aAAA,OAAO,IAAI,UAAU;AACrB,aAAA,OAAO,IAAI,UAAU;IAAA;AAG1B,UAAA,YAAY,OAAO,KAAK,GAAG;AAExB,aAAA,aAAa,mBAAmB,WAAW,UAAU;AAE9D,WAAO,SAAS;EAAA;EAGZ,+BAA+B,SACvC;AACI,UAAM,SAAS,CAAA;AACf,QAAI,QAAQ;AAGZ,UAAM,gBAAgB,OAAO,KAAK,QAAQ,aAAa,EAAE,KAAK;AAE9D,aAAS,IAAI,GAAG,IAAI,cAAc,QAAQ,KAC1C;AACI,YAAM,YAAY,QAAQ,cAAc,cAAc,CAAC,CAAC;AAEjD,aAAA,OAAO,IAAI,UAAU;IAAA;AAG1B,UAAA,YAAY,OAAO,KAAK,GAAG;AAEzB,YAAA,yBAAyB,mBAAmB,WAAW,mBAAmB;AAElF,WAAO,QAAQ;EAAA;;;;;;;;EAUZ,qBAAqB,UAAoB,SAChD;AACI,UAAM,MAAO,SAAS,cAAc,KAAM,QAAQ;AAE9C,QAAA,KAAK,mBAAmB,GAAG;AAAU,aAAA,KAAK,mBAAmB,GAAG;AAEpE,UAAM,OAAO,KAAK,2BAA2B,UAAU,OAAO;AAGxD,UAAA,oBAAmD,uBAAA,OAAO,IAAI;AAEpE,UAAM,gBAAgB,QAAQ;AAE9B,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KACjC;AACI,YAAM,aAAa,OAAO,OAAO,KAAK,CAAC,EAAE,UAAU;AAE7C,YAAA,iBAAiB,WAAW,CAAC,EAAE;AAErC,iBAAW,KAAK,eAChB;AACI,YAAI,cAAc,CAAC,EAAE,aAAa,gBAClC;AACI,4BAAkB,CAAC,IAAI;AACvB;QAAA;MACJ;IACJ;AAGC,SAAA,mBAAmB,GAAG,IAAI;AAExB,WAAA;EAAA;EAGH,2BAA2B,UAAoB,SACvD;AACI,QAAI,CAAC,QAAQ;AAAwB,WAAK,+BAA+B,OAAO;AAEhF,UAAM,MAAO,SAAS,cAAc,KAAM,QAAQ;AAE9C,QAAA,KAAK,oBAAoB,GAAG,GAChC;AACW,aAAA,KAAK,oBAAoB,GAAG;IAAA;AAGvC,UAAM,sBAA+C,CAAA;AAE5C,aAAA,QAAQ,QAAQ,CAAC,WAC1B;AACI,YAAM,cAAqC;QACvC,aAAa;QACb,UAAU;QACV,YAAY,CAAA;MAAC;AAGjB,YAAM,wBAAwB,YAAY;AAE/B,iBAAA,KAAK,QAAQ,eACxB;AACU,cAAA,YAAY,SAAS,WAAW,CAAC;AAElC,aAAA,UAAU,WAAW,OAAO,GACjC;AAGI,eAAK,aAAa,CAAC,qCAAqC,UAAU,OAAO,8CACxB;QAAA;AAGjD,YAAA,UAAU,WAAW,QACzB;AACI,sBAAY,cAAc,UAAU;AACxB,sBAAA,WAAW,UAAU,WAAW,aAAa;AAEzD,gCAAsB,KAAK;YACvB,gBAAgB,QAAQ,cAAc,CAAC,EAAE;YACzC,QAAQ,UAAU;YAClB,QAAQ,UAAU;UAAA,CACrB;QAAA;MACL;AAGJ,UAAI,sBAAsB,QAC1B;AACI,4BAAoB,KAAK,WAAW;MAAA;IACxC,CACH;AAEI,SAAA,oBAAoB,GAAG,IAAI;AAEzB,WAAA;EAAA;EAGH,kBACR;AACI,UAAM,MAAM;MACR,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;IAAA;AAGT,QAAI,CAAC,KAAK,iBAAiB,GAAG,GAC9B;AACI,WAAK,iBAAiB,GAAG,IAAI,uBAAO,OAAO,IAAI;IAAA;AAG9C,SAAA,aAAa,KAAK,iBAAiB,GAAG;EAAA;EAGxC,UACP;AACK,SAAK,YAAqB;AAC3B,SAAK,sBAAsB;EAAA;AAEnC;AAnWa,eAGK,YAAY;EACtB,MAAM,CAAC,cAAc,YAAY;EACjC,MAAM;AACV;;;ACjFG,IAAM,kBAAN,MACP;EADO,cAAA;AAEH,SAAO,WAA+B,CAAA;AACtC,SAAO,eAAgC,CAAA;AAEvC,SAAO,cAAc;EAAA;AAIzB;;;ACGO,IAAM,yBAAN,MACP;EAIW,KAAK,UAA0B,oBACtC;AACI,SAAK,YAAY;AACjB,SAAK,sBAAsB;EAAA;EAGxB,cACH,4BACA,oBACA,WACA,MACA,YAEJ;AACI,UAAM,WAAW,KAAK;AAEtB,UAAM,iBAAiB,KAAK;MACxB;IAAA;AAGE,UAAA,iBAAiB,SAAS,QAAQ;MACpC,mBAAmB;IAAA;AAGvB,aAAS,QAAQ,eAAe;MAC5B;QACI,SAAS;QACT,QAAQ;MAAA;MAEZ;QACI,SAAS;QACT,QAAQ;MAAA;MAEZ;IAAA;AAGG,WAAA;EAAA;EAGJ,gBACH,cACA,QAAuB,MACvB,YACA,UAEJ;AACI,UAAM,qBAAqB,KAAK;AAE1B,UAAA,kBAAkB,mBAAmB,mBAAmB,YAAY;AAE1E,UAAM,aAAa,KAAK,cAAc,cAAc,OAAO,UAAU;AAErE,oBAAgB,aAAa;AAIxB,SAAA,UAAU,SAAS,gBAAgB,eAAe;AAClD,SAAA,UAAU,QAAQ,gBAAgB,eAAe;AACjD,SAAA,UAAU,QAAQ,YAAY,QAAQ;EAAA;EAGxC,mBACP;AACS,SAAA,UAAU,QAAQ,cAAc;EAAA;;;;;;;EASjC,oBAAoB,cAC5B;AACI,UAAM,kBAAkB,KAAK,oBAAoB,mBAAmB,YAAY;AAE5E,QAAA,gBAAgB,SAAS,CAAC,GAC9B;AACI,aAAO,gBAAgB,SAAS,CAAC,EAAE,kBAAkB;IAAA;AAGlD,WAAA,KAAK,UAAU,QAAQ;MAC1B,aAAa,cAAc,CAAC,EAAE;IAAA;EAClC;EAGG,cACH,cACA,OACA,YAEJ;AACQ,QAAA,OAAO,UAAU,WACrB;AACY,cAAA,QAAQ,MAAM,MAAM,MAAM;IAAA;AAGtC,UAAM,qBAAqB,KAAK;AAE1B,UAAA,kBAAkB,mBAAmB,mBAAmB,YAAY;AAEpE,UAAA,mBAAmB,aAAa,cAAc;MAChD,CAAC,SAAS,MACV;AACU,cAAA,UAAU,gBAAgB,SAAS,CAAC;AAEtC,YAAA;AACA,YAAA;AAEJ,YAAI,SACJ;AACU,gBAAA,iBAAiB,QAAQ,kBAAkB;AAE3C,gBAAA,oBAAoB,eAAe,WAAW;AAE7C,iBAAA;QAAA,OAGX;AACI,iBAAO,KAAK,UAAU,QAAQ,aAAa,OAAO,EAAE,WAAW;YAC3D,eAAe;UAAA,CAClB;QAAA;AAGD,YAAA,gBAAgB,aAAa,CAAC,GAClC;AACoB,0BAAA;AACT,iBAAA,KAAK,UAAU,QAAQ;YAC1B,gBAAgB,aAAa,CAAC;UAAA;QAClC;AAGJ,cAAM,SAAW,QAAkB,MAAM,QAAQ,UAAU;AAE3D,uBAAA,aAAe,mBAAmB;AAE3B,eAAA;UACH;UACA;UACA;UACA,SAAS;UACT;QAAA;MACJ;IACJ;AAGA,QAAA;AAIJ,SAAK,aAAa,WAAW,aAAa,UAAU,CAAC,aAAa,qBAClE;AACI,mBAAa,0BAA0B;AACvC,mBAAa,oBAAoB,OAAO,cAAc,gBAAgB,OAAO,IAAI;IAAA;AAGrF,QAAI,aAAa,qBACjB;AACI,YAAM,gBAAiB,QAAQ,MAAM,UAAU,UAAU;AACzD,YAAM,cAAe,QAAQ,MAAM,QAAQ,UAAU;AAE5B,+BAAA;QACrB,MAAM,KAAK,UAAU,QAChB,aAAa,aAAa,oBAAoB,MAAM,EACpD,WAAW;QAChB,gBAAgB;QAChB;QACA,iBAAiB;QACjB;QACA,cAAc;MAAA;IAClB;AAGJ,UAAM,aAAsC;MACxC;MACA;IAAA;AAGG,WAAA;EAAA;EAGJ,MAAM,cAA4B,QAAuB,MAAM,YAAwB,UAC9F;AACI,QAAI,CAAC;AAAO;AAEZ,UAAM,EAAE,KAAK,QAAQ,IAAI,KAAK;AAE9B,UAAM,SAAS,IAAI;AAEb,UAAA,aAAa,QAAQ,mBAAmB;AAE9C,QAAI,YACJ;AACU,YAAA,iBAAiB,OAAO,qBAAqB;AACnD,YAAM,uBAAuB,KAAK,cAAc,cAAc,OAAO,UAAU;AAEzE,YAAA,cAAc,eAAe,gBAAgB,oBAAoB;AAE3D,kBAAA,YAAY,SAAS,GAAG,SAAS,GAAG,SAAS,OAAO,SAAS,QAAQ,GAAG,CAAC;AAErF,kBAAY,IAAI;AAEV,YAAA,cAAc,eAAe,OAAO;AAE1C,aAAO,MAAM,OAAO,CAAC,WAAW,CAAC;IAAA,OAGrC;AACI,WAAK,gBAAgB,cAAc,OAAO,YAAY,QAAQ;IAAA;EAClE;EAGG,oBAAoB,cAC3B;AAEI,iBAAa,SAAS;AAEhB,UAAA,kBAAkB,IAAI,gBAAgB;AAI5C,iBAAa,cAAc,QAAQ,CAAC,cAAc,MAClD;AACI,UAAI,wBAAwB,cAC5B;AACU,cAAA,UAAU,aAAa,SAAS;UAClC;QAAA;AAGE,cAAA,YAAa,aAA8B,cAAc,kBAAkB;AAGjF,YAAA;AACI,kBAAQ,UAAU;YACd,QAAQ,KAAK,UAAU,IAAI;YAC3B,OAAO,gBAAgB,kBACjB,gBAAgB,WAChB,gBAAgB,oBAChB,gBAAgB;YACtB,QAAQ;YACR;UAAA,CACH;QAAA,SAEE,GACP;AACI,kBAAQ,MAAM,CAAC;QAAA;AAGH,wBAAA,SAAS,CAAC,IAAI;MAAA;AAGlB,sBAAA,OAAO,aAAa,OAAO;AAEvC,UAAA,aAAa,OAAO,WACxB;AACU,cAAA,cAAc,IAAI,cAAc;UAClC,OAAO;UACP,QAAQ;UACR,aAAa;QAAA,CAChB;AAEe,wBAAA,aAAa,CAAC,IAAI;MAAA;IACtC,CACH;AAED,QAAI,gBAAgB,MACpB;AACI,sBAAgB,cAAc;AAE9B,UAAI,aAAa,qBACjB;AACiB,qBAAA,oBAAoB,OAAO,cAAc;MAAA;IAC1D;AAGG,WAAA;EAAA;EAGJ,uBAAuB,iBAC9B;AACoB,oBAAA,SAAS,QAAQ,CAAC,YAClC;AACI,cAAQ,YAAY;IAAA,CACvB;AAEe,oBAAA,aAAa,QAAQ,CAAC,YACtC;AACI,cAAQ,QAAQ;IAAA,CACnB;AAED,oBAAgB,aAAa,SAAS;AACtC,oBAAgB,SAAS,SAAS;EAAA;EAG/B,0BAA0B,cACjC;AAEI,UAAM,kBAAkB,KAAK,oBAAoB,mBAAmB,YAAY;AAE5E,QAAA,aAAa,uBAAuB,gBAAgB,MACxD;AACiB,mBAAA,oBAAoB,OAAO,cAAc;IAAA;EAC1D;EAGG,sBAAsB,cAC7B;AACI,UAAM,kBAAkB,KAAK,oBAAoB,mBAAmB,YAAY;AAEhF,oBAAgB,QAAQ,aAAa;AACrC,oBAAgB,SAAS,aAAa;AAEtC,QAAI,gBAAgB,MACpB;AACI,mBAAa,cAAc,QAAQ,CAAC,cAAc,MAClD;AACU,cAAA,cAAc,gBAAgB,aAAa,CAAC;AAErC,qBAAA;UACT,aAAa,OAAO;UACpB,aAAa,OAAO;UACpB,aAAa,OAAO;QAAA;MACxB,CACH;IAAA;EACL;AAER;;;AClVO,IAAM,wBAAN,cAAoC,mBAC3C;EASI,YAAY,UACZ;AACI,UAAM,QAAQ;AAJX,SAAA,UAAU,IAAI,uBAAuB;AAMnC,SAAA,QAAQ,KAAK,UAAU,IAAI;EAAA;AAExC;AAhBa,sBAGK,YAAY;EACtB,MAAM,CAAC,cAAc,YAAY;EACjC,MAAM;AACV;;;ACIG,IAAM,kBAAN,MACP;EADO,cAAA;AAYc,SAAA,kBAAyD,uBAAA,OAAO,IAAI;EAAA;EAE3E,cAAc,KACxB;AACI,SAAK,OAAO;EAAA;EAGT,eAAe,SACtB;AACI,WAAO,KAAK,gBAAgB,QAAQ,UAAU,KAAK,KAAK,sBAAsB,OAAO;EAAA;EAGjF,sBAAsB,SAC9B;AACU,UAAA,SAAS,KAAK,KAAK;AAEzB,UAAM,aAAa,QAAQ,UAAU,IAAI,CAAC,UAAU,OAAO,sBAAsB,EAAE,SAAS,MAAM,CAAC,CAAC;AAE9F,UAAA,qBAAqB,EAAE,kBAAkB,WAAW;AAErD,SAAA,gBAAgB,QAAQ,UAAU,IAAI;MACvC;MACA,UAAU,OAAO,qBAAqB,kBAAkB;IAAA;AAUrD,WAAA,KAAK,gBAAgB,QAAQ,UAAU;EAAA;EAG3C,UACP;AAEI,SAAK,OAAO;AACX,SAAK,kBAA2B;EAAA;AAEzC;AArDa,gBAGK,YAAY;EACtB,MAAM;IACF,cAAc;EAAA;EAElB,MAAM;AACV;;;AC3BG,IAAM,sBAAmE,CAAA;AAEhF,oBAAoB,SAAS;EACzB,OAAO;IACH,WAAW;IACX,WAAW;IACX,WAAW;EAAA;EAEf,OAAO;IACH,WAAW;IACX,WAAW;IACX,WAAW;EAAA;AAEnB;AAEA,oBAAoB,MAAM;EACtB,OAAO;IACH,WAAW;IACX,WAAW;IACX,WAAW;EAAA;EAEf,OAAO;IACH,WAAW;IACX,WAAW;IACX,WAAW;EAAA;AAEnB;AAEA,oBAAoB,WAAW;EAC3B,OAAO;IACH,WAAW;IACX,WAAW;IACX,WAAW;EAAA;EAEf,OAAO;IACH,WAAW;IACX,WAAW;IACX,WAAW;EAAA;AAEnB;AAEA,oBAAoB,SAAS;EACzB,OAAO;IACH,WAAW;IACX,WAAW;IACX,WAAW;EAAA;EAEf,OAAO;IACH,WAAW;IACX,WAAW;IACX,WAAW;EAAA;AAEnB;AAEA,oBAAoB,UAAU;EAC1B,OAAO;IACH,WAAW;IACX,WAAW;IACX,WAAW;EAAA;EAEf,OAAO;IACH,WAAW;IACX,WAAW;IACX,WAAW;EAAA;AAEnB;AAEA,oBAAoB,OAAO;EACvB,OAAO;IACH,WAAW;IACX,WAAW;IACX,WAAW;EAAA;EAEf,OAAO;IACH,WAAW;IACX,WAAW;IACX,WAAW;EAAA;AAEnB;AAGA,oBAAoB,YAAY,IAAI;EAChC,OAAO;IACH,WAAW;IACX,WAAW;IACX,WAAW;EAAA;EAEf,OAAO;IACH,WAAW;IACX,WAAW;IACX,WAAW;EAAA;AAEnB;AAEA,oBAAoB,SAAS,IAAI;EAC7B,OAAO;IACH,WAAW;IACX,WAAW;IACX,WAAW;EAAA;EAEf,OAAO;IACH,WAAW;IACX,WAAW;IACX,WAAW;EAAA;AAEnB;AAEA,oBAAoB,YAAY,IAAI;EAChC,OAAO;IACH,WAAW;IACX,WAAW;IACX,WAAW;EAAA;EAEf,OAAO;IACH,WAAW;IACX,WAAW;IACX,WAAW;EAAA;AAEnB;AAEA,oBAAoB,QAAQ;EACxB,OAAO;IACH,WAAW;IACX,WAAW;IACX,WAAW;EAAA;EAEf,OAAO;IACH,WAAW;IACX,WAAW;IACX,WAAW;EAAA;AAEnB;AAEA,oBAAoB,MAAM;EACtB,OAAO;IACH,WAAW;IACX,WAAW;IACX,WAAW;EAAA;EAEf,OAAO;IACH,WAAW;IACX,WAAW;IACX,WAAW;EAAA;AAEnB;AAEA,oBAAoB,MAAM;EACtB,OAAO;IACH,WAAW;IACX,WAAW;IACX,WAAW;EAAA;EAEf,OAAO;IACH,WAAW;IACX,WAAW;IACX,WAAW;EAAA;AAEnB;;;ACnJO,IAAM,iBAAN,MACP;EA2CI,cACA;AACS,SAAA,eAAe,IAAI,MAAM;AAC9B,SAAK,aAAa,QAAQ;EAAA;EAGpB,cAAc,KACxB;AACI,SAAK,MAAM;EAAA;;;;;EAOR,gBAAgB,OACvB;AACI,UAAM,QAAQ,oBAAoB,MAAM,SAAS,KAAK,oBAAoB;AAEnE,WAAA;MACH;QACI,QAAQ;QACR,WAAW;QACX;MAAA;IACJ;EACJ;EAGG,UACP;AACI,SAAK,MAAM;EAAA;AAEnB;AA5Ea,eAGK,YAAY;EACtB,MAAM;IACF,cAAc;EAAA;EAElB,MAAM;AACV;;;AChBG,IAAM,+BAA+B;EAExC,MAAM;EAEN,OAAO,QAA2B,YAAwB,KAC1D;AACI,UAAM,WAAW,OAAO;AAExB,UAAM,SAAS,OAAO,aAAa,MAAM,OAAO,cAAc;AAExD,UAAA,gBAAgB,SAAS,aAAa;AAE5C,QAAI,OAAO,MAAM;MACb,EAAE,SAAS,WAAW;MACtB;MACA;QACI,QAAQ;QACR,cAAc,OAAO;QACrB,aAAa,OAAO,cAAc;MAAA;MAEtC;QACI,OAAO,OAAO;QACd,QAAQ,OAAO;QACf,oBAAoB;MAAA;IACxB;EACJ;AAER;;;AC3BO,IAAM,eAA8F;EACvG,kBAAkB,EAAE,YAAY,GAAG,YAAY,GAAG,aAAa,EAAE;EACjE,kBAAkB,EAAE,YAAY,IAAI,YAAY,GAAG,aAAa,EAAE;EAClE,kBAAkB,EAAE,YAAY,IAAI,YAAY,GAAG,aAAa,EAAE;EAClE,kBAAkB,EAAE,YAAY,IAAI,YAAY,GAAG,aAAa,EAAE;EAClE,kBAAkB,EAAE,YAAY,GAAG,YAAY,GAAG,aAAa,EAAE;EACjE,mBAAmB,EAAE,YAAY,IAAI,YAAY,GAAG,aAAa,EAAE;EACnE,kBAAkB,EAAE,YAAY,IAAI,YAAY,GAAG,aAAa,EAAE;AACtE;AAEA,IAAM,mBAAmB,EAAE,YAAY,GAAG,YAAY,GAAG,aAAa,EAAE;AAGjE,IAAM,qCAAqC;EAE9C,MAAM;EAEN,OAAO,QAA0B,YAAwB,KACzD;AACI,QAAI,WAAW,OAAO;AACtB,QAAI,YAAY,OAAO;AAEvB,UAAM,YAAY,aAAa,OAAO,MAAM,KAAK;AAEjD,aAAS,IAAI,GAAG,IAAI,OAAO,SAAS,QAAQ,KAC5C;AACU,YAAA,cAAc,OAAO,SAAS,CAAC;AAErC,YAAM,cAAc,KAAK,KAAK,WAAW,UAAU,UAAU,IAAI,UAAU;AAE3E,UAAI,OAAO,MAAM;QACb;UACI,SAAS;UACT,UAAU;QAAA;QAEd;QACA;UACI,QAAQ;UACR;QAAA;QAEJ;UACI,OAAO,KAAK,KAAK,WAAW,UAAU,UAAU,IAAI,UAAU;UAC9D,QAAQ,KAAK,KAAK,YAAY,UAAU,WAAW,IAAI,UAAU;UACjE,oBAAoB;QAAA;MACxB;AAGJ,iBAAW,KAAK,IAAI,YAAY,GAAG,CAAC;AACpC,kBAAY,KAAK,IAAI,aAAa,GAAG,CAAC;IAAA;EAC1C;AAER;;;AChDO,IAAM,yBAAyB;EAElC,MAAM;EAEN,OAAO,QAAuB,YAAwB,KACtD;AACI,UAAM,WAAW,OAAO;AAExB,QAAI,CAAC;AAAU;AAIX,QAAA,WAAW,oBAAoB,oBAAoB,kBACvD;AACU,YAAA,SAAS,WAAW,IAAI,EAAE,aAAa,SAAS,OAAO,SAAS,MAAM;AACtE,YAAA,UAAU,OAAO,WAAW,IAAI;AAEtC,cAAQ,UAAU,UAAU,GAAG,GAAG,SAAS,OAAO,SAAS,MAAM;AAGjE,aAAO,WAAW;AAGlB,WAAK,iFAAiF;IAAA;AAIpF,UAAA,QAAQ,KAAK,IAAI,WAAW,OAAO,OAAO,iBAAiB,OAAO,UAAU;AAC5E,UAAA,SAAS,KAAK,IAAI,WAAW,QAAQ,OAAO,kBAAkB,OAAO,WAAW;AAEhF,UAAA,qBAAqB,OAAO,cAAc;AAEhD,QAAI,OAAO,MAAM;MACb,EAAE,QAAQ,SAAS;MACnB,EAAE,SAAS,YAAY,mBAAmB;MAC1C;QACI;QACA;MAAA;IACJ;EACJ;AAER;;;AC1CO,IAAM,yBAAyB;EAElC,MAAM;EAEN,OAAO,QAAqB,YAAwB,KACpD;AAC2B,2BAAA,OAAO,QAAQ,YAAY,GAAG;EAAA;AAE7D;;;ACRO,IAAM,qBAAN,MACP;EAOI,YAAY,QACZ;AACI,SAAK,SAAS;AACd,SAAK,UAAU,OAAO,cAAc,EAAE,WAAW,SAAA,CAAU;AAE3D,SAAK,YAAY,CAAA;EAAC;EAGd,mBAAmB,QAC3B;AACQ,QAAA,WAAW,KAAK,UAAU,MAAM;AAEpC,QAAI,CAAC,UACL;AAEQ,UAAA,CAAC,KAAK,oBACV;AACS,aAAA,qBAAqB,KAAK,OAAO,mBAAmB;UACrD;;YAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;QAAA,CAyBpB;MAAA;AAGM,iBAAA,KAAK,OAAO,qBAAqB;QACxC,QAAQ;QACR,QAAQ;UACJ,QAAQ,KAAK;UACb,YAAY;QAAA;QAEhB,UAAU;UACN,QAAQ,KAAK;UACb,YAAY;UACZ,SAAS,CAAC,EAAE,OAAA,CAAQ;QAAA;MACxB,CACH;AAEI,WAAA,UAAU,MAAM,IAAI;IAAA;AAGtB,WAAA;EAAA;;;;;;EAQJ,eAAe,SACtB;AACI,UAAM,WAAW,KAAK,mBAAmB,QAAQ,MAAM;AAEvD,QAAI,QAAQ,cAAc,QAAQ,QAAQ,cAAc,MACxD;AACU,YAAA,IAAI,MAAM,kEAAkE;IAAA;AAGtF,QAAI,aAAa;AACX,UAAA,kBAAkB,QAAQ,sBAAsB;AAGhD,UAAA,iBAAiB,QAAQ,QAAQ,gBAAgB;AAEvD,QAAI,CAAC,gBACL;AAGI,YAAM,uBAAuB;QACzB,MAAM;UACF,OAAO,KAAK,KAAK,QAAQ,QAAQ,CAAC;UAClC,QAAQ,KAAK,KAAK,QAAQ,SAAS,CAAC;UACpC,oBAAoB;QAAA;QAExB,QAAQ,QAAQ;QAChB,OAAO,gBAAgB,kBAAkB,gBAAgB,WAAW,gBAAgB;QACpF,eAAe,QAAQ,gBAAgB;MAAA;AAG9B,mBAAA,KAAK,OAAO,cAAc,oBAAoB;IAAA;AAG/D,UAAM,iBAAiB,KAAK,OAAO,qBAAqB,CAAA,CAAE;AAEpD,UAAA,kBAAkB,SAAS,mBAAmB,CAAC;AAErD,aAAS,aAAa,GAAG,aAAa,iBAAiB,EAAE,YACzD;AACQ,UAAA,UAAU,QAAQ,WAAW;QAC7B,cAAc;QACd,eAAe;QACf,WAAW;QACX,gBAAgB;QAChB,iBAAiB;MAAA,CACpB;AAEG,UAAA,cAAc,iBAAiB,IAAI;AAEvC,eAAS,IAAI,GAAG,IAAI,QAAQ,eAAe,EAAE,GAC7C;AACU,cAAA,UAAU,WAAW,WAAW;UAClC,cAAc;UACd,eAAe;UACf,WAAW;UACX,gBAAgB;UAChB,iBAAiB;QAAA,CACpB;AAEK,cAAA,cAAc,eAAe,gBAAgB;UAC/C,kBAAkB,CAAC;YACf,MAAM;YACN,SAAS;YACT,QAAQ;YACR,YAAY,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE;UAAA,CACxC;QAAA,CACJ;AAEK,cAAA,YAAY,KAAK,OAAO,gBAAgB;UAC1C,QAAQ;UACR,SAAS,CAAC;YACN,SAAS;YACT,UAAU,KAAK;UAAA,GAChB;YACC,SAAS;YACT,UAAU;UAAA,CACb;QAAA,CACJ;AAED,oBAAY,YAAY,QAAQ;AACpB,oBAAA,aAAa,GAAG,SAAS;AACrC,oBAAY,KAAK,GAAG,GAAG,GAAG,CAAC;AAE3B,oBAAY,IAAI;AAEN,kBAAA;MAAA;IACd;AAKJ,QAAI,CAAC,gBACL;AACI,YAAM,eAAe;QACjB,OAAO,KAAK,KAAK,QAAQ,QAAQ,CAAC;QAClC,QAAQ,KAAK,KAAK,QAAQ,SAAS,CAAC;QACpC,oBAAoB;MAAA;AAGxB,eAAS,IAAI,GAAG,IAAI,QAAQ,eAAe,EAAE,GAC7C;AACI,uBAAe,qBAAqB;UAChC,SAAS;UACT,UAAU,IAAI;QAAA,GACf;UACC;UACA,UAAU;QAAA,GACX,YAAY;AAEf,qBAAa,QAAQ,KAAK,KAAK,aAAa,QAAQ,CAAC;AACrD,qBAAa,SAAS,KAAK,KAAK,aAAa,SAAS,CAAC;MAAA;IAC3D;AAGJ,SAAK,OAAO,MAAM,OAAO,CAAC,eAAe,OAAA,CAAQ,CAAC;AAElD,QAAI,CAAC,gBACL;AACI,iBAAW,QAAQ;IAAA;AAGhB,WAAA;EAAA;AAEf;;;ACtLO,IAAM,mBAAN,MACP;EA6BI,YAAY,UACZ;AArBA,SAAgB,kBAAmC,CAAA;AAG3C,SAAA,cAAiD,uBAAA,OAAO,IAAI;AAC5D,SAAA,eAAkD,uBAAA,OAAO,IAAI;AAC7D,SAAA,iBAAmD,uBAAA,OAAO,IAAI;AAC9D,SAAA,mBAA0D,uBAAA,OAAO,IAAI;AAE7E,SAAiB,WAA+C;MAC5D,OAAO;MACP,QAAQ;MACR,OAAO;MACP,YAAY;IAAA;AAUZ,SAAK,YAAY;AACR,aAAA,aAAa,eAAe,MAAM,aAAa;AAC/C,aAAA,aAAa,eAAe,MAAM,cAAc;AAChD,aAAA,aAAa,eAAe,MAAM,gBAAgB;AAClD,aAAA,aAAa,eAAe,MAAM,kBAAkB;EAAA;EAGvD,cAAc,KACxB;AACI,SAAK,OAAO;EAAA;EAGT,WAAW,QAClB;AACI,QAAI,OAAO,qBACX;AACI,YAAM,mBAAmB,KAAK,IAAI,OAAO,YAAY,OAAO,WAAW;AAEvE,aAAO,gBAAgB,KAAK,MAAM,KAAK,KAAK,gBAAgB,CAAC,IAAI;IAAA;AAGjE,QAAA,QAAQ,gBAAgB,kBAAkB,gBAAgB;AAE1D,QAAA,OAAO,mBAAmB,cAC9B;AACI,eAAS,gBAAgB;AACzB,eAAS,gBAAgB;IAAA;AAGvB,UAAA,YAAY,aAAa,OAAO,MAAM,KAAK,EAAE,YAAY,GAAG,YAAY,GAAG,aAAa,EAAE;AAE1F,UAAA,QAAQ,KAAK,KAAK,OAAO,aAAa,UAAU,UAAU,IAAI,UAAU;AACxE,UAAA,SAAS,KAAK,KAAK,OAAO,cAAc,UAAU,WAAW,IAAI,UAAU;AAEjF,UAAM,oBAA0C;MAC5C,OAAO,OAAO;MACd,MAAM,EAAE,OAAO,OAAO;MACtB,QAAQ,OAAO;MACf,aAAa,OAAO;MACpB,eAAe,OAAO;MACtB,WAAW,OAAO;MAClB;IAAA;AAGJ,UAAM,aAAa,KAAK,KAAK,OAAO,cAAc,iBAAiB;AAE9D,SAAA,YAAY,OAAO,GAAG,IAAI;AAE/B,QAAI,CAAC,KAAK,gBAAgB,SAAS,MAAM,GACzC;AACI,aAAO,GAAG,UAAU,KAAK,gBAAgB,IAAI;AAC7C,aAAO,GAAG,UAAU,KAAK,gBAAgB,IAAI;AAC7C,aAAO,GAAG,WAAW,KAAK,iBAAiB,IAAI;AAC/C,aAAO,GAAG,UAAU,KAAK,gBAAgB,IAAI;AAC7C,aAAO,GAAG,iBAAiB,KAAK,iBAAiB,IAAI;AAEhD,WAAA,gBAAgB,KAAK,MAAM;IAAA;AAGpC,SAAK,eAAe,MAAM;AAEnB,WAAA;EAAA;EAGD,eAAe,QACzB;AACU,UAAA,aAAa,KAAK,aAAa,MAAM;AAG3C,QAAI,CAAC;AAAY;AAEjB,QAAI,KAAK,SAAS,OAAO,cAAc,GACvC;AACS,WAAA,SAAS,OAAO,cAAc,EAAE,OAAO,QAAQ,YAAY,KAAK,IAAI;IAAA;AAG7E,QAAI,OAAO,uBAAuB,OAAO,gBAAgB,GACzD;AACI,WAAK,gBAAgB,MAAM;IAAA;EAC/B;EAGM,eAAe,QACzB;AACI,UAAM,aAAa,KAAK,YAAY,OAAO,GAAG;AAE9C,QAAI,YACJ;AACS,WAAA,YAAY,OAAO,GAAG,IAAI;AAE/B,iBAAW,QAAQ;IAAA;EACvB;EAGM,gBAAgB,QAC1B;AACQ,QAAA,CAAC,KAAK,kBACV;AACI,WAAK,mBAAmB,IAAI,mBAAmB,KAAK,KAAK,MAAM;IAAA;AAG7D,UAAA,aAAa,KAAK,aAAa,MAAM;AAEtC,SAAA,iBAAiB,eAAe,UAAU;EAAA;EAGzC,gBAAgB,QAC1B;AACI,WAAO,IAAI,UAAU,KAAK,gBAAgB,IAAI;AAC9C,WAAO,IAAI,UAAU,KAAK,gBAAgB,IAAI;AAC9C,WAAO,IAAI,WAAW,KAAK,iBAAiB,IAAI;AAChD,WAAO,IAAI,UAAU,KAAK,gBAAgB,IAAI;AAC9C,WAAO,IAAI,iBAAiB,KAAK,iBAAiB,IAAI;AAEtD,SAAK,gBAAgB,OAAO,KAAK,gBAAgB,QAAQ,MAAM,GAAG,CAAC;AAEnE,SAAK,eAAe,MAAM;EAAA;EAGpB,eAAe,QACzB;AACI,UAAM,aAAa,KAAK,YAAY,OAAO,GAAG;AAE9C,QAAI,CAAC,YACL;AACI,WAAK,WAAW,MAAM;IAAA,WAEjB,WAAW,UAAU,OAAO,cAAc,WAAW,WAAW,OAAO,aAChF;AACS,WAAA,iBAAiB,OAAO,GAAG,IAAI;AAC/B,WAAA,eAAe,OAAO,GAAG,IAAI;AAElC,WAAK,eAAe,MAAM;AAC1B,WAAK,WAAW,MAAM;IAAA;EAC1B;EAGI,aAAa,SACrB;AACS,SAAA,aAAa,QAAQ,WAAW,IAAI,KAAK,KAAK,OAAO,cAAc,OAAO;AAExE,WAAA,KAAK,aAAa,QAAQ,WAAW;EAAA;EAGzC,cAAc,SACrB;AACI,WAAO,KAAK,aAAa,QAAQ,WAAW,KAAK,KAAK,aAAa,OAAO;EAAA;EAGvE,aAAa,QACpB;AACI,WAAO,KAAK,YAAY,OAAO,GAAG,KAAK,KAAK,WAAW,MAAM;EAAA;;;;;;;;;;EAY1D,oBAAoB,SAC3B;AACI,WAAO,KAAK,eAAe,QAAQ,GAAG,KAAK,KAAK,wBAAwB,OAAO;EAAA;EAG3E,wBAAwB,SAChC;AACI,UAAM,SAAS,QAAQ;AAEvB,SAAK,eAAe,QAAQ,GAAG,IAAI,IAAI,UAAU;MAC7C,GAAG;MACH,GAAG,OAAO;MACV,GAAG,IAAI,aAAa;QAChB,gBAAgB,EAAE,MAAM,eAAe,OAAO,QAAQ,cAAc,SAAS;MAAA,CAChF;IAAA,CACJ;AAEM,WAAA,KAAK,eAAe,QAAQ,GAAG;EAAA;EAGnC,eAAe,SACtB;AACI,UAAM,SAAS,QAAQ;AAEvB,WAAO,KAAK,iBAAiB,OAAO,GAAG,KAAK,KAAK,mBAAmB,MAAM;EAAA;EAGtE,mBAAmB,SAC3B;AACS,SAAA,iBAAiB,QAAQ,GAAG,IAAI,KAAK,aAAa,OAAO,EAAE,WAAW;AAEpE,WAAA,KAAK,iBAAiB,QAAQ,GAAG;EAAA;EAGrC,eAAe,SACtB;AACI,UAAM,WAAW,KAAK;AAEtB,UAAM,iBAAiB,SAAS,IAAI,OAAO,qBAAqB;AAGhE,UAAM,SAAS,WAAW,IAAI,EAAE,aAAa;AAEtC,WAAA,QAAQ,QAAQ,OAAO;AACvB,WAAA,SAAS,QAAQ,OAAO;AAEzB,UAAA,UAAU,OAAO,WAAW,QAAQ;AAE1C,YAAQ,UAAU;MACd,QAAQ,SAAS,IAAI;MAErB,OAAO,gBAAgB,WAAW,gBAAgB;MAClD,QAAQ,WAAW,IAAA,EAAM,aAAa,EAAE,IAAI,yBAAyB;MACrE,WAAW;IAAA,CACd;AAED,mBAAe,qBAAqB;MAChC,SAAS,SAAS,QAAQ,aAAa,QAAQ,MAAM;MACrD,QAAQ;QACJ,GAAG;QACH,GAAG;MAAA;IACP,GACD;MACC,SAAS,QAAQ,kBAAkB;IAAA,GACpC;MACC,OAAO,OAAO;MACd,QAAQ,OAAO;IAAA,CAClB;AAEQ,aAAA,IAAI,OAAO,MAAM,OAAO,CAAC,eAAe,OAAA,CAAQ,CAAC;AAEnD,WAAA;EAAA;EAGJ,UAAU,SACjB;AACU,UAAA,eAAe,KAAK,eAAe,OAAO;AAEhD,UAAM,mBAAmB,WAAW,2BAA2B,aAAa,OAAO,aAAa,MAAM;AAEtG,UAAM,UAAU,iBAAiB;AAEzB,YAAA,UAAU,cAAc,GAAG,CAAC;AAE9B,UAAA,EAAE,OAAO,OAAA,IAAW;AAE1B,UAAM,YAAY,QAAQ,aAAa,GAAG,GAAG,OAAO,MAAM;AAE1D,UAAM,SAAS,IAAI,kBAAkB,UAAU,KAAK,MAAM;AAE1D,eAAW,uBAAuB,gBAAgB;AAE3C,WAAA,EAAE,QAAQ,OAAO,OAAO;EAAA;EAG5B,UACP;AAGS,SAAA,gBACA,MAAA,EACA,QAAQ,CAAC,WAAW,KAAK,gBAAgB,MAAM,CAAC;AAEpD,SAAK,kBAA2B;AAEjC,eAAW,KAAK,OAAO,KAAK,KAAK,cAAc,GAC/C;AACU,YAAA,MAAM,OAAO,CAAC;AACd,YAAA,YAAY,KAAK,eAAe,GAAG;AAEzC,iBAAW,QAAQ;AACd,WAAA,eAAe,GAAG,IAAI;IAAA;AAG/B,SAAK,OAAO;AACZ,SAAK,mBAAmB;AACxB,SAAK,cAAc;AACnB,SAAK,iBAAiB;AACtB,SAAK,mBAAmB;AACxB,SAAK,eAAe;EAAA;AAE5B;AA5Ta,iBAGK,YAAY;EACtB,MAAM;IACF,cAAc;EAAA;EAElB,MAAM;AACV;;;ACNJ,IAAM,uBAAuB;EACzB,GAAG;EACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACJ;AACA,IAAM,qBAAqB,CAAC,GAAG,mBAAmB,mBAAmB;AACrE,IAAM,wBAAwB,CAAC,iBAAiB,gBAAgB,kBAAkB;AAGlF,IAAM,UAAwD,CAAA;AAC9D,IAAM,cAA0D,CAAA;AAChE,IAAM,qBAAqD,CAAA;AAE3D,WAAW,kBAAkB,cAAc,cAAc,OAAO;AAChE,WAAW,kBAAkB,cAAc,aAAa,WAAW;AACnE,WAAW,kBAAkB,cAAc,oBAAoB,kBAAkB;AAGjF,WAAW,IAAI,GAAG,sBAAsB,GAAG,oBAAoB,GAAG,qBAAqB;AAiGhF,IAAM,iBAAN,cACK,iBAEZ;EAII,cACA;AACI,UAAM,eAAe;MACjB,MAAM;MACN,MAAM,aAAa;MACnB;MACA;MACA;IAAA;AAGJ,UAAM,YAAY;EAAA;AAE1B;", "names": ["minUniformOffsetAlignment"]}