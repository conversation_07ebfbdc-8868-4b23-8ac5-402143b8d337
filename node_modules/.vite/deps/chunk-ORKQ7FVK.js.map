{"version": 3, "sources": ["../../pixi.js/src/filters/Filter.ts", "../../pixi.js/src/environment/autoDetectEnvironment.ts", "../../pixi.js/src/utils/browser/unsafeEvalSupported.ts", "../../pixi.js/src/rendering/renderers/gl/const.ts", "../../pixi.js/src/rendering/renderers/shared/system/SystemRunner.ts", "../../pixi.js/src/rendering/renderers/shared/system/AbstractRenderer.ts", "../../pixi.js/src/rendering/high-shader/shader-bits/textureBit.ts", "../../pixi.js/src/scene/container/CustomRenderPipe.ts", "../../pixi.js/src/scene/container/utils/executeInstructions.ts", "../../pixi.js/src/scene/container/RenderGroupPipe.ts", "../../pixi.js/src/scene/container/utils/clearList.ts", "../../pixi.js/src/scene/container/utils/updateRenderGroupTransforms.ts", "../../pixi.js/src/scene/container/utils/validateRenderables.ts", "../../pixi.js/src/scene/container/RenderGroupSystem.ts", "../../pixi.js/src/scene/sprite/SpritePipe.ts", "../../pixi.js/src/utils/const.ts", "../../pixi.js/src/utils/global/globalHooks.ts", "../../pixi.js/src/rendering/batcher/shared/BatcherPipe.ts", "../../pixi.js/lib/filters/mask/mask.frag.mjs", "../../pixi.js/lib/filters/mask/mask.vert.mjs", "../../pixi.js/lib/filters/mask/mask.wgsl.mjs", "../../pixi.js/src/filters/mask/MaskFilter.ts", "../../pixi.js/src/rendering/mask/alpha/AlphaMaskPipe.ts", "../../pixi.js/src/rendering/mask/color/ColorMaskPipe.ts", "../../pixi.js/src/rendering/mask/stencil/StencilMaskPipe.ts", "../../pixi.js/src/rendering/renderers/shared/background/BackgroundSystem.ts", "../../pixi.js/src/rendering/renderers/shared/blendModes/BlendModePipe.ts", "../../pixi.js/src/rendering/renderers/shared/extract/ExtractSystem.ts", "../../pixi.js/src/rendering/renderers/shared/texture/RenderTexture.ts", "../../pixi.js/src/rendering/renderers/shared/extract/GenerateTextureSystem.ts", "../../pixi.js/src/rendering/renderers/shared/renderTarget/GlobalUniformSystem.ts", "../../pixi.js/src/rendering/renderers/shared/SchedulerSystem.ts", "../../pixi.js/src/utils/sayHello.ts", "../../pixi.js/src/rendering/renderers/shared/startup/HelloSystem.ts", "../../pixi.js/src/utils/data/clean.ts", "../../pixi.js/src/rendering/renderers/shared/texture/RenderableGCSystem.ts", "../../pixi.js/src/rendering/renderers/shared/texture/TextureGCSystem.ts", "../../pixi.js/src/rendering/renderers/shared/renderTarget/RenderTarget.ts", "../../pixi.js/src/rendering/renderers/shared/texture/utils/getCanvasTexture.ts", "../../pixi.js/src/rendering/renderers/shared/view/ViewSystem.ts", "../../pixi.js/src/rendering/renderers/shared/system/SharedSystems.ts", "../../pixi.js/src/rendering/renderers/shared/shader/UboSystem.ts", "../../pixi.js/src/rendering/renderers/shared/shader/utils/uniformParsers.ts", "../../pixi.js/src/rendering/renderers/shared/shader/utils/createUboSyncFunction.ts", "../../pixi.js/src/rendering/renderers/shared/shader/utils/uboSyncFunctions.ts", "../../pixi.js/src/rendering/renderers/shared/buffer/BufferResource.ts", "../../pixi.js/src/rendering/renderers/gl/shader/program/ensureAttributes.ts", "../../pixi.js/src/rendering/renderers/gpu/state/GpuStencilModesToPixi.ts", "../../pixi.js/src/rendering/renderers/gpu/renderTarget/calculateProjection.ts", "../../pixi.js/src/rendering/renderers/shared/renderTarget/isRenderingToScreen.ts", "../../pixi.js/src/rendering/renderers/shared/renderTarget/RenderTargetSystem.ts"], "sourcesContent": ["import { GlProgram } from '../rendering/renderers/gl/shader/GlProgram';\nimport { GpuProgram } from '../rendering/renderers/gpu/shader/GpuProgram';\nimport { Shader } from '../rendering/renderers/shared/shader/Shader';\nimport { State } from '../rendering/renderers/shared/state/State';\n\nimport type { RenderSurface } from '../rendering/renderers/shared/renderTarget/RenderTargetSystem';\nimport type {\n    IShaderWithResources,\n    ShaderFromResources,\n    ShaderWithResources\n} from '../rendering/renderers/shared/shader/Shader';\nimport type { BLEND_MODES } from '../rendering/renderers/shared/state/const';\nimport type { Texture } from '../rendering/renderers/shared/texture/Texture';\nimport type { FilterSystem } from './FilterSystem';\n\n/**\n * The options to use when creating a new filter.\n * @category filters\n * @advanced\n */\nexport interface FilterOptions\n{\n    /** optional blend mode used by the filter when rendering (defaults to 'normal') */\n    blendMode?: BLEND_MODES;\n    /**\n     * the resolution the filter should be rendered at. The lower the resolution, the more performant\n     * the filter will be, but the lower the quality of the output. (default 1)\n     * If 'inherit', the resolution of the render target is used.\n     * Consider lowering this for things like blurs filters\n     */\n    resolution?: number | 'inherit';\n    /**\n     * the amount of pixels to pad the container with when applying the filter. For example a blur extends the\n     * container out as it blurs, so padding is applied to ensure that extra detail is rendered as well\n     * without clipping occurring. (default 0)\n     */\n    padding?: number;\n    /**\n     * If true the filter will make use of antialiasing. Although it looks better this can have a performance impact.\n     * If set to 'inherit', the filter will detect the antialiasing of the render target and change this automatically.\n     * Definitely don't set this to true if the render target has antialiasing set to false. As it will antialias,\n     * but you won't see the difference. (default 'off')\n     *\n     * This can be a boolean or [FilterAntialias]{@link FilterAntialias} string.\n     */\n    antialias?: FilterAntialias | boolean;\n    /**\n     * If this is set to true, the filter system will grab a snap shot of the area being rendered\n     * to and pass this into the shader. This is useful for blend modes that need to be aware of the pixels\n     * they are rendering to. Only use if you need that data, otherwise its an extra gpu copy you don't need!\n     * (default false)\n     */\n    blendRequired?: boolean;\n    /**\n     * If this is set to true, the filter system will clip filter texture into viewport\n     * This is useful for filters that applied to whole texture.\n     * (default true)\n     */\n    clipToViewport?: boolean;\n}\n\n/**\n * Filter options mixed with shader resources. A filter needs a shader and some resources to work.\n * @category filters\n * @advanced\n * @see {@link FilterOptions}\n */\nexport type FilterWithShader = FilterOptions & IShaderWithResources;\n\n/**\n * The antialiasing mode of the filter. This can be either:\n * - `on` - the filter is always antialiased regardless of the render target settings\n * - `off` - (default) the filter is never antialiased regardless of the render target settings\n * - `inherit` - the filter uses the antialias settings of the render target\n * @category filters\n * @advanced\n */\nexport type FilterAntialias = 'on' | 'off' | 'inherit';\n\n/**\n * The Filter class is the base for all filter effects used in Pixi.js\n * As it extends a shader, it requires that a glProgram is parsed in to work with WebGL and a gpuProgram for WebGPU.\n * If you don't proved one, then the filter is skipped and just rendered as if it wasn't there for that renderer.\n *\n * A filter can be applied to anything that extends Container in Pixi.js which also includes Sprites, Graphics etc.\n *\n * Its worth noting Performance-wise filters can be pretty expensive if used too much in a single scene.\n * The following happens under the hood when a filter is applied:\n *\n * .1. Break the current batch\n * <br>\n * .2. The target is measured using getGlobalBounds\n * (recursively go through all children and figure out how big the object is)\n * <br>\n * .3. Get the closest Po2 Textures from the texture pool\n * <br>\n * .4. Render the target to that texture\n * <br>\n * .5. Render that texture back to the main frame buffer as a quad using the filters program.\n * <br>\n * <br>\n * Some filters (such as blur) require multiple passes too which can result in an even bigger performance hit. So be careful!\n * Its not generally the complexity of the shader that is the bottle neck,\n * but all the framebuffer / shader switching that has to take place.\n * One filter applied to a container with many objects is MUCH faster than many filter applied to many objects.\n * @category filters\n * @advanced\n * @example\n * import { Filter } from 'pixi.js';\n *\n * const customFilter = new Filter({\n *     glProgram: new GlProgram({\n *         fragment,\n *         vertex,\n *     }),\n *     resources: {\n *         timeUniforms: {\n *             uTime: { value: 0.0, type: 'f32' },\n *         },\n *     },\n * });\n *\n * // Apply the filter\n * sprite.filters = [customFilter];\n *\n * // Update uniform\n * app.ticker.add((ticker) => {\n *     filter.resources.timeUniforms.uniforms.uTime += 0.04 * ticker.deltaTime;\n * });\n */\nexport class Filter extends Shader\n{\n    /** The default filter settings */\n    public static defaultOptions: FilterOptions = {\n        blendMode: 'normal',\n        resolution: 1,\n        padding: 0,\n        antialias: 'off',\n        blendRequired: false,\n        clipToViewport: true,\n    };\n\n    /**\n     * The padding of the filter. Some filters require extra space to breath such as a blur.\n     * Increasing this will add extra width and height to the bounds of the object that the\n     * filter is applied to.\n     * @default 0\n     */\n    public padding: number;\n\n    /**\n     * should the filter use antialiasing?\n     * @default inherit\n     */\n    public antialias: FilterAntialias;\n\n    /** If enabled is true the filter is applied, if false it will not. */\n    public enabled = true;\n\n    /**\n     * The gpu state the filter requires to render.\n     * @internal\n     */\n    public _state = State.for2d();\n\n    /**\n     * The resolution of the filter. Setting this to be lower will lower the quality but\n     * increase the performance of the filter.\n     * @default 1\n     */\n    public resolution: number | 'inherit';\n\n    /**\n     * Whether or not this filter requires the previous render texture for blending.\n     * @default false\n     */\n    public blendRequired: boolean;\n\n    /**\n     * Clip texture into viewport or not\n     * @default true\n     */\n    public clipToViewport: boolean;\n\n    /**\n     * @param options - The optional parameters of this filter.\n     */\n    constructor(options: FilterWithShader)\n    {\n        options = { ...Filter.defaultOptions, ...options };\n\n        super(options as ShaderWithResources);\n\n        this.blendMode = options.blendMode;\n        this.padding = options.padding;\n\n        // check if is boolean\n        if (typeof options.antialias === 'boolean')\n        {\n            this.antialias = options.antialias ? 'on' : 'off';\n        }\n        else\n        {\n            this.antialias = options.antialias;\n        }\n\n        this.resolution = options.resolution;\n        this.blendRequired = options.blendRequired;\n        this.clipToViewport = options.clipToViewport;\n\n        this.addResource('uTexture', 0, 1);\n    }\n\n    /**\n     * Applies the filter\n     * @param filterManager - The renderer to retrieve the filter from\n     * @param input - The input render target.\n     * @param output - The target to output to.\n     * @param clearMode - Should the output be cleared before rendering to it\n     */\n    public apply(\n        filterManager: FilterSystem,\n        input: Texture,\n        output: RenderSurface,\n        clearMode: boolean\n    ): void\n    {\n        filterManager.applyFilter(this, input, output, clearMode);\n    }\n\n    /**\n     * Get the blend mode of the filter.\n     * @default \"normal\"\n     */\n    get blendMode(): BLEND_MODES\n    {\n        return this._state.blendMode;\n    }\n\n    /** Sets the blend mode of the filter. */\n    set blendMode(value: BLEND_MODES)\n    {\n        this._state.blendMode = value;\n    }\n\n    /**\n     * A short hand function to create a filter based of a vertex and fragment shader src.\n     * @param options\n     * @returns A shiny new PixiJS filter!\n     */\n    public static from(options: FilterOptions & ShaderFromResources): Filter\n    {\n        const { gpu, gl, ...rest } = options;\n\n        let gpuProgram: GpuProgram;\n        let glProgram: GlProgram;\n\n        if (gpu)\n        {\n            gpuProgram = GpuProgram.from(gpu);\n        }\n\n        if (gl)\n        {\n            glProgram = GlProgram.from(gl);\n        }\n\n        return new Filter({\n            gpuProgram,\n            glProgram,\n            ...rest\n        });\n    }\n}\n", "import { extensions, ExtensionType } from '../extensions/Extensions';\n\nconst environments: { name: string; value: { test: () => boolean; load: () => Promise<boolean> } }[] = [];\n\nextensions.handleByNamedList(ExtensionType.Environment, environments);\n\n/**\n * Automatically detects the environment and loads the appropriate extensions.\n * @param skip - whether to skip loading the default extensions\n * @category environment\n * @advanced\n */\nexport async function loadEnvironmentExtensions(skip: boolean): Promise<void>\n{\n    if (skip) return;\n\n    for (let i = 0; i < environments.length; i++)\n    {\n        const env = environments[i];\n\n        if (env.value.test())\n        {\n            await env.value.load();\n\n            return;\n        }\n    }\n}\n\n/**\n * @param add - whether to add the default imports to the bundle\n * @deprecated since 8.1.6. Use `loadEnvironmentExtensions` instead\n * @category environment\n * @advanced\n */\nexport async function autoDetectEnvironment(add: boolean): Promise<void>\n{\n    return loadEnvironmentExtensions(!add);\n}\n", "// Cache the result to prevent running this over and over\nlet unsafeEval: boolean;\n\n/**\n * Not all platforms allow to generate function code (e.g., `new Function`).\n * this provides the platform-level detection.\n * @private\n * @returns {boolean} `true` if `new Function` is supported.\n */\nexport function unsafeEvalSupported(): boolean\n{\n    if (typeof unsafeEval === 'boolean')\n    {\n        return unsafeEval;\n    }\n\n    try\n    {\n        /* eslint-disable no-new-func */\n        const func = new Function('param1', 'param2', 'param3', 'return param1[param2] === param3;');\n        /* eslint-enable no-new-func */\n\n        unsafeEval = func({ a: 'b' }, 'a', 'b') === true;\n    }\n    catch (_e)\n    {\n        unsafeEval = false;\n    }\n\n    return unsafeEval;\n}\n", "/**\n * Constants used by the renderer for clearing the screen or render textures.\n * @category rendering\n * @advanced\n */\nexport enum CLEAR\n{\n    /** No clear operation. */\n    NONE = 0,\n    /** Clear the color buffer. */\n    COLOR = 16384,\n    /** Clear the stencil buffer. */\n    STENCIL = 1024,\n    /** Clear the depth buffer. */\n    DEPTH = 256,\n\n    /** Clear the color and depth buffers. */\n    COLOR_DEPTH = COLOR | DEPTH,\n    /** Clear the color and stencil buffers. */\n    COLOR_STENCIL = COLOR | STENCIL,\n    /** Clear the depth and stencil buffers. */\n    DEPTH_STENCIL = DEPTH | STENCIL,\n    /** Clear the color, depth, and stencil buffers. */\n    ALL = COLOR | DEPTH | STENCIL,\n\n}\n\n/**\n * Used for clearing render textures. true is the same as `ALL` false is the same as `NONE`\n * @category rendering\n * @advanced\n */\nexport type CLEAR_OR_BOOL = CLEAR | boolean;\n", "/**\n * SystemRunner is used internally by the renderers as an efficient way for systems to\n * be notified about what the renderer is up to during the rendering phase.\n *\n * ```ts\n * import { SystemRunner } from 'pixi.js';\n *\n * const myObject = {\n *     loaded: new SystemRunner('loaded')\n * }\n *\n * const listener = {\n *     loaded: function(){\n *         // thin\n *     }\n * }\n *\n * myObject.loaded.add(listener);\n *\n * myObject.loaded.emit();\n * ```\n *\n * Or for handling calling the same function on many items\n * ```ts\n * import { SystemRunner } from 'pixi.js';\n *\n * const myGame = {\n *     update: new SystemRunner('update')\n * }\n *\n * const gameObject = {\n *     update: function(time){\n *         // update my gamey state\n *     }\n * }\n *\n * myGame.update.add(gameObject);\n *\n * myGame.update.emit(time);\n * ```\n * @category rendering\n * @internal\n */\nexport class SystemRunner\n{\n    public items: any[];\n    private _name: string;\n\n    /**\n     * @param name - The function name that will be executed on the listeners added to this Runner.\n     */\n    constructor(name: string)\n    {\n        this.items = [];\n        this._name = name;\n    }\n\n    /* jsdoc/check-param-names */\n    /**\n     * Dispatch/Broadcast Runner to all listeners added to the queue.\n     * @param {...any} params - (optional) parameters to pass to each listener\n     */\n    /* jsdoc/check-param-names */\n    public emit(a0?: unknown, a1?: unknown, a2?: unknown, a3?: unknown,\n        a4?: unknown, a5?: unknown, a6?: unknown, a7?: unknown): this\n    {\n        const { name, items } = this;\n\n        for (let i = 0, len = items.length; i < len; i++)\n        {\n            items[i][name](a0, a1, a2, a3, a4, a5, a6, a7);\n        }\n\n        return this;\n    }\n\n    /**\n     * Add a listener to the Runner\n     *\n     * Runners do not need to have scope or functions passed to them.\n     * All that is required is to pass the listening object and ensure that it has contains a function that has the same name\n     * as the name provided to the Runner when it was created.\n     *\n     * Eg A listener passed to this Runner will require a 'complete' function.\n     *\n     * ```ts\n     * import { Runner } from 'pixi.js';\n     *\n     * const complete = new Runner('complete');\n     * ```\n     *\n     * The scope used will be the object itself.\n     * @param {any} item - The object that will be listening.\n     */\n    public add(item: unknown): this\n    {\n        if ((item as any)[this._name])\n        {\n            this.remove(item);\n            this.items.push(item);\n        }\n\n        return this;\n    }\n\n    /**\n     * Remove a single listener from the dispatch queue.\n     * @param {any} item - The listener that you would like to remove.\n     */\n    public remove(item: unknown): this\n    {\n        const index = this.items.indexOf(item);\n\n        if (index !== -1)\n        {\n            this.items.splice(index, 1);\n        }\n\n        return this;\n    }\n\n    /**\n     * Check to see if the listener is already in the Runner\n     * @param {any} item - The listener that you would like to check.\n     */\n    public contains(item: unknown): boolean\n    {\n        return this.items.indexOf(item) !== -1;\n    }\n\n    /** Remove all listeners from the Runner */\n    public removeAll(): this\n    {\n        this.items.length = 0;\n\n        return this;\n    }\n\n    /** Remove all references, don't use after this. */\n    public destroy(): void\n    {\n        this.removeAll();\n        this.items = null;\n        this._name = null;\n    }\n\n    /**\n     * `true` if there are no this Runner contains no listeners\n     * @readonly\n     */\n    public get empty(): boolean\n    {\n        return this.items.length === 0;\n    }\n\n    /**\n     * The name of the runner.\n     * @readonly\n     */\n    public get name(): string\n    {\n        return this._name;\n    }\n}\n", "import { Color } from '../../../../color/Color';\nimport { loadEnvironmentExtensions } from '../../../../environment/autoDetectEnvironment';\nimport { Container } from '../../../../scene/container/Container';\nimport { unsafeEvalSupported } from '../../../../utils/browser/unsafeEvalSupported';\nimport { uid } from '../../../../utils/data/uid';\nimport { deprecation, v8_0_0 } from '../../../../utils/logging/deprecation';\nimport { EventEmitter } from '../../../../utils/utils';\nimport { CLEAR } from '../../gl/const';\nimport { SystemRunner } from './SystemRunner';\n\nimport type { ColorSource, RgbaArray } from '../../../../color/Color';\nimport type { ICanvas } from '../../../../environment/canvas/ICanvas';\nimport type { Matrix } from '../../../../maths/matrix/Matrix';\nimport type { Rectangle } from '../../../../maths/shapes/Rectangle';\nimport type { TypeOrBool } from '../../../../scene/container/destroyTypes';\nimport type { CLEAR_OR_BOOL } from '../../gl/const';\nimport type { Renderer } from '../../types';\nimport type { BackgroundSystem } from '../background/BackgroundSystem';\nimport type { GenerateTextureOptions, GenerateTextureSystem } from '../extract/GenerateTextureSystem';\nimport type { PipeConstructor } from '../instructions/RenderPipe';\nimport type { RenderSurface } from '../renderTarget/RenderTargetSystem';\nimport type { Texture } from '../texture/Texture';\nimport type { ViewSystem, ViewSystemDestroyOptions } from '../view/ViewSystem';\nimport type { SharedRendererOptions } from './SharedSystems';\nimport type { System, SystemConstructor } from './System';\n\n/**\n * The configuration for the renderer.\n * This is used to define the systems and render pipes that will be used by the renderer.\n * @category rendering\n * @advanced\n */\nexport interface RendererConfig\n{\n    type: number;\n    name: string;\n    runners?: string[];\n    systems: {name: string, value: SystemConstructor}[];\n    renderPipes: {name: string, value: PipeConstructor}[];\n    renderPipeAdaptors: {name: string, value: any}[];\n}\n\n/**\n * The options for rendering a view.\n * @category rendering\n * @standard\n */\nexport interface RenderOptions extends ClearOptions\n{\n    /** The container to render. */\n    container: Container;\n    /** the transform to apply to the container. */\n    transform?: Matrix;\n}\n\n/**\n * The options for clearing the render target.\n * @category rendering\n * @advanced\n */\nexport interface ClearOptions\n{\n    /**\n     * The render target to render. if this target is a canvas and  you are using the WebGL renderer,\n     * please ensure you have set `multiView` to `true` on renderer.\n     */\n    target?: RenderSurface;\n    /** The color to clear with. */\n    clearColor?: ColorSource;\n    /** The clear mode to use. */\n    clear?: CLEAR_OR_BOOL\n}\n\n/**\n * Options for destroying the renderer.\n * This can be a boolean or an object.\n * @category rendering\n * @standard\n */\nexport type RendererDestroyOptions = TypeOrBool<ViewSystemDestroyOptions>;\n\nconst defaultRunners = [\n    'init',\n    'destroy',\n    'contextChange',\n    'resolutionChange',\n    'resetState',\n    'renderEnd',\n    'renderStart',\n    'render',\n    'update',\n    'postrender',\n    'prerender'\n] as const;\n\ntype DefaultRunners = typeof defaultRunners[number];\ntype Runners = {[key in DefaultRunners]: SystemRunner} & {\n    [K: ({} & string) | ({} & symbol)]: SystemRunner;\n};\n\n/* eslint-disable max-len */\n/**\n * The base class for a PixiJS Renderer. It contains the shared logic for all renderers.\n *\n * You should not use this class directly, but instead use {@link WebGLRenderer}\n * or {@link WebGPURenderer}.\n * Alternatively, you can also use {@link autoDetectRenderer} if you want us to\n * determine the best renderer for you.\n *\n * The renderer is composed of systems that manage specific tasks. The following systems are added by default\n * whenever you create a renderer:\n *\n *\n * | Generic Systems                      | Systems that manage functionality that all renderer types share               |\n * | ------------------------------------ | ----------------------------------------------------------------------------- |\n * | {@link ViewSystem}              | This manages the main view of the renderer usually a Canvas              |\n * | {@link BackgroundSystem}        | This manages the main views background color and alpha                   |\n * | {@link EventSystem}           | This manages UI events.                                                       |\n * | {@link AccessibilitySystem} | This manages accessibility features. Requires `import 'pixi.js/accessibility'`|\n *\n * | Core Systems                   | Provide an optimised, easy to use API to work with WebGL/WebGPU               |\n * | ------------------------------------ | ----------------------------------------------------------------------------- |\n * | {@link GlobalUniformSystem} | This manages shaders, programs that run on the GPU to calculate 'em pixels.   |\n * | {@link TextureGCSystem}     | This will automatically remove textures from the GPU if they are not used.    |\n *\n * | PixiJS High-Level Systems            | Set of specific systems designed to work with PixiJS objects                  |\n * | ------------------------------------ | ----------------------------------------------------------------------------- |\n * | {@link HelloSystem}               | Says hello, buy printing out the pixi version into the console log (along with the renderer type)       |\n * | {@link GenerateTextureSystem} | This adds the ability to generate textures from any Container       |\n * | {@link FilterSystem}          | This manages the filtering pipeline for post-processing effects.             |\n * | {@link PrepareSystem}               | This manages uploading assets to the GPU. Requires `import 'pixi.js/prepare'`|\n * | {@link ExtractSystem}               | This extracts image data from display objects.                               |\n *\n * The breadth of the API surface provided by the renderer is contained within these systems.\n * @abstract\n * @category rendering\n * @advanced\n * @property {HelloSystem} hello - HelloSystem instance.\n * @property {TextureGCSystem} textureGC - TextureGCSystem instance.\n * @property {FilterSystem} filter - FilterSystem instance.\n * @property {GlobalUniformSystem} globalUniforms - GlobalUniformSystem instance.\n * @property {TextureSystem} texture - TextureSystem instance.\n * @property {EventSystem} events - EventSystem instance.\n * @property {ExtractSystem} extract - ExtractSystem instance. Requires `import 'pixi.js/extract'`.\n * @property {PrepareSystem} prepare - PrepareSystem instance. Requires `import 'pixi.js/prepare'`.\n * @property {AccessibilitySystem} accessibility - AccessibilitySystem instance. Requires `import 'pixi.js/accessibility'`.\n */\nexport class AbstractRenderer<\n    PIPES, OPTIONS extends SharedRendererOptions, CANVAS extends ICanvas = HTMLCanvasElement\n> extends EventEmitter<{resize: [screenWidth: number, screenHeight: number, resolution: number]}>\n{\n    /** The default options for the renderer. */\n    public static defaultOptions = {\n        /**\n         * Default resolution / device pixel ratio of the renderer.\n         * @default 1\n         */\n        resolution: 1,\n        /**\n         * Should the `failIfMajorPerformanceCaveat` flag be enabled as a context option used in the `isWebGLSupported`\n         * function. If set to true, a WebGL renderer can fail to be created if the browser thinks there could be\n         * performance issues when using WebGL.\n         *\n         * In PixiJS v6 this has changed from true to false by default, to allow WebGL to work in as many\n         * scenarios as possible. However, some users may have a poor experience, for example, if a user has a gpu or\n         * driver version blacklisted by the\n         * browser.\n         *\n         * If your application requires high performance rendering, you may wish to set this to false.\n         * We recommend one of two options if you decide to set this flag to false:\n         *\n         * 1: Use the Canvas renderer as a fallback in case high performance WebGL is\n         *    not supported.\n         *\n         * 2: Call `isWebGLSupported` (which if found in the utils package) in your code before attempting to create a\n         *    PixiJS renderer, and show an error message to the user if the function returns false, explaining that their\n         *    device & browser combination does not support high performance WebGL.\n         *    This is a much better strategy than trying to create a PixiJS renderer and finding it then fails.\n         * @default false\n         */\n        failIfMajorPerformanceCaveat: false,\n        /**\n         * Should round pixels be forced when rendering?\n         * @default false\n         */\n        roundPixels: false\n    };\n\n    /** @internal */\n    public readonly type: number;\n    /** The name of the renderer. */\n    public readonly name: string;\n\n    /** @internal */\n    public readonly uid = uid('renderer');\n\n    /** @internal */\n    public _roundPixels: 0 | 1;\n\n    /** @internal */\n    public readonly runners: Runners = Object.create(null) as Runners;\n    /** @internal */\n    public readonly renderPipes = Object.create(null) as PIPES;\n    /** The view system manages the main canvas that is attached to the DOM */\n    public view!: ViewSystem;\n    /** The background system manages the background color and alpha of the main view. */\n    public background: BackgroundSystem;\n    /** System that manages the generation of textures from the renderer */\n    public textureGenerator: GenerateTextureSystem;\n\n    protected _initOptions: OPTIONS = {} as OPTIONS;\n    protected config: RendererConfig;\n\n    private _systemsHash: Record<string, System> = Object.create(null);\n    private _lastObjectRendered: Container;\n\n    /**\n     * Set up a system with a collection of SystemClasses and runners.\n     * Systems are attached dynamically to this class when added.\n     * @param config - the config for the system manager\n     */\n    constructor(config: RendererConfig)\n    {\n        super();\n        this.type = config.type;\n        this.name = config.name;\n        this.config = config;\n\n        const combinedRunners = [...defaultRunners, ...(this.config.runners ?? [])];\n\n        this._addRunners(...combinedRunners);\n        // Validation check that this environment support `new Function`\n        this._unsafeEvalCheck();\n    }\n\n    /**\n     * Initialize the renderer.\n     * @param options - The options to use to create the renderer.\n     */\n    public async init(options: Partial<OPTIONS> = {})\n    {\n        const skip = options.skipExtensionImports === true ? true : options.manageImports === false;\n\n        await loadEnvironmentExtensions(skip);\n\n        this._addSystems(this.config.systems);\n        this._addPipes(this.config.renderPipes, this.config.renderPipeAdaptors);\n\n        // loop through all systems...\n        for (const systemName in this._systemsHash)\n        {\n            const system = this._systemsHash[systemName];\n\n            const defaultSystemOptions = (system.constructor as any).defaultOptions;\n\n            options = { ...defaultSystemOptions, ...options };\n        }\n\n        options = { ...AbstractRenderer.defaultOptions, ...options };\n        this._roundPixels = options.roundPixels ? 1 : 0;\n\n        // await emits..\n        for (let i = 0; i < this.runners.init.items.length; i++)\n        {\n            await this.runners.init.items[i].init(options);\n        }\n\n        // store options\n        this._initOptions = options as OPTIONS;\n    }\n\n    /**\n     * Renders the object to its view.\n     * @param options - The options to render with.\n     * @param options.container - The container to render.\n     * @param [options.target] - The target to render to.\n     */\n    public render(options: RenderOptions | Container): void;\n    /** @deprecated since 8.0.0 */\n    public render(container: Container, options: {renderTexture: any}): void;\n    public render(args: RenderOptions | Container, deprecated?: {renderTexture: any}): void\n    {\n        let options = args;\n\n        if (options instanceof Container)\n        {\n            options = { container: options };\n\n            if (deprecated)\n            {\n                // #if _DEBUG\n                deprecation(v8_0_0, 'passing a second argument is deprecated, please use render options instead');\n                // #endif\n\n                options.target = deprecated.renderTexture;\n            }\n        }\n\n        options.target ||= this.view.renderTarget;\n\n        // TODO: we should eventually fix events so that it can handle multiple canvas elements\n        if (options.target === this.view.renderTarget)\n        {\n            // TODO get rid of this\n            this._lastObjectRendered = options.container;\n\n            options.clearColor ??= this.background.colorRgba;\n            options.clear ??= this.background.clearBeforeRender;\n        }\n\n        if (options.clearColor)\n        {\n            const isRGBAArray = Array.isArray(options.clearColor) && options.clearColor.length === 4;\n\n            options.clearColor = isRGBAArray ? options.clearColor : Color.shared.setValue(options.clearColor).toArray();\n        }\n\n        if (!options.transform)\n        {\n            options.container.updateLocalTransform();\n            options.transform = options.container.localTransform;\n        }\n\n        // lets ensure this object is a render group so we can render it!\n        // the renderer only likes to render - render groups.\n        options.container.enableRenderGroup();\n\n        this.runners.prerender.emit(options);\n        this.runners.renderStart.emit(options);\n        this.runners.render.emit(options);\n        this.runners.renderEnd.emit(options);\n        this.runners.postrender.emit(options);\n    }\n\n    /**\n     * Resizes the WebGL view to the specified width and height.\n     * @param desiredScreenWidth - The desired width of the screen.\n     * @param desiredScreenHeight - The desired height of the screen.\n     * @param resolution - The resolution / device pixel ratio of the renderer.\n     */\n    public resize(desiredScreenWidth: number, desiredScreenHeight: number, resolution?: number): void\n    {\n        const previousResolution = this.view.resolution;\n\n        this.view.resize(desiredScreenWidth, desiredScreenHeight, resolution);\n        this.emit('resize', this.view.screen.width, this.view.screen.height, this.view.resolution);\n        if (resolution !== undefined && resolution !== previousResolution)\n        {\n            this.runners.resolutionChange.emit(resolution);\n        }\n    }\n\n    /**\n     * Clears the render target.\n     * @param options - The options to use when clearing the render target.\n     * @param options.target - The render target to clear.\n     * @param options.clearColor - The color to clear with.\n     * @param options.clear - The clear mode to use.\n     * @advanced\n     */\n    public clear(options: ClearOptions = {}): void\n    {\n        // override!\n        const renderer = this as unknown as Renderer;\n\n        options.target ||= renderer.renderTarget.renderTarget;\n        options.clearColor ||= this.background.colorRgba;\n        options.clear ??= CLEAR.ALL;\n\n        const { clear, clearColor, target } = options;\n\n        Color.shared.setValue(clearColor ?? this.background.colorRgba);\n\n        renderer.renderTarget.clear(target, clear, Color.shared.toArray() as RgbaArray);\n    }\n\n    /** The resolution / device pixel ratio of the renderer. */\n    get resolution(): number\n    {\n        return this.view.resolution;\n    }\n\n    set resolution(value: number)\n    {\n        this.view.resolution = value;\n        this.runners.resolutionChange.emit(value);\n    }\n\n    /**\n     * Same as view.width, actual number of pixels in the canvas by horizontal.\n     * @type {number}\n     * @readonly\n     * @default 800\n     */\n    get width(): number\n    {\n        return this.view.texture.frame.width;\n    }\n\n    /**\n     * Same as view.height, actual number of pixels in the canvas by vertical.\n     * @default 600\n     */\n    get height(): number\n    {\n        return this.view.texture.frame.height;\n    }\n\n    // NOTE: this was `view` in v7\n    /**\n     * The canvas element that everything is drawn to.\n     * @type {environment.ICanvas}\n     */\n    get canvas(): CANVAS\n    {\n        return this.view.canvas as CANVAS;\n    }\n\n    /**\n     * the last object rendered by the renderer. Useful for other plugins like interaction managers\n     * @readonly\n     */\n    get lastObjectRendered(): Container\n    {\n        return this._lastObjectRendered;\n    }\n\n    /**\n     * Flag if we are rendering to the screen vs renderTexture\n     * @readonly\n     * @default true\n     */\n    get renderingToScreen(): boolean\n    {\n        const renderer = this as unknown as Renderer;\n\n        return renderer.renderTarget.renderingToScreen;\n    }\n\n    /**\n     * Measurements of the screen. (0, 0, screenWidth, screenHeight).\n     *\n     * Its safe to use as filterArea or hitArea for the whole stage.\n     */\n    get screen(): Rectangle\n    {\n        return this.view.screen;\n    }\n\n    /**\n     * Create a bunch of runners based of a collection of ids\n     * @param runnerIds - the runner ids to add\n     */\n    private _addRunners(...runnerIds: string[]): void\n    {\n        runnerIds.forEach((runnerId) =>\n        {\n            this.runners[runnerId] = new SystemRunner(runnerId);\n        });\n    }\n\n    private _addSystems(systems: RendererConfig['systems']): void\n    {\n        let i: keyof typeof systems;\n\n        for (i in systems)\n        {\n            const val = systems[i];\n\n            this._addSystem(val.value, val.name);\n        }\n    }\n\n    /**\n     * Add a new system to the renderer.\n     * @param ClassRef - Class reference\n     * @param name - Property name for system, if not specified\n     *        will use a static `name` property on the class itself. This\n     *        name will be assigned as s property on the Renderer so make\n     *        sure it doesn't collide with properties on Renderer.\n     * @returns Return instance of renderer\n     */\n    private _addSystem(ClassRef: SystemConstructor, name: string): this\n    {\n        const system = new ClassRef(this as unknown as Renderer);\n\n        if ((this as any)[name])\n        {\n            throw new Error(`Whoops! The name \"${name}\" is already in use`);\n        }\n\n        (this as any)[name] = system;\n\n        this._systemsHash[name] = system;\n\n        for (const i in this.runners)\n        {\n            this.runners[i].add(system);\n        }\n\n        return this;\n    }\n\n    private _addPipes(pipes: RendererConfig['renderPipes'], pipeAdaptors: RendererConfig['renderPipeAdaptors']): void\n    {\n        const adaptors = pipeAdaptors.reduce((acc, adaptor) =>\n        {\n            acc[adaptor.name] = adaptor.value;\n\n            return acc;\n        }, {} as Record<string, any>);\n\n        pipes.forEach((pipe) =>\n        {\n            const PipeClass = pipe.value;\n            const name = pipe.name;\n\n            const Adaptor = adaptors[name];\n\n            // sorry typescript..\n            (this.renderPipes as any)[name] = new PipeClass(\n                this as unknown as Renderer,\n                Adaptor ? new Adaptor() : null\n            );\n        });\n    }\n\n    public destroy(options: RendererDestroyOptions = false): void\n    {\n        this.runners.destroy.items.reverse();\n        this.runners.destroy.emit(options);\n\n        // destroy all runners\n        Object.values(this.runners).forEach((runner) =>\n        {\n            runner.destroy();\n        });\n\n        this._systemsHash = null;\n\n        // destroy all pipes\n        (this.renderPipes as null) = null;\n    }\n\n    /**\n     * Generate a texture from a container.\n     * @param options - options or container target to use when generating the texture\n     * @returns a texture\n     */\n    public generateTexture(options: GenerateTextureOptions | Container): Texture\n    {\n        return this.textureGenerator.generateTexture(options);\n    }\n\n    /**\n     * Whether the renderer will round coordinates to whole pixels when rendering.\n     * Can be overridden on a per scene item basis.\n     */\n    get roundPixels(): boolean\n    {\n        return !!this._roundPixels;\n    }\n\n    /**\n     * Overridable function by `pixi.js/unsafe-eval` to silence\n     * throwing an error if platform doesn't support unsafe-evals.\n     * @private\n     * @ignore\n     */\n    public _unsafeEvalCheck(): void\n    {\n        if (!unsafeEvalSupported())\n        {\n            throw new Error('Current environment does not allow unsafe-eval, '\n               + 'please use pixi.js/unsafe-eval module to enable support.');\n        }\n    }\n    /**\n     * Resets the rendering state of the renderer.\n     * This is useful when you want to use the WebGL context directly and need to ensure PixiJS's internal state\n     * stays synchronized. When modifying the WebGL context state externally, calling this method before the next Pixi\n     * render will reset all internal caches and ensure it executes correctly.\n     *\n     * This is particularly useful when combining PixiJS with other rendering engines like Three.js:\n     * ```js\n     * // Reset Three.js state\n     * threeRenderer.resetState();\n     *\n     * // Render a Three.js scene\n     * threeRenderer.render(threeScene, threeCamera);\n     *\n     * // Reset PixiJS state since Three.js modified the WebGL context\n     * pixiRenderer.resetState();\n     *\n     * // Now render Pixi content\n     * pixiRenderer.render(pixiScene);\n     * ```\n     * @advanced\n     */\n    public resetState(): void\n    {\n        this.runners.resetState.emit();\n    }\n}\n", "/** @internal */\nexport const textureBit = {\n    name: 'texture-bit',\n    vertex: {\n        header: /* wgsl */`\n\n        struct TextureUniforms {\n            uTextureMatrix:mat3x3<f32>,\n        }\n\n        @group(2) @binding(2) var<uniform> textureUniforms : TextureUniforms;\n        `,\n        main: /* wgsl */`\n            uv = (textureUniforms.uTextureMatrix * vec3(uv, 1.0)).xy;\n        `\n    },\n    fragment: {\n        header: /* wgsl */`\n            @group(2) @binding(0) var uTexture: texture_2d<f32>;\n            @group(2) @binding(1) var uSampler: sampler;\n\n\n        `,\n        main: /* wgsl */`\n            outColor = textureSample(uTexture, uSampler, vUV);\n        `\n    }\n};\n\n/** @internal */\nexport const textureBitGl = {\n    name: 'texture-bit',\n    vertex: {\n        header: /* glsl */`\n            uniform mat3 uTextureMatrix;\n        `,\n        main: /* glsl */`\n            uv = (uTextureMatrix * vec3(uv, 1.0)).xy;\n        `\n    },\n    fragment: {\n        header: /* glsl */`\n        uniform sampler2D uTexture;\n\n\n        `,\n        main: /* glsl */`\n            outColor = texture(uTexture, vUV);\n        `\n    }\n};\n\n", "import { ExtensionType } from '../../extensions/Extensions';\n\nimport type { InstructionSet } from '../../rendering/renderers/shared/instructions/InstructionSet';\nimport type { InstructionPipe, RenderPipe } from '../../rendering/renderers/shared/instructions/RenderPipe';\nimport type { Renderer } from '../../rendering/renderers/types';\nimport type { RenderContainer } from './RenderContainer';\n\n/**\n * The CustomRenderPipe is a render pipe that allows for custom rendering logic for your renderable objects.\n * @example\n * import { RenderContainer } from 'pixi.js';\n *\n * const renderContainer = new RenderContainer(\n * (renderer) =>  {\n *     renderer.clear({\n *       clearColor: 'green', // clear the screen to green when rendering this item\n *     });\n * })\n * @category rendering\n * @internal\n */\nexport class CustomRenderPipe implements InstructionPipe<RenderContainer>, RenderPipe<RenderContainer>\n{\n    public static extension = {\n        type: [\n            ExtensionType.WebGLPipes,\n            ExtensionType.WebGPUPipes,\n            ExtensionType.CanvasPipes,\n        ],\n        name: 'customR<PERSON>',\n    } as const;\n\n    private _renderer: Renderer;\n\n    constructor(renderer: Renderer)\n    {\n        this._renderer = renderer;\n    }\n\n    public updateRenderable() { /** empty */ }\n    public destroyRenderable() { /** empty */ }\n    public validateRenderable() { return false; }\n\n    public addRenderable(container: RenderContainer, instructionSet: InstructionSet): void\n    {\n        this._renderer.renderPipes.batch.break(instructionSet);\n\n        instructionSet.add(container);\n    }\n\n    public execute(container: RenderContainer)\n    {\n        if (!container.isRenderable) return;\n\n        container.render(this._renderer);\n    }\n\n    public destroy(): void\n    {\n        this._renderer = null;\n    }\n}\n", "import type { InstructionPipe } from '../../../rendering/renderers/shared/instructions/RenderPipe';\nimport type { RenderPipes } from '../../../rendering/renderers/types';\nimport type { RenderGroup } from '../RenderGroup';\n\n/**\n * @param renderGroup\n * @param renderer\n * @internal\n */\nexport function executeInstructions(renderGroup: RenderGroup, renderer: RenderPipes)\n{\n    const instructionSet = renderGroup.instructionSet;\n    const instructions = instructionSet.instructions;\n\n    for (let i = 0; i < instructionSet.instructionSize; i++)\n    {\n        const instruction = instructions[i];\n\n        (renderer[instruction.renderPipeId as keyof RenderPipes] as InstructionPipe<any>).execute(instruction);\n    }\n}\n", "import { ExtensionType } from '../../extensions/Extensions';\nimport { Matrix } from '../../maths/matrix/Matrix';\nimport { BigPool } from '../../utils/pool/PoolGroup';\nimport { BatchableSprite } from '../sprite/BatchableSprite';\nimport { executeInstructions } from './utils/executeInstructions';\n\nimport type { InstructionSet } from '../../rendering/renderers/shared/instructions/InstructionSet';\nimport type { InstructionPipe } from '../../rendering/renderers/shared/instructions/RenderPipe';\nimport type { Renderer } from '../../rendering/renderers/types';\nimport type { RenderGroup } from './RenderGroup';\n\nconst tempMatrix = new Matrix();\n\n/**\n * The RenderGroupPipe is a render pipe for rendering RenderGroups.\n * @internal\n */\nexport class RenderGroupPipe implements InstructionPipe<RenderGroup>\n{\n    public static extension = {\n        type: [\n            ExtensionType.WebGLPipes,\n            ExtensionType.WebGPUPipes,\n            ExtensionType.CanvasPipes,\n        ],\n        name: 'renderGroup',\n    } as const;\n\n    private _renderer: Renderer;\n\n    constructor(renderer: Renderer)\n    {\n        this._renderer = renderer;\n    }\n\n    public addRenderGroup(renderGroup: RenderGroup, instructionSet: InstructionSet): void\n    {\n        if (renderGroup.isCachedAsTexture)\n        {\n            this._addRenderableCacheAsTexture(renderGroup, instructionSet);\n        }\n        else\n        {\n            this._addRenderableDirect(renderGroup, instructionSet);\n        }\n    }\n\n    public execute(renderGroup: RenderGroup)\n    {\n        if (!renderGroup.isRenderable) return;\n\n        if (renderGroup.isCachedAsTexture)\n        {\n            this._executeCacheAsTexture(renderGroup);\n        }\n        else\n        {\n            this._executeDirect(renderGroup);\n        }\n    }\n\n    public destroy(): void\n    {\n        this._renderer = null;\n    }\n\n    private _addRenderableDirect(renderGroup: RenderGroup, instructionSet: InstructionSet): void\n    {\n        this._renderer.renderPipes.batch.break(instructionSet);\n\n        if (renderGroup._batchableRenderGroup)\n        {\n            BigPool.return(renderGroup._batchableRenderGroup);\n            renderGroup._batchableRenderGroup = null;\n        }\n\n        instructionSet.add(renderGroup);\n    }\n\n    private _addRenderableCacheAsTexture(renderGroup: RenderGroup, instructionSet: InstructionSet): void\n    {\n        const batchableRenderGroup = renderGroup._batchableRenderGroup ??= BigPool.get(BatchableSprite);\n\n        batchableRenderGroup.renderable = renderGroup.root;\n        batchableRenderGroup.transform = renderGroup.root.relativeGroupTransform;\n        batchableRenderGroup.texture = renderGroup.texture;\n        batchableRenderGroup.bounds = renderGroup._textureBounds;\n\n        instructionSet.add(renderGroup);\n        this._renderer.renderPipes.batch.addToBatch(batchableRenderGroup, instructionSet);\n    }\n\n    private _executeCacheAsTexture(renderGroup: RenderGroup): void\n    {\n        if (renderGroup.textureNeedsUpdate)\n        {\n            renderGroup.textureNeedsUpdate = false;\n\n            const worldTransformMatrix = tempMatrix\n                .identity()\n                .translate(\n                    -renderGroup._textureBounds.x,\n                    -renderGroup._textureBounds.y\n                );\n\n            this._renderer.renderTarget.push(renderGroup.texture, true, null, renderGroup.texture.frame);\n\n            this._renderer.globalUniforms.push({\n                worldTransformMatrix,\n                worldColor: 0xFFFFFFFF,\n            });\n\n            executeInstructions(renderGroup, this._renderer.renderPipes);\n\n            this._renderer.renderTarget.finishRenderPass();\n\n            this._renderer.renderTarget.pop();\n            this._renderer.globalUniforms.pop();\n        }\n\n        renderGroup._batchableRenderGroup._batcher.updateElement(renderGroup._batchableRenderGroup);\n        renderGroup._batchableRenderGroup._batcher.geometry.buffers[0].update();\n    }\n\n    private _executeDirect(renderGroup: RenderGroup): void\n    {\n        this._renderer.globalUniforms.push({\n            worldTransformMatrix: renderGroup.inverseParentTextureTransform,\n            worldColor: renderGroup.worldColorAlpha,\n        });\n\n        executeInstructions(renderGroup, this._renderer.renderPipes);\n\n        this._renderer.globalUniforms.pop();\n    }\n}\n", "/**\n * nulls all slots in an array from a certain index.\n * assume that when a null item is hit, the rest are also null.\n * Which will be the case for where this is used!\n * @param list - the array to clean\n * @param index - the index to start from\n * @category utils\n * @internal\n */\nexport function clearList(list: Array<unknown>, index?: number)\n{\n    index ||= 0;\n\n    for (let j = index; j < list.length; j++)\n    {\n        if (list[j])\n        {\n            list[j] = null;\n        }\n        else\n        {\n            break;\n        }\n    }\n}\n", "import { Container, UPDATE_BLEND, UPDATE_COLOR, UPDATE_VISIBLE } from '../Container';\nimport { clearList } from './clearList';\nimport { multiplyColors } from './multiplyColors';\n\nimport type { ViewContainer } from '../../view/ViewContainer';\nimport type { RenderGroup } from '../RenderGroup';\n\nconst tempContainer = new Container();\nconst UPDATE_BLEND_COLOR_VISIBLE = UPDATE_VISIBLE | UPDATE_COLOR | UPDATE_BLEND;\n\n/**\n * @param renderGroup\n * @param updateChildRenderGroups\n * @internal\n */\nexport function updateRenderGroupTransforms(renderGroup: RenderGroup, updateChildRenderGroups = false)\n{\n    updateRenderGroupTransform(renderGroup);\n\n    const childrenToUpdate = renderGroup.childrenToUpdate;\n\n    const updateTick = renderGroup.updateTick++;\n\n    for (const j in childrenToUpdate)\n    {\n        const renderGroupDepth = Number(j);\n\n        const childrenAtDepth = childrenToUpdate[j];\n\n        const list = childrenAtDepth.list;\n        const index = childrenAtDepth.index;\n\n        for (let i = 0; i < index; i++)\n        {\n            const child = list[i];\n\n            // check that these things match our layer and depth - if the renderGroup does not match,\n            // the child has been re-parented into another rendergroup since it asked to be updated so we can ignore it here\n            // secondly if the relativeRenderGroupDepth has changed, then the it means it will have been nested at a\n            // different different level in the render group - so we can wait for the update that does in fact match\n            if (child.parentRenderGroup === renderGroup && child.relativeRenderGroupDepth === renderGroupDepth)\n            {\n                updateTransformAndChildren(child, updateTick, 0);\n            }\n        }\n\n        clearList(list, index);\n\n        childrenAtDepth.index = 0;\n    }\n\n    if (updateChildRenderGroups)\n    {\n        for (let i = 0; i < renderGroup.renderGroupChildren.length; i++)\n        {\n            updateRenderGroupTransforms(renderGroup.renderGroupChildren[i], updateChildRenderGroups);\n        }\n    }\n}\n\n/**\n * @param renderGroup\n * @internal\n */\nexport function updateRenderGroupTransform(renderGroup: RenderGroup)\n{\n    const root = renderGroup.root;\n\n    let worldAlpha;\n\n    if (renderGroup.renderGroupParent)\n    {\n        const renderGroupParent = renderGroup.renderGroupParent;\n\n        renderGroup.worldTransform.appendFrom(\n            root.relativeGroupTransform,\n            renderGroupParent.worldTransform,\n        );\n\n        renderGroup.worldColor = multiplyColors(\n            root.groupColor,\n            renderGroupParent.worldColor,\n        );\n\n        worldAlpha = root.groupAlpha * renderGroupParent.worldAlpha;\n    }\n    else\n    {\n        renderGroup.worldTransform.copyFrom(root.localTransform);\n        renderGroup.worldColor = root.localColor;\n        worldAlpha = root.localAlpha;\n    }\n\n    // eslint-disable-next-line no-nested-ternary\n    worldAlpha = worldAlpha < 0 ? 0 : (worldAlpha > 1 ? 1 : worldAlpha);\n    renderGroup.worldAlpha = worldAlpha;\n\n    renderGroup.worldColorAlpha = renderGroup.worldColor\n            + (((worldAlpha * 255) | 0) << 24);\n}\n\n/**\n * @param container\n * @param updateTick\n * @param updateFlags\n * @internal\n */\nexport function updateTransformAndChildren(container: Container, updateTick: number, updateFlags: number)\n{\n    if (updateTick === container.updateTick) return;\n    container.updateTick = updateTick;\n\n    container.didChange = false;\n\n    const localTransform = container.localTransform;\n\n    container.updateLocalTransform();\n\n    const parent = container.parent;\n\n    if ((parent && !parent.renderGroup))\n    {\n        updateFlags |= container._updateFlags;\n\n        container.relativeGroupTransform.appendFrom(\n            localTransform,\n            parent.relativeGroupTransform,\n        );\n\n        if (updateFlags & UPDATE_BLEND_COLOR_VISIBLE)\n        {\n            updateColorBlendVisibility(container, parent, updateFlags);\n        }\n    }\n    else\n    {\n        updateFlags = container._updateFlags;\n\n        container.relativeGroupTransform.copyFrom(localTransform);\n\n        if (updateFlags & UPDATE_BLEND_COLOR_VISIBLE)\n        {\n            updateColorBlendVisibility(container, tempContainer, updateFlags);\n        }\n    }\n\n    // don't update children if its a layer..\n    if (!container.renderGroup)\n    {\n        const children = container.children;\n        const length = children.length;\n\n        for (let i = 0; i < length; i++)\n        {\n            updateTransformAndChildren(children[i], updateTick, updateFlags);\n        }\n\n        const renderGroup = container.parentRenderGroup;\n        const renderable = container as ViewContainer;\n\n        if (renderable.renderPipeId && !renderGroup.structureDidChange)\n        {\n            renderGroup.updateRenderable(renderable);\n        }\n    }\n}\n\nfunction updateColorBlendVisibility(\n    container: Container,\n    parent: Container,\n    updateFlags: number,\n): void\n{\n    if (updateFlags & UPDATE_COLOR)\n    {\n        container.groupColor = multiplyColors(\n            container.localColor,\n            parent.groupColor\n        );\n\n        let groupAlpha = container.localAlpha * parent.groupAlpha;\n\n        // eslint-disable-next-line no-nested-ternary\n        groupAlpha = groupAlpha < 0 ? 0 : (groupAlpha > 1 ? 1 : groupAlpha);\n\n        container.groupAlpha = groupAlpha;\n        container.groupColorAlpha = container.groupColor + (((groupAlpha * 255) | 0) << 24);\n    }\n\n    if (updateFlags & UPDATE_BLEND)\n    {\n        container.groupBlendMode = container.localBlendMode === 'inherit' ? parent.groupBlendMode : container.localBlendMode;\n    }\n\n    if (updateFlags & UPDATE_VISIBLE)\n    {\n        container.globalDisplayStatus = container.localDisplayStatus & parent.globalDisplayStatus;\n    }\n\n    container._updateFlags = 0;\n}\n\n", "import type { RenderPipe } from '../../../rendering/renderers/shared/instructions/RenderPipe';\nimport type { RenderPipes } from '../../../rendering/renderers/types';\nimport type { RenderGroup } from '../RenderGroup';\n\n/**\n * @param renderGroup\n * @param renderPipes\n * @internal\n */\nexport function validateRenderables(renderGroup: RenderGroup, renderPipes: RenderPipes): boolean\n{\n    const { list, index } = renderGroup.childrenRenderablesToUpdate;\n\n    let rebuildRequired = false;\n\n    for (let i = 0; i < index; i++)\n    {\n        const container = list[i];\n\n        // note to self: there is no need to check if container.parentRenderGroup || !container.renderGroup\n        // exist here, as this function is only called if the structure did NOT change\n        // which means they have to be valid if this function is called\n\n        const renderable = container;\n        const pipe = renderPipes[renderable.renderPipeId as keyof RenderPipes] as RenderPipe<any>;\n\n        rebuildRequired = pipe.validateRenderable(container);\n\n        if (rebuildRequired)\n        {\n            break;\n        }\n    }\n\n    renderGroup.structureDidChange = rebuildRequired;\n\n    return rebuildRequired;\n}\n", "import { ExtensionType } from '../../extensions/Extensions';\nimport { Matrix } from '../../maths/matrix/Matrix';\nimport { TexturePool } from '../../rendering/renderers/shared/texture/TexturePool';\nimport { Bounds } from './bounds/Bounds';\nimport { clearList } from './utils/clearList';\nimport { executeInstructions } from './utils/executeInstructions';\nimport { updateRenderGroupTransforms } from './utils/updateRenderGroupTransforms';\nimport { validateRenderables } from './utils/validateRenderables';\n\nimport type { WebGPURenderer } from '../../rendering/renderers/gpu/WebGPURenderer';\nimport type { System } from '../../rendering/renderers/shared/system/System';\nimport type { Renderer, RenderPipes } from '../../rendering/renderers/types';\nimport type { ViewContainer } from '../view/ViewContainer';\nimport type { Container } from './Container';\nimport type { RenderGroup } from './RenderGroup';\n\nconst tempMatrix = new Matrix();\n\n/**\n * The view system manages the main canvas that is attached to the DOM.\n * This main role is to deal with how the holding the view reference and dealing with how it is resized.\n * @category rendering\n * @internal\n */\nexport class RenderGroupSystem implements System\n{\n    /** @ignore */\n    public static extension = {\n        type: [\n            ExtensionType.WebGLSystem,\n            ExtensionType.WebGPUSystem,\n            ExtensionType.CanvasSystem,\n        ],\n        name: 'renderGroup',\n    } as const;\n\n    private readonly _renderer: Renderer;\n\n    constructor(renderer: Renderer)\n    {\n        this._renderer = renderer;\n    }\n\n    protected render({ container, transform }: {container: Container, transform: Matrix}): void\n    {\n        // we need to save the parent and renderGroupParent, so we can restore them later\n        const parent = container.parent;\n        const renderGroupParent = container.renderGroup.renderGroupParent;\n\n        // we set the transforms and parents to null, so we can render the container without any transforms\n        container.parent = null;\n        container.renderGroup.renderGroupParent = null;\n\n        const renderer = this._renderer;\n\n        // collect all the renderGroups in the scene and then render them one by one..\n        let originalLocalTransform: Matrix = tempMatrix;\n\n        if (transform)\n        {\n            originalLocalTransform = originalLocalTransform.copyFrom(container.renderGroup.localTransform);\n            container.renderGroup.localTransform.copyFrom(transform);\n        }\n\n        //  this._assignTop(container.renderGroup, null);\n        const renderPipes = (renderer as WebGPURenderer).renderPipes;\n\n        this._updateCachedRenderGroups(container.renderGroup, null);\n\n        this._updateRenderGroups(container.renderGroup);\n\n        renderer.globalUniforms.start({\n            worldTransformMatrix: transform ? container.renderGroup.localTransform : container.renderGroup.worldTransform,\n            worldColor: container.renderGroup.worldColorAlpha,\n        });\n\n        executeInstructions(container.renderGroup, renderPipes);\n\n        // TODO need to add some events / runners for things like this to hook up to\n        if (renderPipes.uniformBatch)\n        {\n            renderPipes.uniformBatch.renderEnd();\n        }\n\n        // now return the transforms back to normal..\n        if (transform)\n        {\n            container.renderGroup.localTransform.copyFrom(originalLocalTransform);\n        }\n\n        container.parent = parent;\n        container.renderGroup.renderGroupParent = renderGroupParent;\n    }\n\n    public destroy()\n    {\n        (this._renderer as null) = null;\n    }\n\n    private _updateCachedRenderGroups(renderGroup: RenderGroup, closestCacheAsTexture: RenderGroup | null): void\n    {\n        if (renderGroup.isCachedAsTexture)\n        {\n            // early out as nothing further needs to be updated!\n            if (!renderGroup.updateCacheTexture) return;\n\n            closestCacheAsTexture = renderGroup;\n        }\n\n        renderGroup._parentCacheAsTextureRenderGroup = closestCacheAsTexture;\n\n        // now check the cacheAsTexture stuff...\n        for (let i = renderGroup.renderGroupChildren.length - 1; i >= 0; i--)\n        {\n            this._updateCachedRenderGroups(renderGroup.renderGroupChildren[i], closestCacheAsTexture);\n        }\n\n        renderGroup.invalidateMatrices();\n\n        if (renderGroup.isCachedAsTexture)\n        {\n            if (renderGroup.textureNeedsUpdate)\n            {\n                // lets get the texture ready for rendering\n                // but the rendering will not happen until the renderGroup is rendered!\n                // We also want to know now, what the bounds of the texture will be.\n                // as if the texture changes, we need to invalidate the parent render group!\n                const bounds = renderGroup.root.getLocalBounds();\n\n                bounds.ceil();\n\n                const lastTexture = renderGroup.texture;\n\n                if (renderGroup.texture)\n                {\n                    TexturePool.returnTexture(renderGroup.texture);\n                }\n\n                const renderer = this._renderer;\n                const resolution = renderGroup.textureOptions.resolution || renderer.view.resolution;\n                const antialias = renderGroup.textureOptions.antialias ?? renderer.view.antialias;\n\n                renderGroup.texture = TexturePool.getOptimalTexture(\n                    bounds.width,\n                    bounds.height,\n                    resolution,\n                    antialias\n                );\n\n                renderGroup._textureBounds ||= new Bounds();\n                renderGroup._textureBounds.copyFrom(bounds);\n\n                if (lastTexture !== renderGroup.texture)\n                {\n                    if (renderGroup.renderGroupParent)\n                    {\n                        renderGroup.renderGroupParent.structureDidChange = true;\n                    }\n                }\n            }\n        }\n        else if (renderGroup.texture)\n        {\n            TexturePool.returnTexture(renderGroup.texture);\n            renderGroup.texture = null;\n        }\n    }\n\n    private _updateRenderGroups(renderGroup: RenderGroup): void\n    {\n        const renderer = this._renderer;\n        const renderPipes = renderer.renderPipes;\n\n        renderGroup.runOnRender(renderer);\n\n        renderGroup.instructionSet.renderPipes = renderPipes;\n\n        if (!renderGroup.structureDidChange)\n        {\n            // phase 1 - validate all the renderables\n            validateRenderables(renderGroup, renderPipes);\n        }\n        else\n        {\n            clearList(renderGroup.childrenRenderablesToUpdate.list, 0);\n        }\n\n        // phase 2 - update all the transforms\n        // including updating the renderables..\n        updateRenderGroupTransforms(renderGroup);\n\n        if (renderGroup.structureDidChange)\n        {\n            renderGroup.structureDidChange = false;\n\n            // build the renderables\n            this._buildInstructions(renderGroup, renderer);\n        }\n        else\n        {\n            // update remaining renderables\n            this._updateRenderables(renderGroup);\n        }\n\n        // reset the renderables to update\n        renderGroup.childrenRenderablesToUpdate.index = 0;\n\n        // upload all the things!\n        renderer.renderPipes.batch.upload(renderGroup.instructionSet);\n\n        // early out if it's a texture and it hasn't changed!\n        if (renderGroup.isCachedAsTexture && !renderGroup.textureNeedsUpdate) return;\n\n        for (let i = 0; i < renderGroup.renderGroupChildren.length; i++)\n        {\n            this._updateRenderGroups(renderGroup.renderGroupChildren[i]);\n        }\n    }\n\n    private _updateRenderables(renderGroup: RenderGroup)\n    {\n        const { list, index } = renderGroup.childrenRenderablesToUpdate;\n\n        for (let i = 0; i < index; i++)\n        {\n            const container = list[i];\n\n            if (container.didViewUpdate)\n            {\n                renderGroup.updateRenderable(container as ViewContainer);\n            }\n        }\n\n        clearList(list, index);\n    }\n\n    /**\n     * @param renderGroup\n     * @param renderPipes\n     * @deprecated since 8.3.0\n     */\n    private _buildInstructions(renderGroup: RenderGroup, renderPipes: RenderPipes): void;\n    private _buildInstructions(renderGroup: RenderGroup, renderer: Renderer): void;\n    private _buildInstructions(renderGroup: RenderGroup, rendererOrPipes: RenderPipes | Renderer): void\n    {\n    // rebuild the scene graph based on layers...\n        const root = renderGroup.root;\n        const instructionSet = renderGroup.instructionSet;\n\n        instructionSet.reset();\n\n        // deprecate the use of renderPipes by finding the renderer attached to the batch pipe as this is always there\n        const renderer = (rendererOrPipes as Renderer).renderPipes\n            ? (rendererOrPipes as Renderer)\n            : (rendererOrPipes as RenderPipes).batch.renderer;\n        const renderPipes = renderer.renderPipes;\n\n        // TODO add some events / runners for build start\n        renderPipes.batch.buildStart(instructionSet);\n        renderPipes.blendMode.buildStart();\n        renderPipes.colorMask.buildStart();\n\n        if (root.sortableChildren)\n        {\n            root.sortChildren();\n        }\n\n        root.collectRenderablesWithEffects(instructionSet, renderer, null);\n\n        // TODO add some events / runners for build end\n        renderPipes.batch.buildEnd(instructionSet);\n        renderPipes.blendMode.buildEnd(instructionSet);\n    }\n}\n\n", "import { ExtensionType } from '../../extensions/Extensions';\nimport { BatchableSprite } from './BatchableSprite';\n\nimport type { InstructionSet } from '../../rendering/renderers/shared/instructions/InstructionSet';\nimport type { RenderPipe } from '../../rendering/renderers/shared/instructions/RenderPipe';\nimport type { Renderer } from '../../rendering/renderers/types';\nimport type { Sprite } from './Sprite';\n\n/** @internal */\nexport class SpritePipe implements RenderPipe<Sprite>\n{\n    /** @ignore */\n    public static extension = {\n        type: [\n            ExtensionType.WebGLPipes,\n            ExtensionType.WebGPUPipes,\n            ExtensionType.CanvasPipes,\n        ],\n        name: 'sprite',\n    } as const;\n\n    private _renderer: Renderer;\n\n    constructor(renderer: Renderer)\n    {\n        this._renderer = renderer;\n    }\n\n    public addRenderable(sprite: Sprite, instructionSet: InstructionSet)\n    {\n        const gpuSprite = this._getGpuSprite(sprite);\n\n        if (sprite.didViewUpdate) this._updateBatchableSprite(sprite, gpuSprite);\n\n        // TODO visibility\n        this._renderer.renderPipes.batch.addToBatch(gpuSprite, instructionSet);\n    }\n\n    public updateRenderable(sprite: Sprite)\n    {\n        const gpuSprite = this._getGpuSprite(sprite);\n\n        if (sprite.didViewUpdate) this._updateBatchableSprite(sprite, gpuSprite);\n\n        gpuSprite._batcher.updateElement(gpuSprite);\n    }\n\n    public validateRenderable(sprite: Sprite): boolean\n    {\n        const gpuSprite = this._getGpuSprite(sprite);\n\n        return !gpuSprite._batcher.checkAndUpdateTexture(\n            gpuSprite,\n            sprite._texture)\n        ;\n    }\n\n    private _updateBatchableSprite(sprite: Sprite, batchableSprite: BatchableSprite)\n    {\n        batchableSprite.bounds = sprite.visualBounds;\n        batchableSprite.texture = sprite._texture;\n    }\n\n    private _getGpuSprite(sprite: Sprite): BatchableSprite\n    {\n        return sprite._gpuData[this._renderer.uid] || this._initGPUSprite(sprite);\n    }\n\n    private _initGPUSprite(sprite: Sprite): BatchableSprite\n    {\n        const batchableSprite = new BatchableSprite();\n\n        batchableSprite.renderable = sprite;\n\n        batchableSprite.transform = sprite.groupTransform;\n        batchableSprite.texture = sprite._texture;\n        batchableSprite.bounds = sprite.visualBounds;\n        batchableSprite.roundPixels = (this._renderer._roundPixels | sprite._roundPixels) as 0 | 1;\n\n        sprite._gpuData[this._renderer.uid] = batchableSprite;\n\n        return batchableSprite;\n    }\n\n    public destroy()\n    {\n        this._renderer = null;\n    }\n}\n", "import EventEmitter from 'eventemitter3';\n\n/**\n * Regexp for data URI.\n * Based on: {@link https://github.com/ragingwind/data-uri-regex}\n * @type {RegExp}\n * @default /(?:^data:image\\/([\\w+]+);(?:[\\w=]+|charset=[\\w-]+)?(?:;base64)?,)/i\n * @example\n * import { DATA_URI } from 'pixi.js';\n *\n * DATA_URI.test('data:image/png;base64,foobar'); // => true\n * @category utils\n * @advanced\n */\nexport const DATA_URI = /^\\s*data:(?:([\\w-]+)\\/([\\w+.-]+))?(?:;charset=([\\w-]+))?(?:;(base64))?,(.*)/i;\n\n// export the event emitter so we can use it in external modules\nexport { EventEmitter };\n\n/**\n * The current version of PixiJS. This is automatically replaced by the build process.\n * @internal\n */\nexport const VERSION = '$_VERSION';\n", "import { type ExtensionMetadata, ExtensionType } from '../../extensions/Extensions';\nimport { VERSION } from '../const';\n\nimport type { Application } from '../../app/Application';\nimport type { System } from '../../rendering/renderers/shared/system/System';\nimport type { Renderer } from '../../rendering/renderers/types';\n\ndeclare global\n{\n    /* eslint-disable no-var */\n    var __PIXI_APP_INIT__: undefined | ((arg: Application | Renderer, version: string) => void);\n    var __PIXI_RENDERER_INIT__: undefined | ((arg: Application | Renderer, version: string) => void);\n    /* eslint-enable no-var */\n}\n\n/**\n * Calls global __PIXI_APP_INIT__ hook with the application instance, after the application is initialized.\n * @category app\n * @internal\n */\nexport class ApplicationInitHook\n{\n    /** @ignore */\n    public static extension: ExtensionMetadata = ExtensionType.Application;\n    public static init(): void\n    {\n        globalThis.__PIXI_APP_INIT__?.(this as unknown as Application, VERSION);\n    }\n    public static destroy(): void\n    {\n        // nothing to do\n    }\n}\n\n/**\n * Calls global __PIXI_RENDERER_INIT__ hook with the renderer instance, after the renderer is initialized.\n * @category rendering\n * @internal\n */\nexport class RendererInitHook implements System\n{\n    /** @ignore */\n    public static extension = {\n        type: [\n            ExtensionType.WebGLSystem,\n            ExtensionType.WebGPUSystem,\n        ],\n        name: 'initHook',\n        priority: -10,\n    } as const;\n\n    private _renderer: Renderer;\n\n    constructor(renderer: Renderer)\n    {\n        this._renderer = renderer;\n    }\n    public init(): void\n    {\n        globalThis.__PIXI_RENDERER_INIT__?.(this._renderer, VERSION);\n    }\n    public destroy(): void\n    {\n        this._renderer = null;\n    }\n}\n", "import { extensions, ExtensionType } from '../../../extensions/Extensions';\nimport { State } from '../../renderers/shared/state/State';\nimport { DefaultBatcher } from './DefaultBatcher';\n\nimport type { Geometry } from '../../renderers/shared/geometry/Geometry';\nimport type { InstructionSet } from '../../renderers/shared/instructions/InstructionSet';\nimport type { BatchPipe, InstructionPipe } from '../../renderers/shared/instructions/RenderPipe';\nimport type { Shader } from '../../renderers/shared/shader/Shader';\nimport type { Renderer } from '../../renderers/types';\nimport type { Batch, BatchableElement, Batcher } from './Batcher';\n\n/** @internal */\nexport interface BatcherAdaptor\n{\n    start(batchPipe: BatcherPipe, geometry: Geometry, shader: Shader): void\n    init?(batchPipe: BatcherPipe): void;\n    execute(batchPipe: BatcherPipe, batch: Batch): void\n    contextChange?(): void;\n}\n\n/**\n * A pipe that batches elements into batches and sends them to the renderer.\n *\n * You can install new Batchers using ExtensionType.Batcher. Each render group will\n * have a default batcher and any required ones will be created on demand.\n * @category rendering\n * @advanced\n */\nexport class BatcherPipe implements InstructionPipe<Batch>, BatchPipe\n{\n    /** @ignore */\n    public static extension = {\n        type: [\n            ExtensionType.WebGLPipes,\n            ExtensionType.WebGPUPipes,\n            ExtensionType.CanvasPipes,\n        ],\n        name: 'batch',\n    } as const;\n\n    public state: State = State.for2d();\n    public renderer: Renderer;\n\n    private readonly _batchersByInstructionSet: Record<number, Record<string, Batcher>> = Object.create(null);\n\n    private _adaptor: BatcherAdaptor;\n\n    /** A record of all active batchers, keyed by their names */\n    private _activeBatches: Record<string, Batcher> = Object.create(null);\n\n    /** The currently active batcher being used to batch elements */\n    private _activeBatch: Batcher;\n\n    public static _availableBatchers: Record<string, new () => Batcher> = Object.create(null);\n\n    public static getBatcher(name: string): Batcher\n    {\n        return new this._availableBatchers[name as keyof typeof this._availableBatchers]();\n    }\n\n    constructor(renderer: Renderer, adaptor: BatcherAdaptor)\n    {\n        this.renderer = renderer;\n        this._adaptor = adaptor;\n\n        this._adaptor.init?.(this);\n    }\n\n    public buildStart(instructionSet: InstructionSet)\n    {\n        let batchers = this._batchersByInstructionSet[instructionSet.uid];\n\n        if (!batchers)\n        {\n            batchers = this._batchersByInstructionSet[instructionSet.uid] = Object.create(null);\n            batchers.default ||= new DefaultBatcher({\n                maxTextures: this.renderer.limits.maxBatchableTextures,\n            });\n        }\n\n        this._activeBatches = batchers;\n\n        this._activeBatch = this._activeBatches.default;\n\n        for (const i in this._activeBatches)\n        {\n            this._activeBatches[i].begin();\n        }\n    }\n\n    public addToBatch(batchableObject: BatchableElement, instructionSet: InstructionSet)\n    {\n        if (this._activeBatch.name !== batchableObject.batcherName)\n        {\n            this._activeBatch.break(instructionSet);\n\n            let batch = this._activeBatches[batchableObject.batcherName];\n\n            if (!batch)\n            {\n                batch = this._activeBatches[batchableObject.batcherName]\n                    = BatcherPipe.getBatcher(batchableObject.batcherName);\n                batch.begin();\n            }\n\n            this._activeBatch = batch;\n        }\n\n        this._activeBatch.add(batchableObject);\n    }\n\n    public break(instructionSet: InstructionSet)\n    {\n        this._activeBatch.break(instructionSet);\n    }\n\n    public buildEnd(instructionSet: InstructionSet)\n    {\n        this._activeBatch.break(instructionSet);\n\n        const batches = this._activeBatches;\n\n        for (const i in batches)\n        {\n            const batch = batches[i as keyof typeof batches];\n            const geometry = batch.geometry;\n\n            geometry.indexBuffer.setDataWithSize(batch.indexBuffer, batch.indexSize, true);\n\n            geometry.buffers[0].setDataWithSize(batch.attributeBuffer.float32View, batch.attributeSize, false);\n        }\n    }\n\n    public upload(instructionSet: InstructionSet)\n    {\n        const batchers = this._batchersByInstructionSet[instructionSet.uid];\n\n        for (const i in batchers)\n        {\n            const batcher = batchers[i as keyof typeof batchers];\n            const geometry = batcher.geometry;\n\n            if (batcher.dirty)\n            {\n                batcher.dirty = false;\n\n                geometry.buffers[0].update(batcher.attributeSize * 4);\n            }\n        }\n    }\n\n    public execute(batch: Batch)\n    {\n        if (batch.action === 'startBatch')\n        {\n            const batcher = batch.batcher;\n            const geometry = batcher.geometry;\n            const shader = batcher.shader;\n\n            this._adaptor.start(this, geometry, shader);\n        }\n\n        this._adaptor.execute(this, batch);\n    }\n\n    public destroy()\n    {\n        this.state = null;\n        this.renderer = null;\n\n        this._adaptor = null;\n\n        for (const i in this._activeBatches)\n        {\n            this._activeBatches[i].destroy();\n        }\n\n        this._activeBatches = null;\n    }\n}\n\nextensions.handleByMap(ExtensionType.Batcher, BatcherPipe._availableBatchers);\n\nextensions.add(DefaultBatcher);\n", "var fragment = \"in vec2 vMaskCoord;\\nin vec2 vTextureCoord;\\n\\nuniform sampler2D uTexture;\\nuniform sampler2D uMaskTexture;\\n\\nuniform float uAlpha;\\nuniform vec4 uMaskClamp;\\nuniform float uInverse;\\n\\nout vec4 finalColor;\\n\\nvoid main(void)\\n{\\n    float clip = step(3.5,\\n        step(uMaskClamp.x, vMaskCoord.x) +\\n        step(uMaskClamp.y, vMaskCoord.y) +\\n        step(vMaskCoord.x, uMaskClamp.z) +\\n        step(vMaskCoord.y, uMaskClamp.w));\\n\\n    // TODO look into why this is needed\\n    float npmAlpha = uAlpha;\\n    vec4 original = texture(uTexture, vTextureCoord);\\n    vec4 masky = texture(uMaskTexture, vMaskCoord);\\n    float alphaMul = 1.0 - npmAlpha * (1.0 - masky.a);\\n\\n    float a = alphaMul * masky.r * npmAlpha * clip;\\n\\n    if (uInverse == 1.0) {\\n        a = 1.0 - a;\\n    }\\n\\n    finalColor = original * a;\\n}\\n\";\n\nexport { fragment as default };\n//# sourceMappingURL=mask.frag.mjs.map\n", "var vertex = \"in vec2 aPosition;\\n\\nout vec2 vTextureCoord;\\nout vec2 vMaskCoord;\\n\\n\\nuniform vec4 uInputSize;\\nuniform vec4 uOutputFrame;\\nuniform vec4 uOutputTexture;\\nuniform mat3 uFilterMatrix;\\n\\nvec4 filterVertexPosition(  vec2 aPosition )\\n{\\n    vec2 position = aPosition * uOutputFrame.zw + uOutputFrame.xy;\\n       \\n    position.x = position.x * (2.0 / uOutputTexture.x) - 1.0;\\n    position.y = position.y * (2.0*uOutputTexture.z / uOutputTexture.y) - uOutputTexture.z;\\n\\n    return vec4(position, 0.0, 1.0);\\n}\\n\\nvec2 filterTextureCoord(  vec2 aPosition )\\n{\\n    return aPosition * (uOutputFrame.zw * uInputSize.zw);\\n}\\n\\nvec2 getFilterCoord( vec2 aPosition )\\n{\\n    return  ( uFilterMatrix * vec3( filterTextureCoord(aPosition), 1.0)  ).xy;\\n}   \\n\\nvoid main(void)\\n{\\n    gl_Position = filterVertexPosition(aPosition);\\n    vTextureCoord = filterTextureCoord(aPosition);\\n    vMaskCoord = getFilterCoord(aPosition);\\n}\\n\";\n\nexport { vertex as default };\n//# sourceMappingURL=mask.vert.mjs.map\n", "var source = \"struct GlobalFilterUniforms {\\n  uInputSize:vec4<f32>,\\n  uInputPixel:vec4<f32>,\\n  uInputClamp:vec4<f32>,\\n  uOutputFrame:vec4<f32>,\\n  uGlobalFrame:vec4<f32>,\\n  uOutputTexture:vec4<f32>,\\n};\\n\\nstruct MaskUniforms {\\n  uFilterMatrix:mat3x3<f32>,\\n  uMaskClamp:vec4<f32>,\\n  uAlpha:f32,\\n  uInverse:f32,\\n};\\n\\n@group(0) @binding(0) var<uniform> gfu: GlobalFilterUniforms;\\n@group(0) @binding(1) var uTexture: texture_2d<f32>;\\n@group(0) @binding(2) var uSampler : sampler;\\n\\n@group(1) @binding(0) var<uniform> filterUniforms : MaskUniforms;\\n@group(1) @binding(1) var uMaskTexture: texture_2d<f32>;\\n\\nstruct VSOutput {\\n    @builtin(position) position: vec4<f32>,\\n    @location(0) uv : vec2<f32>,\\n    @location(1) filterUv : vec2<f32>,\\n};\\n\\nfn filterVertexPosition(aPosition:vec2<f32>) -> vec4<f32>\\n{\\n    var position = aPosition * gfu.uOutputFrame.zw + gfu.uOutputFrame.xy;\\n\\n    position.x = position.x * (2.0 / gfu.uOutputTexture.x) - 1.0;\\n    position.y = position.y * (2.0*gfu.uOutputTexture.z / gfu.uOutputTexture.y) - gfu.uOutputTexture.z;\\n\\n    return vec4(position, 0.0, 1.0);\\n}\\n\\nfn filterTextureCoord( aPosition:vec2<f32> ) -> vec2<f32>\\n{\\n    return aPosition * (gfu.uOutputFrame.zw * gfu.uInputSize.zw);\\n}\\n\\nfn globalTextureCoord( aPosition:vec2<f32> ) -> vec2<f32>\\n{\\n  return  (aPosition.xy / gfu.uGlobalFrame.zw) + (gfu.uGlobalFrame.xy / gfu.uGlobalFrame.zw);\\n}\\n\\nfn getFilterCoord(aPosition:vec2<f32> ) -> vec2<f32>\\n{\\n  return ( filterUniforms.uFilterMatrix * vec3( filterTextureCoord(aPosition), 1.0)  ).xy;\\n}\\n\\nfn getSize() -> vec2<f32>\\n{\\n  return gfu.uGlobalFrame.zw;\\n}\\n\\n@vertex\\nfn mainVertex(\\n  @location(0) aPosition : vec2<f32>,\\n) -> VSOutput {\\n  return VSOutput(\\n   filterVertexPosition(aPosition),\\n   filterTextureCoord(aPosition),\\n   getFilterCoord(aPosition)\\n  );\\n}\\n\\n@fragment\\nfn mainFragment(\\n  @location(0) uv: vec2<f32>,\\n  @location(1) filterUv: vec2<f32>,\\n  @builtin(position) position: vec4<f32>\\n) -> @location(0) vec4<f32> {\\n\\n    var maskClamp = filterUniforms.uMaskClamp;\\n    var uAlpha = filterUniforms.uAlpha;\\n\\n    var clip = step(3.5,\\n      step(maskClamp.x, filterUv.x) +\\n      step(maskClamp.y, filterUv.y) +\\n      step(filterUv.x, maskClamp.z) +\\n      step(filterUv.y, maskClamp.w));\\n\\n    var mask = textureSample(uMaskTexture, uSampler, filterUv);\\n    var source = textureSample(uTexture, uSampler, uv);\\n    var alphaMul = 1.0 - uAlpha * (1.0 - mask.a);\\n\\n    var a: f32 = alphaMul * mask.r * uAlpha * clip;\\n\\n    if (filterUniforms.uInverse == 1.0) {\\n        a = 1.0 - a;\\n    }\\n\\n    return source * a;\\n}\\n\";\n\nexport { source as default };\n//# sourceMappingURL=mask.wgsl.mjs.map\n", "import { Matrix } from '../../maths/matrix/Matrix';\nimport { GlProgram } from '../../rendering/renderers/gl/shader/GlProgram';\nimport { GpuProgram } from '../../rendering/renderers/gpu/shader/GpuProgram';\nimport { UniformGroup } from '../../rendering/renderers/shared/shader/UniformGroup';\nimport { TextureMatrix } from '../../rendering/renderers/shared/texture/TextureMatrix';\nimport { Filter } from '../Filter';\nimport fragment from './mask.frag';\nimport vertex from './mask.vert';\nimport source from './mask.wgsl';\n\nimport type { Texture } from '../../rendering/renderers/shared/texture/Texture';\nimport type { Sprite } from '../../scene/sprite/Sprite';\nimport type { FilterOptions } from '../Filter';\nimport type { FilterSystem } from '../FilterSystem';\n\n/** @internal */\nexport interface MaskFilterOptions extends FilterOptions\n{\n    sprite: Sprite,\n    inverse?: boolean;\n    scale?: number | { x: number, y: number },\n}\n\n/** @internal */\nexport class MaskFilter extends Filter\n{\n    public sprite: Sprite;\n    private readonly _textureMatrix: TextureMatrix;\n\n    constructor(options: MaskFilterOptions)\n    {\n        const { sprite, ...rest } = options;\n\n        const textureMatrix = new TextureMatrix(sprite.texture);\n\n        const filterUniforms = new UniformGroup({\n            uFilterMatrix: { value: new Matrix(), type: 'mat3x3<f32>' },\n            uMaskClamp: { value: textureMatrix.uClampFrame, type: 'vec4<f32>' },\n            uAlpha: { value: 1, type: 'f32' },\n            uInverse: { value: options.inverse ? 1 : 0, type: 'f32' },\n        });\n\n        const gpuProgram = GpuProgram.from({\n            vertex: {\n                source,\n                entryPoint: 'mainVertex',\n            },\n            fragment: {\n                source,\n                entryPoint: 'mainFragment',\n            },\n        });\n\n        const glProgram = GlProgram.from({\n            vertex,\n            fragment,\n            name: 'mask-filter',\n        });\n\n        super({\n            ...rest,\n            gpuProgram,\n            glProgram,\n            resources: {\n                filterUniforms,\n                uMaskTexture: sprite.texture.source,\n            },\n        });\n\n        this.sprite = sprite;\n\n        this._textureMatrix = textureMatrix;\n    }\n\n    set inverse(value: boolean)\n    {\n        this.resources.filterUniforms.uniforms.uInverse = value ? 1 : 0;\n    }\n\n    get inverse(): boolean\n    {\n        return this.resources.filterUniforms.uniforms.uInverse === 1;\n    }\n\n    public apply(\n        filterManager: FilterSystem,\n        input: Texture,\n        output: Texture,\n        clearMode: boolean\n    ): void\n    {\n        // will trigger an update if the texture changed..\n        this._textureMatrix.texture = this.sprite.texture;\n\n        filterManager.calculateSpriteMatrix(\n            this.resources.filterUniforms.uniforms.uFilterMatrix as Matrix,\n            this.sprite\n        ).prepend(this._textureMatrix.mapCoord);\n\n        this.resources.uMaskTexture = this.sprite.texture.source;\n\n        filterManager.applyFilter(this, input, output, clearMode);\n    }\n}\n", "import { ExtensionType } from '../../../extensions/Extensions';\nimport { FilterEffect } from '../../../filters/FilterEffect';\nimport { MaskFilter } from '../../../filters/mask/MaskFilter';\nimport { Bounds } from '../../../scene/container/bounds/Bounds';\nimport { getGlobalBounds } from '../../../scene/container/bounds/getGlobalBounds';\nimport { Sprite } from '../../../scene/sprite/Sprite';\nimport { BigPool } from '../../../utils/pool/PoolGroup';\nimport { Texture } from '../../renderers/shared/texture/Texture';\nimport { TexturePool } from '../../renderers/shared/texture/TexturePool';\nimport { RendererType } from '../../renderers/types';\n\nimport type { Container } from '../../../scene/container/Container';\nimport type { Effect } from '../../../scene/container/Effect';\nimport type { PoolItem } from '../../../utils/pool/Pool';\nimport type { Instruction } from '../../renderers/shared/instructions/Instruction';\nimport type { InstructionSet } from '../../renderers/shared/instructions/InstructionSet';\nimport type { InstructionPipe } from '../../renderers/shared/instructions/RenderPipe';\nimport type { RenderTarget } from '../../renderers/shared/renderTarget/RenderTarget';\nimport type { Renderer } from '../../renderers/types';\nimport type { AlphaMask } from './AlphaMask';\n\ntype MaskMode = 'pushMaskBegin' | 'pushMaskEnd' | 'popMaskBegin' | 'popMaskEnd';\n\nconst tempBounds = new Bounds();\n\n/** @internal */\nclass AlphaMaskEffect extends FilterEffect implements PoolItem\n{\n    constructor()\n    {\n        super();\n\n        this.filters = [new MaskFilter({\n            sprite: new Sprite(Texture.EMPTY),\n            inverse: false,\n            resolution: 'inherit',\n            antialias: 'inherit'\n        })];\n    }\n\n    get sprite(): Sprite\n    {\n        return (this.filters[0] as MaskFilter).sprite;\n    }\n\n    set sprite(value: Sprite)\n    {\n        (this.filters[0] as MaskFilter).sprite = value;\n    }\n\n    get inverse(): boolean\n    {\n        return (this.filters[0] as MaskFilter).inverse;\n    }\n\n    set inverse(value: boolean)\n    {\n        (this.filters[0] as MaskFilter).inverse = value;\n    }\n\n    public init: () => void;\n}\n\n/** @internal */\nexport interface AlphaMaskInstruction extends Instruction\n{\n    renderPipeId: 'alphaMask',\n    action: MaskMode,\n    mask: AlphaMask,\n    inverse: boolean;\n    maskedContainer: Container,\n    renderMask: boolean,\n}\n\n/** @internal */\nexport interface AlphaMaskData\n{\n    filterEffect: AlphaMaskEffect,\n    maskedContainer: Container,\n    previousRenderTarget?: RenderTarget,\n    filterTexture?: Texture,\n}\n\n/** @internal */\nexport class AlphaMaskPipe implements InstructionPipe<AlphaMaskInstruction>\n{\n    /** @ignore */\n    public static extension = {\n        type: [\n            ExtensionType.WebGLPipes,\n            ExtensionType.WebGPUPipes,\n            ExtensionType.CanvasPipes,\n        ],\n        name: 'alphaMask',\n    } as const;\n\n    private _renderer: Renderer;\n    private _activeMaskStage: AlphaMaskData[] = [];\n\n    constructor(renderer: Renderer)\n    {\n        this._renderer = renderer;\n    }\n\n    public push(mask: Effect, maskedContainer: Container, instructionSet: InstructionSet): void\n    {\n        const renderer = this._renderer;\n\n        renderer.renderPipes.batch.break(instructionSet);\n\n        instructionSet.add({\n            renderPipeId: 'alphaMask',\n            action: 'pushMaskBegin',\n            mask,\n            inverse: maskedContainer._maskOptions.inverse,\n            canBundle: false,\n            maskedContainer\n        } as AlphaMaskInstruction);\n\n        (mask as AlphaMask).inverse = maskedContainer._maskOptions.inverse;\n\n        if ((mask as AlphaMask).renderMaskToTexture)\n        {\n            const maskContainer = (mask as AlphaMask).mask;\n\n            maskContainer.includeInBuild = true;\n\n            maskContainer.collectRenderables(\n                instructionSet,\n                renderer,\n                null\n            );\n\n            maskContainer.includeInBuild = false;\n        }\n\n        renderer.renderPipes.batch.break(instructionSet);\n\n        instructionSet.add({\n            renderPipeId: 'alphaMask',\n            action: 'pushMaskEnd',\n            mask,\n            maskedContainer,\n            inverse: maskedContainer._maskOptions.inverse,\n            canBundle: false,\n        } as AlphaMaskInstruction);\n    }\n\n    public pop(mask: Effect, _maskedContainer: Container, instructionSet: InstructionSet): void\n    {\n        const renderer = this._renderer;\n\n        renderer.renderPipes.batch.break(instructionSet);\n\n        instructionSet.add({\n            renderPipeId: 'alphaMask',\n            action: 'popMaskEnd',\n            mask,\n            inverse: _maskedContainer._maskOptions.inverse,\n            canBundle: false,\n        } as AlphaMaskInstruction);\n    }\n\n    public execute(instruction: AlphaMaskInstruction)\n    {\n        const renderer = this._renderer;\n        const renderMask = instruction.mask.renderMaskToTexture;\n\n        if (instruction.action === 'pushMaskBegin')\n        {\n            const filterEffect = BigPool.get(AlphaMaskEffect);\n\n            filterEffect.inverse = instruction.inverse;\n\n            if (renderMask)\n            {\n                instruction.mask.mask.measurable = true;\n\n                const bounds = getGlobalBounds(instruction.mask.mask, true, tempBounds);\n\n                instruction.mask.mask.measurable = false;\n\n                bounds.ceil();\n\n                const colorTextureSource = renderer.renderTarget.renderTarget.colorTexture.source;\n                const filterTexture = TexturePool.getOptimalTexture(\n                    bounds.width,\n                    bounds.height,\n                    colorTextureSource._resolution,\n                    colorTextureSource.antialias\n                );\n\n                renderer.renderTarget.push(filterTexture, true);\n\n                renderer.globalUniforms.push({\n                    offset: bounds,\n                    worldColor: 0xFFFFFFFF\n                });\n\n                const sprite = filterEffect.sprite;\n\n                sprite.texture = filterTexture;\n\n                sprite.worldTransform.tx = bounds.minX;\n                sprite.worldTransform.ty = bounds.minY;\n\n                this._activeMaskStage.push({\n                    filterEffect,\n                    maskedContainer: instruction.maskedContainer,\n                    filterTexture,\n                });\n            }\n            else\n            {\n                filterEffect.sprite = instruction.mask.mask as Sprite;\n\n                this._activeMaskStage.push({\n                    filterEffect,\n                    maskedContainer: instruction.maskedContainer,\n                });\n            }\n        }\n        else if (instruction.action === 'pushMaskEnd')\n        {\n            const maskData = this._activeMaskStage[this._activeMaskStage.length - 1];\n\n            if (renderMask)\n            {\n                // WebGPU blit's automatically, but WebGL does not!\n                if (renderer.type === RendererType.WEBGL)\n                {\n                    renderer.renderTarget.finishRenderPass();\n                }\n\n                renderer.renderTarget.pop();\n                renderer.globalUniforms.pop();\n            }\n\n            renderer.filter.push({\n                renderPipeId: 'filter',\n                action: 'pushFilter',\n                container: maskData.maskedContainer,\n                filterEffect: maskData.filterEffect,\n                canBundle: false,\n            });\n        }\n        else if (instruction.action === 'popMaskEnd')\n        {\n            renderer.filter.pop();\n\n            const maskData = this._activeMaskStage.pop();\n\n            if (renderMask)\n            {\n                TexturePool.returnTexture(maskData.filterTexture);\n            }\n\n            BigPool.return(maskData.filterEffect);\n        }\n    }\n\n    public destroy(): void\n    {\n        this._renderer = null;\n        this._activeMaskStage = null;\n    }\n}\n", "import { ExtensionType } from '../../../extensions/Extensions';\n\nimport type { Container } from '../../../scene/container/Container';\nimport type { Effect } from '../../../scene/container/Effect';\nimport type { Instruction } from '../../renderers/shared/instructions/Instruction';\nimport type { InstructionSet } from '../../renderers/shared/instructions/InstructionSet';\nimport type { InstructionPipe } from '../../renderers/shared/instructions/RenderPipe';\nimport type { Renderer } from '../../renderers/types';\nimport type { ColorMask } from './ColorMask';\n\n/** @internal */\nexport interface ColorMaskInstruction extends Instruction\n{\n    renderPipeId: 'colorMask',\n    colorMask: number,\n}\n\n/** @internal */\nexport class ColorMaskPipe implements InstructionPipe<ColorMaskInstruction>\n{\n    /** @ignore */\n    public static extension = {\n        type: [\n            ExtensionType.WebGLPipes,\n            ExtensionType.WebGPUPipes,\n            ExtensionType.CanvasPipes,\n        ],\n        name: 'colorMask',\n    } as const;\n\n    private readonly _renderer: Renderer;\n    private _colorStack: number[] = [];\n    private _colorStackIndex = 0;\n    private _currentColor = 0;\n\n    constructor(renderer: Renderer)\n    {\n        this._renderer = renderer;\n    }\n\n    public buildStart()\n    {\n        this._colorStack[0] = 0xF;\n        this._colorStackIndex = 1;\n        this._currentColor = 0xF;\n    }\n\n    public push(mask: Effect, _container: Container, instructionSet: InstructionSet): void\n    {\n        const renderer = this._renderer;\n\n        renderer.renderPipes.batch.break(instructionSet);\n\n        const colorStack = this._colorStack;\n\n        colorStack[this._colorStackIndex] = colorStack[this._colorStackIndex - 1] & (mask as ColorMask).mask;\n\n        const currentColor = this._colorStack[this._colorStackIndex];\n\n        if (currentColor !== this._currentColor)\n        {\n            this._currentColor = currentColor;\n            instructionSet.add({\n                renderPipeId: 'colorMask',\n                colorMask: currentColor,\n                canBundle: false,\n            } as ColorMaskInstruction);\n        }\n\n        this._colorStackIndex++;\n    }\n\n    public pop(_mask: Effect, _container: Container, instructionSet: InstructionSet): void\n    {\n        const renderer = this._renderer;\n\n        renderer.renderPipes.batch.break(instructionSet);\n\n        const colorStack = this._colorStack;\n\n        this._colorStackIndex--;\n\n        const currentColor = colorStack[this._colorStackIndex - 1];\n\n        if (currentColor !== this._currentColor)\n        {\n            this._currentColor = currentColor;\n\n            instructionSet.add({\n                renderPipeId: 'colorMask',\n                colorMask: currentColor,\n                canBundle: false,\n            } as ColorMaskInstruction);\n        }\n    }\n\n    public execute(instruction: ColorMaskInstruction)\n    {\n        const renderer = this._renderer;\n\n        renderer.colorMask.setMask(instruction.colorMask);\n    }\n\n    public destroy()\n    {\n        this._colorStack = null;\n    }\n}\n", "import { ExtensionType } from '../../../extensions/Extensions';\nimport { <PERSON>LEAR } from '../../renderers/gl/const';\nimport { STENCIL_MODES } from '../../renderers/shared/state/const';\n\nimport type { Container } from '../../../scene/container/Container';\nimport type { Effect } from '../../../scene/container/Effect';\nimport type { Instruction } from '../../renderers/shared/instructions/Instruction';\nimport type { InstructionSet } from '../../renderers/shared/instructions/InstructionSet';\nimport type { InstructionPipe } from '../../renderers/shared/instructions/RenderPipe';\nimport type { Renderable } from '../../renderers/shared/Renderable';\nimport type { Renderer } from '../../renderers/types';\nimport type { StencilMask } from './StencilMask';\n\n/** @internal */\ntype MaskMode = 'pushMaskBegin' | 'pushMaskEnd' | 'popMaskBegin' | 'popMaskEnd';\n\n/** @internal */\nexport interface StencilMaskInstruction extends Instruction\n{\n    renderPipeId: 'stencilMask',\n    action: MaskMode,\n    inverse: boolean,\n    mask: StencilMask,\n}\n\n/** @internal */\nexport class StencilMaskPipe implements InstructionPipe<StencilMaskInstruction>\n{\n    public static extension = {\n        type: [\n            ExtensionType.WebGLPipes,\n            ExtensionType.WebGPUPipes,\n            ExtensionType.CanvasPipes,\n        ],\n        name: 'stencilMask',\n    } as const;\n\n    private _renderer: Renderer;\n\n    // used when building and also when executing..\n    private _maskStackHash: Record<number, number> = {};\n\n    private _maskHash = new WeakMap<StencilMask, {\n        instructionsStart: number,\n        instructionsLength: number,\n    }>();\n\n    constructor(renderer: Renderer)\n    {\n        this._renderer = renderer;\n    }\n\n    public push(mask: Effect, _container: Container, instructionSet: InstructionSet): void\n    {\n        const effect = mask as StencilMask;\n\n        const renderer = this._renderer;\n\n        renderer.renderPipes.batch.break(instructionSet);\n\n        renderer.renderPipes.blendMode.setBlendMode(effect.mask as Renderable, 'none', instructionSet);\n\n        instructionSet.add({\n            renderPipeId: 'stencilMask',\n            action: 'pushMaskBegin',\n            mask,\n            inverse: _container._maskOptions.inverse,\n            canBundle: false,\n        } as StencilMaskInstruction);\n\n        const maskContainer = effect.mask;\n\n        maskContainer.includeInBuild = true;\n\n        if (!this._maskHash.has(effect))\n        {\n            this._maskHash.set(effect, {\n                instructionsStart: 0,\n                instructionsLength: 0,\n            });\n        }\n\n        const maskData = this._maskHash.get(effect);\n\n        maskData.instructionsStart = instructionSet.instructionSize;\n\n        maskContainer.collectRenderables(\n            instructionSet,\n            renderer,\n            null\n        );\n\n        maskContainer.includeInBuild = false;\n\n        renderer.renderPipes.batch.break(instructionSet);\n\n        instructionSet.add({\n            renderPipeId: 'stencilMask',\n            action: 'pushMaskEnd',\n            mask,\n            inverse: _container._maskOptions.inverse,\n            canBundle: false,\n        } as StencilMaskInstruction);\n\n        const instructionsLength = instructionSet.instructionSize - maskData.instructionsStart - 1;\n\n        maskData.instructionsLength = instructionsLength;\n\n        const renderTargetUid = renderer.renderTarget.renderTarget.uid;\n\n        this._maskStackHash[renderTargetUid] ??= 0;\n    }\n\n    public pop(mask: Effect, _container: Container, instructionSet: InstructionSet): void\n    {\n        const effect = mask as StencilMask;\n\n        const renderer = this._renderer;\n\n        // stencil is stored based on current render target..\n        renderer.renderPipes.batch.break(instructionSet);\n        renderer.renderPipes.blendMode.setBlendMode(effect.mask as Renderable, 'none', instructionSet);\n\n        instructionSet.add({\n            renderPipeId: 'stencilMask',\n            action: 'popMaskBegin',\n            inverse: _container._maskOptions.inverse,\n            canBundle: false,\n        } as StencilMaskInstruction);\n\n        const maskData = this._maskHash.get(mask as StencilMask);\n\n        for (let i = 0; i < maskData.instructionsLength; i++)\n        {\n            // eslint-disable-next-line max-len\n            instructionSet.instructions[instructionSet.instructionSize++] = instructionSet.instructions[maskData.instructionsStart++];\n        }\n\n        instructionSet.add({\n            renderPipeId: 'stencilMask',\n            action: 'popMaskEnd',\n            canBundle: false,\n        });\n    }\n\n    public execute(instruction: StencilMaskInstruction)\n    {\n        const renderer = this._renderer;\n        const renderTargetUid = renderer.renderTarget.renderTarget.uid;\n\n        let maskStackIndex = this._maskStackHash[renderTargetUid] ??= 0;\n\n        if (instruction.action === 'pushMaskBegin')\n        {\n            // we create the depth and stencil buffers JIT\n            // as no point allocating the memory if we don't use it\n            renderer.renderTarget.ensureDepthStencil();\n\n            renderer.stencil.setStencilMode(STENCIL_MODES.RENDERING_MASK_ADD, maskStackIndex);\n\n            maskStackIndex++;\n\n            renderer.colorMask.setMask(0);\n        }\n        else if (instruction.action === 'pushMaskEnd')\n        {\n            if (instruction.inverse)\n            {\n                renderer.stencil.setStencilMode(STENCIL_MODES.INVERSE_MASK_ACTIVE, maskStackIndex);\n            }\n            else\n            {\n                renderer.stencil.setStencilMode(STENCIL_MODES.MASK_ACTIVE, maskStackIndex);\n            }\n\n            renderer.colorMask.setMask(0xF);\n        }\n        else if (instruction.action === 'popMaskBegin')\n        {\n            renderer.colorMask.setMask(0);\n\n            if (maskStackIndex !== 0)\n            {\n                renderer.stencil.setStencilMode(STENCIL_MODES.RENDERING_MASK_REMOVE, maskStackIndex);\n            }\n            else\n            {\n                renderer.renderTarget.clear(null, CLEAR.STENCIL);\n                renderer.stencil.setStencilMode(STENCIL_MODES.DISABLED, maskStackIndex);\n            }\n\n            maskStackIndex--;\n        }\n        else if (instruction.action === 'popMaskEnd')\n        {\n            if (instruction.inverse)\n            {\n                renderer.stencil.setStencilMode(STENCIL_MODES.INVERSE_MASK_ACTIVE, maskStackIndex);\n            }\n            else\n            {\n                renderer.stencil.setStencilMode(STENCIL_MODES.MASK_ACTIVE, maskStackIndex);\n            }\n\n            renderer.colorMask.setMask(0xF);\n        }\n\n        this._maskStackHash[renderTargetUid] = maskStackIndex;\n    }\n\n    public destroy()\n    {\n        this._renderer = null;\n        this._maskStackHash = null;\n        this._maskHash = null;\n    }\n}\n", "import { Color } from '../../../../color/Color';\nimport { ExtensionType } from '../../../../extensions/Extensions';\nimport { warn } from '../../../../utils/logging/warn';\n\nimport type { ColorSource, RgbaArray } from '../../../../color/Color';\nimport type { System } from '../system/System';\n/**\n * Options for the background system.\n * @category rendering\n * @advanced\n */\nexport interface BackgroundSystemOptions\n{\n    /**\n     * The background color used to clear the canvas. See {@link ColorSource} for accepted color values.\n     * @default 'black'\n     */\n    backgroundColor: ColorSource;\n    /** Alias for `backgroundColor` */\n    background?: ColorSource\n    /**\n     * Transparency of the background color, value from `0` (fully transparent) to `1` (fully opaque).\n     * This value determines whether the canvas is initialized with alpha transparency support.\n     * Note: This cannot be changed after initialization. If set to `1`, the canvas will remain opaque,\n     * even if a transparent background color is set later.\n     * @default 1\n     */\n    backgroundAlpha?: number;\n    /**\n     * Whether to clear the canvas before new render passes.\n     * @default true\n     */\n    clearBeforeRender?: boolean;\n}\n\n/**\n * The background system manages the background color and alpha of the main view.\n * @category rendering\n * @advanced\n */\nexport class BackgroundSystem implements System<BackgroundSystemOptions>\n{\n    /** @ignore */\n    public static extension = {\n        type: [\n            ExtensionType.WebGLSystem,\n            ExtensionType.WebGPUSystem,\n            ExtensionType.CanvasSystem,\n        ],\n        name: 'background',\n        priority: 0,\n    } as const;\n\n    /** default options used by the system */\n    public static defaultOptions: BackgroundSystemOptions = {\n        /**\n         * {@link WebGLOptions.backgroundAlpha}\n         * @default 1\n         */\n        backgroundAlpha: 1,\n        /**\n         * {@link WebGLOptions.backgroundColor}\n         * @default 0x000000\n         */\n        backgroundColor: 0x0,\n        /**\n         * {@link WebGLOptions.clearBeforeRender}\n         * @default true\n         */\n        clearBeforeRender: true,\n    };\n\n    /**\n     * This sets if the CanvasRenderer will clear the canvas or not before the new render pass.\n     * If the scene is NOT transparent PixiJS will use a canvas sized fillRect operation every\n     * frame to set the canvas background color. If the scene is transparent PixiJS will use clearRect\n     * to clear the canvas every frame. Disable this by setting this to false. For example, if\n     * your game has a canvas filling background image you often don't need this set.\n     */\n    public clearBeforeRender: boolean;\n\n    private readonly _backgroundColor: Color;\n\n    constructor()\n    {\n        this.clearBeforeRender = true;\n\n        this._backgroundColor = new Color(0x000000);\n\n        this.color = this._backgroundColor; // run bg color setter\n        this.alpha = 1;\n    }\n\n    /**\n     * initiates the background system\n     * @param options - the options for the background colors\n     */\n    public init(options: BackgroundSystemOptions): void\n    {\n        options = { ...BackgroundSystem.defaultOptions, ...options };\n\n        this.clearBeforeRender = options.clearBeforeRender;\n        this.color = options.background || options.backgroundColor || this._backgroundColor; // run bg color setter\n        this.alpha = options.backgroundAlpha;\n\n        this._backgroundColor.setAlpha(options.backgroundAlpha);\n    }\n\n    /** The background color to fill if not transparent */\n    get color(): Color\n    {\n        return this._backgroundColor;\n    }\n\n    set color(value: ColorSource)\n    {\n        // #if _DEBUG\n\n        const incoming = Color.shared.setValue(value);\n\n        if (incoming.alpha < 1 && this._backgroundColor.alpha === 1)\n        {\n            warn(\n                'Cannot set a transparent background on an opaque canvas. '\n                + 'To enable transparency, set backgroundAlpha < 1 when initializing your Application.'\n            );\n        }\n        // #endif\n        this._backgroundColor.setValue(value);\n    }\n\n    /** The background color alpha. Setting this to 0 will make the canvas transparent. */\n    get alpha(): number\n    {\n        return this._backgroundColor.alpha;\n    }\n\n    set alpha(value: number)\n    {\n        this._backgroundColor.setAlpha(value);\n    }\n\n    /** The background color as an [R, G, B, A] array. */\n    get colorRgba(): RgbaArray\n    {\n        return this._backgroundColor.toArray() as RgbaArray;\n    }\n\n    /**\n     * destroys the background system\n     * @internal\n     */\n    public destroy(): void\n    {\n        // No cleanup required\n    }\n}\n", "import { extensions, ExtensionType } from '../../../../extensions/Extensions';\nimport { FilterEffect } from '../../../../filters/FilterEffect';\nimport { warn } from '../../../../utils/logging/warn';\n\nimport type { BlendModeFilter } from '../../../../filters/blend-modes/BlendModeFilter';\nimport type { FilterInstruction } from '../../../../filters/FilterSystem';\nimport type { Renderer } from '../../types';\nimport type { Instruction } from '../instructions/Instruction';\nimport type { InstructionSet } from '../instructions/InstructionSet';\nimport type { InstructionPipe } from '../instructions/RenderPipe';\nimport type { Renderable } from '../Renderable';\nimport type { BLEND_MODES } from '../state/const';\n\ninterface AdvancedBlendInstruction extends Instruction\n{\n    renderPipeId: 'blendMode',\n    blendMode: BLEND_MODES,\n    activeBlend: Renderable[],\n}\n\n// class map\nconst BLEND_MODE_FILTERS: Partial<Record<BLEND_MODES, new () => BlendModeFilter>> = {} as const;\n\nextensions.handle(ExtensionType.BlendMode, (value) =>\n{\n    if (!value.name)\n    {\n        throw new Error('BlendMode extension must have a name property');\n    }\n    BLEND_MODE_FILTERS[value.name as BLEND_MODES] = value.ref;\n}, (value) =>\n{\n    delete BLEND_MODE_FILTERS[value.name as BLEND_MODES];\n});\n\n/**\n * This Pipe handles the blend mode switching of the renderer.\n * It will insert instructions into the {@link InstructionSet} to switch the blend mode according to the\n * blend modes of the scene graph.\n *\n * This pipe is were wwe handle Advanced blend modes. Advanced blend modes essentially wrap the renderables\n * in a filter that applies the blend mode.\n *\n * You only need to use this class if you are building your own render instruction set rather than letting PixiJS build\n * the instruction set for you by traversing the scene graph\n * @category rendering\n * @internal\n */\nexport class BlendModePipe implements InstructionPipe<AdvancedBlendInstruction>\n{\n    /** @ignore */\n    public static extension = {\n        type: [\n            ExtensionType.WebGLPipes,\n            ExtensionType.WebGPUPipes,\n            ExtensionType.CanvasPipes,\n        ],\n        name: 'blendMode',\n    } as const;\n\n    private _renderer: Renderer;\n\n    private _renderableList: Renderable[];\n    private _activeBlendMode: BLEND_MODES;\n\n    private _isAdvanced = false;\n\n    private _filterHash: Partial<Record<BLEND_MODES, FilterEffect>> = Object.create(null);\n\n    constructor(renderer: Renderer)\n    {\n        this._renderer = renderer;\n        this._renderer.runners.prerender.add(this);\n    }\n\n    public prerender()\n    {\n        // make sure we reset the blend modes to normal\n        // this way the next render will register any changes\n        this._activeBlendMode = 'normal';\n        this._isAdvanced = false;\n    }\n\n    /**\n     * This ensures that a blendMode switch is added to the instruction set if the blend mode has changed.\n     * @param renderable - The renderable we are adding to the instruction set\n     * @param blendMode - The blend mode of the renderable\n     * @param instructionSet - The instruction set we are adding to\n     */\n    public setBlendMode(renderable: Renderable, blendMode: BLEND_MODES, instructionSet: InstructionSet)\n    {\n        if (this._activeBlendMode === blendMode)\n        {\n            if (this._isAdvanced) this._renderableList.push(renderable);\n\n            return;\n        }\n\n        this._activeBlendMode = blendMode;\n\n        if (this._isAdvanced)\n        {\n            this._endAdvancedBlendMode(instructionSet);\n        }\n\n        this._isAdvanced = !!BLEND_MODE_FILTERS[blendMode];\n\n        if (this._isAdvanced)\n        {\n            this._beginAdvancedBlendMode(instructionSet);\n\n            this._renderableList.push(renderable);\n        }\n    }\n\n    private _beginAdvancedBlendMode(instructionSet: InstructionSet)\n    {\n        this._renderer.renderPipes.batch.break(instructionSet);\n\n        const blendMode = this._activeBlendMode;\n\n        if (!BLEND_MODE_FILTERS[blendMode as keyof typeof BLEND_MODE_FILTERS])\n        {\n            // #if _DEBUG\n            warn(`Unable to assign BlendMode: '${blendMode}'. `\n            + `You may want to include: import 'pixi.js/advanced-blend-modes'`);\n            // #endif\n\n            return;\n        }\n\n        let filterEffect = this._filterHash[blendMode];\n\n        // this does need an execute?\n        if (!filterEffect)\n        {\n            filterEffect = this._filterHash[blendMode] = new FilterEffect();\n\n            filterEffect.filters = [new BLEND_MODE_FILTERS[blendMode as keyof typeof BLEND_MODE_FILTERS]()];\n        }\n\n        const instruction: FilterInstruction = {\n            renderPipeId: 'filter',\n            action: 'pushFilter',\n            renderables: [],\n            filterEffect,\n            canBundle: false,\n        };\n\n        this._renderableList = instruction.renderables;\n        instructionSet.add(instruction);\n    }\n\n    private _endAdvancedBlendMode(instructionSet: InstructionSet)\n    {\n        this._renderableList = null;\n        this._renderer.renderPipes.batch.break(instructionSet);\n\n        instructionSet.add({\n            renderPipeId: 'filter',\n            action: 'popFilter',\n            canBundle: false,\n        });\n    }\n\n    /**\n     * called when the instruction build process is starting this will reset internally to the default blend mode\n     * @internal\n     */\n    public buildStart()\n    {\n        this._isAdvanced = false;\n    }\n\n    /**\n     * called when the instruction build process is finished, ensuring that if there is an advanced blend mode\n     * active, we add the final render instructions added to the instruction set\n     * @param instructionSet - The instruction set we are adding to\n     * @internal\n     */\n    public buildEnd(instructionSet: InstructionSet)\n    {\n        if (this._isAdvanced)\n        {\n            this._endAdvancedBlendMode(instructionSet);\n        }\n    }\n\n    /** @internal */\n    public destroy()\n    {\n        this._renderer = null;\n        this._renderableList = null;\n\n        for (const i in this._filterHash)\n        {\n            this._filterHash[i as BLEND_MODES].destroy();\n        }\n\n        this._filterHash = null;\n    }\n}\n", "import { ExtensionType } from '../../../../extensions/Extensions';\nimport { Container } from '../../../../scene/container/Container';\nimport { Texture } from '../texture/Texture';\n\nimport type { ColorSource } from '../../../../color/Color';\nimport type { ICanvas } from '../../../../environment/canvas/ICanvas';\nimport type { Rectangle } from '../../../../maths/shapes/Rectangle';\nimport type { Renderer } from '../../types';\nimport type { System } from '../system/System';\nimport type { GetPixelsOutput } from '../texture/GenerateCanvas';\nimport type { GenerateTextureOptions } from './GenerateTextureSystem';\n\nconst imageTypes = {\n    png: 'image/png',\n    jpg: 'image/jpeg',\n    webp: 'image/webp',\n};\n\ntype Formats = keyof typeof imageTypes;\n\n/**\n * Options for creating an image from a renderer.\n * Controls the output format and quality of extracted images.\n * @example\n * ```ts\n * // Extract as PNG (default)\n * const pngImage = await renderer.extract.image({\n *     target: sprite,\n *     format: 'png'\n * });\n *\n * // Extract as JPEG with quality setting\n * const jpgImage = await renderer.extract.image({\n *     target: sprite,\n *     format: 'jpg',\n *     quality: 0.8\n * });\n *\n * // Extract as WebP for better compression\n * const webpImage = await renderer.extract.image({\n *     target: sprite,\n *     format: 'webp',\n *     quality: 0.9\n * });\n * ```\n * @category rendering\n * @advanced\n */\nexport interface ImageOptions\n{\n    /**\n     * The format of the extracted image.\n     * - 'png': Lossless format, best for images with text or sharp edges\n     * - 'jpg': Lossy format, smaller file size, good for photos\n     * - 'webp': Modern format with better compression\n     * @example\n     * ```ts\n     * // Extract as PNG\n     * const pngImage = await renderer.extract.image({\n     *     target: sprite,\n     *     format: 'png'\n     * });\n     * // Extract as JPEG\n     * const jpgImage = await renderer.extract.image({\n     *     target: sprite,\n     *     format: 'jpg',\n     * });\n     * ```\n     * @default 'png'\n     */\n    format?: Formats;\n\n    /**\n     * The quality of the extracted image, between 0 and 1.\n     * Only applies to lossy formats (jpg, webp).\n     * - 1: Maximum quality\n     * - 0: Maximum compression\n     * @example\n     * ```ts\n     * // Extract as JPEG with 80% quality\n     * const jpgImage = await renderer.extract.image({\n     *     target: sprite,\n     *     format: 'jpg',\n     *     quality: 0.8\n     * });\n     * // Extract as WebP with 90% quality\n     * const webpImage = await renderer.extract.image({\n     *     target: sprite,\n     *     format: 'webp',\n     *     quality: 0.9\n     * });\n     * ```\n     * @default 1\n     */\n    quality?: number;\n}\n\n/**\n * Options for extracting content from a renderer.\n * These options control how content is extracted and processed from the renderer.\n * @example\n * ```ts\n * // Basic extraction\n * const pixels = renderer.extract.pixels({\n *     target: sprite,\n * });\n *\n * // Extract with custom region and resolution\n * const canvas = renderer.extract.canvas({\n *     target: container,\n *     frame: new Rectangle(0, 0, 100, 100),\n *     resolution: 2,\n * });\n *\n * // Extract with background color and anti-aliasing\n * const image = await renderer.extract.image({\n *     target: graphics,\n *     clearColor: '#ff0000',\n *     antialias: true\n * });\n * ```\n * @category rendering\n * @advanced\n */\nexport interface BaseExtractOptions\n{\n    /**\n     * The target to extract. Can be a Container or Texture.\n     * @example\n     * ```ts\n     * // Extract from a sprite\n     * const sprite = new Sprite(texture);\n     * renderer.extract.pixels({ target: sprite });\n     *\n     * // Extract from a texture directly\n     * renderer.extract.pixels({ target: texture });\n     * ```\n     */\n    target: Container | Texture;\n\n    /**\n     * The region of the target to extract. If not specified, extracts the entire target.\n     * @example\n     * ```ts\n     * // Extract a specific region\n     * renderer.extract.canvas({\n     *     target: sprite,\n     *     frame: new Rectangle(10, 10, 100, 100)\n     * });\n     * ```\n     */\n    frame?: Rectangle;\n\n    /**\n     * The resolution of the extracted content. Higher values create sharper images.\n     * @default 1\n     * @example\n     * ```ts\n     * // Extract at 2x resolution for retina displays\n     * renderer.extract.image({\n     *     target: sprite,\n     *     resolution: 2\n     * });\n     * ```\n     */\n    resolution?: number;\n\n    /**\n     * The color used to clear the extracted content before rendering.\n     * Can be a hex number, string, or array of numbers.\n     * @example\n     * ```ts\n     * // Clear with red background\n     * renderer.extract.canvas({\n     *     target: sprite,\n     *     clearColor: '#ff0000'\n     * });\n     *\n     * // Clear with semi-transparent black\n     * renderer.extract.canvas({\n     *     target: sprite,\n     *     clearColor: [0, 0, 0, 0.5]\n     * });\n     * ```\n     */\n    clearColor?: ColorSource;\n\n    /**\n     * Whether to enable anti-aliasing during extraction.\n     * Improves quality but may affect performance.\n     * @default false\n     * @example\n     * ```ts\n     * // Enable anti-aliasing for smoother edges\n     * renderer.extract.image({\n     *     target: graphics,\n     *     antialias: true\n     * });\n     * ```\n     */\n    antialias?: boolean;\n}\n/**\n * Options for extracting an HTMLImage from the renderer.\n * Combines base extraction options with image-specific settings.\n * @example\n * ```ts\n * // Basic PNG extraction\n * const image = await renderer.extract.image({\n *     target: sprite,\n *     format: 'png'\n * });\n *\n * // High-quality JPEG with custom region\n * const image = await renderer.extract.image({\n *     target: container,\n *     format: 'jpg',\n *     quality: 0.9,\n *     frame: new Rectangle(0, 0, 100, 100),\n *     resolution: 2\n * });\n *\n * // WebP with background and anti-aliasing\n * const image = await renderer.extract.image({\n *     target: graphics,\n *     format: 'webp',\n *     quality: 0.8,\n *     clearColor: '#ff0000',\n *     antialias: true\n * });\n * ```\n *\n * Combines all options from:\n * - {@link BaseExtractOptions} for basic extraction settings\n * - {@link ImageOptions} for image format and quality settings\n *\n * Common use cases:\n * - Capturing game screenshots\n * - Saving rendered content\n * - Creating image thumbnails\n * - Exporting canvas content\n * @see {@link ExtractSystem.image} For the method that uses these options\n * @see {@link ExtractSystem.base64} For base64 encoding\n * @category rendering\n * @advanced\n * @interface\n */\nexport type ExtractImageOptions = BaseExtractOptions & ImageOptions;\n/**\n * Options for extracting and downloading content from a renderer.\n * Combines base extraction options with download-specific settings.\n * @example\n * ```ts\n * // Basic download with default filename\n * renderer.extract.download({\n *     target: sprite\n * });\n *\n * // Download with custom filename and region\n * renderer.extract.download({\n *     target: container,\n *     filename: 'screenshot.png',\n *     frame: new Rectangle(0, 0, 100, 100)\n * });\n *\n * // Download with high resolution and background\n * renderer.extract.download({\n *     target: stage,\n *     filename: 'hd-capture.png',\n *     resolution: 2,\n *     clearColor: '#ff0000'\n * });\n *\n * // Download with anti-aliasing\n * renderer.extract.download({\n *     target: graphics,\n *     filename: 'smooth.png',\n *     antialias: true\n * });\n * ```\n *\n * Combines all options from:\n * - {@link BaseExtractOptions} for basic extraction settings\n * - Additional download-specific options\n *\n * Common use cases:\n * - Saving game screenshots\n * - Exporting rendered content\n * - Creating downloadable assets\n * - Saving canvas state\n * @see {@link ExtractSystem.download} For the method that uses these options\n * @see {@link ExtractSystem.image} For creating images without download\n * @category rendering\n * @advanced\n * @interface\n */\nexport type ExtractDownloadOptions = BaseExtractOptions & {\n    /**\n     * The filename to use when downloading the content.\n     * Should include the desired file extension (e.g., .png).\n     * @default 'image.png'\n     * @example\n     * ```ts\n     * renderer.extract.download({\n     *     target: sprite,\n     *     filename: 'my-screenshot.png'\n     * });\n     * ```\n     */\n    filename: string;\n};\n/**\n * Options for extracting content from a renderer. Represents a union of all possible extraction option types.\n * Used by various extraction methods to support different output formats and configurations.\n * @example\n * ```ts\n * // Basic canvas extraction\n * const canvas = renderer.extract.canvas({\n *     target: sprite\n * });\n *\n * // Image extraction with format\n * const image = await renderer.extract.image({\n *     target: sprite,\n *     format: 'png',\n *     quality: 1\n * });\n *\n * // Download with filename\n * renderer.extract.download({\n *     target: sprite,\n *     filename: 'screenshot.png'\n * });\n *\n * // Advanced extraction with multiple options\n * const image = await renderer.extract.image({\n *     target: container,\n *     frame: new Rectangle(0, 0, 100, 100),\n *     resolution: 2,\n *     clearColor: '#ff0000',\n *     antialias: true,\n *     format: 'webp',\n *     quality: 0.8\n * });\n * ```\n *\n * Supports three types of options:\n * - {@link BaseExtractOptions} - Basic extraction settings\n * - {@link ExtractImageOptions} - Image-specific settings with format and quality\n * - {@link ExtractDownloadOptions} - Download settings with filename\n *\n * Common use cases:\n * - Extracting raw pixels\n * - Creating canvas elements\n * - Generating downloadable images\n * - Taking screenshots\n * - Creating thumbnails\n * @see {@link ExtractSystem.canvas} For canvas extraction\n * @see {@link ExtractSystem.image} For image extraction\n * @see {@link ExtractSystem.download} For downloading content\n * @category rendering\n * @advanced\n */\nexport type ExtractOptions = BaseExtractOptions | ExtractImageOptions | ExtractDownloadOptions;\n\n/**\n * System for exporting content from a renderer. It provides methods to extract content as images,\n * canvases, or raw pixel data. Available through `renderer.extract`.\n * @example\n * ```ts\n * import { Application, Graphics } from 'pixi.js';\n *\n * // Create a new application\n * const app = new Application();\n * await app.init();\n *\n * // Draw something to extract\n * const graphics = new Graphics()\n *     .circle(0, 0, 50)\n *     .fill(0xFF0000);\n *\n * // Basic extraction examples\n * const image = await app.renderer.extract.image(graphics);    // As HTMLImageElement\n * const canvas = app.renderer.extract.canvas(graphics);        // As Canvas\n * const pixels = app.renderer.extract.pixels(graphics);        // As pixel data\n * const base64 = await app.renderer.extract.base64(graphics); // As base64 string\n *\n * // Advanced extraction with options\n * const customImage = await app.renderer.extract.image({\n *     target: graphics,\n *     format: 'png',\n *     resolution: 2,\n *     frame: new Rectangle(0, 0, 100, 100),\n *     clearColor: '#00000000'\n * });\n *\n * // Download content\n * app.renderer.extract.download({\n *     target: graphics,\n *     filename: 'my-image.png'\n * });\n *\n * // Debug visualization\n * app.renderer.extract.log(graphics);\n * ```\n *\n * Features:\n * - Extract as various formats (PNG, JPEG, WebP)\n * - Control output quality and resolution\n * - Extract specific regions\n * - Download extracted content\n * - Debug visualization\n *\n * Common Use Cases:\n * - Creating thumbnails\n * - Saving game screenshots\n * - Processing visual content\n * - Debugging renders\n * - Creating textures from rendered content\n *\n * Performance Considerations:\n * - Extraction operations are relatively expensive\n * - Consider caching results for frequently used content\n * - Be mindful of resolution and format choices\n * - Large extractions may impact performance\n * @category rendering\n * @standard\n */\nexport class ExtractSystem implements System\n{\n    /** @ignore */\n    public static extension = {\n        type: [\n            ExtensionType.WebGLSystem,\n            ExtensionType.WebGPUSystem,\n        ],\n        name: 'extract',\n    } as const;\n\n    /**\n     * Default options for image extraction.\n     * @example\n     * ```ts\n     * // Customize default options\n     * ExtractSystem.defaultImageOptions.format = 'webp';\n     * ExtractSystem.defaultImageOptions.quality = 0.8;\n     *\n     * // Use defaults\n     * const image = await renderer.extract.image(sprite);\n     * ```\n     */\n    public static defaultImageOptions: ImageOptions = {\n        format: 'png' as Formats,\n        quality: 1,\n    };\n\n    private _renderer: Renderer;\n\n    /** @param renderer - The renderer this System works for. */\n    constructor(renderer: Renderer)\n    {\n        this._renderer = renderer;\n    }\n\n    private _normalizeOptions<T extends ExtractOptions>(\n        options: ExtractImageOptions | Container | Texture,\n        defaults: Partial<T> = {},\n    ): T\n    {\n        if (options instanceof Container || options instanceof Texture)\n        {\n            return {\n                target: options,\n                ...defaults\n            } as T;\n        }\n\n        return {\n            ...defaults,\n            ...options,\n        } as T;\n    }\n\n    /**\n     * Creates an HTMLImageElement from a display object or texture.\n     * @param options - Options for creating the image, or the target to extract\n     * @returns Promise that resolves with the generated HTMLImageElement\n     * @example\n     * ```ts\n     * // Basic usage with a sprite\n     * const sprite = new Sprite(texture);\n     * const image = await renderer.extract.image(sprite);\n     * document.body.appendChild(image);\n     *\n     * // Advanced usage with options\n     * const image = await renderer.extract.image({\n     *     target: container,\n     *     format: 'webp',\n     *     quality: 0.8,\n     *     frame: new Rectangle(0, 0, 100, 100),\n     *     resolution: 2,\n     *     clearColor: '#ff0000',\n     *     antialias: true\n     * });\n     *\n     * // Extract directly from a texture\n     * const texture = Texture.from('myTexture.png');\n     * const image = await renderer.extract.image(texture);\n     * ```\n     * @see {@link ExtractImageOptions} For detailed options\n     * @see {@link ExtractSystem.base64} For base64 string output\n     * @see {@link ExtractSystem.canvas} For canvas output\n     * @category rendering\n     */\n    public async image(options: ExtractImageOptions | Container | Texture): Promise<HTMLImageElement>\n    {\n        const image = new Image();\n\n        image.src = await this.base64(options);\n\n        return image;\n    }\n\n    /**\n     * Converts the target into a base64 encoded string.\n     *\n     * This method works by first creating\n     * a canvas using `Extract.canvas` and then converting it to a base64 string.\n     * @param options - The options for creating the base64 string, or the target to extract\n     * @returns Promise that resolves with the base64 encoded string\n     * @example\n     * ```ts\n     * // Basic usage with a sprite\n     * const sprite = new Sprite(texture);\n     * const base64 = await renderer.extract.base64(sprite);\n     * console.log(base64); // data:image/png;base64,...\n     *\n     * // Advanced usage with options\n     * const base64 = await renderer.extract.base64({\n     *     target: container,\n     *     format: 'webp',\n     *     quality: 0.8,\n     *     frame: new Rectangle(0, 0, 100, 100),\n     *     resolution: 2\n     * });\n     * ```\n     * @throws Will throw an error if the platform doesn't support any of:\n     * - ICanvas.toDataURL\n     * - ICanvas.toBlob\n     * - ICanvas.convertToBlob\n     * @see {@link ExtractImageOptions} For detailed options\n     * @see {@link ExtractSystem.canvas} For canvas output\n     * @see {@link ExtractSystem.image} For HTMLImage output\n     * @category rendering\n     */\n    public async base64(options: ExtractImageOptions | Container | Texture): Promise<string>\n    {\n        options = this._normalizeOptions<ExtractImageOptions>(\n            options,\n            ExtractSystem.defaultImageOptions\n        );\n\n        const { format, quality } = options;\n\n        const canvas = this.canvas(options);\n\n        if (canvas.toBlob !== undefined)\n        {\n            return new Promise<string>((resolve, reject) =>\n            {\n                canvas.toBlob!((blob) =>\n                {\n                    if (!blob)\n                    {\n                        reject(new Error('ICanvas.toBlob failed!'));\n\n                        return;\n                    }\n\n                    const reader = new FileReader();\n\n                    reader.onload = () => resolve(reader.result as string);\n                    reader.onerror = reject;\n                    reader.readAsDataURL(blob);\n                }, imageTypes[format], quality);\n            });\n        }\n        if (canvas.toDataURL !== undefined)\n        {\n            return canvas.toDataURL(imageTypes[format], quality);\n        }\n        if (canvas.convertToBlob !== undefined)\n        {\n            const blob = await canvas.convertToBlob({ type: imageTypes[format], quality });\n\n            return new Promise<string>((resolve, reject) =>\n            {\n                const reader = new FileReader();\n\n                reader.onload = () => resolve(reader.result as string);\n                reader.onerror = reject;\n                reader.readAsDataURL(blob);\n            });\n        }\n\n        throw new Error('Extract.base64() requires ICanvas.toDataURL, ICanvas.toBlob, '\n            + 'or ICanvas.convertToBlob to be implemented');\n    }\n\n    /**\n     * Creates a Canvas element, renders the target to it and returns it.\n     * This method is useful for creating static images or when you need direct canvas access.\n     * @param options - The options for creating the canvas, or the target to extract\n     * @returns A Canvas element with the texture rendered on\n     * @example\n     * ```ts\n     * // Basic canvas extraction from a sprite\n     * const sprite = new Sprite(texture);\n     * const canvas = renderer.extract.canvas(sprite);\n     * document.body.appendChild(canvas);\n     *\n     * // Extract with custom region\n     * const canvas = renderer.extract.canvas({\n     *     target: container,\n     *     frame: new Rectangle(0, 0, 100, 100)\n     * });\n     *\n     * // Extract with high resolution\n     * const canvas = renderer.extract.canvas({\n     *     target: sprite,\n     *     resolution: 2,\n     *     clearColor: '#ff0000'\n     * });\n     *\n     * // Extract directly from a texture\n     * const texture = Texture.from('myTexture.png');\n     * const canvas = renderer.extract.canvas(texture);\n     *\n     * // Extract with anti-aliasing\n     * const canvas = renderer.extract.canvas({\n     *     target: graphics,\n     *     antialias: true\n     * });\n     * ```\n     * @see {@link ExtractOptions} For detailed options\n     * @see {@link ExtractSystem.image} For HTMLImage output\n     * @see {@link ExtractSystem.pixels} For raw pixel data\n     * @category rendering\n     */\n    public canvas(options: ExtractOptions | Container | Texture): ICanvas\n    {\n        options = this._normalizeOptions(options);\n\n        const target = options.target;\n\n        const renderer = this._renderer;\n\n        if (target instanceof Texture)\n        {\n            return renderer.texture.generateCanvas(target);\n        }\n\n        const texture = renderer.textureGenerator.generateTexture(options as GenerateTextureOptions);\n\n        const canvas = renderer.texture.generateCanvas(texture);\n\n        texture.destroy(true);\n\n        return canvas;\n    }\n\n    /**\n     * Returns a one-dimensional array containing the pixel data of the entire texture in RGBA order,\n     * with integer values between 0 and 255 (inclusive).\n     * > [!NOE] The returned array is a flat Uint8Array where every 4 values represent RGBA\n     * @param options - The options for extracting the image, or the target to extract\n     * @returns One-dimensional Uint8Array containing the pixel data in RGBA format\n     * @example\n     * ```ts\n     * // Basic pixel extraction\n     * const sprite = new Sprite(texture);\n     * const pixels = renderer.extract.pixels(sprite);\n     * console.log(pixels[0], pixels[1], pixels[2], pixels[3]); // R,G,B,A values\n     *\n     * // Extract with custom region\n     * const pixels = renderer.extract.pixels({\n     *     target: sprite,\n     *     frame: new Rectangle(0, 0, 100, 100)\n     * });\n     *\n     * // Extract with high resolution\n     * const pixels = renderer.extract.pixels({\n     *     target: sprite,\n     *     resolution: 2\n     * });\n     * ```\n     * @see {@link ExtractOptions} For detailed options\n     * @see {@link ExtractSystem.canvas} For canvas output\n     * @see {@link ExtractSystem.image} For image output\n     * @category rendering\n     */\n    public pixels(options: ExtractOptions | Container | Texture): GetPixelsOutput\n    {\n        options = this._normalizeOptions(options);\n\n        const target = options.target;\n\n        const renderer = this._renderer;\n        const texture = target instanceof Texture\n            ? target\n            : renderer.textureGenerator.generateTexture(options as GenerateTextureOptions);\n\n        const pixelInfo = renderer.texture.getPixels(texture);\n\n        if (target instanceof Container)\n        {\n            // destroy generated texture\n            texture.destroy(true);\n        }\n\n        return pixelInfo;\n    }\n\n    /**\n     * Creates a texture from a display object or existing texture.\n     *\n     * This is useful for creating\n     * reusable textures from rendered content or making copies of existing textures.\n     * > [!NOTE] The returned texture should be destroyed when no longer needed\n     * @param options - The options for creating the texture, or the target to extract\n     * @returns A new texture containing the extracted content\n     * @example\n     * ```ts\n     * // Basic texture extraction from a sprite\n     * const sprite = new Sprite(texture);\n     * const extractedTexture = renderer.extract.texture(sprite);\n     *\n     * // Extract with custom region\n     * const regionTexture = renderer.extract.texture({\n     *     target: container,\n     *     frame: new Rectangle(0, 0, 100, 100)\n     * });\n     *\n     * // Extract with high resolution\n     * const hiResTexture = renderer.extract.texture({\n     *     target: sprite,\n     *     resolution: 2,\n     *     clearColor: '#ff0000'\n     * });\n     *\n     * // Create a new sprite from extracted texture\n     * const newSprite = new Sprite(\n     *     renderer.extract.texture({\n     *         target: graphics,\n     *         antialias: true\n     *     })\n     * );\n     *\n     * // Clean up when done\n     * extractedTexture.destroy(true);\n     * ```\n     * @see {@link ExtractOptions} For detailed options\n     * @see {@link Texture} For texture management\n     * @see {@link GenerateTextureSystem} For texture generation\n     * @category rendering\n     */\n    public texture(options: ExtractOptions | Container | Texture): Texture\n    {\n        options = this._normalizeOptions(options);\n\n        if (options.target instanceof Texture) return options.target;\n\n        return this._renderer.textureGenerator.generateTexture(options as GenerateTextureOptions);\n    }\n\n    /**\n     * Extracts and downloads content from the renderer as an image file.\n     * This is a convenient way to save screenshots or export rendered content.\n     * > [!NOTE] The download will use PNG format regardless of the filename extension\n     * @param options - The options for downloading and extracting the image, or the target to extract\n     * @example\n     * ```ts\n     * // Basic download with default filename\n     * const sprite = new Sprite(texture);\n     * renderer.extract.download(sprite); // Downloads as 'image.png'\n     *\n     * // Download with custom filename\n     * renderer.extract.download({\n     *     target: sprite,\n     *     filename: 'screenshot.png'\n     * });\n     *\n     * // Download with custom region\n     * renderer.extract.download({\n     *     target: container,\n     *     filename: 'region.png',\n     *     frame: new Rectangle(0, 0, 100, 100)\n     * });\n     *\n     * // Download with high resolution and background\n     * renderer.extract.download({\n     *     target: stage,\n     *     filename: 'hd-screenshot.png',\n     *     resolution: 2,\n     *     clearColor: '#ff0000'\n     * });\n     *\n     * // Download with anti-aliasing\n     * renderer.extract.download({\n     *     target: graphics,\n     *     filename: 'smooth.png',\n     *     antialias: true\n     * });\n     * ```\n     * @see {@link ExtractDownloadOptions} For detailed options\n     * @see {@link ExtractSystem.image} For creating images without download\n     * @see {@link ExtractSystem.canvas} For canvas output\n     * @category rendering\n     */\n    public download(options: ExtractDownloadOptions | Container | Texture)\n    {\n        options = this._normalizeOptions<ExtractDownloadOptions>(options);\n\n        const canvas = this.canvas(options);\n\n        const link = document.createElement('a');\n\n        link.download = options.filename ?? 'image.png';\n        link.href = canvas.toDataURL('image/png');\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n    }\n\n    /**\n     * Logs the target to the console as an image. This is a useful way to debug what's happening in the renderer.\n     * The image will be displayed in the browser's console using CSS background images.\n     * @param options - The options for logging the image, or the target to log\n     * @param options.width - The width of the logged image preview in the console (in pixels)\n     * @example\n     * ```ts\n     * // Basic usage\n     * const sprite = new Sprite(texture);\n     * renderer.extract.log(sprite);\n     * ```\n     * @see {@link ExtractSystem.canvas} For getting raw canvas output\n     * @see {@link ExtractSystem.pixels} For raw pixel data\n     * @category rendering\n     * @advanced\n     */\n    public log(options: (ExtractOptions & {width?: number}) | Container | Texture)\n    {\n        const width = options.width ?? 200;\n\n        options = this._normalizeOptions(options);\n\n        const canvas = this.canvas(options);\n\n        const base64 = canvas.toDataURL();\n\n        // eslint-disable-next-line no-console\n        console.log(`[Pixi Texture] ${canvas.width}px ${canvas.height}px`);\n\n        const style = [\n            'font-size: 1px;',\n            `padding: ${width}px ${300}px;`,\n            `background: url(${base64}) no-repeat;`,\n            'background-size: contain;',\n        ].join(' ');\n\n        // eslint-disable-next-line no-console\n        console.log('%c ', style);\n    }\n\n    public destroy(): void\n    {\n        this._renderer = null as any as Renderer;\n    }\n}\n", "import { TextureSource } from './sources/TextureSource';\nimport { Texture } from './Texture';\n\nimport type { TextureSourceOptions } from './sources/TextureSource';\n\n/**\n * A render texture, extends `Texture`.\n * @see {@link Texture}\n * @category rendering\n * @advanced\n */\nexport class RenderTexture extends Texture\n{\n    public static create(options: TextureSourceOptions): RenderTexture\n    {\n        return new RenderTexture({\n            source: new TextureSource(options)\n        });\n    }\n\n    /**\n     * Resizes the render texture.\n     * @param width - The new width of the render texture.\n     * @param height - The new height of the render texture.\n     * @param resolution - The new resolution of the render texture.\n     * @returns This texture.\n     */\n    public resize(width: number, height: number, resolution?: number): this\n    {\n        this.source.resize(width, height, resolution);\n\n        return this;\n    }\n}\n", "import { Color, type ColorSource } from '../../../../color/Color';\nimport { ExtensionType } from '../../../../extensions/Extensions';\nimport { Matrix } from '../../../../maths/matrix/Matrix';\nimport { Rectangle } from '../../../../maths/shapes/Rectangle';\nimport { Bounds } from '../../../../scene/container/bounds/Bounds';\nimport { getLocalBounds } from '../../../../scene/container/bounds/getLocalBounds';\nimport { Container } from '../../../../scene/container/Container';\nimport { RenderTexture } from '../texture/RenderTexture';\n\nimport type { Renderer } from '../../types';\nimport type { System } from '../system/System';\nimport type { TextureSourceOptions } from '../texture/sources/TextureSource';\n\n/**\n * Options for generating a texture source.\n * @category rendering\n * @advanced\n * @interface\n */\nexport type GenerateTextureSourceOptions = Omit<TextureSourceOptions, 'resource' | 'width' | 'height' | 'resolution'>;\n\n/**\n * Options for generating a texture from a container.\n * Used to create reusable textures from display objects, which can improve performance\n * when the same content needs to be rendered multiple times.\n * @example\n * ```ts\n * // Basic texture generation\n * const sprite = new Sprite(texture);\n * const generatedTexture = renderer.generateTexture({\n *     target: sprite\n * });\n *\n * // Generate with custom region and resolution\n * const texture = renderer.generateTexture({\n *     target: container,\n *     frame: new Rectangle(0, 0, 100, 100),\n *     resolution: 2\n * });\n *\n * // Generate with background color and anti-aliasing\n * const highQualityTexture = renderer.generateTexture({\n *     target: graphics,\n *     clearColor: '#ff0000',\n *     antialias: true,\n *     textureSourceOptions: {\n *         scaleMode: 'linear'\n *     }\n * });\n * ```\n * @category rendering\n * @advanced\n */\nexport type GenerateTextureOptions = {\n    /**\n     * The container to generate the texture from.\n     * This can be any display object like Sprite, Container, or Graphics.\n     * @example\n     * ```ts\n     * const graphics = new Graphics()\n     *     .circle(0, 0, 50)\n     *     .fill('red');\n     *\n     * const texture = renderer.generateTexture({\n     *     target: graphics\n     * });\n     * ```\n     */\n    target: Container;\n\n    /**\n     * The region of the container that should be rendered.\n     * If not specified, defaults to the local bounds of the container.\n     * @example\n     * ```ts\n     * // Extract only a portion of the container\n     * const texture = renderer.generateTexture({\n     *     target: container,\n     *     frame: new Rectangle(10, 10, 100, 100)\n     * });\n     * ```\n     */\n    frame?: Rectangle;\n\n    /**\n     * The resolution of the texture being generated.\n     * Higher values create sharper textures at the cost of memory.\n     * @default renderer.resolution\n     * @example\n     * ```ts\n     * // Generate a high-resolution texture\n     * const hiResTexture = renderer.generateTexture({\n     *     target: sprite,\n     *     resolution: 2 // 2x resolution\n     * });\n     * ```\n     */\n    resolution?: number;\n\n    /**\n     * The color used to clear the texture before rendering.\n     * Can be a hex number, string, or array of numbers.\n     * @example\n     * ```ts\n     * // Clear with red background\n     * const texture = renderer.generateTexture({\n     *     target: sprite,\n     *     clearColor: '#ff0000'\n     * });\n     *\n     * // Clear with semi-transparent black\n     * const texture = renderer.generateTexture({\n     *     target: sprite,\n     *     clearColor: [0, 0, 0, 0.5]\n     * });\n     * ```\n     */\n    clearColor?: ColorSource;\n\n    /**\n     * Whether to enable anti-aliasing. This may affect performance.\n     * @default false\n     * @example\n     * ```ts\n     * // Generate a smooth texture\n     * const texture = renderer.generateTexture({\n     *     target: graphics,\n     *     antialias: true\n     * });\n     * ```\n     */\n    antialias?: boolean;\n\n    /**\n     * Advanced options for configuring the texture source.\n     * Controls texture properties like scale mode and filtering.\n     * @advanced\n     * @example\n     * ```ts\n     * const texture = renderer.generateTexture({\n     *     target: sprite,\n     *     textureSourceOptions: {\n     *         scaleMode: 'linear',\n     *         multisample: 4\n     *     }\n     * });\n     * ```\n     */\n    textureSourceOptions?: GenerateTextureSourceOptions;\n};\n\nconst tempRect = new Rectangle();\nconst tempBounds = new Bounds();\nconst noColor: ColorSource = [0, 0, 0, 0];\n\n/**\n * System that manages the generation of textures from display objects in the renderer.\n * This system is responsible for creating reusable textures from containers, sprites, and other display objects.\n * Available through `renderer.textureGenerator`.\n * @example\n * ```ts\n * import { Application, Sprite, Graphics } from 'pixi.js';\n *\n * const app = new Application();\n * await app.init();\n *\n * // Create a complex display object\n * const container = new Container();\n *\n * const graphics = new Graphics()\n *     .circle(0, 0, 50)\n *     .fill('red');\n *\n * const sprite = new Sprite(texture);\n * sprite.x = 100;\n *\n * container.addChild(graphics, sprite);\n *\n * // Generate a texture from the container\n * const generatedTexture = app.renderer.textureGenerator.generateTexture({\n *     target: container,\n *     resolution: 2,\n *     antialias: true\n * });\n *\n * // Use the generated texture\n * const newSprite = new Sprite(generatedTexture);\n * app.stage.addChild(newSprite);\n *\n * // Clean up when done\n * generatedTexture.destroy(true);\n * ```\n *\n * Features:\n * - Convert any display object to a texture\n * - Support for custom regions and resolutions\n * - Anti-aliasing support\n * - Background color configuration\n * - Texture source options customization\n *\n * Common Use Cases:\n * - Creating texture atlases dynamically\n * - Caching complex container content\n * - Generating thumbnails\n * - Creating reusable textures from rendered content\n *\n * Performance Considerations:\n * - Generating textures is relatively expensive\n * - Cache results when possible\n * - Be mindful of resolution and size\n * - Clean up unused textures\n * @see {@link GenerateTextureOptions} For detailed texture generation options\n * @see {@link AbstractRenderer.generateTexture} For the main renderer method\n * @see {@link RenderTexture} For the resulting texture type\n * @category rendering\n * @standard\n */\nexport class GenerateTextureSystem implements System\n{\n    /** @ignore */\n    public static extension = {\n        type: [\n            ExtensionType.WebGLSystem,\n            ExtensionType.WebGPUSystem,\n        ],\n        name: 'textureGenerator',\n    } as const;\n\n    private readonly _renderer: Renderer;\n\n    constructor(renderer: Renderer)\n    {\n        this._renderer = renderer;\n    }\n\n    /**\n     * Creates a texture from a display object that can be used for creating sprites and other textures.\n     * This is particularly useful for optimizing performance when a complex container needs to be reused.\n     * @param options - Generate texture options or a container to convert to texture\n     * @returns A new RenderTexture containing the rendered display object\n     * @example\n     * ```ts\n     * // Basic usage with a container\n     * const container = new Container();\n     * container.addChild(\n     *     new Graphics()\n     *         .circle(0, 0, 50)\n     *         .fill('red')\n     * );\n     *\n     * const texture = renderer.textureGenerator.generateTexture(container);\n     *\n     * // Advanced usage with options\n     * const texture = renderer.textureGenerator.generateTexture({\n     *     target: container,\n     *     frame: new Rectangle(0, 0, 100, 100), // Specific region\n     *     resolution: 2,                        // High DPI\n     *     clearColor: '#ff0000',               // Red background\n     *     antialias: true                      // Smooth edges\n     * });\n     *\n     * // Create a sprite from the generated texture\n     * const sprite = new Sprite(texture);\n     *\n     * // Clean up when done\n     * texture.destroy(true);\n     * ```\n     * @see {@link GenerateTextureOptions} For detailed texture generation options\n     * @see {@link RenderTexture} For the type of texture created\n     * @category rendering\n     */\n    public generateTexture(options: GenerateTextureOptions | Container): RenderTexture\n    {\n        if (options instanceof Container)\n        {\n            options = {\n                target: options,\n                frame: undefined,\n                textureSourceOptions: {},\n                resolution: undefined,\n            };\n        }\n\n        const resolution = options.resolution || this._renderer.resolution;\n        const antialias = options.antialias || this._renderer.view.antialias;\n\n        const container = options.target;\n\n        let clearColor = options.clearColor;\n\n        if (clearColor)\n        {\n            const isRGBAArray = Array.isArray(clearColor) && clearColor.length === 4;\n\n            clearColor = isRGBAArray ? clearColor : Color.shared.setValue(clearColor).toArray();\n        }\n        else\n        {\n            clearColor = noColor;\n        }\n\n        const region = options.frame?.copyTo(tempRect)\n            || getLocalBounds(container, tempBounds).rectangle;\n\n        region.width = Math.max(region.width, 1 / resolution) | 0;\n        region.height = Math.max(region.height, 1 / resolution) | 0;\n\n        const target = RenderTexture.create({\n            ...options.textureSourceOptions,\n            width: region.width,\n            height: region.height,\n            resolution,\n            antialias,\n        });\n\n        const transform = Matrix.shared.translate(-region.x, -region.y);\n\n        this._renderer.render({\n            container,\n            transform,\n            target,\n            clearColor,\n        });\n\n        target.source.updateMipmaps();\n\n        return target;\n    }\n\n    public destroy(): void\n    {\n        (this._renderer as null) = null;\n    }\n}\n", "import { ExtensionType } from '../../../../extensions/Extensions';\nimport { Matrix } from '../../../../maths/matrix/Matrix';\nimport { Point } from '../../../../maths/point/Point';\nimport { color32BitToUniform } from '../../../../scene/graphics/gpu/colorToUniform';\nimport { BindGroup } from '../../gpu/shader/BindGroup';\nimport { type Renderer, RendererType } from '../../types';\nimport { UniformGroup } from '../shader/UniformGroup';\n\nimport type { PointData } from '../../../../maths/point/PointData';\nimport type { GlRenderTargetSystem } from '../../gl/renderTarget/GlRenderTargetSystem';\nimport type { GpuRenderTargetSystem } from '../../gpu/renderTarget/GpuRenderTargetSystem';\nimport type { WebGPURenderer } from '../../gpu/WebGPURenderer';\nimport type { UboSystem } from '../shader/UboSystem';\nimport type { System } from '../system/System';\n\n/**\n * Type definition for the global uniforms used in the renderer.\n * This includes projection matrix, world transform matrix, world color, and resolution.\n * @category rendering\n * @advanced\n */\nexport type GlobalUniformGroup = UniformGroup<{\n    uProjectionMatrix: { value: Matrix; type: 'mat3x3<f32>' }\n    uWorldTransformMatrix: { value: Matrix; type: 'mat3x3<f32>' }\n    uWorldColorAlpha: { value: Float32Array; type: 'vec4<f32>' }\n    uResolution: { value: number[]; type: 'vec2<f32>' }\n}>;\n\n/**\n * Options for the global uniforms system.\n * This includes size, projection matrix, world transform matrix, world color, and offset.\n * @category rendering\n * @advanced\n */\nexport interface GlobalUniformOptions\n{\n    size?: number[],\n    projectionMatrix?: Matrix,\n    worldTransformMatrix?: Matrix\n    worldColor?: number\n    offset?: PointData\n}\n\n/**\n * Data structure for the global uniforms used in the renderer.\n * This includes the projection matrix, world transform matrix, world color, resolution, and bind group.\n * @category rendering\n * @advanced\n */\nexport interface GlobalUniformData\n{\n    projectionMatrix: Matrix\n    worldTransformMatrix: Matrix\n    worldColor: number\n    resolution: number[]\n    offset: PointData\n    bindGroup: BindGroup\n}\n\n/** @internal */\nexport interface GlobalUniformRenderer\n{\n    renderTarget: GlRenderTargetSystem | GpuRenderTargetSystem\n    renderPipes: Renderer['renderPipes'];\n    ubo: UboSystem;\n    type: RendererType;\n}\n\n/**\n * System plugin to the renderer to manage global uniforms for the renderer.\n * @category rendering\n * @advanced\n */\nexport class GlobalUniformSystem implements System\n{\n    /** @ignore */\n    public static extension = {\n        type: [\n            ExtensionType.WebGLSystem,\n            ExtensionType.WebGPUSystem,\n            ExtensionType.CanvasSystem,\n        ],\n        name: 'globalUniforms',\n    } as const;\n\n    private readonly _renderer: GlobalUniformRenderer;\n\n    private _stackIndex = 0;\n    private _globalUniformDataStack: GlobalUniformData[] = [];\n\n    private readonly _uniformsPool: GlobalUniformGroup[] = [];\n    private readonly _activeUniforms: GlobalUniformGroup[] = [];\n\n    private readonly _bindGroupPool: BindGroup[] = [];\n    private readonly _activeBindGroups: BindGroup[] = [];\n\n    private _currentGlobalUniformData: GlobalUniformData;\n\n    constructor(renderer: GlobalUniformRenderer)\n    {\n        this._renderer = renderer;\n    }\n\n    public reset()\n    {\n        this._stackIndex = 0;\n\n        for (let i = 0; i < this._activeUniforms.length; i++)\n        {\n            this._uniformsPool.push(this._activeUniforms[i]);\n        }\n\n        for (let i = 0; i < this._activeBindGroups.length; i++)\n        {\n            this._bindGroupPool.push(this._activeBindGroups[i]);\n        }\n\n        this._activeUniforms.length = 0;\n        this._activeBindGroups.length = 0;\n    }\n\n    public start(options: GlobalUniformOptions): void\n    {\n        this.reset();\n\n        this.push(options);\n    }\n\n    public bind({\n        size,\n        projectionMatrix,\n        worldTransformMatrix,\n        worldColor,\n        offset,\n    }: GlobalUniformOptions)\n    {\n        const renderTarget = this._renderer.renderTarget.renderTarget;\n\n        const currentGlobalUniformData = this._stackIndex ? this._globalUniformDataStack[this._stackIndex - 1] : {\n            projectionData: renderTarget,\n            worldTransformMatrix: new Matrix(),\n            worldColor: 0xFFFFFFFF,\n            offset: new Point(),\n        };\n\n        const globalUniformData: GlobalUniformData = {\n            projectionMatrix: projectionMatrix || this._renderer.renderTarget.projectionMatrix,\n            resolution: size || renderTarget.size,\n            worldTransformMatrix: worldTransformMatrix || currentGlobalUniformData.worldTransformMatrix,\n            worldColor: worldColor || currentGlobalUniformData.worldColor,\n            offset: offset || currentGlobalUniformData.offset,\n            bindGroup: null,\n        };\n\n        const uniformGroup = this._uniformsPool.pop() || this._createUniforms();\n\n        this._activeUniforms.push(uniformGroup);\n\n        const uniforms = uniformGroup.uniforms;\n\n        uniforms.uProjectionMatrix = globalUniformData.projectionMatrix;\n\n        uniforms.uResolution = globalUniformData.resolution;\n\n        uniforms.uWorldTransformMatrix.copyFrom(globalUniformData.worldTransformMatrix);\n\n        uniforms.uWorldTransformMatrix.tx -= globalUniformData.offset.x;\n        uniforms.uWorldTransformMatrix.ty -= globalUniformData.offset.y;\n\n        color32BitToUniform(\n            globalUniformData.worldColor,\n            uniforms.uWorldColorAlpha,\n            0\n        );\n\n        uniformGroup.update();\n\n        let bindGroup: BindGroup;\n\n        if ((this._renderer as WebGPURenderer).renderPipes.uniformBatch)\n        {\n            bindGroup = (this._renderer as WebGPURenderer).renderPipes.uniformBatch.getUniformBindGroup(uniformGroup, false);\n        }\n        else\n        {\n            bindGroup = this._bindGroupPool.pop() || new BindGroup();\n            this._activeBindGroups.push(bindGroup);\n            bindGroup.setResource(uniformGroup, 0);\n        }\n\n        globalUniformData.bindGroup = bindGroup;\n\n        this._currentGlobalUniformData = globalUniformData;\n    }\n\n    public push(options: GlobalUniformOptions)\n    {\n        this.bind(options);\n\n        this._globalUniformDataStack[this._stackIndex++] = this._currentGlobalUniformData;\n    }\n\n    public pop()\n    {\n        this._currentGlobalUniformData = this._globalUniformDataStack[--this._stackIndex - 1];\n\n        // for webGL we need to update the uniform group here\n        // as we are not using bind groups\n        if (this._renderer.type === RendererType.WEBGL)\n        {\n            (this._currentGlobalUniformData.bindGroup.resources[0] as UniformGroup).update();\n        }\n    }\n\n    get bindGroup(): BindGroup\n    {\n        return this._currentGlobalUniformData.bindGroup;\n    }\n\n    get globalUniformData()\n    {\n        return this._currentGlobalUniformData;\n    }\n\n    get uniformGroup()\n    {\n        return this._currentGlobalUniformData.bindGroup.resources[0] as UniformGroup;\n    }\n\n    private _createUniforms(): GlobalUniformGroup\n    {\n        const globalUniforms = new UniformGroup({\n            uProjectionMatrix: { value: new Matrix(), type: 'mat3x3<f32>' },\n            uWorldTransformMatrix: { value: new Matrix(), type: 'mat3x3<f32>' },\n            // TODO - someone smart - set this to be a unorm8x4 rather than a vec4<f32>\n            uWorldColorAlpha: { value: new Float32Array(4), type: 'vec4<f32>' },\n            uResolution: { value: [0, 0], type: 'vec2<f32>' },\n        }, {\n            isStatic: true,\n        });\n\n        return globalUniforms;\n    }\n\n    public destroy()\n    {\n        (this._renderer as null) = null;\n    }\n}\n", "import { ExtensionType } from '../../../extensions/Extensions';\nimport { Ticker } from '../../../ticker/Ticker';\n\nimport type { System } from './system/System';\n\n// start at one too keep it positive!\nlet uid = 1;\n\n/**\n * The SchedulerSystem manages scheduled tasks with specific intervals.\n * @category rendering\n * @advanced\n */\nexport class SchedulerSystem implements System<null>\n{\n    /** @ignore */\n    public static extension = {\n        type: [\n            ExtensionType.WebGLSystem,\n            ExtensionType.WebGPUSystem,\n            ExtensionType.CanvasSystem,\n        ],\n        name: 'scheduler',\n        priority: 0,\n    } as const;\n\n    private readonly _tasks: {\n        func: (elapsed: number) => void;\n        duration: number;\n        offset: number\n        start: number;\n        last: number;\n        repeat: boolean;\n        id: number;\n    }[] = [];\n\n    /** a small off set to apply to the repeat schedules. This is just to make sure they run at slightly different times */\n    private _offset = 0;\n\n    /** Initializes the scheduler system and starts the ticker. */\n    public init(): void\n    {\n        Ticker.system.add(this._update, this);\n    }\n\n    /**\n     * Schedules a repeating task.\n     * @param func - The function to execute.\n     * @param duration - The interval duration in milliseconds.\n     * @param useOffset - this will spread out tasks so that they do not all run at the same time\n     * @returns The unique identifier for the scheduled task.\n     */\n    public repeat(func: (elapsed: number) => void, duration: number, useOffset = true): number\n    {\n        const id = uid++;\n\n        let offset = 0;\n\n        if (useOffset)\n        {\n            this._offset += 1000;\n            offset = this._offset;\n        }\n\n        this._tasks.push({\n            func,\n            duration,\n            start: performance.now(),\n            offset,\n            last: performance.now(),\n            repeat: true,\n            id\n        });\n\n        return id;\n    }\n\n    /**\n     * Cancels a scheduled task.\n     * @param id - The unique identifier of the task to cancel.\n     */\n    public cancel(id: number): void\n    {\n        for (let i = 0; i < this._tasks.length; i++)\n        {\n            if (this._tasks[i].id === id)\n            {\n                this._tasks.splice(i, 1);\n\n                return;\n            }\n        }\n    }\n\n    /**\n     * Updates and executes the scheduled tasks.\n     * @private\n     */\n    private _update(): void\n    {\n        const now = performance.now();\n\n        for (let i = 0; i < this._tasks.length; i++)\n        {\n            const task = this._tasks[i];\n\n            if ((now - task.offset) - task.last >= task.duration)\n            {\n                const elapsed = now - task.start;\n\n                task.func(elapsed);\n                task.last = now;\n            }\n        }\n    }\n\n    /**\n     * Destroys the scheduler system and removes all tasks.\n     * @internal\n     */\n    public destroy(): void\n    {\n        Ticker.system.remove(this._update, this);\n\n        this._tasks.length = 0;\n    }\n}\n", "import { DOMAdapter } from '../environment/adapter';\nimport { VERSION } from './const';\n\nlet saidHello = false;\n\n/**\n * Prints out the version and renderer information for this running instance of PixiJS.\n * @param type - The name of the renderer this instance is using.\n * @returns {void}\n * @category utils\n * @advanced\n */\nexport function sayHello(type: string): void\n{\n    if (saidHello)\n    {\n        return;\n    }\n\n    if (DOMAdapter.get().getNavigator().userAgent.toLowerCase().indexOf('chrome') > -1)\n    {\n        const args = [\n            `%c  %c  %c  %c  %c PixiJS %c v${VERSION} (${type}) http://www.pixijs.com/\\n\\n`,\n            'background: #E72264; padding:5px 0;',\n            'background: #6CA2EA; padding:5px 0;',\n            'background: #B5D33D; padding:5px 0;',\n            'background: #FED23F; padding:5px 0;',\n            'color: #FFFFFF; background: #E72264; padding:5px 0;',\n            'color: #E72264; background: #FFFFFF; padding:5px 0;',\n        ];\n\n        globalThis.console.log(...args);\n    }\n    else if (globalThis.console)\n    {\n        globalThis.console.log(`PixiJS ${VERSION} - ${type} - http://www.pixijs.com/`);\n    }\n\n    saidHello = true;\n}\n", "import { ExtensionType } from '../../../../extensions/Extensions';\nimport { sayHello } from '../../../../utils/sayHello';\nimport { type Renderer, RendererType } from '../../types';\n\nimport type { WebGLRenderer } from '../../gl/WebGLRenderer';\nimport type { System } from '../system/System';\n\n/**\n * Options for the startup system.\n * @property {boolean} [hello=false] - Whether to log the version and type information of renderer to console.\n * @category rendering\n * @advanced\n */\nexport interface HelloSystemOptions\n{\n    /**\n     * Whether to log the version and type information of renderer to console.\n     * @default false\n     */\n    hello: boolean;\n}\n\n/**\n * A simple system responsible for initiating the renderer.\n * @category rendering\n * @advanced\n */\nexport class HelloSystem implements System<HelloSystemOptions>\n{\n    /** @ignore */\n    public static extension = {\n        type: [\n            ExtensionType.WebGLSystem,\n            ExtensionType.WebGPUSystem,\n            ExtensionType.CanvasSystem,\n        ],\n        name: 'hello',\n        priority: -2,\n    } as const;\n\n    /** The default options for the system. */\n    public static defaultOptions: HelloSystemOptions = {\n        /** {@link WebGLOptions.hello} */\n        hello: false,\n    };\n\n    private readonly _renderer: Renderer;\n\n    constructor(renderer: Renderer)\n    {\n        this._renderer = renderer;\n    }\n\n    /**\n     * It all starts here! This initiates every system, passing in the options for any system by name.\n     * @param options - the config for the renderer and all its systems\n     */\n    public init(options: HelloSystemOptions): void\n    {\n        if (options.hello)\n        {\n            let name = this._renderer.name;\n\n            if (this._renderer.type === RendererType.WEBGL)\n            {\n                name += ` ${(this._renderer as WebGLRenderer).context.webGLVersion}`;\n            }\n\n            sayHello(name);\n        }\n    }\n}\n", "/**\n * Takes a hash and removes all the `undefined`/`null` values from it.\n * In PixiJS, we tend to null properties instead of using 'delete' for performance reasons.\n * However, in some cases, this could be a problem if the hash grows too large over time,\n * this function can be used to clean a hash.\n * @param hash - The hash to clean.\n * @returns A new hash with all the `undefined`/`null` values removed.\n * @category utils\n * @internal\n */\nexport function cleanHash<T>(hash: Record<string, T>): Record<string, T>\n{\n    let clean = false;\n\n    for (const i in hash)\n    {\n        // eslint-disable-next-line eqeqeq\n        if (hash[i] == undefined)\n        {\n            clean = true;\n            break;\n        }\n    }\n\n    if (!clean) return hash;\n\n    const cleanHash = Object.create(null);\n\n    for (const i in hash)\n    {\n        const value = hash[i];\n\n        if (value)\n        {\n            cleanHash[i] = value;\n        }\n    }\n\n    return cleanHash;\n}\n\n/**\n * Removes all `undefined`/`null` elements from the given array and compacts the array.\n *\n * This function iterates through the array, shifting non-undefined elements to the left\n * to fill gaps created by `undefined` elements. The length of the array is then adjusted\n * to remove the trailing `undefined` elements.\n * @param arr - The array to be cleaned.\n * @returns The cleaned array with all `undefined` elements removed.\n * @example\n * // Example usage:\n * const arr = [1, undefined, 2, undefined, 3];\n * const cleanedArr = cleanArray(arr);\n * console.log(cleanedArr); // Output: [1, 2, 3]\n * @category utils\n * @internal\n */\nexport function cleanArray<T>(arr: T[]): T[]\n{\n    let offset = 0;\n\n    for (let i = 0; i < arr.length; i++)\n    {\n        // eslint-disable-next-line eqeqeq\n        if (arr[i] == undefined)\n        {\n            offset++;\n        }\n        else\n        {\n            arr[i - offset] = arr[i];\n        }\n    }\n\n    arr.length -= offset;\n\n    return arr;\n}\n", "import { ExtensionType } from '../../../../extensions/Extensions';\nimport { type RenderGroup } from '../../../../scene/container/RenderGroup';\nimport { cleanArray, cleanHash } from '../../../../utils/data/clean';\nimport { type RenderOptions } from '../system/AbstractRenderer';\n\nimport type { Container } from '../../../../scene/container/Container';\nimport type { Renderer } from '../../types';\nimport type { RenderPipe } from '../instructions/RenderPipe';\nimport type { Renderable } from '../Renderable';\nimport type { System } from '../system/System';\n\nlet renderableGCTick = 0;\n\n/**\n * Options for the {@link RenderableGCSystem}.\n * @category rendering\n * @property {boolean} [renderableGCActive=true] - If set to true, this will enable the garbage collector on the renderables.\n * @property {number} [renderableGCAMaxIdle=60000] -\n * The maximum idle frames before a texture is destroyed by garbage collection.\n * @property {number} [renderableGCCheckCountMax=60000] - time between two garbage collections.\n * @advanced\n */\nexport interface RenderableGCSystemOptions\n{\n    /**\n     * If set to true, this will enable the garbage collector on the GPU.\n     * @default true\n     */\n    renderableGCActive: boolean;\n    /**\n     * The maximum idle frames before a texture is destroyed by garbage collection.\n     * @default 60 * 60\n     */\n    renderableGCMaxUnusedTime: number;\n    /**\n     * Frames between two garbage collections.\n     * @default 600\n     */\n    renderableGCFrequency: number;\n}\n\n/**\n * The RenderableGCSystem is responsible for cleaning up GPU resources that are no longer being used.\n *\n * When rendering objects like sprites, text, etc - GPU resources are created and managed by the renderer.\n * If these objects are no longer needed but not properly destroyed (via sprite.destroy()), their GPU resources\n * would normally leak. This system prevents that by automatically cleaning up unused GPU resources.\n *\n * Key features:\n * - Runs every 30 seconds by default to check for unused resources\n * - Cleans up resources not rendered for over 1 minute\n * - Works independently of rendering - will clean up even when not actively rendering\n * - When cleaned up resources are needed again, new GPU objects are quickly assigned from a pool\n * - Can be disabled with renderableGCActive:false for manual control\n *\n * Best practices:\n * - Always call destroy() explicitly when done with renderables (e.g. sprite.destroy())\n * - This system is a safety net, not a replacement for proper cleanup\n * - Adjust frequency and timeouts via options if needed\n * @example\n * ```js\n * // Sprite created but reference lost without destroy\n * let sprite = new Sprite(texture);\n *\n * // internally the renderer will assign a resource to the sprite\n * renderer.render(sprite);\n *\n * sprite = null; // Reference lost but GPU resources still exist\n *\n * // After 1 minute of not being rendered:\n * // - RenderableGC will clean up the sprite's GPU resources\n * // - JS garbage collector can then clean up the sprite itself\n * ```\n * @category rendering\n * @advanced\n */\nexport class RenderableGCSystem implements System<RenderableGCSystemOptions>\n{\n    /**\n     * Extension metadata for registering this system with the renderer.\n     * @ignore\n     */\n    public static extension = {\n        type: [\n            ExtensionType.WebGLSystem,\n            ExtensionType.WebGPUSystem,\n        ],\n        name: 'renderableGC',\n        priority: 0\n    } as const;\n\n    /**\n     * Default configuration options for the garbage collection system.\n     * These can be overridden when initializing the renderer.\n     */\n    public static defaultOptions: RenderableGCSystemOptions = {\n        /** Enable/disable the garbage collector */\n        renderableGCActive: true,\n        /** Time in ms before an unused resource is collected (default 1 minute) */\n        renderableGCMaxUnusedTime: 60000,\n        /** How often to run garbage collection in ms (default 30 seconds) */\n        renderableGCFrequency: 30000,\n    };\n\n    /** Maximum time in ms a resource can be unused before being garbage collected */\n    public maxUnusedTime: number;\n\n    /** Reference to the renderer this system belongs to */\n    private _renderer: Renderer;\n\n    /** Array of renderables being tracked for garbage collection */\n    private readonly _managedRenderables: Renderable[] = [];\n    /** ID of the main GC scheduler handler */\n    private _handler: number;\n    /** How frequently GC runs in ms */\n    private _frequency: number;\n    /** Current timestamp used for age calculations */\n    private _now: number;\n\n    /** Array of hash objects being tracked for cleanup */\n    private readonly _managedHashes: {context: any, hash: string}[] = [];\n    /** ID of the hash cleanup scheduler handler */\n    private _hashHandler: number;\n\n    /** Array of arrays being tracked for cleanup */\n    private readonly _managedArrays: {context: any, hash: string}[] = [];\n    /** ID of the array cleanup scheduler handler */\n    private _arrayHandler: number;\n\n    /**\n     * Creates a new RenderableGCSystem instance.\n     * @param renderer - The renderer this garbage collection system works for\n     */\n    constructor(renderer: Renderer)\n    {\n        this._renderer = renderer;\n    }\n\n    /**\n     * Initializes the garbage collection system with the provided options.\n     * @param options - Configuration options for the renderer\n     */\n    public init(options: RenderableGCSystemOptions): void\n    {\n        options = { ...RenderableGCSystem.defaultOptions, ...options };\n\n        this.maxUnusedTime = options.renderableGCMaxUnusedTime;\n        this._frequency = options.renderableGCFrequency;\n\n        this.enabled = options.renderableGCActive;\n    }\n\n    /**\n     * Gets whether the garbage collection system is currently enabled.\n     * @returns True if GC is enabled, false otherwise\n     */\n    get enabled(): boolean\n    {\n        return !!this._handler;\n    }\n\n    /**\n     * Enables or disables the garbage collection system.\n     * When enabled, schedules periodic cleanup of resources.\n     * When disabled, cancels all scheduled cleanups.\n     */\n    set enabled(value: boolean)\n    {\n        if (this.enabled === value) return;\n\n        if (value)\n        {\n            // Schedule periodic garbage collection\n            this._handler = this._renderer.scheduler.repeat(\n                () => this.run(),\n                this._frequency,\n                false\n            );\n\n            // Schedule periodic hash table cleanup\n            this._hashHandler = this._renderer.scheduler.repeat(\n                () =>\n                {\n                    for (const hash of this._managedHashes)\n                    {\n                        hash.context[hash.hash] = cleanHash(hash.context[hash.hash]);\n                    }\n                },\n                this._frequency\n            );\n\n            // Schedule periodic array cleanup\n            this._arrayHandler = this._renderer.scheduler.repeat(\n                () =>\n                {\n                    for (const array of this._managedArrays)\n                    {\n                        cleanArray(array.context[array.hash]);\n                    }\n                },\n                this._frequency\n            );\n        }\n        else\n        {\n            // Cancel all scheduled cleanups\n            this._renderer.scheduler.cancel(this._handler);\n            this._renderer.scheduler.cancel(this._hashHandler);\n            this._renderer.scheduler.cancel(this._arrayHandler);\n        }\n    }\n\n    /**\n     * Adds a hash table to be managed by the garbage collector.\n     * @param context - The object containing the hash table\n     * @param hash - The property name of the hash table\n     */\n    public addManagedHash<T>(context: T, hash: string): void\n    {\n        this._managedHashes.push({ context, hash: hash as string });\n    }\n\n    /**\n     * Adds an array to be managed by the garbage collector.\n     * @param context - The object containing the array\n     * @param hash - The property name of the array\n     */\n    public addManagedArray<T>(context: T, hash: string): void\n    {\n        this._managedArrays.push({ context, hash: hash as string });\n    }\n\n    /**\n     * Updates the GC timestamp and tracking before rendering.\n     * @param options - The render options\n     * @param options.container - The container to render\n     */\n    public prerender({\n        container\n    }: RenderOptions): void\n    {\n        this._now = performance.now();\n\n        // The gcTick is a monotonically increasing counter that tracks render cycles\n        // Each time we render, we increment the global renderableGCTick counter\n        // and assign the new tick value to the render group being rendered.\n        // This lets us know which render groups were rendered in the current frame\n        // versus ones that haven't been rendered recently.\n        // The instruction set also gets updated with this tick value to track\n        // when its renderables were last used.\n        container.renderGroup.gcTick = renderableGCTick++;\n\n        this._updateInstructionGCTick(container.renderGroup, container.renderGroup.gcTick);\n    }\n\n    /**\n     * Starts tracking a renderable for garbage collection.\n     * @param renderable - The renderable to track\n     */\n    public addRenderable(renderable: Renderable): void\n    {\n        if (!this.enabled) return;\n\n        if (renderable._lastUsed === -1)\n        {\n            this._managedRenderables.push(renderable);\n            renderable.once('destroyed', this._removeRenderable, this);\n        }\n\n        renderable._lastUsed = this._now;\n    }\n\n    /**\n     * Performs garbage collection by cleaning up unused renderables.\n     * Removes renderables that haven't been used for longer than maxUnusedTime.\n     */\n    public run(): void\n    {\n        const now = this._now;\n        const managedRenderables = this._managedRenderables;\n        const renderPipes = this._renderer.renderPipes;\n        let offset = 0;\n\n        for (let i = 0; i < managedRenderables.length; i++)\n        {\n            const renderable = managedRenderables[i];\n\n            if (renderable === null)\n            {\n                offset++;\n                continue;\n            }\n\n            const renderGroup = renderable.renderGroup ?? renderable.parentRenderGroup;\n            const currentTick = renderGroup?.instructionSet?.gcTick ?? -1;\n\n            // Update last used time if the renderable's group was rendered this tick\n            if ((renderGroup?.gcTick ?? 0) === currentTick)\n            {\n                renderable._lastUsed = now;\n            }\n\n            // Clean up if unused for too long\n            if (now - renderable._lastUsed > this.maxUnusedTime)\n            {\n                if (!renderable.destroyed)\n                {\n                    const rp = renderPipes as unknown as Record<string, RenderPipe>;\n\n                    if (renderGroup)renderGroup.structureDidChange = true;\n\n                    rp[renderable.renderPipeId].destroyRenderable(renderable);\n                }\n\n                renderable._lastUsed = -1;\n                offset++;\n                renderable.off('destroyed', this._removeRenderable, this);\n            }\n            else\n            {\n                managedRenderables[i - (offset)] = renderable;\n            }\n        }\n\n        managedRenderables.length -= offset;\n    }\n\n    /** Cleans up the garbage collection system. Disables GC and removes all tracked resources. */\n    public destroy(): void\n    {\n        this.enabled = false;\n        this._renderer = null as any as Renderer;\n        this._managedRenderables.length = 0;\n        this._managedHashes.length = 0;\n        this._managedArrays.length = 0;\n    }\n\n    /**\n     * Removes a renderable from being tracked when it's destroyed.\n     * @param renderable - The renderable to stop tracking\n     */\n    private _removeRenderable(renderable: Container): void\n    {\n        const index = this._managedRenderables.indexOf(renderable as Renderable);\n\n        if (index >= 0)\n        {\n            renderable.off('destroyed', this._removeRenderable, this);\n            this._managedRenderables[index] = null;\n        }\n    }\n\n    /**\n     * Updates the GC tick counter for a render group and its children.\n     * @param renderGroup - The render group to update\n     * @param gcTick - The new tick value\n     */\n    private _updateInstructionGCTick(renderGroup: RenderGroup, gcTick: number): void\n    {\n        renderGroup.instructionSet.gcTick = gcTick;\n\n        for (const child of renderGroup.renderGroupChildren)\n        {\n            this._updateInstructionGCTick(child, gcTick);\n        }\n    }\n}\n", "import { ExtensionType } from '../../../../extensions/Extensions';\n\nimport type { Renderer } from '../../types';\nimport type { System } from '../system/System';\n\n/**\n * Options for the {@link TextureGCSystem}.\n * @category rendering\n * @advanced\n */\nexport interface TextureGCSystemOptions\n{\n    /**\n     * If set to true, this will enable the garbage collector on the GPU.\n     * @default true\n     */\n    textureGCActive: boolean;\n    /**\n     * @deprecated since 8.3.0\n     * @see {@link TextureGCSystemOptions.textureGCMaxIdle}\n     */\n    textureGCAMaxIdle: number;\n    /**\n     * The maximum idle frames before a texture is destroyed by garbage collection.\n     * @default 60 * 60\n     */\n    textureGCMaxIdle: number;\n    /**\n     * Frames between two garbage collections.\n     * @default 600\n     */\n    textureGCCheckCountMax: number;\n}\n/**\n * System plugin to the renderer to manage texture garbage collection on the GPU,\n * ensuring that it does not get clogged up with textures that are no longer being used.\n * @category rendering\n * @advanced\n */\nexport class TextureGCSystem implements System<TextureGCSystemOptions>\n{\n    /** @ignore */\n    public static extension = {\n        type: [\n            ExtensionType.WebGLSystem,\n            ExtensionType.WebGPUSystem,\n        ],\n        name: 'textureGC',\n    } as const;\n\n    /** default options for the TextureGCSystem */\n    public static defaultOptions: TextureGCSystemOptions = {\n        /**\n         * If set to true, this will enable the garbage collector on the GPU.\n         * @default true\n         */\n        textureGCActive: true,\n        /**\n         * @deprecated since 8.3.0\n         * @see {@link TextureGCSystemOptions.textureGCMaxIdle}\n         */\n        textureGCAMaxIdle: null,\n        /**\n         * The maximum idle frames before a texture is destroyed by garbage collection.\n         * @default 60 * 60\n         */\n        textureGCMaxIdle: 60 * 60,\n        /**\n         * Frames between two garbage collections.\n         * @default 600\n         */\n        textureGCCheckCountMax: 600,\n    };\n\n    /**\n     * Frame count since started.\n     * @readonly\n     */\n    public count: number;\n\n    /**\n     * Frame count since last garbage collection.\n     * @readonly\n     */\n    public checkCount: number;\n\n    /**\n     * Maximum idle frames before a texture is destroyed by garbage collection.\n     * @see TextureGCSystem.defaultMaxIdle\n     */\n    public maxIdle: number;\n\n    /**\n     * Frames between two garbage collections.\n     * @see TextureGCSystem.defaultCheckCountMax\n     */\n    public checkCountMax: number;\n\n    /**\n     * Current garbage collection mode.\n     * @see TextureGCSystem.defaultMode\n     */\n    public active: boolean;\n    private _renderer: Renderer;\n\n    /** @param renderer - The renderer this System works for. */\n    constructor(renderer: Renderer)\n    {\n        this._renderer = renderer;\n\n        this.count = 0;\n        this.checkCount = 0;\n    }\n\n    public init(options: TextureGCSystemOptions): void\n    {\n        options = { ...TextureGCSystem.defaultOptions, ...options };\n\n        this.checkCountMax = options.textureGCCheckCountMax;\n        this.maxIdle = options.textureGCAMaxIdle ?? options.textureGCMaxIdle;\n        this.active = options.textureGCActive;\n    }\n\n    /**\n     * Checks to see when the last time a texture was used.\n     * If the texture has not been used for a specified amount of time, it will be removed from the GPU.\n     */\n    protected postrender(): void\n    {\n        if (!this._renderer.renderingToScreen)\n        {\n            return;\n        }\n\n        this.count++;\n\n        if (!this.active) return;\n\n        this.checkCount++;\n\n        if (this.checkCount > this.checkCountMax)\n        {\n            this.checkCount = 0;\n\n            this.run();\n        }\n    }\n\n    /**\n     * Checks to see when the last time a texture was used.\n     * If the texture has not been used for a specified amount of time, it will be removed from the GPU.\n     */\n    public run(): void\n    {\n        const managedTextures = this._renderer.texture.managedTextures;\n\n        for (let i = 0; i < managedTextures.length; i++)\n        {\n            const texture = managedTextures[i];\n\n            // Only supports non generated textures at the moment!\n            if (\n                texture.autoGarbageCollect\n                && texture.resource\n                && texture._touched > -1\n                && this.count - texture._touched > this.maxIdle\n            )\n            {\n                texture._touched = -1;\n                texture.unload();\n            }\n        }\n    }\n\n    public destroy(): void\n    {\n        this._renderer = null as any as Renderer;\n    }\n}\n", "// what we are building is a platform and a framework.\n// import { Matrix } from '../../shared/maths/Matrix';\nimport { uid } from '../../../../utils/data/uid';\nimport { TextureSource } from '../texture/sources/TextureSource';\nimport { Texture } from '../texture/Texture';\n\nimport type { BindableTexture } from '../texture/Texture';\n\n/**\n * Options for creating a render target.\n * @category rendering\n * @advanced\n */\nexport interface RenderTargetOptions\n{\n    /** the width of the RenderTarget */\n    width?: number;\n    /** the height of the RenderTarget */\n    height?: number;\n    /** the resolution of the RenderTarget */\n    resolution?: number;\n    /** an array of textures, or a number indicating how many color textures there should be */\n    colorTextures?: BindableTexture[] | number;\n    /** should this render target have a stencil buffer? */\n    stencil?: boolean;\n    /** should this render target have a depth buffer? */\n    depth?: boolean;\n    /** a depth stencil texture that the depth and stencil outputs will be written to */\n    depthStencilTexture?: BindableTexture | boolean;\n    /** should this render target be antialiased? */\n    antialias?: boolean;\n    /** is this a root element, true if this is gl context owners render target */\n    isRoot?: boolean;\n}\n\n/**\n * A class that describes what the renderers are rendering to.\n * This can be as simple as a Texture, or as complex as a multi-texture, multi-sampled render target.\n * Support for stencil and depth buffers is also included.\n *\n * If you need something more complex than a Texture to render to, you should use this class.\n * Under the hood, all textures you render to have a RenderTarget created on their behalf.\n * @category rendering\n * @advanced\n */\nexport class RenderTarget\n{\n    /** The default options for a render target */\n    public static defaultOptions: RenderTargetOptions = {\n        /** the width of the RenderTarget */\n        width: 0,\n        /** the height of the RenderTarget */\n        height: 0,\n        /** the resolution of the RenderTarget */\n        resolution: 1,\n        /** an array of textures, or a number indicating how many color textures there should be */\n        colorTextures: 1,\n        /** should this render target have a stencil buffer? */\n        stencil: false,\n        /** should this render target have a depth buffer? */\n        depth: false,\n        /** should this render target be antialiased? */\n        antialias: false, // save on perf by default!\n        /** is this a root element, true if this is gl context owners render target */\n        isRoot: false\n    };\n\n    /** unique id for this render target */\n    public readonly uid: number = uid('renderTarget');\n\n    /**\n     * An array of textures that can be written to by the GPU - mostly this has one texture in Pixi, but you could\n     * write to multiple if required! (eg deferred lighting)\n     */\n    public colorTextures: TextureSource[] = [];\n    /** the stencil and depth buffer will right to this texture in WebGPU */\n    public depthStencilTexture: TextureSource;\n    /** if true, will ensure a stencil buffer is added. For WebGPU, this will automatically create a depthStencilTexture */\n    public stencil: boolean;\n    /** if true, will ensure a depth buffer is added. For WebGPU, this will automatically create a depthStencilTexture */\n    public depth: boolean;\n\n    public dirtyId = 0;\n    public isRoot = false;\n\n    private readonly _size = new Float32Array(2);\n    /** if true, then when the render target is destroyed, it will destroy all the textures that were created for it. */\n    private readonly _managedColorTextures: boolean = false;\n\n    /**\n     * @param [descriptor] - Options for creating a render target.\n     */\n    constructor(descriptor: RenderTargetOptions = {})\n    {\n        descriptor = { ...RenderTarget.defaultOptions, ...descriptor };\n\n        this.stencil = descriptor.stencil;\n        this.depth = descriptor.depth;\n        this.isRoot = descriptor.isRoot;\n\n        if (typeof descriptor.colorTextures === 'number')\n        {\n            this._managedColorTextures = true;\n\n            for (let i = 0; i < descriptor.colorTextures; i++)\n            {\n                this.colorTextures.push(new TextureSource({\n                    width: descriptor.width,\n                    height: descriptor.height,\n                    resolution: descriptor.resolution,\n                    antialias: descriptor.antialias,\n                })\n                );\n            }\n        }\n        else\n        {\n            this.colorTextures = [...descriptor.colorTextures.map((texture) => texture.source)];\n\n            const colorSource = this.colorTexture.source;\n\n            this.resize(colorSource.width, colorSource.height, colorSource._resolution);\n        }\n\n        // the first color texture drives the size of all others..\n        this.colorTexture.source.on('resize', this.onSourceResize, this);\n\n        // TODO should listen for texture destroyed?\n\n        if (descriptor.depthStencilTexture || this.stencil)\n        {\n            // TODO add a test\n            if (descriptor.depthStencilTexture instanceof Texture\n                || descriptor.depthStencilTexture instanceof TextureSource)\n            {\n                this.depthStencilTexture = descriptor.depthStencilTexture.source;\n            }\n            else\n            {\n                this.ensureDepthStencilTexture();\n            }\n        }\n    }\n\n    get size(): [number, number]\n    {\n        const _size = this._size;\n\n        _size[0] = this.pixelWidth;\n        _size[1] = this.pixelHeight;\n\n        return _size as any as [number, number];\n    }\n\n    get width(): number\n    {\n        return this.colorTexture.source.width;\n    }\n\n    get height(): number\n    {\n        return this.colorTexture.source.height;\n    }\n    get pixelWidth(): number\n    {\n        return this.colorTexture.source.pixelWidth;\n    }\n\n    get pixelHeight(): number\n    {\n        return this.colorTexture.source.pixelHeight;\n    }\n\n    get resolution(): number\n    {\n        return this.colorTexture.source._resolution;\n    }\n\n    get colorTexture(): TextureSource\n    {\n        return this.colorTextures[0];\n    }\n\n    protected onSourceResize(source: TextureSource)\n    {\n        this.resize(source.width, source.height, source._resolution, true);\n    }\n\n    /**\n     * This will ensure a depthStencil texture is created for this render target.\n     * Most likely called by the mask system to make sure we have stencil buffer added.\n     * @internal\n     */\n    public ensureDepthStencilTexture()\n    {\n        if (!this.depthStencilTexture)\n        {\n            this.depthStencilTexture = new TextureSource({\n                width: this.width,\n                height: this.height,\n                resolution: this.resolution,\n                format: 'depth24plus-stencil8',\n                autoGenerateMipmaps: false,\n                antialias: false,\n                mipLevelCount: 1,\n                // sampleCount: handled by the render target system..\n            });\n        }\n    }\n\n    public resize(width: number, height: number, resolution = this.resolution, skipColorTexture = false)\n    {\n        this.dirtyId++;\n\n        this.colorTextures.forEach((colorTexture, i) =>\n        {\n            if (skipColorTexture && i === 0) return;\n\n            colorTexture.source.resize(width, height, resolution);\n        });\n\n        if (this.depthStencilTexture)\n        {\n            this.depthStencilTexture.source.resize(width, height, resolution);\n        }\n    }\n\n    public destroy()\n    {\n        this.colorTexture.source.off('resize', this.onSourceResize, this);\n\n        if (this._managedColorTextures)\n        {\n            this.colorTextures.forEach((texture) =>\n            {\n                texture.destroy();\n            });\n        }\n\n        if (this.depthStencilTexture)\n        {\n            this.depthStencilTexture.destroy();\n            delete this.depthStencilTexture;\n        }\n    }\n}\n", "import { CanvasSource } from '../sources/CanvasSource';\nimport { Texture } from '../Texture';\n\nimport type { ICanvas } from '../../../../../environment/canvas/ICanvas';\nimport type { CanvasSourceOptions } from '../sources/CanvasSource';\n\nconst canvasCache: Map<ICanvas, Texture<CanvasSource>> = new Map();\n\n/**\n * @param canvas\n * @param options\n * @internal\n */\nexport function getCanvasTexture(canvas: ICanvas, options?: CanvasSourceOptions): Texture<CanvasSource>\n{\n    if (!canvasCache.has(canvas))\n    {\n        const texture = new Texture({\n            source: new CanvasSource({\n                resource: canvas,\n                ...options,\n            })\n        });\n\n        const onDestroy = () =>\n        {\n            if (canvasCache.get(canvas) === texture)\n            {\n                canvasCache.delete(canvas);\n            }\n        };\n\n        texture.once('destroy', onDestroy);\n        texture.source.once('destroy', onDestroy);\n\n        canvasCache.set(canvas, texture);\n    }\n\n    return canvasCache.get(canvas);\n}\n\n/**\n * @param canvas\n * @internal\n */\nexport function hasCachedCanvasTexture(canvas: ICanvas): boolean\n{\n    return canvasCache.has(canvas);\n}\n", "import { DOMAdapter } from '../../../../environment/adapter';\nimport { ExtensionType } from '../../../../extensions/Extensions';\nimport { Rectangle } from '../../../../maths/shapes/Rectangle';\nimport { deprecation, v8_0_0 } from '../../../../utils/logging/deprecation';\nimport { type RendererOptions } from '../../types';\nimport { RenderTarget } from '../renderTarget/RenderTarget';\nimport { getCanvasTexture } from '../texture/utils/getCanvasTexture';\n\nimport type { ICanvas } from '../../../../environment/canvas/ICanvas';\nimport type { TypeOrBool } from '../../../../scene/container/destroyTypes';\nimport type { System } from '../system/System';\nimport type { CanvasSource } from '../texture/sources/CanvasSource';\nimport type { Texture } from '../texture/Texture';\n\n/**\n * Options passed to the ViewSystem\n * @category rendering\n * @advanced\n */\nexport interface ViewSystemOptions\n{\n    /**\n     * The width of the screen.\n     * @default 800\n     */\n    width?: number;\n    /**\n     * The height of the screen.\n     * @default 600\n     */\n    height?: number;\n    /** The canvas to use as a view, optional. */\n    canvas?: ICanvas;\n    /**\n     * Alias for `canvas`.\n     * @deprecated since 8.0.0\n     */\n    view?: ICanvas;\n    /**\n     * Resizes renderer view in CSS pixels to allow for resolutions other than 1.\n     *\n     * This is only supported for HTMLCanvasElement\n     * and will be ignored if the canvas is an OffscreenCanvas.\n     */\n    autoDensity?: boolean;\n    /** The resolution / device pixel ratio of the renderer. */\n    resolution?: number;\n    /** Whether to enable anti-aliasing. This may affect performance. */\n    antialias?: boolean;\n    /** Whether to ensure the main view has can make use of the depth buffer. Always true for WebGL renderer. */\n    depth?: boolean;\n}\n\n/**\n * Options for destroying the ViewSystem.\n * @category rendering\n * @advanced\n */\nexport interface ViewSystemDestroyOptions\n{\n    /** Whether to remove the view element from the DOM. Defaults to `false`. */\n    removeView?: boolean;\n}\n\n/**\n * The view system manages the main canvas that is attached to the DOM.\n * This main role is to deal with how the holding the view reference and dealing with how it is resized.\n * @category rendering\n * @advanced\n */\nexport class ViewSystem implements System<ViewSystemOptions, TypeOrBool<ViewSystemDestroyOptions> >\n{\n    /** @ignore */\n    public static extension = {\n        type: [\n            ExtensionType.WebGLSystem,\n            ExtensionType.WebGPUSystem,\n            ExtensionType.CanvasSystem,\n        ],\n        name: 'view',\n        priority: 0,\n    } as const;\n\n    /** The default options for the view system. */\n    public static defaultOptions: ViewSystemOptions = {\n        /**\n         * {@link WebGLOptions.width}\n         * @default 800\n         */\n        width: 800,\n        /**\n         * {@link WebGLOptions.height}\n         * @default 600\n         */\n        height: 600,\n        /**\n         * {@link WebGLOptions.autoDensity}\n         * @default false\n         */\n        autoDensity: false,\n        /**\n         * {@link WebGLOptions.antialias}\n         * @default false\n         */\n        antialias: false,\n    };\n\n    /** The canvas element that everything is drawn to. */\n    public canvas!: ICanvas;\n\n    /** The texture that is used to draw the canvas to the screen. */\n    public texture: Texture<CanvasSource>;\n\n    /**\n     * Whether CSS dimensions of canvas view should be resized to screen dimensions automatically.\n     * This is only supported for HTMLCanvasElement and will be ignored if the canvas is an OffscreenCanvas.\n     * @type {boolean}\n     */\n    public get autoDensity(): boolean\n    {\n        return this.texture.source.autoDensity;\n    }\n    public set autoDensity(value: boolean)\n    {\n        this.texture.source.autoDensity = value;\n    }\n\n    /** Whether to enable anti-aliasing. This may affect performance. */\n    public antialias: boolean;\n\n    /**\n     * Measurements of the screen. (0, 0, screenWidth, screenHeight).\n     *\n     * Its safe to use as filterArea or hitArea for the whole stage.\n     */\n    public screen: Rectangle;\n    /** The render target that the view is drawn to. */\n    public renderTarget: RenderTarget;\n\n    /** The resolution / device pixel ratio of the renderer. */\n    get resolution(): number\n    {\n        return this.texture.source._resolution;\n    }\n\n    set resolution(value: number)\n    {\n        this.texture.source.resize(\n            this.texture.source.width,\n            this.texture.source.height,\n            value\n        );\n    }\n\n    /**\n     * initiates the view system\n     * @param options - the options for the view\n     */\n    public init(options: ViewSystemOptions): void\n    {\n        options = {\n            ...ViewSystem.defaultOptions,\n            ...options,\n        };\n\n        if (options.view)\n        {\n            // #if _DEBUG\n            deprecation(v8_0_0, 'ViewSystem.view has been renamed to ViewSystem.canvas');\n            // #endif\n\n            options.canvas = options.view;\n        }\n\n        this.screen = new Rectangle(0, 0, options.width, options.height);\n        this.canvas = options.canvas || DOMAdapter.get().createCanvas();\n        this.antialias = !!options.antialias;\n        this.texture = getCanvasTexture(this.canvas, options);\n        this.renderTarget = new RenderTarget({\n            colorTextures: [this.texture],\n            depth: !!options.depth,\n            isRoot: true,\n        });\n\n        this.texture.source.transparent = (options as RendererOptions).backgroundAlpha < 1;\n        this.resolution = options.resolution;\n    }\n\n    /**\n     * Resizes the screen and canvas to the specified dimensions.\n     * @param desiredScreenWidth - The new width of the screen.\n     * @param desiredScreenHeight - The new height of the screen.\n     * @param resolution\n     */\n    public resize(desiredScreenWidth: number, desiredScreenHeight: number, resolution: number): void\n    {\n        this.texture.source.resize(desiredScreenWidth, desiredScreenHeight, resolution);\n\n        this.screen.width = this.texture.frame.width;\n        this.screen.height = this.texture.frame.height;\n    }\n\n    /**\n     * Destroys this System and optionally removes the canvas from the dom.\n     * @param {options | false} options - The options for destroying the view, or \"false\".\n     * @example\n     * viewSystem.destroy();\n     * viewSystem.destroy(true);\n     * viewSystem.destroy({ removeView: true });\n     */\n    public destroy(options: TypeOrBool<ViewSystemDestroyOptions> = false): void\n    {\n        const removeView = typeof options === 'boolean' ? options : !!options?.removeView;\n\n        if (removeView && this.canvas.parentNode)\n        {\n            this.canvas.parentNode.removeChild(this.canvas);\n        }\n\n        // note: don't nullify the element\n        //       other systems may need to unbind from it during the destroy iteration (eg. GLContextSystem)\n    }\n}\n", "import { CustomRenderPipe } from '../../../../scene/container/CustomRenderPipe';\nimport { RenderGroupPipe } from '../../../../scene/container/RenderGroupPipe';\nimport { RenderGroupSystem } from '../../../../scene/container/RenderGroupSystem';\nimport { SpritePipe } from '../../../../scene/sprite/SpritePipe';\nimport { RendererInitHook } from '../../../../utils/global/globalHooks';\nimport { BatcherPipe } from '../../../batcher/shared/BatcherPipe';\nimport { AlphaMaskPipe } from '../../../mask/alpha/AlphaMaskPipe';\nimport { ColorMaskPipe } from '../../../mask/color/ColorMaskPipe';\nimport { StencilMaskPipe } from '../../../mask/stencil/StencilMaskPipe';\nimport { BackgroundSystem } from '../background/BackgroundSystem';\nimport { BlendModePipe } from '../blendModes/BlendModePipe';\nimport { ExtractSystem } from '../extract/ExtractSystem';\nimport { GenerateTextureSystem } from '../extract/GenerateTextureSystem';\nimport { GlobalUniformSystem } from '../renderTarget/GlobalUniformSystem';\nimport { SchedulerSystem } from '../SchedulerSystem';\nimport { HelloSystem } from '../startup/HelloSystem';\nimport { RenderableGCSystem } from '../texture/RenderableGCSystem';\nimport { TextureGCSystem } from '../texture/TextureGCSystem';\nimport { ViewSystem } from '../view/ViewSystem';\n\nimport type { ExtractRendererOptions } from './utils/typeUtils';\n\n/**\n * Shared systems for the renderer.\n * @category rendering\n * @internal\n */\nexport const SharedSystems = [\n    BackgroundSystem,\n    GlobalUniformSystem,\n    HelloSystem,\n    ViewSystem,\n    RenderGroupSystem,\n    TextureGCSystem,\n    GenerateTextureSystem,\n    ExtractSystem,\n    RendererInitHook,\n    RenderableGCSystem,\n    SchedulerSystem,\n];\n\n/**\n * Shared render pipes for the renderer.\n * @category rendering\n * @internal\n */\nexport const SharedRenderPipes = [\n    BlendModePipe,\n    BatcherPipe,\n    SpritePipe,\n    RenderGroupPipe,\n    AlphaMaskPipe,\n    StencilMaskPipe,\n    ColorMaskPipe,\n    CustomRenderPipe\n];\n\n/**\n * Options for the shared systems of a renderer.\n * @category rendering\n * @advanced\n */\nexport interface SharedRendererOptions extends ExtractRendererOptions<typeof SharedSystems>, PixiMixins.RendererOptions\n{\n    /**\n     * Whether to stop PixiJS from dynamically importing default extensions for the renderer.\n     * It is false by default, and means PixiJS will load all the default extensions, based\n     * on the environment e.g browser/webworker.\n     * If you set this to true, then you will need to manually import the systems and extensions you need.\n     *\n     * e.g.\n     * ```js\n     * import 'accessibility';\n     * import 'app';\n     * import 'events';\n     * import 'spritesheet';\n     * import 'graphics';\n     * import 'mesh';\n     * import 'text';\n     * import 'text-bitmap';\n     * import 'text-html';\n     * import { autoDetectRenderer } from 'pixi.js';\n     *\n     * const renderer = await autoDetectRenderer({\n     *   width: 800,\n     *   height: 600,\n     *   skipExtensionImports: true,\n     * });\n     * ```\n     * @default false\n     */\n    skipExtensionImports?: boolean;\n    /**\n     * @default true\n     * @deprecated since 8.1.6\n     * @see `skipExtensionImports`\n     */\n    manageImports?: boolean;\n}\n", "import { unsafeEvalSupported } from '../../../../utils/browser/unsafeEvalSupported';\nimport { Buffer } from '../buffer/Buffer';\nimport { BufferUsage } from '../buffer/const';\n\nimport type { System } from '../system/System';\nimport type { UboElement, UboLayout, UniformData, UniformsSyncCallback } from './types';\nimport type { UniformGroup } from './UniformGroup';\n\n/** @internal */\nexport interface UboAdaptor\n{\n    createUboElements: (uniformData: UniformData[]) => UboLayout;\n    generateUboSync: (uboElements: UboElement[]) => UniformsSyncCallback;\n}\n\n/**\n * System plugin to the renderer to manage uniform buffers.\n * @category rendering\n * @advanced\n */\nexport class UboSystem implements System\n{\n    /** Cache of uniform buffer layouts and sync functions, so we don't have to re-create them */\n    private _syncFunctionHash: Record<string, {\n        layout: UboLayout,\n        syncFunction: (uniforms: Record<string, any>, data: Float32Array, dataInt32: Int32Array, offset: number) => void\n    }> = Object.create(null);\n\n    private readonly _adaptor: UboAdaptor;\n\n    constructor(adaptor: UboAdaptor)\n    {\n        this._adaptor = adaptor;\n\n        // Validation check that this environment support `new Function`\n        this._systemCheck();\n    }\n\n    /**\n     * Overridable function by `pixi.js/unsafe-eval` to silence\n     * throwing an error if platform doesn't support unsafe-evals.\n     * @private\n     */\n    private _systemCheck(): void\n    {\n        if (!unsafeEvalSupported())\n        {\n            throw new Error('Current environment does not allow unsafe-eval, '\n                 + 'please use pixi.js/unsafe-eval module to enable support.');\n        }\n    }\n\n    public ensureUniformGroup(uniformGroup: UniformGroup): void\n    {\n        const uniformData = this.getUniformGroupData(uniformGroup);\n\n        uniformGroup.buffer ||= new Buffer({\n            data: new Float32Array(uniformData.layout.size / 4),\n            usage: BufferUsage.UNIFORM | BufferUsage.COPY_DST,\n        });\n    }\n\n    public getUniformGroupData(uniformGroup: UniformGroup)\n    {\n        return this._syncFunctionHash[uniformGroup._signature] || this._initUniformGroup(uniformGroup);\n    }\n\n    private _initUniformGroup(uniformGroup: UniformGroup)\n    {\n        const uniformGroupSignature = uniformGroup._signature;\n\n        let uniformData = this._syncFunctionHash[uniformGroupSignature];\n\n        if (!uniformData)\n        {\n            const elements = Object.keys(uniformGroup.uniformStructures).map((i) => uniformGroup.uniformStructures[i]);\n\n            const layout = this._adaptor.createUboElements(elements);\n\n            const syncFunction = this._generateUboSync(layout.uboElements);\n\n            uniformData = this._syncFunctionHash[uniformGroupSignature] = {\n                layout,\n                syncFunction\n            };\n        }\n\n        return this._syncFunctionHash[uniformGroupSignature];\n    }\n\n    private _generateUboSync(\n        uboElements: UboElement[],\n    ): UniformsSyncCallback\n    {\n        return this._adaptor.generateUboSync(uboElements);\n    }\n\n    public syncUniformGroup(uniformGroup: UniformGroup, data?: Float32Array, offset?: number): boolean\n    {\n        const uniformGroupData = this.getUniformGroupData(uniformGroup);\n\n        uniformGroup.buffer ||= new Buffer({\n            data: new Float32Array(uniformGroupData.layout.size / 4),\n            usage: BufferUsage.UNIFORM | BufferUsage.COPY_DST,\n        });\n\n        let dataInt32: Int32Array = null;\n\n        if (!data)\n        {\n            data = uniformGroup.buffer.data as Float32Array;\n            dataInt32 = uniformGroup.buffer.dataInt32;\n        }\n        offset ||= 0;\n\n        uniformGroupData.syncFunction(uniformGroup.uniforms, data, dataInt32, offset);\n\n        return true;\n    }\n\n    public updateUniformGroup(uniformGroup: UniformGroup): boolean\n    {\n        if (uniformGroup.isStatic && !uniformGroup._dirtyId) return false;\n        uniformGroup._dirtyId = 0;\n\n        const synced = this.syncUniformGroup(uniformGroup);\n\n        uniformGroup.buffer.update();\n\n        return synced;\n    }\n\n    public destroy(): void\n    {\n        this._syncFunctionHash = null;\n    }\n}\n", "// Parsers, each one of these will take a look at the type of shader property and uniform.\n// if they pass the test function then the code function is called that returns a the shader upload code for that uniform.\n// Shader upload code is automagically generated with these parsers.\n// If no parser is valid then the default upload functions are used.\n// exposing Parsers means that custom upload logic can be added to pixi's shaders.\n// A good example would be a pixi rectangle can be directly set on a uniform.\n// If the shader sees it it knows how to upload the rectangle structure as a vec4\n// format is as follows:\n//\n// {\n//     test: (data, uniform) => {} <--- test is this code should be used for this uniform\n//     code: (name, uniform) => {} <--- returns the string of the piece of code that uploads the uniform\n//     codeUbo: (name, uniform) => {} <--- returns the string of the piece of code that uploads the\n//                                         uniform to a uniform buffer\n// }\n// import { Texture } from '../../texture/Texture';\n\nimport type { Color } from '../../../../../color/Color';\nimport type { Matrix } from '../../../../../maths/matrix/Matrix';\nimport type { PointLike } from '../../../../../maths/point/PointLike';\nimport type { Rectangle } from '../../../../../maths/shapes/Rectangle';\nimport type { UNIFORM_TYPES, UniformData } from '../types';\n\ninterface UniformParserDefinition\n{\n    type: UNIFORM_TYPES;\n    test(data: UniformData): boolean;\n    ubo?: string;\n    uboWgsl?: string;\n    uboStd40?: string;\n    uniform?: string;\n}\n\n/** @internal */\nexport const uniformParsers: UniformParserDefinition[] = [\n    // uploading pixi matrix object to mat3\n    {\n        type: 'mat3x3<f32>',\n        test: (data: UniformData): boolean =>\n        {\n            const value = data.value as Matrix;\n\n            return value.a !== undefined;\n        },\n        ubo: `\n            var matrix = uv[name].toArray(true);\n            data[offset] = matrix[0];\n            data[offset + 1] = matrix[1];\n            data[offset + 2] = matrix[2];\n            data[offset + 4] = matrix[3];\n            data[offset + 5] = matrix[4];\n            data[offset + 6] = matrix[5];\n            data[offset + 8] = matrix[6];\n            data[offset + 9] = matrix[7];\n            data[offset + 10] = matrix[8];\n        `,\n        uniform: `\n            gl.uniformMatrix3fv(ud[name].location, false, uv[name].toArray(true));\n        `\n    },\n    // uploading a pixi rectangle as a vec4\n    {\n        type: 'vec4<f32>',\n        test: (data: UniformData): boolean =>\n            data.type === 'vec4<f32>' && data.size === 1 && (data.value as Rectangle).width !== undefined,\n        ubo: `\n            v = uv[name];\n            data[offset] = v.x;\n            data[offset + 1] = v.y;\n            data[offset + 2] = v.width;\n            data[offset + 3] = v.height;\n        `,\n        uniform: `\n            cv = ud[name].value;\n            v = uv[name];\n            if (cv[0] !== v.x || cv[1] !== v.y || cv[2] !== v.width || cv[3] !== v.height) {\n                cv[0] = v.x;\n                cv[1] = v.y;\n                cv[2] = v.width;\n                cv[3] = v.height;\n                gl.uniform4f(ud[name].location, v.x, v.y, v.width, v.height);\n            }\n        `\n    },\n    // uploading a pixi point as a vec2\n    {\n        type: 'vec2<f32>',\n        test: (data: UniformData): boolean =>\n            data.type === 'vec2<f32>' && data.size === 1 && (data.value as PointLike).x !== undefined,\n        ubo:  `\n            v = uv[name];\n            data[offset] = v.x;\n            data[offset + 1] = v.y;\n        `,\n        uniform: `\n            cv = ud[name].value;\n            v = uv[name];\n            if (cv[0] !== v.x || cv[1] !== v.y) {\n                cv[0] = v.x;\n                cv[1] = v.y;\n                gl.uniform2f(ud[name].location, v.x, v.y);\n            }\n        `\n    },\n    // uploading a pixi color as a vec4\n    {\n        type: 'vec4<f32>',\n        test: (data: UniformData): boolean =>\n            data.type === 'vec4<f32>' && data.size === 1 && (data.value as Color).red !== undefined,\n        ubo: `\n            v = uv[name];\n            data[offset] = v.red;\n            data[offset + 1] = v.green;\n            data[offset + 2] = v.blue;\n            data[offset + 3] = v.alpha;\n        `,\n        uniform: `\n            cv = ud[name].value;\n            v = uv[name];\n            if (cv[0] !== v.red || cv[1] !== v.green || cv[2] !== v.blue || cv[3] !== v.alpha) {\n                cv[0] = v.red;\n                cv[1] = v.green;\n                cv[2] = v.blue;\n                cv[3] = v.alpha;\n                gl.uniform4f(ud[name].location, v.red, v.green, v.blue, v.alpha);\n            }\n        `\n    },\n    // uploading a pixi color as a vec3\n    {\n        type: 'vec3<f32>',\n        test: (data: UniformData): boolean =>\n            data.type === 'vec3<f32>' && data.size === 1 && (data.value as Color).red !== undefined,\n        ubo: `\n            v = uv[name];\n            data[offset] = v.red;\n            data[offset + 1] = v.green;\n            data[offset + 2] = v.blue;\n        `,\n        uniform: `\n            cv = ud[name].value;\n            v = uv[name];\n            if (cv[0] !== v.red || cv[1] !== v.green || cv[2] !== v.blue) {\n                cv[0] = v.red;\n                cv[1] = v.green;\n                cv[2] = v.blue;\n                gl.uniform3f(ud[name].location, v.red, v.green, v.blue);\n            }\n        `\n    },\n];\n", "import { uniformParsers } from './uniformParsers';\n\nimport type { UboElement, UNIFORM_TYPES_SINGLE, UniformsSyncCallback } from '../types';\n\n/**\n * @param uboElements\n * @param parserCode\n * @param arrayGenerationFunction\n * @param singleSettersMap\n * @internal\n */\nexport function createUboSyncFunction(\n    uboElements: UboElement[],\n    parserCode: 'uboWgsl' | 'uboStd40',\n    arrayGenerationFunction: (uboElement: UboElement, offsetToAdd: number) => string,\n    singleSettersMap: Record<UNIFORM_TYPES_SINGLE, string>,\n): UniformsSyncCallback\n{\n    const funcFragments = [`\n        var v = null;\n        var v2 = null;\n        var t = 0;\n        var index = 0;\n        var name = null;\n        var arrayOffset = null;\n    `];\n\n    let prev = 0;\n\n    for (let i = 0; i < uboElements.length; i++)\n    {\n        const uboElement = uboElements[i];\n\n        const name = uboElement.data.name;\n\n        let parsed = false;\n        let offset = 0;\n\n        for (let j = 0; j < uniformParsers.length; j++)\n        {\n            const uniformParser = uniformParsers[j];\n\n            if (uniformParser.test(uboElement.data))\n            {\n                offset = uboElement.offset / 4;\n\n                funcFragments.push(\n                    `name = \"${name}\";`,\n                    `offset += ${offset - prev};`,\n                    uniformParsers[j][parserCode] || uniformParsers[j].ubo);\n                parsed = true;\n\n                break;\n            }\n        }\n\n        if (!parsed)\n        {\n            if (uboElement.data.size > 1)\n            {\n                offset = uboElement.offset / 4;\n\n                funcFragments.push(arrayGenerationFunction(uboElement, offset - prev));\n            }\n            else\n            {\n                const template = singleSettersMap[uboElement.data.type as UNIFORM_TYPES_SINGLE];\n\n                offset = uboElement.offset / 4;\n\n                funcFragments.push(/* wgsl */`\n                    v = uv.${name};\n                    offset += ${offset - prev};\n                    ${template};\n                `);\n            }\n        }\n\n        prev = offset;\n    }\n\n    const fragmentSrc = funcFragments.join('\\n');\n\n    // eslint-disable-next-line no-new-func\n    return new Function(\n        'uv',\n        'data',\n        'dataInt32',\n        'offset',\n        fragmentSrc,\n    ) as UniformsSyncCallback;\n}\n", "import type { UNIFORM_TYPES_SINGLE } from '../types';\n\nfunction loopMatrix(col: number, row: number)\n{\n    const total = col * row;\n\n    return `\n        for (let i = 0; i < ${total}; i++) {\n            data[offset + (((i / ${col})|0) * 4) + (i % ${col})] = v[i];\n        }\n    `;\n}\n\n/** @internal */\nexport const uboSyncFunctionsSTD40: Record<UNIFORM_TYPES_SINGLE, string> = {\n    f32: `\n        data[offset] = v;`,\n    i32: `\n        dataInt32[offset] = v;`,\n    'vec2<f32>': `\n        data[offset] = v[0];\n        data[offset + 1] = v[1];`,\n    'vec3<f32>': `\n        data[offset] = v[0];\n        data[offset + 1] = v[1];\n        data[offset + 2] = v[2];`,\n    'vec4<f32>': `\n        data[offset] = v[0];\n        data[offset + 1] = v[1];\n        data[offset + 2] = v[2];\n        data[offset + 3] = v[3];`,\n    'vec2<i32>': `\n        dataInt32[offset] = v[0];\n        dataInt32[offset + 1] = v[1];`,\n    'vec3<i32>': `\n        dataInt32[offset] = v[0];\n        dataInt32[offset + 1] = v[1];\n        dataInt32[offset + 2] = v[2];`,\n    'vec4<i32>': `\n        dataInt32[offset] = v[0];\n        dataInt32[offset + 1] = v[1];\n        dataInt32[offset + 2] = v[2];\n        dataInt32[offset + 3] = v[3];`,\n    'mat2x2<f32>': `\n        data[offset] = v[0];\n        data[offset + 1] = v[1];\n        data[offset + 4] = v[2];\n        data[offset + 5] = v[3];`,\n    'mat3x3<f32>': `\n        data[offset] = v[0];\n        data[offset + 1] = v[1];\n        data[offset + 2] = v[2];\n        data[offset + 4] = v[3];\n        data[offset + 5] = v[4];\n        data[offset + 6] = v[5];\n        data[offset + 8] = v[6];\n        data[offset + 9] = v[7];\n        data[offset + 10] = v[8];`,\n    'mat4x4<f32>': `\n        for (let i = 0; i < 16; i++) {\n            data[offset + i] = v[i];\n        }`,\n    'mat3x2<f32>': loopMatrix(3, 2),\n    'mat4x2<f32>': loopMatrix(4, 2),\n    'mat2x3<f32>': loopMatrix(2, 3),\n    'mat4x3<f32>': loopMatrix(4, 3),\n    'mat2x4<f32>': loopMatrix(2, 4),\n    'mat3x4<f32>': loopMatrix(3, 4),\n};\n\n/** @internal */\nexport const uboSyncFunctionsWGSL: Record<UNIFORM_TYPES_SINGLE, string> = {\n    ...uboSyncFunctionsSTD40,\n    'mat2x2<f32>': `\n        data[offset] = v[0];\n        data[offset + 1] = v[1];\n        data[offset + 2] = v[2];\n        data[offset + 3] = v[3];\n    `,\n};\n", "import EventEmitter from 'eventemitter3';\nimport { uid } from '../../../../utils/data/uid';\n\nimport type { BindResource } from '../../gpu/shader/BindResource';\nimport type { Buffer } from './Buffer';\n\n/**\n * A resource that can be bound to a bind group and used in a shader.\n * Whilst a buffer can be used as a resource, this class allows you to specify an offset and size of the buffer to use.\n * This is useful if you have a large buffer and only part of it is used in a shader.\n *\n * This resource, will listen for changes on the underlying buffer and emit a itself if the buffer changes shape.\n * @example\n *\n * const buffer = new Buffer({\n *     data: new Float32Array(1000),\n *    usage: BufferUsage.UNIFORM,\n * });\n * // Create a buffer resource that uses the first 100 bytes of a buffer\n * const bufferResource = new BufferResource({\n *    buffer,\n *    offset: 0,\n *    size: 100,\n * });\n * @category rendering\n * @advanced\n */\nexport class BufferResource extends EventEmitter<{\n    change: BindResource,\n}> implements BindResource\n{\n    /**\n     * emits when the underlying buffer has changed shape (i.e. resized)\n     * letting the renderer know that it needs to discard the old buffer on the GPU and create a new one\n     * @event change\n     */\n\n    /** a unique id for this uniform group used through the renderer */\n    public readonly uid: number = uid('buffer');\n\n    /**\n     * a resource type, used to identify how to handle it when its in a bind group / shader resource\n     * @internal\n     */\n    public readonly _resourceType = 'bufferResource';\n\n    /**\n     * used internally to know if a uniform group was used in the last render pass\n     * @internal\n     */\n    public _touched = 0;\n\n    /**\n     * the resource id used internally by the renderer to build bind group keys\n     * @internal\n     */\n    public _resourceId = uid('resource');\n\n    /** the underlying buffer that this resource is using */\n    public buffer: Buffer;\n    /** the offset of the buffer this resource is using. If not provided, then it will use the offset of the buffer. */\n    public readonly offset: number;\n    /** the size of the buffer this resource is using. If not provided, then it will use the size of the buffer. */\n    public readonly size: number;\n    /**\n     * A cheeky hint to the GL renderer to let it know this is a BufferResource\n     * @internal\n     */\n    public readonly _bufferResource = true;\n\n    /**\n     * Has the Buffer resource been destroyed?\n     * @readonly\n     */\n    public destroyed = false;\n\n    /**\n     * Create a new Buffer Resource.\n     * @param options - The options for the buffer resource\n     * @param options.buffer - The underlying buffer that this resource is using\n     * @param options.offset - The offset of the buffer this resource is using.\n     * If not provided, then it will use the offset of the buffer.\n     * @param options.size - The size of the buffer this resource is using.\n     * If not provided, then it will use the size of the buffer.\n     */\n    constructor({ buffer, offset, size }: { buffer: Buffer; offset?: number; size?: number; })\n    {\n        super();\n\n        this.buffer = buffer;\n        this.offset = offset | 0;\n        this.size = size;\n\n        this.buffer.on('change', this.onBufferChange, this);\n    }\n\n    protected onBufferChange(): void\n    {\n        this._resourceId = uid('resource');\n\n        this.emit('change', this);\n    }\n\n    /**\n     * Destroys this resource. Make sure the underlying buffer is not used anywhere else\n     * if you want to destroy it as well, or code will explode\n     * @param destroyBuffer - Should the underlying buffer be destroyed as well?\n     */\n    public destroy(destroyBuffer = false): void\n    {\n        this.destroyed = true;\n\n        if (destroyBuffer)\n        {\n            this.buffer.destroy();\n        }\n\n        this.emit('change', this);\n\n        this.buffer = null;\n    }\n}\n", "import { warn } from '../../../../../utils/logging/warn';\nimport { getAttributeInfoFromFormat } from '../../../shared/geometry/utils/getAttributeInfoFromFormat';\n\nimport type { Geometry } from '../../../shared/geometry/Geometry';\nimport type { ExtractedAttributeData } from './extractAttributesFromGlProgram';\n\n/**\n * This function looks at the attribute information provided to the geometry and attempts\n * to fill in an gaps. WE do this by looking at the extracted data from the shader and\n * making best guesses.\n *\n * Most of th etime users don't need to provide all the attribute info beyond the data itself, so we\n * can fill in the gaps for them. If you are using attributes in a more advanced way, you can\n * don't forget to add all the info at creation!\n * @param geometry - the geometry to ensure attributes for\n * @param extractedData - the extracted data from the shader\n * @internal\n */\nexport function ensureAttributes(\n    geometry: Geometry,\n    extractedData: Record<string, ExtractedAttributeData>\n): void\n{\n    for (const i in geometry.attributes)\n    {\n        const attribute = geometry.attributes[i];\n        const attributeData = extractedData[i];\n\n        if (attributeData)\n        {\n            attribute.format ??= attributeData.format;\n            attribute.offset ??= attributeData.offset;\n            attribute.instance ??= attributeData.instance;\n        }\n        else\n        {\n            // eslint-disable-next-line max-len\n            warn(`Attribute ${i} is not present in the shader, but is present in the geometry. Unable to infer attribute details.`);\n        }\n    }\n\n    ensureStartAndStride(geometry);\n}\n\nfunction ensureStartAndStride(geometry: Geometry): void\n{\n    const { buffers, attributes } = geometry;\n\n    const tempStride: Record<string, number> = {};\n    const tempStart: Record<string, number> = {};\n\n    for (const j in buffers)\n    {\n        const buffer = buffers[j];\n\n        tempStride[buffer.uid] = 0;\n        tempStart[buffer.uid] = 0;\n    }\n\n    for (const j in attributes)\n    {\n        const attribute = attributes[j];\n\n        tempStride[attribute.buffer.uid] += getAttributeInfoFromFormat(attribute.format).stride;\n    }\n\n    for (const j in attributes)\n    {\n        const attribute = attributes[j];\n\n        attribute.stride ??= tempStride[attribute.buffer.uid];\n\n        attribute.start ??= tempStart[attribute.buffer.uid];\n\n        tempStart[attribute.buffer.uid] += getAttributeInfoFromFormat(attribute.format).stride;\n    }\n}\n", "import { STENCIL_MODES } from '../../shared/state/const';\n\n/**\n * The stencil state for the GPU renderer.\n * This is used to define how the stencil buffer should be configured.\n * @category rendering\n * @advanced\n */\nexport interface StencilState\n{\n    stencilWriteMask?: number\n    stencilReadMask?: number;\n    stencilFront?: {\n        compare: 'always' | 'equal' | 'not-equal';\n        passOp: 'increment-clamp' | 'decrement-clamp' | 'keep' | 'replace';\n    },\n    stencilBack?: {\n        compare: 'always' | 'equal' | 'not-equal';\n        passOp: 'increment-clamp' | 'decrement-clamp' | 'keep' | 'replace';\n    }\n}\n\n/** @internal */\nexport const GpuStencilModesToPixi: StencilState[] = [];\n\nGpuStencilModesToPixi[STENCIL_MODES.NONE] = undefined;\n\nGpuStencilModesToPixi[STENCIL_MODES.DISABLED] = {\n    stencilWriteMask: 0,\n    stencilReadMask: 0,\n};\n\nGpuStencilModesToPixi[STENCIL_MODES.RENDERING_MASK_ADD] = {\n    stencilFront: {\n        compare: 'equal',\n        passOp: 'increment-clamp',\n    },\n    stencilBack: {\n        compare: 'equal',\n        passOp: 'increment-clamp',\n    },\n};\n\nGpuStencilModesToPixi[STENCIL_MODES.RENDERING_MASK_REMOVE] = {\n    stencilFront: {\n        compare: 'equal',\n        passOp: 'decrement-clamp',\n    },\n    stencilBack: {\n        compare: 'equal',\n        passOp: 'decrement-clamp',\n    },\n};\n\nGpuStencilModesToPixi[STENCIL_MODES.MASK_ACTIVE] = {\n    stencilWriteMask: 0,\n    stencilFront: {\n        compare: 'equal',\n        passOp: 'keep',\n    },\n    stencilBack: {\n        compare: 'equal',\n        passOp: 'keep',\n    },\n};\n\nGpuStencilModesToPixi[STENCIL_MODES.INVERSE_MASK_ACTIVE] = {\n    stencilWriteMask: 0,\n    stencilFront: {\n        compare: 'not-equal',\n        passOp: 'keep',\n    },\n    stencilBack: {\n        compare: 'not-equal',\n        passOp: 'keep',\n    },\n};\n", "import type { Matrix } from '../../../../maths/matrix/Matrix';\n\n/**\n * @param pm\n * @param x\n * @param y\n * @param width\n * @param height\n * @param flipY\n * @internal\n */\nexport function calculateProjection(\n    pm: Matrix,\n    x: number,\n    y: number,\n    width: number,\n    height: number,\n    flipY: boolean\n): Matrix\n{\n    const sign = flipY ? 1 : -1;\n\n    pm.identity();\n\n    pm.a = (1 / width * 2);\n    pm.d = sign * (1 / height * 2);\n\n    pm.tx = -1 - (x * pm.a);\n    pm.ty = -sign - (y * pm.d);\n\n    return pm;\n}\n", "import type { RenderTarget } from './RenderTarget';\n\n/**\n * Checks if the render target is viewable on the screen\n * Basically, is it a canvas element and is that canvas element in the DOM\n * @param renderTarget - the render target to check\n * @returns true if the render target is viewable on the screen\n * @internal\n */\nexport function isRenderingToScreen(renderTarget: RenderTarget): boolean\n{\n    const resource = renderTarget.colorTexture.source.resource;\n\n    return ((globalThis.HTMLCanvasElement && resource instanceof HTMLCanvasElement) && document.body.contains(resource));\n}\n", "import { Matrix } from '../../../../maths/matrix/Matrix';\nimport { Rectangle } from '../../../../maths/shapes/Rectangle';\nimport { CLEAR } from '../../gl/const';\nimport { calculateProjection } from '../../gpu/renderTarget/calculateProjection';\nimport { SystemRunner } from '../system/SystemRunner';\nimport { CanvasSource } from '../texture/sources/CanvasSource';\nimport { TextureSource } from '../texture/sources/TextureSource';\nimport { Texture } from '../texture/Texture';\nimport { getCanvasTexture } from '../texture/utils/getCanvasTexture';\nimport { isRenderingToScreen } from './isRenderingToScreen';\nimport { RenderTarget } from './RenderTarget';\n\nimport type { RgbaArray } from '../../../../color/Color';\nimport type { ICanvas } from '../../../../environment/canvas/ICanvas';\nimport type { CLEAR_OR_BOOL } from '../../gl/const';\nimport type { GlRenderTarget } from '../../gl/GlRenderTarget';\nimport type { GpuRenderTarget } from '../../gpu/renderTarget/GpuRenderTarget';\nimport type { Renderer } from '../../types';\nimport type { System } from '../system/System';\nimport type { BindableTexture } from '../texture/Texture';\n\n/**\n * A render surface is a texture, canvas, or render target\n * @category rendering\n * @see environment.ICanvas\n * @see Texture\n * @see RenderTarget\n * @advanced\n */\nexport type RenderSurface = ICanvas | BindableTexture | RenderTarget;\n\n/**\n * stores a render target and its frame\n * @ignore\n */\ninterface RenderTargetAndFrame\n{\n    /** the render target */\n    renderTarget: RenderTarget;\n    /** the frame to use when using the render target */\n    frame: Rectangle\n}\n\n/**\n * An adaptor interface for RenderTargetSystem to support WebGL and WebGPU.\n * This is used internally by the renderer, and is not intended to be used directly.\n * @ignore\n */\nexport interface RenderTargetAdaptor<RENDER_TARGET extends GlRenderTarget | GpuRenderTarget>\n{\n    init(\n        /** the renderer */\n        renderer: Renderer,\n        /** the render target system */\n        renderTargetSystem: RenderTargetSystem<RENDER_TARGET>\n    ): void\n\n    /** A function copies the contents of a render surface to a texture */\n    copyToTexture(\n        /** the render surface to copy from  */\n        sourceRenderSurfaceTexture: RenderTarget,\n        /** the texture to copy to */\n        destinationTexture: Texture,\n        /** the origin of the copy */\n        originSrc: { x: number; y: number },\n        /** the size of the copy */\n        size: { width: number; height: number },\n        /** the destination origin (top left to paste from!) */\n        originDest?: { x: number; y: number },\n    ): Texture\n\n    /** starts a render pass on the render target */\n    startRenderPass(\n        /** the render target to start the render pass on */\n        renderTarget: RenderTarget,\n        /* the clear mode to use. Can be true or a CLEAR number 'COLOR | DEPTH | STENCIL' 0b111* */\n        clear: CLEAR_OR_BOOL,\n        /** the color to clear to */\n        clearColor?: RgbaArray,\n        /** the viewport to use */\n        viewport?: Rectangle\n    ): void\n\n    /** clears the current render target to the specified color */\n    clear(\n        /** the render target to clear */\n        renderTarget: RenderTarget,\n        /** the clear mode to use. Can be true or a CLEAR number 'COLOR | DEPTH | STENCIL' 0b111 */\n        clear: CLEAR_OR_BOOL,\n        /** the color to clear to   */\n        clearColor?: RgbaArray,\n        /** the viewport to use */\n        viewport?: Rectangle\n    ): void\n\n    /** finishes the current render pass */\n    finishRenderPass(renderTarget: RenderTarget): void\n\n    /** called after the render pass is finished */\n    postrender?(renderTarget: RenderTarget): void;\n\n    /** called before the render main pass is started */\n    prerender?(renderTarget: RenderTarget): void;\n\n    /**\n     * initializes a gpu render target. Both renderers use this function to initialize a gpu render target\n     * Its different type of object depending on the renderer.\n     */\n    initGpuRenderTarget(\n        /** the render target to initialize */\n        renderTarget: RenderTarget\n    ): RENDER_TARGET\n\n    /** called when a render target is resized */\n    resizeGpuRenderTarget(\n        /** the render target to resize */\n        renderTarget: RenderTarget\n    ): void\n\n    /** destroys the gpu render target */\n    destroyGpuRenderTarget(\n        /** the render target to destroy */\n        gpuRenderTarget: RENDER_TARGET\n    ): void\n}\n\n/**\n * A system that manages render targets. A render target is essentially a place where the shaders can color in the pixels.\n * The render target system is responsible for binding the render target to the renderer, and managing the viewport.\n * Render targets can be pushed and popped.\n *\n * To make it easier, you can also bind textures and canvases too. This will automatically create a render target for you.\n * The render target itself is a lot more powerful than just a texture or canvas,\n * as it can have multiple textures attached to it.\n * It will also give ou fine grain control over the stencil buffer / depth texture.\n * @example\n *\n * ```js\n *\n * // create a render target\n * const renderTarget = new RenderTarget({\n *   colorTextures: [new TextureSource({ width: 100, height: 100 })],\n * });\n *\n * // bind the render target\n * renderer.renderTarget.bind(renderTarget);\n *\n * // draw something!\n * ```\n * @category rendering\n * @advanced\n */\nexport class RenderTargetSystem<RENDER_TARGET extends GlRenderTarget | GpuRenderTarget> implements System\n{\n    /** When rendering of a scene begins, this is where the root render surface is stored */\n    public rootRenderTarget: RenderTarget;\n    /** This is the root viewport for the render pass*/\n    public rootViewPort = new Rectangle();\n    /** A boolean that lets the dev know if the current render pass is rendering to the screen. Used by some plugins */\n    public renderingToScreen: boolean;\n    /** the current active render target */\n    public renderTarget: RenderTarget;\n    /** the current active render surface that the render target is created from */\n    public renderSurface: RenderSurface;\n    /** the current viewport that the gpu is using */\n    public readonly viewport = new Rectangle();\n    /**\n     * a runner that lets systems know if the active render target has changed.\n     * Eg the Stencil System needs to know so it can manage the stencil buffer\n     */\n    public readonly onRenderTargetChange = new SystemRunner('onRenderTargetChange');\n    /** the projection matrix that is used by the shaders based on the active render target and the viewport */\n    public readonly projectionMatrix = new Matrix();\n    /** the default clear color for render targets */\n    public readonly defaultClearColor: RgbaArray = [0, 0, 0, 0];\n    /** a reference to the adaptor that interfaces with WebGL / WebGP */\n    public readonly adaptor: RenderTargetAdaptor<RENDER_TARGET>;\n    /**\n     * a hash that stores the render target for a given render surface. When you pass in a texture source,\n     * a render target is created for it. This map stores and makes it easy to retrieve the render target\n     */\n    private readonly _renderSurfaceToRenderTargetHash: Map<RenderSurface, RenderTarget>\n        = new Map();\n    /** A hash that stores a gpu render target for a given render target. */\n    private _gpuRenderTargetHash: Record<number, RENDER_TARGET> = Object.create(null);\n    /**\n     * A stack that stores the render target and frame that is currently being rendered to.\n     * When push is called, the current render target is stored in this stack.\n     * When pop is called, the previous render target is restored.\n     */\n    private readonly _renderTargetStack: RenderTargetAndFrame[] = [];\n    /** A reference to the renderer */\n    private readonly _renderer: Renderer;\n\n    constructor(renderer: Renderer)\n    {\n        this._renderer = renderer;\n        renderer.renderableGC.addManagedHash(this, '_gpuRenderTargetHash');\n    }\n\n    /** called when dev wants to finish a render pass */\n    public finishRenderPass()\n    {\n        this.adaptor.finishRenderPass(this.renderTarget);\n    }\n\n    /**\n     * called when the renderer starts to render a scene.\n     * @param options\n     * @param options.target - the render target to render to\n     * @param options.clear - the clear mode to use. Can be true or a CLEAR number 'COLOR | DEPTH | STENCIL' 0b111\n     * @param options.clearColor - the color to clear to\n     * @param options.frame - the frame to render to\n     */\n    public renderStart({\n        target,\n        clear,\n        clearColor,\n        frame\n    }: {\n        target: RenderSurface;\n        clear: CLEAR_OR_BOOL;\n        clearColor: RgbaArray;\n        frame?: Rectangle\n    }): void\n    {\n        // TODO no need to reset this - use optimised index instead\n        this._renderTargetStack.length = 0;\n\n        this.push(\n            target,\n            clear,\n            clearColor,\n            frame\n        );\n\n        this.rootViewPort.copyFrom(this.viewport);\n        this.rootRenderTarget = this.renderTarget;\n        this.renderingToScreen = isRenderingToScreen(this.rootRenderTarget);\n\n        this.adaptor.prerender?.(this.rootRenderTarget);\n    }\n\n    public postrender()\n    {\n        this.adaptor.postrender?.(this.rootRenderTarget);\n    }\n\n    /**\n     * Binding a render surface! This is the main function of the render target system.\n     * It will take the RenderSurface (which can be a texture, canvas, or render target) and bind it to the renderer.\n     * Once bound all draw calls will be rendered to the render surface.\n     *\n     * If a frame is not provide and the render surface is a texture, the frame of the texture will be used.\n     * @param renderSurface - the render surface to bind\n     * @param clear - the clear mode to use. Can be true or a CLEAR number 'COLOR | DEPTH | STENCIL' 0b111\n     * @param clearColor - the color to clear to\n     * @param frame - the frame to render to\n     * @returns the render target that was bound\n     */\n    public bind(\n        renderSurface: RenderSurface,\n        clear: CLEAR_OR_BOOL = true,\n        clearColor?: RgbaArray,\n        frame?: Rectangle\n    ): RenderTarget\n    {\n        const renderTarget = this.getRenderTarget(renderSurface);\n\n        const didChange = this.renderTarget !== renderTarget;\n\n        this.renderTarget = renderTarget;\n        this.renderSurface = renderSurface;\n\n        const gpuRenderTarget = this.getGpuRenderTarget(renderTarget);\n\n        if (renderTarget.pixelWidth !== gpuRenderTarget.width\n            || renderTarget.pixelHeight !== gpuRenderTarget.height)\n        {\n            this.adaptor.resizeGpuRenderTarget(renderTarget);\n\n            gpuRenderTarget.width = renderTarget.pixelWidth;\n            gpuRenderTarget.height = renderTarget.pixelHeight;\n        }\n\n        const source = renderTarget.colorTexture;\n        const viewport = this.viewport;\n\n        const pixelWidth = source.pixelWidth;\n        const pixelHeight = source.pixelHeight;\n\n        if (!frame && renderSurface instanceof Texture)\n        {\n            frame = renderSurface.frame;\n        }\n\n        if (frame)\n        {\n            const resolution = source._resolution;\n\n            viewport.x = ((frame.x * resolution) + 0.5) | 0;\n            viewport.y = ((frame.y * resolution) + 0.5) | 0;\n            viewport.width = ((frame.width * resolution) + 0.5) | 0;\n            viewport.height = ((frame.height * resolution) + 0.5) | 0;\n        }\n        else\n        {\n            viewport.x = 0;\n            viewport.y = 0;\n            viewport.width = pixelWidth;\n            viewport.height = pixelHeight;\n        }\n\n        calculateProjection(\n            this.projectionMatrix,\n            0, 0,\n            viewport.width / source.resolution,\n            viewport.height / source.resolution,\n            !renderTarget.isRoot\n        );\n\n        this.adaptor.startRenderPass(renderTarget, clear, clearColor, viewport);\n\n        if (didChange)\n        {\n            this.onRenderTargetChange.emit(renderTarget);\n        }\n\n        return renderTarget;\n    }\n\n    public clear(\n        target?: RenderSurface,\n        clear: CLEAR_OR_BOOL = CLEAR.ALL,\n        clearColor?: RgbaArray,\n    )\n    {\n        if (!clear) return;\n\n        if (target)\n        {\n            target = this.getRenderTarget(target);\n        }\n\n        this.adaptor.clear(\n            (target as RenderTarget) || this.renderTarget,\n            clear,\n            clearColor,\n            this.viewport\n        );\n    }\n\n    protected contextChange(): void\n    {\n        this._gpuRenderTargetHash = Object.create(null);\n    }\n\n    /**\n     * Push a render surface to the renderer. This will bind the render surface to the renderer,\n     * @param renderSurface - the render surface to push\n     * @param clear - the clear mode to use. Can be true or a CLEAR number 'COLOR | DEPTH | STENCIL' 0b111\n     * @param clearColor - the color to clear to\n     * @param frame - the frame to use when rendering to the render surface\n     */\n    public push(\n        renderSurface: RenderSurface,\n        clear: CLEAR | boolean = CLEAR.ALL,\n        clearColor?: RgbaArray,\n        frame?: Rectangle\n    )\n    {\n        const renderTarget = this.bind(renderSurface, clear, clearColor, frame);\n\n        this._renderTargetStack.push({\n            renderTarget,\n            frame,\n        });\n\n        return renderTarget;\n    }\n\n    /** Pops the current render target from the renderer and restores the previous render target. */\n    public pop()\n    {\n        this._renderTargetStack.pop();\n\n        const currentRenderTargetData = this._renderTargetStack[this._renderTargetStack.length - 1];\n\n        this.bind(currentRenderTargetData.renderTarget, false, null, currentRenderTargetData.frame);\n    }\n\n    /**\n     * Gets the render target from the provide render surface. Eg if its a texture,\n     * it will return the render target for the texture.\n     * If its a render target, it will return the same render target.\n     * @param renderSurface - the render surface to get the render target for\n     * @returns the render target for the render surface\n     */\n    public getRenderTarget(renderSurface: RenderSurface): RenderTarget\n    {\n        if (((renderSurface as Texture).isTexture))\n        {\n            renderSurface = (renderSurface as Texture).source;\n        }\n\n        return this._renderSurfaceToRenderTargetHash.get(renderSurface)\n        ?? this._initRenderTarget(renderSurface);\n    }\n\n    /**\n     * Copies a render surface to another texture.\n     *\n     * NOTE:\n     * for sourceRenderSurfaceTexture, The render target must be something that is written too by the renderer\n     *\n     * The following is not valid:\n     * @example\n     * const canvas = document.createElement('canvas')\n     * canvas.width = 200;\n     * canvas.height = 200;\n     *\n     * const ctx = canvas2.getContext('2d')!\n     * ctx.fillStyle = 'red'\n     * ctx.fillRect(0, 0, 200, 200);\n     *\n     * const texture = RenderTexture.create({\n     *   width: 200,\n     *   height: 200,\n     * })\n     * const renderTarget = renderer.renderTarget.getRenderTarget(canvas2);\n     *\n     * renderer.renderTarget.copyToTexture(renderTarget,texture, {x:0,y:0},{width:200,height:200},{x:0,y:0});\n     *\n     * The best way to copy a canvas is to create a texture from it. Then render with that.\n     *\n     * Parsing in a RenderTarget canvas context (with a 2d context)\n     * @param sourceRenderSurfaceTexture - the render surface to copy from\n     * @param destinationTexture - the texture to copy to\n     * @param originSrc - the origin of the copy\n     * @param originSrc.x - the x origin of the copy\n     * @param originSrc.y - the y origin of the copy\n     * @param size - the size of the copy\n     * @param size.width - the width of the copy\n     * @param size.height - the height of the copy\n     * @param originDest - the destination origin (top left to paste from!)\n     * @param originDest.x - the x origin of the paste\n     * @param originDest.y - the y origin of the paste\n     */\n    public copyToTexture(\n        sourceRenderSurfaceTexture: RenderTarget,\n        destinationTexture: Texture,\n        originSrc: { x: number; y: number },\n        size: { width: number; height: number },\n        originDest: { x: number; y: number; },\n    )\n    {\n        // fit the size to the source we don't want to go out of bounds\n\n        if (originSrc.x < 0)\n        {\n            size.width += originSrc.x;\n            originDest.x -= originSrc.x;\n            originSrc.x = 0;\n        }\n\n        if (originSrc.y < 0)\n        {\n            size.height += originSrc.y;\n            originDest.y -= originSrc.y;\n            originSrc.y = 0;\n        }\n\n        const { pixelWidth, pixelHeight } = sourceRenderSurfaceTexture;\n\n        size.width = Math.min(size.width, pixelWidth - originSrc.x);\n        size.height = Math.min(size.height, pixelHeight - originSrc.y);\n\n        return this.adaptor.copyToTexture(\n            sourceRenderSurfaceTexture,\n            destinationTexture,\n            originSrc,\n            size,\n            originDest\n        );\n    }\n\n    /**\n     * ensures that we have a depth stencil buffer available to render to\n     * This is used by the mask system to make sure we have a stencil buffer.\n     */\n    public ensureDepthStencil()\n    {\n        if (!this.renderTarget.stencil)\n        {\n            this.renderTarget.stencil = true;\n\n            this.adaptor.startRenderPass(this.renderTarget, false, null, this.viewport);\n        }\n    }\n\n    /** nukes the render target system */\n    public destroy()\n    {\n        (this._renderer as null) = null;\n\n        this._renderSurfaceToRenderTargetHash.forEach((renderTarget, key) =>\n        {\n            if (renderTarget !== key)\n            {\n                renderTarget.destroy();\n            }\n        });\n\n        this._renderSurfaceToRenderTargetHash.clear();\n\n        this._gpuRenderTargetHash = Object.create(null);\n    }\n\n    private _initRenderTarget(renderSurface: RenderSurface): RenderTarget\n    {\n        let renderTarget: RenderTarget = null;\n\n        if (CanvasSource.test(renderSurface))\n        {\n            renderSurface = getCanvasTexture(renderSurface as ICanvas).source;\n        }\n\n        if (renderSurface instanceof RenderTarget)\n        {\n            renderTarget = renderSurface;\n        }\n        else if (renderSurface instanceof TextureSource)\n        {\n            renderTarget = new RenderTarget({\n                colorTextures: [renderSurface],\n            });\n\n            if (renderSurface.source instanceof CanvasSource)\n            {\n                renderTarget.isRoot = true;\n            }\n\n            // TODO add a test for this\n            renderSurface.once('destroy', () =>\n            {\n                renderTarget.destroy();\n\n                this._renderSurfaceToRenderTargetHash.delete(renderSurface);\n\n                const gpuRenderTarget = this._gpuRenderTargetHash[renderTarget.uid];\n\n                if (gpuRenderTarget)\n                {\n                    this._gpuRenderTargetHash[renderTarget.uid] = null;\n                    this.adaptor.destroyGpuRenderTarget(gpuRenderTarget);\n                }\n            });\n        }\n\n        this._renderSurfaceToRenderTargetHash.set(renderSurface, renderTarget);\n\n        return renderTarget;\n    }\n\n    public getGpuRenderTarget(renderTarget: RenderTarget)\n    {\n        return this._gpuRenderTargetHash[renderTarget.uid]\n        || (this._gpuRenderTargetHash[renderTarget.uid] = this.adaptor.initGpuRenderTarget(renderTarget));\n    }\n\n    public resetState(): void\n    {\n        this.renderTarget = null;\n        this.renderSurface = null;\n    }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkIO,IAAM,UAAN,MAAMA,iBAAe,OAC5B;;;;EAwDI,YAAY,SACZ;AACI,cAAU,EAAE,GAAGA,SAAO,gBAAgB,GAAG,QAAQ;AAEjD,UAAM,OAA8B;AAlCxC,SAAO,UAAU;AAMV,SAAA,SAAS,MAAM,MAAM;AA8BxB,SAAK,YAAY,QAAQ;AACzB,SAAK,UAAU,QAAQ;AAGnB,QAAA,OAAO,QAAQ,cAAc,WACjC;AACS,WAAA,YAAY,QAAQ,YAAY,OAAO;IAAA,OAGhD;AACI,WAAK,YAAY,QAAQ;IAAA;AAG7B,SAAK,aAAa,QAAQ;AAC1B,SAAK,gBAAgB,QAAQ;AAC7B,SAAK,iBAAiB,QAAQ;AAEzB,SAAA,YAAY,YAAY,GAAG,CAAC;EAAA;;;;;;;;EAU9B,MACH,eACA,OACA,QACA,WAEJ;AACI,kBAAc,YAAY,MAAM,OAAO,QAAQ,SAAS;EAAA;;;;;EAO5D,IAAI,YACJ;AACI,WAAO,KAAK,OAAO;EAAA;;EAIvB,IAAI,UAAU,OACd;AACI,SAAK,OAAO,YAAY;EAAA;;;;;;EAQ5B,OAAc,KAAK,SACnB;AACI,UAAM,EAAE,KAAK,IAAI,GAAG,KAAA,IAAS;AAEzB,QAAA;AACA,QAAA;AAEJ,QAAI,KACJ;AACiB,mBAAA,WAAW,KAAK,GAAG;IAAA;AAGpC,QAAI,IACJ;AACgB,kBAAA,UAAU,KAAK,EAAE;IAAA;AAGjC,WAAO,IAAIA,SAAO;MACd;MACA;MACA,GAAG;IAAA,CACN;EAAA;AAET;AA/Ia,QAGK,iBAAgC;EAC1C,WAAW;EACX,YAAY;EACZ,SAAS;EACT,WAAW;EACX,eAAe;EACf,gBAAgB;AACpB;AAVG,IAAM,SAAN;;;AChIP,IAAM,eAAiG,CAAA;AAEvG,WAAW,kBAAkB,cAAc,aAAa,YAAY;AAQpE,eAAsB,0BAA0B,MAChD;AACQ,MAAA;AAAM;AAEV,WAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KACzC;AACU,UAAA,MAAM,aAAa,CAAC;AAEtB,QAAA,IAAI,MAAM,KAAA,GACd;AACU,YAAA,IAAI,MAAM,KAAK;AAErB;IAAA;EACJ;AAER;AAQA,eAAsB,sBAAsB,KAC5C;AACW,SAAA,0BAA0B,CAAC,GAAG;AACzC;;;ACrCA,IAAI;AAQG,SAAS,sBAChB;AACQ,MAAA,OAAO,eAAe,WAC1B;AACW,WAAA;EAAA;AAIX,MAAA;AAEI,UAAM,OAAO,IAAI,SAAS,UAAU,UAAU,UAAU,mCAAmC;AAG3F,iBAAa,KAAK,EAAE,GAAG,IAAA,GAAO,KAAK,GAAG,MAAM;EAAA,SAEzC,IACP;AACiB,iBAAA;EAAA;AAGV,SAAA;AACX;;;ACzBY,IAAA,SAAA,CAAAC,WAAL;AAGHA,SAAAA,OAAA,MAAA,IAAO,CAAP,IAAA;AAEAA,SAAAA,OAAA,OAAA,IAAQ,KAAR,IAAA;AAEAA,SAAAA,OAAA,SAAA,IAAU,IAAV,IAAA;AAEAA,SAAAA,OAAA,OAAA,IAAQ,GAAR,IAAA;AAGAA,SAAAA,OAAA,aAAA,IAAc,KAAd,IAAA;AAEAA,SAAAA,OAAA,eAAA,IAAgB,KAAhB,IAAA;AAEAA,SAAAA,OAAA,eAAA,IAAgB,IAAhB,IAAA;AAEAA,SAAAA,OAAA,KAAA,IAAM,KAAN,IAAA;AAlBQA,SAAAA;AAAA,GAAA,SAAA,CAAA,CAAA;;;ACsCL,IAAM,eAAN,MACP;;;;EAOI,YAAY,MACZ;AACI,SAAK,QAAQ,CAAA;AACb,SAAK,QAAQ;EAAA;;;;;;;EASV,KAAK,IAAc,IAAc,IAAc,IAClD,IAAc,IAAc,IAAc,IAC9C;AACU,UAAA,EAAE,MAAM,MAAA,IAAU;AAExB,aAAS,IAAI,GAAG,MAAM,MAAM,QAAQ,IAAI,KAAK,KAC7C;AACU,YAAA,CAAC,EAAE,IAAI,EAAE,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;IAAA;AAG1C,WAAA;EAAA;;;;;;;;;;;;;;;;;;;EAqBJ,IAAI,MACX;AACS,QAAA,KAAa,KAAK,KAAK,GAC5B;AACI,WAAK,OAAO,IAAI;AACX,WAAA,MAAM,KAAK,IAAI;IAAA;AAGjB,WAAA;EAAA;;;;;EAOJ,OAAO,MACd;AACI,UAAM,QAAQ,KAAK,MAAM,QAAQ,IAAI;AAErC,QAAI,UAAU,IACd;AACS,WAAA,MAAM,OAAO,OAAO,CAAC;IAAA;AAGvB,WAAA;EAAA;;;;;EAOJ,SAAS,MAChB;AACI,WAAO,KAAK,MAAM,QAAQ,IAAI,MAAM;EAAA;;EAIjC,YACP;AACI,SAAK,MAAM,SAAS;AAEb,WAAA;EAAA;;EAIJ,UACP;AACI,SAAK,UAAU;AACf,SAAK,QAAQ;AACb,SAAK,QAAQ;EAAA;;;;;EAOjB,IAAW,QACX;AACW,WAAA,KAAK,MAAM,WAAW;EAAA;;;;;EAOjC,IAAW,OACX;AACI,WAAO,KAAK;EAAA;AAEpB;;;AClFA,IAAM,iBAAiB;EACnB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACJ;AAsDO,IAAM,oBAAN,MAAMC,2BAEH,sBACV;;;;;;EAuEI,YAAY,QACZ;AACU,UAAA;AA7BM,SAAA,MAAM,IAAI,UAAU;AAMpB,SAAA,UAA0B,uBAAA,OAAO,IAAI;AAErC,SAAA,cAAqB,uBAAA,OAAO,IAAI;AAQhD,SAAU,eAAwB,CAAA;AAG1B,SAAA,eAA8C,uBAAA,OAAO,IAAI;AAW7D,SAAK,OAAO,OAAO;AACnB,SAAK,OAAO,OAAO;AACnB,SAAK,SAAS;AAER,UAAA,kBAAkB,CAAC,GAAG,gBAAgB,GAAI,KAAK,OAAO,WAAW,CAAA,CAAG;AAErE,SAAA,YAAY,GAAG,eAAe;AAEnC,SAAK,iBAAiB;EAAA;;;;;EAO1B,MAAa,KAAK,UAA4B,CAAA,GAC9C;AACI,UAAM,OAAO,QAAQ,yBAAyB,OAAO,OAAO,QAAQ,kBAAkB;AAEtF,UAAM,0BAA0B,IAAI;AAE/B,SAAA,YAAY,KAAK,OAAO,OAAO;AACpC,SAAK,UAAU,KAAK,OAAO,aAAa,KAAK,OAAO,kBAAkB;AAG3D,eAAA,cAAc,KAAK,cAC9B;AACU,YAAA,SAAS,KAAK,aAAa,UAAU;AAErC,YAAA,uBAAwB,OAAO,YAAoB;AAEzD,gBAAU,EAAE,GAAG,sBAAsB,GAAG,QAAQ;IAAA;AAGpD,cAAU,EAAE,GAAGA,mBAAiB,gBAAgB,GAAG,QAAQ;AACtD,SAAA,eAAe,QAAQ,cAAc,IAAI;AAGrC,aAAA,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK,MAAM,QAAQ,KACpD;AACI,YAAM,KAAK,QAAQ,KAAK,MAAM,CAAC,EAAE,KAAK,OAAO;IAAA;AAIjD,SAAK,eAAe;EAAA;EAYjB,OAAO,MAAiC,YAC/C;AACI,QAAI,UAAU;AAEd,QAAI,mBAAmB,WACvB;AACc,gBAAA,EAAE,WAAW,QAAQ;AAE/B,UAAI,YACJ;AAEI,oBAAY,QAAQ,4EAA4E;AAGhG,gBAAQ,SAAS,WAAW;MAAA;IAChC;AAGJ,YAAQ,WAAR,QAAQ,SAAW,KAAK,KAAK;AAG7B,QAAI,QAAQ,WAAW,KAAK,KAAK,cACjC;AAEI,WAAK,sBAAsB,QAAQ;AAEnC,cAAQ,eAAR,QAAQ,aAAe,KAAK,WAAW;AACvC,cAAQ,UAAR,QAAQ,QAAU,KAAK,WAAW;IAAA;AAGtC,QAAI,QAAQ,YACZ;AACU,YAAA,cAAc,MAAM,QAAQ,QAAQ,UAAU,KAAK,QAAQ,WAAW,WAAW;AAE/E,cAAA,aAAa,cAAc,QAAQ,aAAa,MAAM,OAAO,SAAS,QAAQ,UAAU,EAAE,QAAQ;IAAA;AAG1G,QAAA,CAAC,QAAQ,WACb;AACI,cAAQ,UAAU,qBAAqB;AAC/B,cAAA,YAAY,QAAQ,UAAU;IAAA;AAK1C,YAAQ,UAAU,kBAAkB;AAE/B,SAAA,QAAQ,UAAU,KAAK,OAAO;AAC9B,SAAA,QAAQ,YAAY,KAAK,OAAO;AAChC,SAAA,QAAQ,OAAO,KAAK,OAAO;AAC3B,SAAA,QAAQ,UAAU,KAAK,OAAO;AAC9B,SAAA,QAAQ,WAAW,KAAK,OAAO;EAAA;;;;;;;EASjC,OAAO,oBAA4B,qBAA6B,YACvE;AACU,UAAA,qBAAqB,KAAK,KAAK;AAErC,SAAK,KAAK,OAAO,oBAAoB,qBAAqB,UAAU;AACpE,SAAK,KAAK,UAAU,KAAK,KAAK,OAAO,OAAO,KAAK,KAAK,OAAO,QAAQ,KAAK,KAAK,UAAU;AACrF,QAAA,eAAe,UAAa,eAAe,oBAC/C;AACS,WAAA,QAAQ,iBAAiB,KAAK,UAAU;IAAA;EACjD;;;;;;;;;EAWG,MAAM,UAAwB,CAAA,GACrC;AAEI,UAAM,WAAW;AAEjB,YAAQ,WAAR,QAAQ,SAAW,SAAS,aAAa;AACzC,YAAQ,eAAR,QAAQ,aAAe,KAAK,WAAW;AAC/B,YAAA,UAAR,QAAQ,QAAU,MAAM;AAExB,UAAM,EAAE,OAAO,YAAY,OAAA,IAAW;AAEtC,UAAM,OAAO,SAAS,cAAc,KAAK,WAAW,SAAS;AAE7D,aAAS,aAAa,MAAM,QAAQ,OAAO,MAAM,OAAO,QAAA,CAAsB;EAAA;;EAIlF,IAAI,aACJ;AACI,WAAO,KAAK,KAAK;EAAA;EAGrB,IAAI,WAAW,OACf;AACI,SAAK,KAAK,aAAa;AAClB,SAAA,QAAQ,iBAAiB,KAAK,KAAK;EAAA;;;;;;;EAS5C,IAAI,QACJ;AACW,WAAA,KAAK,KAAK,QAAQ,MAAM;EAAA;;;;;EAOnC,IAAI,SACJ;AACW,WAAA,KAAK,KAAK,QAAQ,MAAM;EAAA;;;;;;EAQnC,IAAI,SACJ;AACI,WAAO,KAAK,KAAK;EAAA;;;;;EAOrB,IAAI,qBACJ;AACI,WAAO,KAAK;EAAA;;;;;;EAQhB,IAAI,oBACJ;AACI,UAAM,WAAW;AAEjB,WAAO,SAAS,aAAa;EAAA;;;;;;EAQjC,IAAI,SACJ;AACI,WAAO,KAAK,KAAK;EAAA;;;;;EAOb,eAAe,WACvB;AACc,cAAA,QAAQ,CAAC,aACnB;AACI,WAAK,QAAQ,QAAQ,IAAI,IAAI,aAAa,QAAQ;IAAA,CACrD;EAAA;EAGG,YAAY,SACpB;AACQ,QAAA;AAEJ,SAAK,KAAK,SACV;AACU,YAAA,MAAM,QAAQ,CAAC;AAErB,WAAK,WAAW,IAAI,OAAO,IAAI,IAAI;IAAA;EACvC;;;;;;;;;;EAYI,WAAW,UAA6B,MAChD;AACU,UAAA,SAAS,IAAI,SAAS,IAA2B;AAElD,QAAA,KAAa,IAAI,GACtB;AACI,YAAM,IAAI,MAAM,qBAAqB,IAAI,qBAAqB;IAAA;AAGjE,SAAa,IAAI,IAAI;AAEjB,SAAA,aAAa,IAAI,IAAI;AAEf,eAAA,KAAK,KAAK,SACrB;AACI,WAAK,QAAQ,CAAC,EAAE,IAAI,MAAM;IAAA;AAGvB,WAAA;EAAA;EAGH,UAAU,OAAsC,cACxD;AACI,UAAM,WAAW,aAAa,OAAO,CAAC,KAAK,YAC3C;AACQ,UAAA,QAAQ,IAAI,IAAI,QAAQ;AAErB,aAAA;IAAA,GACR,CAAA,CAAyB;AAEtB,UAAA,QAAQ,CAAC,SACf;AACI,YAAM,YAAY,KAAK;AACvB,YAAM,OAAO,KAAK;AAEZ,YAAA,UAAU,SAAS,IAAI;AAG5B,WAAK,YAAoB,IAAI,IAAI,IAAI;QAClC;QACA,UAAU,IAAI,QAAA,IAAY;MAAA;IAC9B,CACH;EAAA;EAGE,QAAQ,UAAkC,OACjD;AACS,SAAA,QAAQ,QAAQ,MAAM,QAAQ;AAC9B,SAAA,QAAQ,QAAQ,KAAK,OAAO;AAGjC,WAAO,OAAO,KAAK,OAAO,EAAE,QAAQ,CAAC,WACrC;AACI,aAAO,QAAQ;IAAA,CAClB;AAED,SAAK,eAAe;AAGnB,SAAK,cAAuB;EAAA;;;;;;EAQ1B,gBAAgB,SACvB;AACW,WAAA,KAAK,iBAAiB,gBAAgB,OAAO;EAAA;;;;;EAOxD,IAAI,cACJ;AACW,WAAA,CAAC,CAAC,KAAK;EAAA;;;;;;;EASX,mBACP;AACQ,QAAA,CAAC,oBAAA,GACL;AACU,YAAA,IAAI,MAAM,0GAC+C;IAAA;EACnE;;;;;;;;;;;;;;;;;;;;;;;EAwBG,aACP;AACS,SAAA,QAAQ,WAAW,KAAK;EAAA;AAErC;AAxca,kBAKK,iBAAiB;;;;;EAK3B,YAAY;;;;;;;;;;;;;;;;;;;;;;;EAuBZ,8BAA8B;;;;;EAK9B,aAAa;AACjB;AAvCG,IAAM,mBAAN;;;AClJA,IAAM,aAAa;EACtB,MAAM;EACN,QAAQ;IACJ;;MAAkB;;;;;;;;;IAQlB;;MAAgB;;;;EAAA;EAIpB,UAAU;IACN;;MAAkB;;;;;;;IAMlB;;MAAgB;;;;EAAA;AAIxB;AAGO,IAAM,eAAe;EACxB,MAAM;EACN,QAAQ;IACJ;;MAAkB;;;;IAGlB;;MAAgB;;;;EAAA;EAIpB,UAAU;IACN;;MAAkB;;;;;;IAKlB;;MAAgB;;;;EAAA;AAIxB;;;AC7BO,IAAM,mBAAN,MACP;EAYI,YAAY,UACZ;AACI,SAAK,YAAY;EAAA;EAGd,mBAAmB;EAAA;EACnB,oBAAoB;EAAA;EACpB,qBAAqB;AAAS,WAAA;EAAA;EAE9B,cAAc,WAA4B,gBACjD;AACI,SAAK,UAAU,YAAY,MAAM,MAAM,cAAc;AAErD,mBAAe,IAAI,SAAS;EAAA;EAGzB,QAAQ,WACf;AACI,QAAI,CAAC,UAAU;AAAc;AAEnB,cAAA,OAAO,KAAK,SAAS;EAAA;EAG5B,UACP;AACI,SAAK,YAAY;EAAA;AAEzB;AAxCa,iBAEK,YAAY;EACtB,MAAM;IACF,cAAc;IACd,cAAc;IACd,cAAc;EAAA;EAElB,MAAM;AACV;;;ACrBY,SAAA,oBAAoB,aAA0B,UAC9D;AACI,QAAM,iBAAiB,YAAY;AACnC,QAAM,eAAe,eAAe;AAEpC,WAAS,IAAI,GAAG,IAAI,eAAe,iBAAiB,KACpD;AACU,UAAA,cAAc,aAAa,CAAC;AAEjC,aAAS,YAAY,YAAiC,EAA2B,QAAQ,WAAW;EAAA;AAE7G;;;ACTA,IAAM,aAAa,IAAI,OAAO;AAMvB,IAAM,kBAAN,MACP;EAYI,YAAY,UACZ;AACI,SAAK,YAAY;EAAA;EAGd,eAAe,aAA0B,gBAChD;AACI,QAAI,YAAY,mBAChB;AACS,WAAA,6BAA6B,aAAa,cAAc;IAAA,OAGjE;AACS,WAAA,qBAAqB,aAAa,cAAc;IAAA;EACzD;EAGG,QAAQ,aACf;AACI,QAAI,CAAC,YAAY;AAAc;AAE/B,QAAI,YAAY,mBAChB;AACI,WAAK,uBAAuB,WAAW;IAAA,OAG3C;AACI,WAAK,eAAe,WAAW;IAAA;EACnC;EAGG,UACP;AACI,SAAK,YAAY;EAAA;EAGb,qBAAqB,aAA0B,gBACvD;AACI,SAAK,UAAU,YAAY,MAAM,MAAM,cAAc;AAErD,QAAI,YAAY,uBAChB;AACY,cAAA,OAAO,YAAY,qBAAqB;AAChD,kBAAY,wBAAwB;IAAA;AAGxC,mBAAe,IAAI,WAAW;EAAA;EAG1B,6BAA6B,aAA0B,gBAC/D;AACI,UAAM,uBAAuB,YAAY,0BAAZ,YAAY,wBAA0B,QAAQ,IAAI,eAAe;AAE9F,yBAAqB,aAAa,YAAY;AACzB,yBAAA,YAAY,YAAY,KAAK;AAClD,yBAAqB,UAAU,YAAY;AAC3C,yBAAqB,SAAS,YAAY;AAE1C,mBAAe,IAAI,WAAW;AAC9B,SAAK,UAAU,YAAY,MAAM,WAAW,sBAAsB,cAAc;EAAA;EAG5E,uBAAuB,aAC/B;AACI,QAAI,YAAY,oBAChB;AACI,kBAAY,qBAAqB;AAE3B,YAAA,uBAAuB,WACxB,SAAA,EACA;QACG,CAAC,YAAY,eAAe;QAC5B,CAAC,YAAY,eAAe;MAAA;AAG/B,WAAA,UAAU,aAAa,KAAK,YAAY,SAAS,MAAM,MAAM,YAAY,QAAQ,KAAK;AAEtF,WAAA,UAAU,eAAe,KAAK;QAC/B;QACA,YAAY;MAAA,CACf;AAEmB,0BAAA,aAAa,KAAK,UAAU,WAAW;AAEtD,WAAA,UAAU,aAAa,iBAAiB;AAExC,WAAA,UAAU,aAAa,IAAI;AAC3B,WAAA,UAAU,eAAe,IAAI;IAAA;AAGtC,gBAAY,sBAAsB,SAAS,cAAc,YAAY,qBAAqB;AAC1F,gBAAY,sBAAsB,SAAS,SAAS,QAAQ,CAAC,EAAE,OAAO;EAAA;EAGlE,eAAe,aACvB;AACS,SAAA,UAAU,eAAe,KAAK;MAC/B,sBAAsB,YAAY;MAClC,YAAY,YAAY;IAAA,CAC3B;AAEmB,wBAAA,aAAa,KAAK,UAAU,WAAW;AAEtD,SAAA,UAAU,eAAe,IAAI;EAAA;AAE1C;AAtHa,gBAEK,YAAY;EACtB,MAAM;IACF,cAAc;IACd,cAAc;IACd,cAAc;EAAA;EAElB,MAAM;AACV;;;ACjBY,SAAA,UAAU,MAAsB,OAChD;AACc,YAAA,QAAA;AAEV,WAAS,IAAI,OAAO,IAAI,KAAK,QAAQ,KACrC;AACQ,QAAA,KAAK,CAAC,GACV;AACI,WAAK,CAAC,IAAI;IAAA,OAGd;AACI;IAAA;EACJ;AAER;;;ACjBA,IAAM,gBAAgB,IAAI,UAAU;AACpC,IAAM,6BAA6B,iBAAiB,eAAe;AAOnD,SAAA,4BAA4B,aAA0B,0BAA0B,OAChG;AACI,6BAA2B,WAAW;AAEtC,QAAM,mBAAmB,YAAY;AAErC,QAAM,aAAa,YAAY;AAE/B,aAAW,KAAK,kBAChB;AACU,UAAA,mBAAmB,OAAO,CAAC;AAE3B,UAAA,kBAAkB,iBAAiB,CAAC;AAE1C,UAAM,OAAO,gBAAgB;AAC7B,UAAM,QAAQ,gBAAgB;AAE9B,aAAS,IAAI,GAAG,IAAI,OAAO,KAC3B;AACU,YAAA,QAAQ,KAAK,CAAC;AAMpB,UAAI,MAAM,sBAAsB,eAAe,MAAM,6BAA6B,kBAClF;AAC+B,mCAAA,OAAO,YAAY,CAAC;MAAA;IACnD;AAGJ,cAAU,MAAM,KAAK;AAErB,oBAAgB,QAAQ;EAAA;AAG5B,MAAI,yBACJ;AACI,aAAS,IAAI,GAAG,IAAI,YAAY,oBAAoB,QAAQ,KAC5D;AACI,kCAA4B,YAAY,oBAAoB,CAAC,GAAG,uBAAuB;IAAA;EAC3F;AAER;AAMO,SAAS,2BAA2B,aAC3C;AACI,QAAM,OAAO,YAAY;AAErB,MAAA;AAEJ,MAAI,YAAY,mBAChB;AACI,UAAM,oBAAoB,YAAY;AAEtC,gBAAY,eAAe;MACvB,KAAK;MACL,kBAAkB;IAAA;AAGtB,gBAAY,aAAa;MACrB,KAAK;MACL,kBAAkB;IAAA;AAGT,iBAAA,KAAK,aAAa,kBAAkB;EAAA,OAGrD;AACgB,gBAAA,eAAe,SAAS,KAAK,cAAc;AACvD,gBAAY,aAAa,KAAK;AAC9B,iBAAa,KAAK;EAAA;AAItB,eAAa,aAAa,IAAI,IAAK,aAAa,IAAI,IAAI;AACxD,cAAY,aAAa;AAEzB,cAAY,kBAAkB,YAAY,eAC7B,aAAa,MAAO,MAAM;AAC3C;AAQgB,SAAA,2BAA2B,WAAsB,YAAoB,aACrF;AACI,MAAI,eAAe,UAAU;AAAY;AACzC,YAAU,aAAa;AAEvB,YAAU,YAAY;AAEtB,QAAM,iBAAiB,UAAU;AAEjC,YAAU,qBAAqB;AAE/B,QAAM,SAAS,UAAU;AAEpB,MAAA,UAAU,CAAC,OAAO,aACvB;AACI,mBAAe,UAAU;AAEzB,cAAU,uBAAuB;MAC7B;MACA,OAAO;IAAA;AAGX,QAAI,cAAc,4BAClB;AAC+B,iCAAA,WAAW,QAAQ,WAAW;IAAA;EAC7D,OAGJ;AACI,kBAAc,UAAU;AAEd,cAAA,uBAAuB,SAAS,cAAc;AAExD,QAAI,cAAc,4BAClB;AAC+B,iCAAA,WAAW,eAAe,WAAW;IAAA;EACpE;AAIA,MAAA,CAAC,UAAU,aACf;AACI,UAAM,WAAW,UAAU;AAC3B,UAAM,SAAS,SAAS;AAExB,aAAS,IAAI,GAAG,IAAI,QAAQ,KAC5B;AACI,iCAA2B,SAAS,CAAC,GAAG,YAAY,WAAW;IAAA;AAGnE,UAAM,cAAc,UAAU;AAC9B,UAAM,aAAa;AAEnB,QAAI,WAAW,gBAAgB,CAAC,YAAY,oBAC5C;AACI,kBAAY,iBAAiB,UAAU;IAAA;EAC3C;AAER;AAEA,SAAS,2BACL,WACA,QACA,aAEJ;AACI,MAAI,cAAc,cAClB;AACI,cAAU,aAAa;MACnB,UAAU;MACV,OAAO;IAAA;AAGP,QAAA,aAAa,UAAU,aAAa,OAAO;AAG/C,iBAAa,aAAa,IAAI,IAAK,aAAa,IAAI,IAAI;AAExD,cAAU,aAAa;AACvB,cAAU,kBAAkB,UAAU,eAAgB,aAAa,MAAO,MAAM;EAAA;AAGpF,MAAI,cAAc,cAClB;AACI,cAAU,iBAAiB,UAAU,mBAAmB,YAAY,OAAO,iBAAiB,UAAU;EAAA;AAG1G,MAAI,cAAc,gBAClB;AACc,cAAA,sBAAsB,UAAU,qBAAqB,OAAO;EAAA;AAG1E,YAAU,eAAe;AAC7B;;;AC/LgB,SAAA,oBAAoB,aAA0B,aAC9D;AACI,QAAM,EAAE,MAAM,MAAM,IAAI,YAAY;AAEpC,MAAI,kBAAkB;AAEtB,WAAS,IAAI,GAAG,IAAI,OAAO,KAC3B;AACU,UAAA,YAAY,KAAK,CAAC;AAMxB,UAAM,aAAa;AACb,UAAA,OAAO,YAAY,WAAW,YAAiC;AAEnD,sBAAA,KAAK,mBAAmB,SAAS;AAEnD,QAAI,iBACJ;AACI;IAAA;EACJ;AAGJ,cAAY,qBAAqB;AAE1B,SAAA;AACX;;;ACrBA,IAAMC,cAAa,IAAI,OAAO;AAQvB,IAAM,oBAAN,MACP;EAaI,YAAY,UACZ;AACI,SAAK,YAAY;EAAA;EAGX,OAAO,EAAE,WAAW,UAAA,GAC9B;AAEI,UAAM,SAAS,UAAU;AACnB,UAAA,oBAAoB,UAAU,YAAY;AAGhD,cAAU,SAAS;AACnB,cAAU,YAAY,oBAAoB;AAE1C,UAAM,WAAW,KAAK;AAGtB,QAAI,yBAAiCA;AAErC,QAAI,WACJ;AACI,+BAAyB,uBAAuB,SAAS,UAAU,YAAY,cAAc;AACnF,gBAAA,YAAY,eAAe,SAAS,SAAS;IAAA;AAI3D,UAAM,cAAe,SAA4B;AAE5C,SAAA,0BAA0B,UAAU,aAAa,IAAI;AAErD,SAAA,oBAAoB,UAAU,WAAW;AAE9C,aAAS,eAAe,MAAM;MAC1B,sBAAsB,YAAY,UAAU,YAAY,iBAAiB,UAAU,YAAY;MAC/F,YAAY,UAAU,YAAY;IAAA,CACrC;AAEmB,wBAAA,UAAU,aAAa,WAAW;AAGtD,QAAI,YAAY,cAChB;AACI,kBAAY,aAAa,UAAU;IAAA;AAIvC,QAAI,WACJ;AACc,gBAAA,YAAY,eAAe,SAAS,sBAAsB;IAAA;AAGxE,cAAU,SAAS;AACnB,cAAU,YAAY,oBAAoB;EAAA;EAGvC,UACP;AACK,SAAK,YAAqB;EAAA;EAGvB,0BAA0B,aAA0B,uBAC5D;AACI,QAAI,YAAY,mBAChB;AAEI,UAAI,CAAC,YAAY;AAAoB;AAEb,8BAAA;IAAA;AAG5B,gBAAY,mCAAmC;AAG/C,aAAS,IAAI,YAAY,oBAAoB,SAAS,GAAG,KAAK,GAAG,KACjE;AACI,WAAK,0BAA0B,YAAY,oBAAoB,CAAC,GAAG,qBAAqB;IAAA;AAG5F,gBAAY,mBAAmB;AAE/B,QAAI,YAAY,mBAChB;AACI,UAAI,YAAY,oBAChB;AAKU,cAAA,SAAS,YAAY,KAAK,eAAe;AAE/C,eAAO,KAAK;AAEZ,cAAM,cAAc,YAAY;AAEhC,YAAI,YAAY,SAChB;AACgB,sBAAA,cAAc,YAAY,OAAO;QAAA;AAGjD,cAAM,WAAW,KAAK;AACtB,cAAM,aAAa,YAAY,eAAe,cAAc,SAAS,KAAK;AAC1E,cAAM,YAAY,YAAY,eAAe,aAAa,SAAS,KAAK;AAExE,oBAAY,UAAU,YAAY;UAC9B,OAAO;UACP,OAAO;UACP;UACA;QAAA;AAGJ,oBAAY,mBAAZ,YAAY,iBAAmB,IAAI,OAAO;AAC9B,oBAAA,eAAe,SAAS,MAAM;AAEtC,YAAA,gBAAgB,YAAY,SAChC;AACI,cAAI,YAAY,mBAChB;AACI,wBAAY,kBAAkB,qBAAqB;UAAA;QACvD;MACJ;IACJ,WAEK,YAAY,SACrB;AACgB,kBAAA,cAAc,YAAY,OAAO;AAC7C,kBAAY,UAAU;IAAA;EAC1B;EAGI,oBAAoB,aAC5B;AACI,UAAM,WAAW,KAAK;AACtB,UAAM,cAAc,SAAS;AAE7B,gBAAY,YAAY,QAAQ;AAEhC,gBAAY,eAAe,cAAc;AAErC,QAAA,CAAC,YAAY,oBACjB;AAEI,0BAAoB,aAAa,WAAW;IAAA,OAGhD;AACc,gBAAA,YAAY,4BAA4B,MAAM,CAAC;IAAA;AAK7D,gCAA4B,WAAW;AAEvC,QAAI,YAAY,oBAChB;AACI,kBAAY,qBAAqB;AAG5B,WAAA,mBAAmB,aAAa,QAAQ;IAAA,OAGjD;AAEI,WAAK,mBAAmB,WAAW;IAAA;AAIvC,gBAAY,4BAA4B,QAAQ;AAGhD,aAAS,YAAY,MAAM,OAAO,YAAY,cAAc;AAGxD,QAAA,YAAY,qBAAqB,CAAC,YAAY;AAAoB;AAEtE,aAAS,IAAI,GAAG,IAAI,YAAY,oBAAoB,QAAQ,KAC5D;AACI,WAAK,oBAAoB,YAAY,oBAAoB,CAAC,CAAC;IAAA;EAC/D;EAGI,mBAAmB,aAC3B;AACI,UAAM,EAAE,MAAM,MAAM,IAAI,YAAY;AAEpC,aAAS,IAAI,GAAG,IAAI,OAAO,KAC3B;AACU,YAAA,YAAY,KAAK,CAAC;AAExB,UAAI,UAAU,eACd;AACI,oBAAY,iBAAiB,SAA0B;MAAA;IAC3D;AAGJ,cAAU,MAAM,KAAK;EAAA;EAUjB,mBAAmB,aAA0B,iBACrD;AAEI,UAAM,OAAO,YAAY;AACzB,UAAM,iBAAiB,YAAY;AAEnC,mBAAe,MAAM;AAGrB,UAAM,WAAY,gBAA6B,cACxC,kBACA,gBAAgC,MAAM;AAC7C,UAAM,cAAc,SAAS;AAGjB,gBAAA,MAAM,WAAW,cAAc;AAC3C,gBAAY,UAAU,WAAW;AACjC,gBAAY,UAAU,WAAW;AAEjC,QAAI,KAAK,kBACT;AACI,WAAK,aAAa;IAAA;AAGjB,SAAA,8BAA8B,gBAAgB,UAAU,IAAI;AAGrD,gBAAA,MAAM,SAAS,cAAc;AAC7B,gBAAA,UAAU,SAAS,cAAc;EAAA;AAErD;AAzPa,kBAGK,YAAY;EACtB,MAAM;IACF,cAAc;IACd,cAAc;IACd,cAAc;EAAA;EAElB,MAAM;AACV;;;ACzBG,IAAM,aAAN,MACP;EAaI,YAAY,UACZ;AACI,SAAK,YAAY;EAAA;EAGd,cAAc,QAAgB,gBACrC;AACU,UAAA,YAAY,KAAK,cAAc,MAAM;AAE3C,QAAI,OAAO;AAAoB,WAAA,uBAAuB,QAAQ,SAAS;AAGvE,SAAK,UAAU,YAAY,MAAM,WAAW,WAAW,cAAc;EAAA;EAGlE,iBAAiB,QACxB;AACU,UAAA,YAAY,KAAK,cAAc,MAAM;AAE3C,QAAI,OAAO;AAAoB,WAAA,uBAAuB,QAAQ,SAAS;AAE7D,cAAA,SAAS,cAAc,SAAS;EAAA;EAGvC,mBAAmB,QAC1B;AACU,UAAA,YAAY,KAAK,cAAc,MAAM;AAEpC,WAAA,CAAC,UAAU,SAAS;MACvB;MACA,OAAO;IAAA;EAAQ;EAIf,uBAAuB,QAAgB,iBAC/C;AACI,oBAAgB,SAAS,OAAO;AAChC,oBAAgB,UAAU,OAAO;EAAA;EAG7B,cAAc,QACtB;AACW,WAAA,OAAO,SAAS,KAAK,UAAU,GAAG,KAAK,KAAK,eAAe,MAAM;EAAA;EAGpE,eAAe,QACvB;AACU,UAAA,kBAAkB,IAAI,gBAAgB;AAE5C,oBAAgB,aAAa;AAE7B,oBAAgB,YAAY,OAAO;AACnC,oBAAgB,UAAU,OAAO;AACjC,oBAAgB,SAAS,OAAO;AAChC,oBAAgB,cAAe,KAAK,UAAU,eAAe,OAAO;AAEpE,WAAO,SAAS,KAAK,UAAU,GAAG,IAAI;AAE/B,WAAA;EAAA;EAGJ,UACP;AACI,SAAK,YAAY;EAAA;AAEzB;AA/Ea,WAGK,YAAY;EACtB,MAAM;IACF,cAAc;IACd,cAAc;IACd,cAAc;EAAA;EAElB,MAAM;AACV;;;ACLG,IAAM,WAAW;AASjB,IAAM,UAAU;;;ACHhB,IAAM,sBAAN,MACP;EAGI,OAAc,OACd;AACe,eAAA,oBAAoB,MAAgC,OAAO;EAAA;EAE1E,OAAc,UACd;EAAA;AAGJ;AAZa,oBAGK,YAA+B,cAAc;AAgBxD,IAAM,mBAAN,MACP;EAaI,YAAY,UACZ;AACI,SAAK,YAAY;EAAA;EAEd,OACP;AACe,eAAA,yBAAyB,KAAK,WAAW,OAAO;EAAA;EAExD,UACP;AACI,SAAK,YAAY;EAAA;AAEzB;AA1Ba,iBAGK,YAAY;EACtB,MAAM;IACF,cAAc;IACd,cAAc;EAAA;EAElB,MAAM;EACN,UAAU;AACd;;;ACrBG,IAAM,eAAN,MAAMC,cACb;EA+BI,YAAY,UAAoB,SAChC;AArBO,SAAA,QAAe,MAAM,MAAM;AAGjB,SAAA,4BAA4E,uBAAA,OAAO,IAAI;AAKhG,SAAA,iBAAiD,uBAAA,OAAO,IAAI;AAchE,SAAK,WAAW;AAChB,SAAK,WAAW;AAEX,SAAA,SAAS,OAAO,IAAI;EAAA;EAV7B,OAAc,WAAW,MACzB;AACI,WAAO,IAAI,KAAK,mBAAmB,IAA4C,EAAE;EAAA;EAW9E,WAAW,gBAClB;AACI,QAAI,WAAW,KAAK,0BAA0B,eAAe,GAAG;AAEhE,QAAI,CAAC,UACL;AACI,iBAAW,KAAK,0BAA0B,eAAe,GAAG,IAAI,uBAAO,OAAO,IAAI;AAClF,eAAS,YAAT,SAAS,UAAY,IAAI,eAAe;QACpC,aAAa,KAAK,SAAS,OAAO;MAAA,CACrC;IAAA;AAGL,SAAK,iBAAiB;AAEjB,SAAA,eAAe,KAAK,eAAe;AAE7B,eAAA,KAAK,KAAK,gBACrB;AACS,WAAA,eAAe,CAAC,EAAE,MAAM;IAAA;EACjC;EAGG,WAAW,iBAAmC,gBACrD;AACI,QAAI,KAAK,aAAa,SAAS,gBAAgB,aAC/C;AACS,WAAA,aAAa,MAAM,cAAc;AAEtC,UAAI,QAAQ,KAAK,eAAe,gBAAgB,WAAW;AAE3D,UAAI,CAAC,OACL;AACY,gBAAA,KAAK,eAAe,gBAAgB,WAAW,IACjDA,cAAY,WAAW,gBAAgB,WAAW;AACxD,cAAM,MAAM;MAAA;AAGhB,WAAK,eAAe;IAAA;AAGnB,SAAA,aAAa,IAAI,eAAe;EAAA;EAGlC,MAAM,gBACb;AACS,SAAA,aAAa,MAAM,cAAc;EAAA;EAGnC,SAAS,gBAChB;AACS,SAAA,aAAa,MAAM,cAAc;AAEtC,UAAM,UAAU,KAAK;AAErB,eAAW,KAAK,SAChB;AACU,YAAA,QAAQ,QAAQ,CAAyB;AAC/C,YAAM,WAAW,MAAM;AAEvB,eAAS,YAAY,gBAAgB,MAAM,aAAa,MAAM,WAAW,IAAI;AAEpE,eAAA,QAAQ,CAAC,EAAE,gBAAgB,MAAM,gBAAgB,aAAa,MAAM,eAAe,KAAK;IAAA;EACrG;EAGG,OAAO,gBACd;AACI,UAAM,WAAW,KAAK,0BAA0B,eAAe,GAAG;AAElE,eAAW,KAAK,UAChB;AACU,YAAA,UAAU,SAAS,CAA0B;AACnD,YAAM,WAAW,QAAQ;AAEzB,UAAI,QAAQ,OACZ;AACI,gBAAQ,QAAQ;AAEhB,iBAAS,QAAQ,CAAC,EAAE,OAAO,QAAQ,gBAAgB,CAAC;MAAA;IACxD;EACJ;EAGG,QAAQ,OACf;AACQ,QAAA,MAAM,WAAW,cACrB;AACI,YAAM,UAAU,MAAM;AACtB,YAAM,WAAW,QAAQ;AACzB,YAAM,SAAS,QAAQ;AAEvB,WAAK,SAAS,MAAM,MAAM,UAAU,MAAM;IAAA;AAGzC,SAAA,SAAS,QAAQ,MAAM,KAAK;EAAA;EAG9B,UACP;AACI,SAAK,QAAQ;AACb,SAAK,WAAW;AAEhB,SAAK,WAAW;AAEL,eAAA,KAAK,KAAK,gBACrB;AACS,WAAA,eAAe,CAAC,EAAE,QAAQ;IAAA;AAGnC,SAAK,iBAAiB;EAAA;AAE9B;AAvJa,aAGK,YAAY;EACtB,MAAM;IACF,cAAc;IACd,cAAc;IACd,cAAc;EAAA;EAElB,MAAM;AACV;AAVS,aAyBK,qBAA+D,uBAAA,OAAO,IAAI;AAzBrF,IAAM,cAAN;AAyJP,WAAW,YAAY,cAAc,SAAS,YAAY,kBAAkB;AAE5E,WAAW,IAAI,cAAc;;;ACvL7B,IAAI,WAAW;;;ACAf,IAAI,SAAS;;;ACAb,IAAI,SAAS;;;ACwBN,IAAM,aAAN,cAAyB,OAChC;EAII,YAAY,SACZ;AACI,UAAM,EAAE,QAAQ,GAAG,KAAA,IAAS;AAE5B,UAAM,gBAAgB,IAAI,cAAc,OAAO,OAAO;AAEhD,UAAA,iBAAiB,IAAI,aAAa;MACpC,eAAe,EAAE,OAAO,IAAI,OAAO,GAAG,MAAM,cAAc;MAC1D,YAAY,EAAE,OAAO,cAAc,aAAa,MAAM,YAAY;MAClE,QAAQ,EAAE,OAAO,GAAG,MAAM,MAAM;MAChC,UAAU,EAAE,OAAO,QAAQ,UAAU,IAAI,GAAG,MAAM,MAAM;IAAA,CAC3D;AAEK,UAAA,aAAa,WAAW,KAAK;MAC/B,QAAQ;QACJ;QACA,YAAY;MAAA;MAEhB,UAAU;QACN;QACA,YAAY;MAAA;IAChB,CACH;AAEK,UAAA,YAAY,UAAU,KAAK;MAC7B;MACA;MACA,MAAM;IAAA,CACT;AAEK,UAAA;MACF,GAAG;MACH;MACA;MACA,WAAW;QACP;QACA,cAAc,OAAO,QAAQ;MAAA;IACjC,CACH;AAED,SAAK,SAAS;AAEd,SAAK,iBAAiB;EAAA;EAG1B,IAAI,QAAQ,OACZ;AACI,SAAK,UAAU,eAAe,SAAS,WAAW,QAAQ,IAAI;EAAA;EAGlE,IAAI,UACJ;AACI,WAAO,KAAK,UAAU,eAAe,SAAS,aAAa;EAAA;EAGxD,MACH,eACA,OACA,QACA,WAEJ;AAES,SAAA,eAAe,UAAU,KAAK,OAAO;AAE5B,kBAAA;MACV,KAAK,UAAU,eAAe,SAAS;MACvC,KAAK;IAAA,EACP,QAAQ,KAAK,eAAe,QAAQ;AAEtC,SAAK,UAAU,eAAe,KAAK,OAAO,QAAQ;AAElD,kBAAc,YAAY,MAAM,OAAO,QAAQ,SAAS;EAAA;AAEhE;;;AChFA,IAAM,aAAa,IAAI,OAAO;AAG9B,IAAM,kBAAN,cAA8B,aAC9B;EACI,cACA;AACU,UAAA;AAED,SAAA,UAAU,CAAC,IAAI,WAAW;MAC3B,QAAQ,IAAI,OAAO,QAAQ,KAAK;MAChC,SAAS;MACT,YAAY;MACZ,WAAW;IAAA,CACd,CAAC;EAAA;EAGN,IAAI,SACJ;AACY,WAAA,KAAK,QAAQ,CAAC,EAAiB;EAAA;EAG3C,IAAI,OAAO,OACX;AACK,SAAK,QAAQ,CAAC,EAAiB,SAAS;EAAA;EAG7C,IAAI,UACJ;AACY,WAAA,KAAK,QAAQ,CAAC,EAAiB;EAAA;EAG3C,IAAI,QAAQ,OACZ;AACK,SAAK,QAAQ,CAAC,EAAiB,UAAU;EAAA;AAIlD;AAuBO,IAAM,gBAAN,MACP;EAcI,YAAY,UACZ;AAHA,SAAQ,mBAAoC,CAAA;AAIxC,SAAK,YAAY;EAAA;EAGd,KAAK,MAAc,iBAA4B,gBACtD;AACI,UAAM,WAAW,KAAK;AAEb,aAAA,YAAY,MAAM,MAAM,cAAc;AAE/C,mBAAe,IAAI;MACf,cAAc;MACd,QAAQ;MACR;MACA,SAAS,gBAAgB,aAAa;MACtC,WAAW;MACX;IAAA,CACqB;AAExB,SAAmB,UAAU,gBAAgB,aAAa;AAE3D,QAAK,KAAmB,qBACxB;AACI,YAAM,gBAAiB,KAAmB;AAE1C,oBAAc,iBAAiB;AAEjB,oBAAA;QACV;QACA;QACA;MAAA;AAGJ,oBAAc,iBAAiB;IAAA;AAG1B,aAAA,YAAY,MAAM,MAAM,cAAc;AAE/C,mBAAe,IAAI;MACf,cAAc;MACd,QAAQ;MACR;MACA;MACA,SAAS,gBAAgB,aAAa;MACtC,WAAW;IAAA,CACU;EAAA;EAGtB,IAAI,MAAc,kBAA6B,gBACtD;AACI,UAAM,WAAW,KAAK;AAEb,aAAA,YAAY,MAAM,MAAM,cAAc;AAE/C,mBAAe,IAAI;MACf,cAAc;MACd,QAAQ;MACR;MACA,SAAS,iBAAiB,aAAa;MACvC,WAAW;IAAA,CACU;EAAA;EAGtB,QAAQ,aACf;AACI,UAAM,WAAW,KAAK;AAChB,UAAA,aAAa,YAAY,KAAK;AAEhC,QAAA,YAAY,WAAW,iBAC3B;AACU,YAAA,eAAe,QAAQ,IAAI,eAAe;AAEhD,mBAAa,UAAU,YAAY;AAEnC,UAAI,YACJ;AACgB,oBAAA,KAAK,KAAK,aAAa;AAEnC,cAAM,SAAS,gBAAgB,YAAY,KAAK,MAAM,MAAM,UAAU;AAE1D,oBAAA,KAAK,KAAK,aAAa;AAEnC,eAAO,KAAK;AAEZ,cAAM,qBAAqB,SAAS,aAAa,aAAa,aAAa;AAC3E,cAAM,gBAAgB,YAAY;UAC9B,OAAO;UACP,OAAO;UACP,mBAAmB;UACnB,mBAAmB;QAAA;AAGd,iBAAA,aAAa,KAAK,eAAe,IAAI;AAE9C,iBAAS,eAAe,KAAK;UACzB,QAAQ;UACR,YAAY;QAAA,CACf;AAED,cAAM,SAAS,aAAa;AAE5B,eAAO,UAAU;AAEV,eAAA,eAAe,KAAK,OAAO;AAC3B,eAAA,eAAe,KAAK,OAAO;AAElC,aAAK,iBAAiB,KAAK;UACvB;UACA,iBAAiB,YAAY;UAC7B;QAAA,CACH;MAAA,OAGL;AACiB,qBAAA,SAAS,YAAY,KAAK;AAEvC,aAAK,iBAAiB,KAAK;UACvB;UACA,iBAAiB,YAAY;QAAA,CAChC;MAAA;IACL,WAEK,YAAY,WAAW,eAChC;AACI,YAAM,WAAW,KAAK,iBAAiB,KAAK,iBAAiB,SAAS,CAAC;AAEvE,UAAI,YACJ;AAEQ,YAAA,SAAS,SAAS,aAAa,OACnC;AACI,mBAAS,aAAa,iBAAiB;QAAA;AAG3C,iBAAS,aAAa,IAAI;AAC1B,iBAAS,eAAe,IAAI;MAAA;AAGhC,eAAS,OAAO,KAAK;QACjB,cAAc;QACd,QAAQ;QACR,WAAW,SAAS;QACpB,cAAc,SAAS;QACvB,WAAW;MAAA,CACd;IAAA,WAEI,YAAY,WAAW,cAChC;AACI,eAAS,OAAO,IAAI;AAEd,YAAA,WAAW,KAAK,iBAAiB,IAAI;AAE3C,UAAI,YACJ;AACgB,oBAAA,cAAc,SAAS,aAAa;MAAA;AAG5C,cAAA,OAAO,SAAS,YAAY;IAAA;EACxC;EAGG,UACP;AACI,SAAK,YAAY;AACjB,SAAK,mBAAmB;EAAA;AAEhC;AAtLa,cAGK,YAAY;EACtB,MAAM;IACF,cAAc;IACd,cAAc;IACd,cAAc;EAAA;EAElB,MAAM;AACV;;;AC5EG,IAAM,gBAAN,MACP;EAgBI,YAAY,UACZ;AALA,SAAQ,cAAwB,CAAA;AAChC,SAAQ,mBAAmB;AAC3B,SAAQ,gBAAgB;AAIpB,SAAK,YAAY;EAAA;EAGd,aACP;AACS,SAAA,YAAY,CAAC,IAAI;AACtB,SAAK,mBAAmB;AACxB,SAAK,gBAAgB;EAAA;EAGlB,KAAK,MAAc,YAAuB,gBACjD;AACI,UAAM,WAAW,KAAK;AAEb,aAAA,YAAY,MAAM,MAAM,cAAc;AAE/C,UAAM,aAAa,KAAK;AAEb,eAAA,KAAK,gBAAgB,IAAI,WAAW,KAAK,mBAAmB,CAAC,IAAK,KAAmB;AAEhG,UAAM,eAAe,KAAK,YAAY,KAAK,gBAAgB;AAEvD,QAAA,iBAAiB,KAAK,eAC1B;AACI,WAAK,gBAAgB;AACrB,qBAAe,IAAI;QACf,cAAc;QACd,WAAW;QACX,WAAW;MAAA,CACU;IAAA;AAGxB,SAAA;EAAA;EAGF,IAAI,OAAe,YAAuB,gBACjD;AACI,UAAM,WAAW,KAAK;AAEb,aAAA,YAAY,MAAM,MAAM,cAAc;AAE/C,UAAM,aAAa,KAAK;AAEnB,SAAA;AAEL,UAAM,eAAe,WAAW,KAAK,mBAAmB,CAAC;AAErD,QAAA,iBAAiB,KAAK,eAC1B;AACI,WAAK,gBAAgB;AAErB,qBAAe,IAAI;QACf,cAAc;QACd,WAAW;QACX,WAAW;MAAA,CACU;IAAA;EAC7B;EAGG,QAAQ,aACf;AACI,UAAM,WAAW,KAAK;AAEb,aAAA,UAAU,QAAQ,YAAY,SAAS;EAAA;EAG7C,UACP;AACI,SAAK,cAAc;EAAA;AAE3B;AAzFa,cAGK,YAAY;EACtB,MAAM;IACF,cAAc;IACd,cAAc;IACd,cAAc;EAAA;EAElB,MAAM;AACV;;;ACFG,IAAM,kBAAN,MACP;EAoBI,YAAY,UACZ;AARA,SAAQ,iBAAyC,CAAA;AAEzC,SAAA,YAAA,oBAAgB,QAGrB;AAIC,SAAK,YAAY;EAAA;EAGd,KAAK,MAAc,YAAuB,gBACjD;AArDJ,QAAA;AAsDQ,UAAM,SAAS;AAEf,UAAM,WAAW,KAAK;AAEb,aAAA,YAAY,MAAM,MAAM,cAAc;AAE/C,aAAS,YAAY,UAAU,aAAa,OAAO,MAAoB,QAAQ,cAAc;AAE7F,mBAAe,IAAI;MACf,cAAc;MACd,QAAQ;MACR;MACA,SAAS,WAAW,aAAa;MACjC,WAAW;IAAA,CACY;AAE3B,UAAM,gBAAgB,OAAO;AAE7B,kBAAc,iBAAiB;AAE/B,QAAI,CAAC,KAAK,UAAU,IAAI,MAAM,GAC9B;AACS,WAAA,UAAU,IAAI,QAAQ;QACvB,mBAAmB;QACnB,oBAAoB;MAAA,CACvB;IAAA;AAGL,UAAM,WAAW,KAAK,UAAU,IAAI,MAAM;AAE1C,aAAS,oBAAoB,eAAe;AAE9B,kBAAA;MACV;MACA;MACA;IAAA;AAGJ,kBAAc,iBAAiB;AAEtB,aAAA,YAAY,MAAM,MAAM,cAAc;AAE/C,mBAAe,IAAI;MACf,cAAc;MACd,QAAQ;MACR;MACA,SAAS,WAAW,aAAa;MACjC,WAAW;IAAA,CACY;AAE3B,UAAM,qBAAqB,eAAe,kBAAkB,SAAS,oBAAoB;AAEzF,aAAS,qBAAqB;AAExB,UAAA,kBAAkB,SAAS,aAAa,aAAa;AAE3D,KAAA,KAAA,KAAK,gBAAL,eAAyC,MAAA,GAAA,eAAA,IAAA;EAAA;EAGtC,IAAI,MAAc,YAAuB,gBAChD;AACI,UAAM,SAAS;AAEf,UAAM,WAAW,KAAK;AAGb,aAAA,YAAY,MAAM,MAAM,cAAc;AAC/C,aAAS,YAAY,UAAU,aAAa,OAAO,MAAoB,QAAQ,cAAc;AAE7F,mBAAe,IAAI;MACf,cAAc;MACd,QAAQ;MACR,SAAS,WAAW,aAAa;MACjC,WAAW;IAAA,CACY;AAE3B,UAAM,WAAW,KAAK,UAAU,IAAI,IAAmB;AAEvD,aAAS,IAAI,GAAG,IAAI,SAAS,oBAAoB,KACjD;AAEI,qBAAe,aAAa,eAAe,iBAAiB,IAAI,eAAe,aAAa,SAAS,mBAAmB;IAAA;AAG5H,mBAAe,IAAI;MACf,cAAc;MACd,QAAQ;MACR,WAAW;IAAA,CACd;EAAA;EAGE,QAAQ,aACf;AAlJJ,QAAA;AAmJQ,UAAM,WAAW,KAAK;AAChB,UAAA,kBAAkB,SAAS,aAAa,aAAa;AAEvD,QAAA,kBAAiB,KAAK,KAAA,gBAAL,eAAyC,MAAA,GAAA,eAAA,IAAA;AAE1D,QAAA,YAAY,WAAW,iBAC3B;AAGI,eAAS,aAAa,mBAAmB;AAEzC,eAAS,QAAQ,eAAe,cAAc,oBAAoB,cAAc;AAEhF;AAES,eAAA,UAAU,QAAQ,CAAC;IAAA,WAEvB,YAAY,WAAW,eAChC;AACI,UAAI,YAAY,SAChB;AACI,iBAAS,QAAQ,eAAe,cAAc,qBAAqB,cAAc;MAAA,OAGrF;AACI,iBAAS,QAAQ,eAAe,cAAc,aAAa,cAAc;MAAA;AAGpE,eAAA,UAAU,QAAQ,EAAG;IAAA,WAEzB,YAAY,WAAW,gBAChC;AACa,eAAA,UAAU,QAAQ,CAAC;AAE5B,UAAI,mBAAmB,GACvB;AACI,iBAAS,QAAQ,eAAe,cAAc,uBAAuB,cAAc;MAAA,OAGvF;AACI,iBAAS,aAAa,MAAM,MAAM,MAAM,OAAO;AAC/C,iBAAS,QAAQ,eAAe,cAAc,UAAU,cAAc;MAAA;AAG1E;IAAA,WAEK,YAAY,WAAW,cAChC;AACI,UAAI,YAAY,SAChB;AACI,iBAAS,QAAQ,eAAe,cAAc,qBAAqB,cAAc;MAAA,OAGrF;AACI,iBAAS,QAAQ,eAAe,cAAc,aAAa,cAAc;MAAA;AAGpE,eAAA,UAAU,QAAQ,EAAG;IAAA;AAG7B,SAAA,eAAe,eAAe,IAAI;EAAA;EAGpC,UACP;AACI,SAAK,YAAY;AACjB,SAAK,iBAAiB;AACtB,SAAK,YAAY;EAAA;AAEzB;AA9La,gBAEK,YAAY;EACtB,MAAM;IACF,cAAc;IACd,cAAc;IACd,cAAc;EAAA;EAElB,MAAM;AACV;;;ACKG,IAAM,oBAAN,MAAMC,mBACb;EA0CI,cACA;AACI,SAAK,oBAAoB;AAEpB,SAAA,mBAAmB,IAAI,MAAM,CAAQ;AAE1C,SAAK,QAAQ,KAAK;AAClB,SAAK,QAAQ;EAAA;;;;;EAOV,KAAK,SACZ;AACI,cAAU,EAAE,GAAGA,mBAAiB,gBAAgB,GAAG,QAAQ;AAE3D,SAAK,oBAAoB,QAAQ;AACjC,SAAK,QAAQ,QAAQ,cAAc,QAAQ,mBAAmB,KAAK;AACnE,SAAK,QAAQ,QAAQ;AAEhB,SAAA,iBAAiB,SAAS,QAAQ,eAAe;EAAA;;EAI1D,IAAI,QACJ;AACI,WAAO,KAAK;EAAA;EAGhB,IAAI,MAAM,OACV;AAGI,UAAM,WAAW,MAAM,OAAO,SAAS,KAAK;AAE5C,QAAI,SAAS,QAAQ,KAAK,KAAK,iBAAiB,UAAU,GAC1D;AACI;QACI;MAAA;IAEJ;AAGC,SAAA,iBAAiB,SAAS,KAAK;EAAA;;EAIxC,IAAI,QACJ;AACI,WAAO,KAAK,iBAAiB;EAAA;EAGjC,IAAI,MAAM,OACV;AACS,SAAA,iBAAiB,SAAS,KAAK;EAAA;;EAIxC,IAAI,YACJ;AACW,WAAA,KAAK,iBAAiB,QAAQ;EAAA;;;;;EAOlC,UACP;EAAA;AAGJ;AApHa,kBAGK,YAAY;EACtB,MAAM;IACF,cAAc;IACd,cAAc;IACd,cAAc;EAAA;EAElB,MAAM;EACN,UAAU;AACd;AAXS,kBAcK,iBAA0C;;;;;EAKpD,iBAAiB;;;;;EAKjB,iBAAiB;;;;;EAKjB,mBAAmB;AACvB;AA9BG,IAAM,mBAAN;;;ACnBP,IAAM,qBAA8E,CAAA;AAEpF,WAAW,OAAO,cAAc,WAAW,CAAC,UAC5C;AACQ,MAAA,CAAC,MAAM,MACX;AACU,UAAA,IAAI,MAAM,+CAA+C;EAAA;AAEhD,qBAAA,MAAM,IAAmB,IAAI,MAAM;AAC1D,GAAG,CAAC,UACJ;AACW,SAAA,mBAAmB,MAAM,IAAmB;AACvD,CAAC;AAeM,IAAM,gBAAN,MACP;EAoBI,YAAY,UACZ;AALA,SAAQ,cAAc;AAEd,SAAA,cAAiE,uBAAA,OAAO,IAAI;AAIhF,SAAK,YAAY;AACjB,SAAK,UAAU,QAAQ,UAAU,IAAI,IAAI;EAAA;EAGtC,YACP;AAGI,SAAK,mBAAmB;AACxB,SAAK,cAAc;EAAA;;;;;;;EAShB,aAAa,YAAwB,WAAwB,gBACpE;AACQ,QAAA,KAAK,qBAAqB,WAC9B;AACI,UAAI,KAAK;AAAkB,aAAA,gBAAgB,KAAK,UAAU;AAE1D;IAAA;AAGJ,SAAK,mBAAmB;AAExB,QAAI,KAAK,aACT;AACI,WAAK,sBAAsB,cAAc;IAAA;AAG7C,SAAK,cAAc,CAAC,CAAC,mBAAmB,SAAS;AAEjD,QAAI,KAAK,aACT;AACI,WAAK,wBAAwB,cAAc;AAEtC,WAAA,gBAAgB,KAAK,UAAU;IAAA;EACxC;EAGI,wBAAwB,gBAChC;AACI,SAAK,UAAU,YAAY,MAAM,MAAM,cAAc;AAErD,UAAM,YAAY,KAAK;AAEnB,QAAA,CAAC,mBAAmB,SAA4C,GACpE;AAES,WAAA,gCAAgC,SAAS,mEACoB;AAGlE;IAAA;AAGA,QAAA,eAAe,KAAK,YAAY,SAAS;AAG7C,QAAI,CAAC,cACL;AACI,qBAAe,KAAK,YAAY,SAAS,IAAI,IAAI,aAAa;AAE9D,mBAAa,UAAU,CAAC,IAAI,mBAAmB,SAA4C,EAAA,CAAG;IAAA;AAGlG,UAAM,cAAiC;MACnC,cAAc;MACd,QAAQ;MACR,aAAa,CAAA;MACb;MACA,WAAW;IAAA;AAGf,SAAK,kBAAkB,YAAY;AACnC,mBAAe,IAAI,WAAW;EAAA;EAG1B,sBAAsB,gBAC9B;AACI,SAAK,kBAAkB;AACvB,SAAK,UAAU,YAAY,MAAM,MAAM,cAAc;AAErD,mBAAe,IAAI;MACf,cAAc;MACd,QAAQ;MACR,WAAW;IAAA,CACd;EAAA;;;;;EAOE,aACP;AACI,SAAK,cAAc;EAAA;;;;;;;EAShB,SAAS,gBAChB;AACI,QAAI,KAAK,aACT;AACI,WAAK,sBAAsB,cAAc;IAAA;EAC7C;;EAIG,UACP;AACI,SAAK,YAAY;AACjB,SAAK,kBAAkB;AAEZ,eAAA,KAAK,KAAK,aACrB;AACS,WAAA,YAAY,CAAgB,EAAE,QAAQ;IAAA;AAG/C,SAAK,cAAc;EAAA;AAE3B;AAzJa,cAGK,YAAY;EACtB,MAAM;IACF,cAAc;IACd,cAAc;IACd,cAAc;EAAA;EAElB,MAAM;AACV;;;AC9CJ,IAAM,aAAa;EACf,KAAK;EACL,KAAK;EACL,MAAM;AACV;AA4ZO,IAAM,iBAAN,MAAMC,gBACb;;EA8BI,YAAY,UACZ;AACI,SAAK,YAAY;EAAA;EAGb,kBACJ,SACA,WAAuB,CAAA,GAE3B;AACQ,QAAA,mBAAmB,aAAa,mBAAmB,SACvD;AACW,aAAA;QACH,QAAQ;QACR,GAAG;MAAA;IACP;AAGG,WAAA;MACH,GAAG;MACH,GAAG;IAAA;EACP;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAkCJ,MAAa,MAAM,SACnB;AACU,UAAA,QAAQ,IAAI,MAAM;AAExB,UAAM,MAAM,MAAM,KAAK,OAAO,OAAO;AAE9B,WAAA;EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAmCX,MAAa,OAAO,SACpB;AACI,cAAU,KAAK;MACX;MACAA,gBAAc;IAAA;AAGZ,UAAA,EAAE,QAAQ,QAAA,IAAY;AAEtB,UAAA,SAAS,KAAK,OAAO,OAAO;AAE9B,QAAA,OAAO,WAAW,QACtB;AACI,aAAO,IAAI,QAAgB,CAAC,SAAS,WACrC;AACW,eAAA,OAAQ,CAAC,SAChB;AACI,cAAI,CAAC,MACL;AACW,mBAAA,IAAI,MAAM,wBAAwB,CAAC;AAE1C;UAAA;AAGE,gBAAA,SAAS,IAAI,WAAW;AAE9B,iBAAO,SAAS,MAAM,QAAQ,OAAO,MAAgB;AACrD,iBAAO,UAAU;AACjB,iBAAO,cAAc,IAAI;QAAA,GAC1B,WAAW,MAAM,GAAG,OAAO;MAAA,CACjC;IAAA;AAED,QAAA,OAAO,cAAc,QACzB;AACI,aAAO,OAAO,UAAU,WAAW,MAAM,GAAG,OAAO;IAAA;AAEnD,QAAA,OAAO,kBAAkB,QAC7B;AACU,YAAA,OAAO,MAAM,OAAO,cAAc,EAAE,MAAM,WAAW,MAAM,GAAG,QAAA,CAAS;AAE7E,aAAO,IAAI,QAAgB,CAAC,SAAS,WACrC;AACU,cAAA,SAAS,IAAI,WAAW;AAE9B,eAAO,SAAS,MAAM,QAAQ,OAAO,MAAgB;AACrD,eAAO,UAAU;AACjB,eAAO,cAAc,IAAI;MAAA,CAC5B;IAAA;AAGC,UAAA,IAAI,MAAM,yGACkC;EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA2C/C,OAAO,SACd;AACc,cAAA,KAAK,kBAAkB,OAAO;AAExC,UAAM,SAAS,QAAQ;AAEvB,UAAM,WAAW,KAAK;AAEtB,QAAI,kBAAkB,SACtB;AACW,aAAA,SAAS,QAAQ,eAAe,MAAM;IAAA;AAGjD,UAAM,UAAU,SAAS,iBAAiB,gBAAgB,OAAiC;AAE3F,UAAM,SAAS,SAAS,QAAQ,eAAe,OAAO;AAEtD,YAAQ,QAAQ,IAAI;AAEb,WAAA;EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAiCJ,OAAO,SACd;AACc,cAAA,KAAK,kBAAkB,OAAO;AAExC,UAAM,SAAS,QAAQ;AAEvB,UAAM,WAAW,KAAK;AACtB,UAAM,UAAU,kBAAkB,UAC5B,SACA,SAAS,iBAAiB,gBAAgB,OAAiC;AAEjF,UAAM,YAAY,SAAS,QAAQ,UAAU,OAAO;AAEpD,QAAI,kBAAkB,WACtB;AAEI,cAAQ,QAAQ,IAAI;IAAA;AAGjB,WAAA;EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA8CJ,QAAQ,SACf;AACc,cAAA,KAAK,kBAAkB,OAAO;AAExC,QAAI,QAAQ,kBAAkB;AAAS,aAAO,QAAQ;AAEtD,WAAO,KAAK,UAAU,iBAAiB,gBAAgB,OAAiC;EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA+CrF,SAAS,SAChB;AACc,cAAA,KAAK,kBAA0C,OAAO;AAE1D,UAAA,SAAS,KAAK,OAAO,OAAO;AAE5B,UAAA,OAAO,SAAS,cAAc,GAAG;AAElC,SAAA,WAAW,QAAQ,YAAY;AAC/B,SAAA,OAAO,OAAO,UAAU,WAAW;AAC/B,aAAA,KAAK,YAAY,IAAI;AAC9B,SAAK,MAAM;AACF,aAAA,KAAK,YAAY,IAAI;EAAA;;;;;;;;;;;;;;;;;EAmB3B,IAAI,SACX;AACU,UAAA,QAAQ,QAAQ,SAAS;AAErB,cAAA,KAAK,kBAAkB,OAAO;AAElC,UAAA,SAAS,KAAK,OAAO,OAAO;AAE5B,UAAA,SAAS,OAAO,UAAU;AAGhC,YAAQ,IAAI,kBAAkB,OAAO,KAAK,MAAM,OAAO,MAAM,IAAI;AAEjE,UAAM,QAAQ;MACV;MACA,YAAY,KAAK,MAAM,GAAG;MAC1B,mBAAmB,MAAM;MACzB;IAAA,EACF,KAAK,GAAG;AAGF,YAAA,IAAI,OAAO,KAAK;EAAA;EAGrB,UACP;AACI,SAAK,YAAY;EAAA;AAEzB;AAlca,eAGK,YAAY;EACtB,MAAM;IACF,cAAc;IACd,cAAc;EAAA;EAElB,MAAM;AACV;AATS,eAuBK,sBAAoC;EAC9C,QAAQ;EACR,SAAS;AACb;AA1BG,IAAM,gBAAN;;;ACjaA,IAAM,gBAAN,MAAM,uBAAsB,QACnC;EACI,OAAc,OAAO,SACrB;AACI,WAAO,IAAI,eAAc;MACrB,QAAQ,IAAI,cAAc,OAAO;IAAA,CACpC;EAAA;;;;;;;;EAUE,OAAO,OAAe,QAAgB,YAC7C;AACI,SAAK,OAAO,OAAO,OAAO,QAAQ,UAAU;AAErC,WAAA;EAAA;AAEf;;;ACsHA,IAAM,WAAW,IAAI,UAAU;AAC/B,IAAMC,cAAa,IAAI,OAAO;AAC9B,IAAM,UAAuB,CAAC,GAAG,GAAG,GAAG,CAAC;AAgEjC,IAAM,wBAAN,MACP;EAYI,YAAY,UACZ;AACI,SAAK,YAAY;EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAuCd,gBAAgB,SACvB;AACI,QAAI,mBAAmB,WACvB;AACc,gBAAA;QACN,QAAQ;QACR,OAAO;QACP,sBAAsB,CAAA;QACtB,YAAY;MAAA;IAChB;AAGJ,UAAM,aAAa,QAAQ,cAAc,KAAK,UAAU;AACxD,UAAM,YAAY,QAAQ,aAAa,KAAK,UAAU,KAAK;AAE3D,UAAM,YAAY,QAAQ;AAE1B,QAAI,aAAa,QAAQ;AAEzB,QAAI,YACJ;AACI,YAAM,cAAc,MAAM,QAAQ,UAAU,KAAK,WAAW,WAAW;AAEvE,mBAAa,cAAc,aAAa,MAAM,OAAO,SAAS,UAAU,EAAE,QAAQ;IAAA,OAGtF;AACiB,mBAAA;IAAA;AAGX,UAAA,SAAS,QAAQ,OAAO,OAAO,QAAQ,KACtC,eAAe,WAAWA,WAAU,EAAE;AAE7C,WAAO,QAAQ,KAAK,IAAI,OAAO,OAAO,IAAI,UAAU,IAAI;AACxD,WAAO,SAAS,KAAK,IAAI,OAAO,QAAQ,IAAI,UAAU,IAAI;AAEpD,UAAA,SAAS,cAAc,OAAO;MAChC,GAAG,QAAQ;MACX,OAAO,OAAO;MACd,QAAQ,OAAO;MACf;MACA;IAAA,CACH;AAEK,UAAA,YAAY,OAAO,OAAO,UAAU,CAAC,OAAO,GAAG,CAAC,OAAO,CAAC;AAE9D,SAAK,UAAU,OAAO;MAClB;MACA;MACA;MACA;IAAA,CACH;AAED,WAAO,OAAO,cAAc;AAErB,WAAA;EAAA;EAGJ,UACP;AACK,SAAK,YAAqB;EAAA;AAEnC;AApHa,sBAGK,YAAY;EACtB,MAAM;IACF,cAAc;IACd,cAAc;EAAA;EAElB,MAAM;AACV;;;ACzJG,IAAM,sBAAN,MACP;EAwBI,YAAY,UACZ;AAZA,SAAQ,cAAc;AACtB,SAAQ,0BAA+C,CAAA;AAEvD,SAAiB,gBAAsC,CAAA;AACvD,SAAiB,kBAAwC,CAAA;AAEzD,SAAiB,iBAA8B,CAAA;AAC/C,SAAiB,oBAAiC,CAAA;AAM9C,SAAK,YAAY;EAAA;EAGd,QACP;AACI,SAAK,cAAc;AAEnB,aAAS,IAAI,GAAG,IAAI,KAAK,gBAAgB,QAAQ,KACjD;AACI,WAAK,cAAc,KAAK,KAAK,gBAAgB,CAAC,CAAC;IAAA;AAGnD,aAAS,IAAI,GAAG,IAAI,KAAK,kBAAkB,QAAQ,KACnD;AACI,WAAK,eAAe,KAAK,KAAK,kBAAkB,CAAC,CAAC;IAAA;AAGtD,SAAK,gBAAgB,SAAS;AAC9B,SAAK,kBAAkB,SAAS;EAAA;EAG7B,MAAM,SACb;AACI,SAAK,MAAM;AAEX,SAAK,KAAK,OAAO;EAAA;EAGd,KAAK;IACR;IACA;IACA;IACA;IACA;EAAA,GAEJ;AACU,UAAA,eAAe,KAAK,UAAU,aAAa;AAE3C,UAAA,2BAA2B,KAAK,cAAc,KAAK,wBAAwB,KAAK,cAAc,CAAC,IAAI;MACrG,gBAAgB;MAChB,sBAAsB,IAAI,OAAO;MACjC,YAAY;MACZ,QAAQ,IAAI,MAAM;IAAA;AAGtB,UAAM,oBAAuC;MACzC,kBAAkB,oBAAoB,KAAK,UAAU,aAAa;MAClE,YAAY,QAAQ,aAAa;MACjC,sBAAsB,wBAAwB,yBAAyB;MACvE,YAAY,cAAc,yBAAyB;MACnD,QAAQ,UAAU,yBAAyB;MAC3C,WAAW;IAAA;AAGf,UAAM,eAAe,KAAK,cAAc,IAAI,KAAK,KAAK,gBAAgB;AAEjE,SAAA,gBAAgB,KAAK,YAAY;AAEtC,UAAM,WAAW,aAAa;AAE9B,aAAS,oBAAoB,kBAAkB;AAE/C,aAAS,cAAc,kBAAkB;AAEhC,aAAA,sBAAsB,SAAS,kBAAkB,oBAAoB;AAErE,aAAA,sBAAsB,MAAM,kBAAkB,OAAO;AACrD,aAAA,sBAAsB,MAAM,kBAAkB,OAAO;AAE9D;MACI,kBAAkB;MAClB,SAAS;MACT;IAAA;AAGJ,iBAAa,OAAO;AAEhB,QAAA;AAEC,QAAA,KAAK,UAA6B,YAAY,cACnD;AACI,kBAAa,KAAK,UAA6B,YAAY,aAAa,oBAAoB,cAAc,KAAK;IAAA,OAGnH;AACI,kBAAY,KAAK,eAAe,IAAI,KAAK,IAAI,UAAU;AAClD,WAAA,kBAAkB,KAAK,SAAS;AAC3B,gBAAA,YAAY,cAAc,CAAC;IAAA;AAGzC,sBAAkB,YAAY;AAE9B,SAAK,4BAA4B;EAAA;EAG9B,KAAK,SACZ;AACI,SAAK,KAAK,OAAO;AAEjB,SAAK,wBAAwB,KAAK,aAAa,IAAI,KAAK;EAAA;EAGrD,MACP;AACI,SAAK,4BAA4B,KAAK,wBAAwB,EAAE,KAAK,cAAc,CAAC;AAIpF,QAAI,KAAK,UAAU,SAAS,aAAa,OACzC;AACK,WAAK,0BAA0B,UAAU,UAAU,CAAC,EAAmB,OAAO;IAAA;EACnF;EAGJ,IAAI,YACJ;AACI,WAAO,KAAK,0BAA0B;EAAA;EAG1C,IAAI,oBACJ;AACI,WAAO,KAAK;EAAA;EAGhB,IAAI,eACJ;AACI,WAAO,KAAK,0BAA0B,UAAU,UAAU,CAAC;EAAA;EAGvD,kBACR;AACU,UAAA,iBAAiB,IAAI,aAAa;MACpC,mBAAmB,EAAE,OAAO,IAAI,OAAO,GAAG,MAAM,cAAc;MAC9D,uBAAuB,EAAE,OAAO,IAAI,OAAO,GAAG,MAAM,cAAc;;MAElE,kBAAkB,EAAE,OAAO,IAAI,aAAa,CAAC,GAAG,MAAM,YAAY;MAClE,aAAa,EAAE,OAAO,CAAC,GAAG,CAAC,GAAG,MAAM,YAAY;IAAA,GACjD;MACC,UAAU;IAAA,CACb;AAEM,WAAA;EAAA;EAGJ,UACP;AACK,SAAK,YAAqB;EAAA;AAEnC;AA/Ka,oBAGK,YAAY;EACtB,MAAM;IACF,cAAc;IACd,cAAc;IACd,cAAc;EAAA;EAElB,MAAM;AACV;;;AC7EJ,IAAIC,OAAM;AAOH,IAAM,kBAAN,MACP;EADO,cAAA;AAaH,SAAiB,SAQX,CAAA;AAGN,SAAQ,UAAU;EAAA;;EAGX,OACP;AACI,WAAO,OAAO,IAAI,KAAK,SAAS,IAAI;EAAA;;;;;;;;EAUjC,OAAO,MAAiC,UAAkB,YAAY,MAC7E;AACI,UAAM,KAAKA;AAEX,QAAI,SAAS;AAEb,QAAI,WACJ;AACI,WAAK,WAAW;AAChB,eAAS,KAAK;IAAA;AAGlB,SAAK,OAAO,KAAK;MACb;MACA;MACA,OAAO,YAAY,IAAI;MACvB;MACA,MAAM,YAAY,IAAI;MACtB,QAAQ;MACR;IAAA,CACH;AAEM,WAAA;EAAA;;;;;EAOJ,OAAO,IACd;AACI,aAAS,IAAI,GAAG,IAAI,KAAK,OAAO,QAAQ,KACxC;AACI,UAAI,KAAK,OAAO,CAAC,EAAE,OAAO,IAC1B;AACS,aAAA,OAAO,OAAO,GAAG,CAAC;AAEvB;MAAA;IACJ;EACJ;;;;;EAOI,UACR;AACU,UAAA,MAAM,YAAY,IAAI;AAE5B,aAAS,IAAI,GAAG,IAAI,KAAK,OAAO,QAAQ,KACxC;AACU,YAAA,OAAO,KAAK,OAAO,CAAC;AAE1B,UAAK,MAAM,KAAK,SAAU,KAAK,QAAQ,KAAK,UAC5C;AACU,cAAA,UAAU,MAAM,KAAK;AAE3B,aAAK,KAAK,OAAO;AACjB,aAAK,OAAO;MAAA;IAChB;EACJ;;;;;EAOG,UACP;AACI,WAAO,OAAO,OAAO,KAAK,SAAS,IAAI;AAEvC,SAAK,OAAO,SAAS;EAAA;AAE7B;AAjHa,gBAGK,YAAY;EACtB,MAAM;IACF,cAAc;IACd,cAAc;IACd,cAAc;EAAA;EAElB,MAAM;EACN,UAAU;AACd;;;ACrBJ,IAAI,YAAY;AAST,SAAS,SAAS,MACzB;AACI,MAAI,WACJ;AACI;EAAA;AAGA,MAAA,WAAW,IAAI,EAAE,aAAa,EAAE,UAAU,YAAA,EAAc,QAAQ,QAAQ,IAAI,IAChF;AACI,UAAM,OAAO;MACT,iCAAiC,OAAO,KAAK,IAAI;;;MACjD;MACA;MACA;MACA;MACA;MACA;IAAA;AAGO,eAAA,QAAQ,IAAI,GAAG,IAAI;EAAA,WAEzB,WAAW,SACpB;AACI,eAAW,QAAQ,IAAI,UAAU,OAAO,MAAM,IAAI,2BAA2B;EAAA;AAGrE,cAAA;AAChB;;;ACZO,IAAM,cAAN,MACP;EAoBI,YAAY,UACZ;AACI,SAAK,YAAY;EAAA;;;;;EAOd,KAAK,SACZ;AACI,QAAI,QAAQ,OACZ;AACQ,UAAA,OAAO,KAAK,UAAU;AAE1B,UAAI,KAAK,UAAU,SAAS,aAAa,OACzC;AACI,gBAAQ,IAAK,KAAK,UAA4B,QAAQ,YAAY;MAAA;AAGtE,eAAS,IAAI;IAAA;EACjB;AAER;AA5Ca,YAGK,YAAY;EACtB,MAAM;IACF,cAAc;IACd,cAAc;IACd,cAAc;EAAA;EAElB,MAAM;EACN,UAAU;AACd;AAXS,YAcK,iBAAqC;;EAE/C,OAAO;AACX;;;AClCG,SAAS,UAAa,MAC7B;AACI,MAAI,QAAQ;AAEZ,aAAW,KAAK,MAChB;AAEQ,QAAA,KAAK,CAAC,KAAK,QACf;AACY,cAAA;AACR;IAAA;EACJ;AAGJ,MAAI,CAAC;AAAc,WAAA;AAEbC,QAAAA,aAAmB,uBAAA,OAAO,IAAI;AAEpC,aAAW,KAAK,MAChB;AACU,UAAA,QAAQ,KAAK,CAAC;AAEpB,QAAI,OACJ;AACIA,iBAAU,CAAC,IAAI;IAAA;EACnB;AAGGA,SAAAA;AACX;AAkBO,SAAS,WAAc,KAC9B;AACI,MAAI,SAAS;AAEb,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAChC;AAEQ,QAAA,IAAI,CAAC,KAAK,QACd;AACI;IAAA,OAGJ;AACI,UAAI,IAAI,MAAM,IAAI,IAAI,CAAC;IAAA;EAC3B;AAGJ,MAAI,UAAU;AAEP,SAAA;AACX;;;AClEA,IAAI,mBAAmB;AAiEhB,IAAM,sBAAN,MAAMC,qBACb;;;;;EAwDI,YAAY,UACZ;AAvBA,SAAiB,sBAAoC,CAAA;AASrD,SAAiB,iBAAiD,CAAA;AAKlE,SAAiB,iBAAiD,CAAA;AAU9D,SAAK,YAAY;EAAA;;;;;EAOd,KAAK,SACZ;AACI,cAAU,EAAE,GAAGA,qBAAmB,gBAAgB,GAAG,QAAQ;AAE7D,SAAK,gBAAgB,QAAQ;AAC7B,SAAK,aAAa,QAAQ;AAE1B,SAAK,UAAU,QAAQ;EAAA;;;;;EAO3B,IAAI,UACJ;AACW,WAAA,CAAC,CAAC,KAAK;EAAA;;;;;;EAQlB,IAAI,QAAQ,OACZ;AACI,QAAI,KAAK,YAAY;AAAO;AAE5B,QAAI,OACJ;AAES,WAAA,WAAW,KAAK,UAAU,UAAU;QACrC,MAAM,KAAK,IAAI;QACf,KAAK;QACL;MAAA;AAIC,WAAA,eAAe,KAAK,UAAU,UAAU;QACzC,MACA;AACe,qBAAA,QAAQ,KAAK,gBACxB;AACS,iBAAA,QAAQ,KAAK,IAAI,IAAI,UAAU,KAAK,QAAQ,KAAK,IAAI,CAAC;UAAA;QAC/D;QAEJ,KAAK;MAAA;AAIJ,WAAA,gBAAgB,KAAK,UAAU,UAAU;QAC1C,MACA;AACe,qBAAA,SAAS,KAAK,gBACzB;AACI,uBAAW,MAAM,QAAQ,MAAM,IAAI,CAAC;UAAA;QACxC;QAEJ,KAAK;MAAA;IACT,OAGJ;AAEI,WAAK,UAAU,UAAU,OAAO,KAAK,QAAQ;AAC7C,WAAK,UAAU,UAAU,OAAO,KAAK,YAAY;AACjD,WAAK,UAAU,UAAU,OAAO,KAAK,aAAa;IAAA;EACtD;;;;;;EAQG,eAAkB,SAAY,MACrC;AACI,SAAK,eAAe,KAAK,EAAE,SAAS,KAAA,CAAsB;EAAA;;;;;;EAQvD,gBAAmB,SAAY,MACtC;AACI,SAAK,eAAe,KAAK,EAAE,SAAS,KAAA,CAAsB;EAAA;;;;;;EAQvD,UAAU;IACb;EAAA,GAEJ;AACS,SAAA,OAAO,YAAY,IAAI;AAS5B,cAAU,YAAY,SAAS;AAE/B,SAAK,yBAAyB,UAAU,aAAa,UAAU,YAAY,MAAM;EAAA;;;;;EAO9E,cAAc,YACrB;AACI,QAAI,CAAC,KAAK;AAAS;AAEf,QAAA,WAAW,cAAc,IAC7B;AACS,WAAA,oBAAoB,KAAK,UAAU;AACxC,iBAAW,KAAK,aAAa,KAAK,mBAAmB,IAAI;IAAA;AAG7D,eAAW,YAAY,KAAK;EAAA;;;;;EAOzB,MACP;AACI,UAAM,MAAM,KAAK;AACjB,UAAM,qBAAqB,KAAK;AAC1B,UAAA,cAAc,KAAK,UAAU;AACnC,QAAI,SAAS;AAEb,aAAS,IAAI,GAAG,IAAI,mBAAmB,QAAQ,KAC/C;AACU,YAAA,aAAa,mBAAmB,CAAC;AAEvC,UAAI,eAAe,MACnB;AACI;AACA;MAAA;AAGE,YAAA,cAAc,WAAW,eAAe,WAAW;AACnD,YAAA,cAAc,aAAa,gBAAgB,UAAU;AAGtD,WAAA,aAAa,UAAU,OAAO,aACnC;AACI,mBAAW,YAAY;MAAA;AAI3B,UAAI,MAAM,WAAW,YAAY,KAAK,eACtC;AACQ,YAAA,CAAC,WAAW,WAChB;AACI,gBAAM,KAAK;AAEP,cAAA;AAAY,wBAAY,qBAAqB;AAEjD,aAAG,WAAW,YAAY,EAAE,kBAAkB,UAAU;QAAA;AAG5D,mBAAW,YAAY;AACvB;AACA,mBAAW,IAAI,aAAa,KAAK,mBAAmB,IAAI;MAAA,OAG5D;AACuB,2BAAA,IAAK,MAAO,IAAI;MAAA;IACvC;AAGJ,uBAAmB,UAAU;EAAA;;EAI1B,UACP;AACI,SAAK,UAAU;AACf,SAAK,YAAY;AACjB,SAAK,oBAAoB,SAAS;AAClC,SAAK,eAAe,SAAS;AAC7B,SAAK,eAAe,SAAS;EAAA;;;;;EAOzB,kBAAkB,YAC1B;AACI,UAAM,QAAQ,KAAK,oBAAoB,QAAQ,UAAwB;AAEvE,QAAI,SAAS,GACb;AACI,iBAAW,IAAI,aAAa,KAAK,mBAAmB,IAAI;AACnD,WAAA,oBAAoB,KAAK,IAAI;IAAA;EACtC;;;;;;EAQI,yBAAyB,aAA0B,QAC3D;AACI,gBAAY,eAAe,SAAS;AAEzB,eAAA,SAAS,YAAY,qBAChC;AACS,WAAA,yBAAyB,OAAO,MAAM;IAAA;EAC/C;AAER;AAlSa,oBAMK,YAAY;EACtB,MAAM;IACF,cAAc;IACd,cAAc;EAAA;EAElB,MAAM;EACN,UAAU;AACd;AAbS,oBAmBK,iBAA4C;;EAEtD,oBAAoB;;EAEpB,2BAA2B;;EAE3B,uBAAuB;AAC3B;AA1BG,IAAM,qBAAN;;;ACrCA,IAAM,mBAAN,MAAMC,kBACb;;EAkEI,YAAY,UACZ;AACI,SAAK,YAAY;AAEjB,SAAK,QAAQ;AACb,SAAK,aAAa;EAAA;EAGf,KAAK,SACZ;AACI,cAAU,EAAE,GAAGA,kBAAgB,gBAAgB,GAAG,QAAQ;AAE1D,SAAK,gBAAgB,QAAQ;AACxB,SAAA,UAAU,QAAQ,qBAAqB,QAAQ;AACpD,SAAK,SAAS,QAAQ;EAAA;;;;;EAOhB,aACV;AACQ,QAAA,CAAC,KAAK,UAAU,mBACpB;AACI;IAAA;AAGC,SAAA;AAEL,QAAI,CAAC,KAAK;AAAQ;AAEb,SAAA;AAED,QAAA,KAAK,aAAa,KAAK,eAC3B;AACI,WAAK,aAAa;AAElB,WAAK,IAAI;IAAA;EACb;;;;;EAOG,MACP;AACU,UAAA,kBAAkB,KAAK,UAAU,QAAQ;AAE/C,aAAS,IAAI,GAAG,IAAI,gBAAgB,QAAQ,KAC5C;AACU,YAAA,UAAU,gBAAgB,CAAC;AAGjC,UACI,QAAQ,sBACL,QAAQ,YACR,QAAQ,WAAW,MACnB,KAAK,QAAQ,QAAQ,WAAW,KAAK,SAE5C;AACI,gBAAQ,WAAW;AACnB,gBAAQ,OAAO;MAAA;IACnB;EACJ;EAGG,UACP;AACI,SAAK,YAAY;EAAA;AAEzB;AA3Ia,iBAGK,YAAY;EACtB,MAAM;IACF,cAAc;IACd,cAAc;EAAA;EAElB,MAAM;AACV;AATS,iBAYK,iBAAyC;;;;;EAKnD,iBAAiB;;;;;EAKjB,mBAAmB;;;;;EAKnB,kBAAkB,KAAK;;;;;EAKvB,wBAAwB;AAC5B;AAjCG,IAAM,kBAAN;;;ACMA,IAAM,gBAAN,MAAMC,eACb;;;;EA8CI,YAAY,aAAkC,CAAA,GAC9C;AAzBgB,SAAA,MAAc,IAAI,cAAc;AAMhD,SAAO,gBAAiC,CAAA;AAQxC,SAAO,UAAU;AACjB,SAAO,SAAS;AAEC,SAAA,QAAQ,IAAI,aAAa,CAAC;AAE3C,SAAiB,wBAAiC;AAO9C,iBAAa,EAAE,GAAGA,eAAa,gBAAgB,GAAG,WAAW;AAE7D,SAAK,UAAU,WAAW;AAC1B,SAAK,QAAQ,WAAW;AACxB,SAAK,SAAS,WAAW;AAErB,QAAA,OAAO,WAAW,kBAAkB,UACxC;AACI,WAAK,wBAAwB;AAE7B,eAAS,IAAI,GAAG,IAAI,WAAW,eAAe,KAC9C;AACI,aAAK,cAAc;UAAK,IAAI,cAAc;YACtC,OAAO,WAAW;YAClB,QAAQ,WAAW;YACnB,YAAY,WAAW;YACvB,WAAW,WAAW;UAAA,CACzB;QAAA;MACD;IACJ,OAGJ;AACS,WAAA,gBAAgB,CAAC,GAAG,WAAW,cAAc,IAAI,CAAC,YAAY,QAAQ,MAAM,CAAC;AAE5E,YAAA,cAAc,KAAK,aAAa;AAEtC,WAAK,OAAO,YAAY,OAAO,YAAY,QAAQ,YAAY,WAAW;IAAA;AAI9E,SAAK,aAAa,OAAO,GAAG,UAAU,KAAK,gBAAgB,IAAI;AAI3D,QAAA,WAAW,uBAAuB,KAAK,SAC3C;AAEI,UAAI,WAAW,+BAA+B,WACvC,WAAW,+BAA+B,eACjD;AACS,aAAA,sBAAsB,WAAW,oBAAoB;MAAA,OAG9D;AACI,aAAK,0BAA0B;MAAA;IACnC;EACJ;EAGJ,IAAI,OACJ;AACI,UAAM,QAAQ,KAAK;AAEb,UAAA,CAAC,IAAI,KAAK;AACV,UAAA,CAAC,IAAI,KAAK;AAET,WAAA;EAAA;EAGX,IAAI,QACJ;AACW,WAAA,KAAK,aAAa,OAAO;EAAA;EAGpC,IAAI,SACJ;AACW,WAAA,KAAK,aAAa,OAAO;EAAA;EAEpC,IAAI,aACJ;AACW,WAAA,KAAK,aAAa,OAAO;EAAA;EAGpC,IAAI,cACJ;AACW,WAAA,KAAK,aAAa,OAAO;EAAA;EAGpC,IAAI,aACJ;AACW,WAAA,KAAK,aAAa,OAAO;EAAA;EAGpC,IAAI,eACJ;AACW,WAAA,KAAK,cAAc,CAAC;EAAA;EAGrB,eAAeC,SACzB;AACI,SAAK,OAAOA,QAAO,OAAOA,QAAO,QAAQA,QAAO,aAAa,IAAI;EAAA;;;;;;EAQ9D,4BACP;AACQ,QAAA,CAAC,KAAK,qBACV;AACS,WAAA,sBAAsB,IAAI,cAAc;QACzC,OAAO,KAAK;QACZ,QAAQ,KAAK;QACb,YAAY,KAAK;QACjB,QAAQ;QACR,qBAAqB;QACrB,WAAW;QACX,eAAe;;MAAA,CAElB;IAAA;EACL;EAGG,OAAO,OAAe,QAAgB,aAAa,KAAK,YAAY,mBAAmB,OAC9F;AACS,SAAA;AAEL,SAAK,cAAc,QAAQ,CAAC,cAAc,MAC1C;AACI,UAAI,oBAAoB,MAAM;AAAG;AAEjC,mBAAa,OAAO,OAAO,OAAO,QAAQ,UAAU;IAAA,CACvD;AAED,QAAI,KAAK,qBACT;AACI,WAAK,oBAAoB,OAAO,OAAO,OAAO,QAAQ,UAAU;IAAA;EACpE;EAGG,UACP;AACI,SAAK,aAAa,OAAO,IAAI,UAAU,KAAK,gBAAgB,IAAI;AAEhE,QAAI,KAAK,uBACT;AACS,WAAA,cAAc,QAAQ,CAAC,YAC5B;AACI,gBAAQ,QAAQ;MAAA,CACnB;IAAA;AAGL,QAAI,KAAK,qBACT;AACI,WAAK,oBAAoB,QAAQ;AACjC,aAAO,KAAK;IAAA;EAChB;AAER;AAxMa,cAGK,iBAAsC;;EAEhD,OAAO;;EAEP,QAAQ;;EAER,YAAY;;EAEZ,eAAe;;EAEf,SAAS;;EAET,OAAO;;EAEP,WAAW;;;EAEX,QAAQ;AACZ;AApBG,IAAM,eAAN;;;ACvCP,IAAM,cAAA,oBAAuD,IAAI;AAOjD,SAAA,iBAAiB,QAAiB,SAClD;AACI,MAAI,CAAC,YAAY,IAAI,MAAM,GAC3B;AACU,UAAA,UAAU,IAAI,QAAQ;MACxB,QAAQ,IAAI,aAAa;QACrB,UAAU;QACV,GAAG;MAAA,CACN;IAAA,CACJ;AAED,UAAM,YAAY,MAClB;AACI,UAAI,YAAY,IAAI,MAAM,MAAM,SAChC;AACI,oBAAY,OAAO,MAAM;MAAA;IAC7B;AAGI,YAAA,KAAK,WAAW,SAAS;AACzB,YAAA,OAAO,KAAK,WAAW,SAAS;AAE5B,gBAAA,IAAI,QAAQ,OAAO;EAAA;AAG5B,SAAA,YAAY,IAAI,MAAM;AACjC;AAMO,SAAS,uBAAuB,QACvC;AACW,SAAA,YAAY,IAAI,MAAM;AACjC;;;ACsBO,IAAM,cAAN,MAAMC,aACb;;;;;;EA+CI,IAAW,cACX;AACW,WAAA,KAAK,QAAQ,OAAO;EAAA;EAE/B,IAAW,YAAY,OACvB;AACS,SAAA,QAAQ,OAAO,cAAc;EAAA;;EAgBtC,IAAI,aACJ;AACW,WAAA,KAAK,QAAQ,OAAO;EAAA;EAG/B,IAAI,WAAW,OACf;AACI,SAAK,QAAQ,OAAO;MAChB,KAAK,QAAQ,OAAO;MACpB,KAAK,QAAQ,OAAO;MACpB;IAAA;EACJ;;;;;EAOG,KAAK,SACZ;AACc,cAAA;MACN,GAAGA,aAAW;MACd,GAAG;IAAA;AAGP,QAAI,QAAQ,MACZ;AAEI,kBAAY,QAAQ,uDAAuD;AAG3E,cAAQ,SAAS,QAAQ;IAAA;AAGxB,SAAA,SAAS,IAAI,UAAU,GAAG,GAAG,QAAQ,OAAO,QAAQ,MAAM;AAC/D,SAAK,SAAS,QAAQ,UAAU,WAAW,IAAA,EAAM,aAAa;AACzD,SAAA,YAAY,CAAC,CAAC,QAAQ;AAC3B,SAAK,UAAU,iBAAiB,KAAK,QAAQ,OAAO;AAC/C,SAAA,eAAe,IAAI,aAAa;MACjC,eAAe,CAAC,KAAK,OAAO;MAC5B,OAAO,CAAC,CAAC,QAAQ;MACjB,QAAQ;IAAA,CACX;AAED,SAAK,QAAQ,OAAO,cAAe,QAA4B,kBAAkB;AACjF,SAAK,aAAa,QAAQ;EAAA;;;;;;;EASvB,OAAO,oBAA4B,qBAA6B,YACvE;AACI,SAAK,QAAQ,OAAO,OAAO,oBAAoB,qBAAqB,UAAU;AAE9E,SAAK,OAAO,QAAQ,KAAK,QAAQ,MAAM;AACvC,SAAK,OAAO,SAAS,KAAK,QAAQ,MAAM;EAAA;;;;;;;;;EAWrC,QAAQ,UAAgD,OAC/D;AACI,UAAM,aAAa,OAAO,YAAY,YAAY,UAAU,CAAC,CAAC,SAAS;AAEnE,QAAA,cAAc,KAAK,OAAO,YAC9B;AACI,WAAK,OAAO,WAAW,YAAY,KAAK,MAAM;IAAA;EAClD;AAKR;AAxJa,YAGK,YAAY;EACtB,MAAM;IACF,cAAc;IACd,cAAc;IACd,cAAc;EAAA;EAElB,MAAM;EACN,UAAU;AACd;AAXS,YAcK,iBAAoC;;;;;EAK9C,OAAO;;;;;EAKP,QAAQ;;;;;EAKR,aAAa;;;;;EAKb,WAAW;AACf;AAnCG,IAAM,aAAN;;;AC3CA,IAAM,gBAAgB;EACzB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACJ;AAOO,IAAM,oBAAoB;EAC7B;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACJ;;;ACnCO,IAAM,YAAN,MACP;EASI,YAAY,SACZ;AARQ,SAAA,oBAGI,uBAAA,OAAO,IAAI;AAMnB,SAAK,WAAW;AAGhB,SAAK,aAAa;EAAA;;;;;;EAQd,eACR;AACQ,QAAA,CAAC,oBAAA,GACL;AACU,YAAA,IAAI,MAAM,0GACiD;IAAA;EACrE;EAGG,mBAAmB,cAC1B;AACU,UAAA,cAAc,KAAK,oBAAoB,YAAY;AAEzD,iBAAa,WAAb,aAAa,SAAW,IAAI,OAAO;MAC/B,MAAM,IAAI,aAAa,YAAY,OAAO,OAAO,CAAC;MAClD,OAAO,YAAY,UAAU,YAAY;IAAA,CAC5C;EAAA;EAGE,oBAAoB,cAC3B;AACI,WAAO,KAAK,kBAAkB,aAAa,UAAU,KAAK,KAAK,kBAAkB,YAAY;EAAA;EAGzF,kBAAkB,cAC1B;AACI,UAAM,wBAAwB,aAAa;AAEvC,QAAA,cAAc,KAAK,kBAAkB,qBAAqB;AAE9D,QAAI,CAAC,aACL;AACI,YAAM,WAAW,OAAO,KAAK,aAAa,iBAAiB,EAAE,IAAI,CAAC,MAAM,aAAa,kBAAkB,CAAC,CAAC;AAEzG,YAAM,SAAS,KAAK,SAAS,kBAAkB,QAAQ;AAEvD,YAAM,eAAe,KAAK,iBAAiB,OAAO,WAAW;AAE/C,oBAAA,KAAK,kBAAkB,qBAAqB,IAAI;QAC1D;QACA;MAAA;IACJ;AAGG,WAAA,KAAK,kBAAkB,qBAAqB;EAAA;EAG/C,iBACJ,aAEJ;AACW,WAAA,KAAK,SAAS,gBAAgB,WAAW;EAAA;EAG7C,iBAAiB,cAA4B,MAAqB,QACzE;AACU,UAAA,mBAAmB,KAAK,oBAAoB,YAAY;AAE9D,iBAAa,WAAb,aAAa,SAAW,IAAI,OAAO;MAC/B,MAAM,IAAI,aAAa,iBAAiB,OAAO,OAAO,CAAC;MACvD,OAAO,YAAY,UAAU,YAAY;IAAA,CAC5C;AAED,QAAI,YAAwB;AAE5B,QAAI,CAAC,MACL;AACI,aAAO,aAAa,OAAO;AAC3B,kBAAY,aAAa,OAAO;IAAA;AAEzB,eAAA,SAAA;AAEX,qBAAiB,aAAa,aAAa,UAAU,MAAM,WAAW,MAAM;AAErE,WAAA;EAAA;EAGJ,mBAAmB,cAC1B;AACQ,QAAA,aAAa,YAAY,CAAC,aAAa;AAAiB,aAAA;AAC5D,iBAAa,WAAW;AAElB,UAAA,SAAS,KAAK,iBAAiB,YAAY;AAEjD,iBAAa,OAAO,OAAO;AAEpB,WAAA;EAAA;EAGJ,UACP;AACI,SAAK,oBAAoB;EAAA;AAEjC;;;ACtGO,IAAM,iBAA4C;;EAErD;IACI,MAAM;IACN,MAAM,CAAC,SACP;AACI,YAAM,QAAQ,KAAK;AAEnB,aAAO,MAAM,MAAM;IAAA;IAEvB,KAAK;;;;;;;;;;;;IAYL,SAAS;;;EAAA;;EAKb;IACI,MAAM;IACN,MAAM,CAAC,SACH,KAAK,SAAS,eAAe,KAAK,SAAS,KAAM,KAAK,MAAoB,UAAU;IACxF,KAAK;;;;;;;IAOL,SAAS;;;;;;;;;;;EAAA;;EAab;IACI,MAAM;IACN,MAAM,CAAC,SACH,KAAK,SAAS,eAAe,KAAK,SAAS,KAAM,KAAK,MAAoB,MAAM;IACpF,KAAM;;;;;IAKN,SAAS;;;;;;;;;EAAA;;EAWb;IACI,MAAM;IACN,MAAM,CAAC,SACH,KAAK,SAAS,eAAe,KAAK,SAAS,KAAM,KAAK,MAAgB,QAAQ;IAClF,KAAK;;;;;;;IAOL,SAAS;;;;;;;;;;;EAAA;;EAab;IACI,MAAM;IACN,MAAM,CAAC,SACH,KAAK,SAAS,eAAe,KAAK,SAAS,KAAM,KAAK,MAAgB,QAAQ;IAClF,KAAK;;;;;;IAML,SAAS;;;;;;;;;;EAAA;AAWjB;;;AC3IO,SAAS,sBACZ,aACA,YACA,yBACA,kBAEJ;AACI,QAAM,gBAAgB,CAAC;;;;;;;KAOtB;AAED,MAAI,OAAO;AAEX,WAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KACxC;AACU,UAAA,aAAa,YAAY,CAAC;AAE1B,UAAA,OAAO,WAAW,KAAK;AAE7B,QAAI,SAAS;AACb,QAAI,SAAS;AAEb,aAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,KAC3C;AACU,YAAA,gBAAgB,eAAe,CAAC;AAEtC,UAAI,cAAc,KAAK,WAAW,IAAI,GACtC;AACI,iBAAS,WAAW,SAAS;AAEf,sBAAA;UACV,WAAW,IAAI;UACf,aAAa,SAAS,IAAI;UAC1B,eAAe,CAAC,EAAE,UAAU,KAAK,eAAe,CAAC,EAAE;QAAA;AAC9C,iBAAA;AAET;MAAA;IACJ;AAGJ,QAAI,CAAC,QACL;AACQ,UAAA,WAAW,KAAK,OAAO,GAC3B;AACI,iBAAS,WAAW,SAAS;AAE7B,sBAAc,KAAK,wBAAwB,YAAY,SAAS,IAAI,CAAC;MAAA,OAGzE;AACI,cAAM,WAAW,iBAAiB,WAAW,KAAK,IAA4B;AAE9E,iBAAS,WAAW,SAAS;AAEf,sBAAA;;UAAe;6BAChB,IAAI;gCACD,SAAS,IAAI;sBACvB,QAAQ;;QAAA;MACb;IACL;AAGG,WAAA;EAAA;AAGL,QAAA,cAAc,cAAc,KAAK,IAAI;AAG3C,SAAO,IAAI;IACP;IACA;IACA;IACA;IACA;EAAA;AAER;;;ACzFA,SAAS,WAAW,KAAa,KACjC;AACI,QAAM,QAAQ,MAAM;AAEb,SAAA;8BACmB,KAAK;mCACA,GAAG,oBAAoB,GAAG;;;AAG7D;AAGO,IAAM,wBAA8D;EACvE,KAAK;;EAEL,KAAK;;EAEL,aAAa;;;EAGb,aAAa;;;;EAIb,aAAa;;;;;EAKb,aAAa;;;EAGb,aAAa;;;;EAIb,aAAa;;;;;EAKb,eAAe;;;;;EAKf,eAAe;;;;;;;;;;EAUf,eAAe;;;;EAIf,eAAe,WAAW,GAAG,CAAC;EAC9B,eAAe,WAAW,GAAG,CAAC;EAC9B,eAAe,WAAW,GAAG,CAAC;EAC9B,eAAe,WAAW,GAAG,CAAC;EAC9B,eAAe,WAAW,GAAG,CAAC;EAC9B,eAAe,WAAW,GAAG,CAAC;AAClC;AAGO,IAAM,uBAA6D;EACtE,GAAG;EACH,eAAe;;;;;;AAMnB;;;ACpDO,IAAM,iBAAN,cAA6B,sBAGpC;;;;;;;;;;EAuDI,YAAY,EAAE,QAAQ,QAAQ,KAAA,GAC9B;AACU,UAAA;AAjDM,SAAA,MAAc,IAAI,QAAQ;AAM1C,SAAgB,gBAAgB;AAMhC,SAAO,WAAW;AAMX,SAAA,cAAc,IAAI,UAAU;AAYnC,SAAgB,kBAAkB;AAMlC,SAAO,YAAY;AAef,SAAK,SAAS;AACd,SAAK,SAAS,SAAS;AACvB,SAAK,OAAO;AAEZ,SAAK,OAAO,GAAG,UAAU,KAAK,gBAAgB,IAAI;EAAA;EAG5C,iBACV;AACS,SAAA,cAAc,IAAI,UAAU;AAE5B,SAAA,KAAK,UAAU,IAAI;EAAA;;;;;;EAQrB,QAAQ,gBAAgB,OAC/B;AACI,SAAK,YAAY;AAEjB,QAAI,eACJ;AACI,WAAK,OAAO,QAAQ;IAAA;AAGnB,SAAA,KAAK,UAAU,IAAI;AAExB,SAAK,SAAS;EAAA;AAEtB;;;ACvGgB,SAAA,iBACZ,UACA,eAEJ;AACe,aAAA,KAAK,SAAS,YACzB;AACU,UAAA,YAAY,SAAS,WAAW,CAAC;AACjC,UAAA,gBAAgB,cAAc,CAAC;AAErC,QAAI,eACJ;AACc,gBAAA,WAAV,UAAU,SAAW,cAAc;AACzB,gBAAA,WAAV,UAAU,SAAW,cAAc;AACzB,gBAAA,aAAV,UAAU,WAAa,cAAc;IAAA,OAGzC;AAES,WAAA,aAAa,CAAC,mGAAmG;IAAA;EAC1H;AAGJ,uBAAqB,QAAQ;AACjC;AAEA,SAAS,qBAAqB,UAC9B;AACU,QAAA,EAAE,SAAS,WAAA,IAAe;AAEhC,QAAM,aAAqC,CAAA;AAC3C,QAAM,YAAoC,CAAA;AAE1C,aAAW,KAAK,SAChB;AACU,UAAA,SAAS,QAAQ,CAAC;AAEb,eAAA,OAAO,GAAG,IAAI;AACf,cAAA,OAAO,GAAG,IAAI;EAAA;AAG5B,aAAW,KAAK,YAChB;AACU,UAAA,YAAY,WAAW,CAAC;AAE9B,eAAW,UAAU,OAAO,GAAG,KAAK,2BAA2B,UAAU,MAAM,EAAE;EAAA;AAGrF,aAAW,KAAK,YAChB;AACU,UAAA,YAAY,WAAW,CAAC;AAE9B,cAAU,WAAV,UAAU,SAAW,WAAW,UAAU,OAAO,GAAG;AAEpD,cAAU,UAAV,UAAU,QAAU,UAAU,UAAU,OAAO,GAAG;AAElD,cAAU,UAAU,OAAO,GAAG,KAAK,2BAA2B,UAAU,MAAM,EAAE;EAAA;AAExF;;;ACrDO,IAAM,wBAAwC,CAAA;AAErD,sBAAsB,cAAc,IAAI,IAAI;AAE5C,sBAAsB,cAAc,QAAQ,IAAI;EAC5C,kBAAkB;EAClB,iBAAiB;AACrB;AAEA,sBAAsB,cAAc,kBAAkB,IAAI;EACtD,cAAc;IACV,SAAS;IACT,QAAQ;EAAA;EAEZ,aAAa;IACT,SAAS;IACT,QAAQ;EAAA;AAEhB;AAEA,sBAAsB,cAAc,qBAAqB,IAAI;EACzD,cAAc;IACV,SAAS;IACT,QAAQ;EAAA;EAEZ,aAAa;IACT,SAAS;IACT,QAAQ;EAAA;AAEhB;AAEA,sBAAsB,cAAc,WAAW,IAAI;EAC/C,kBAAkB;EAClB,cAAc;IACV,SAAS;IACT,QAAQ;EAAA;EAEZ,aAAa;IACT,SAAS;IACT,QAAQ;EAAA;AAEhB;AAEA,sBAAsB,cAAc,mBAAmB,IAAI;EACvD,kBAAkB;EAClB,cAAc;IACV,SAAS;IACT,QAAQ;EAAA;EAEZ,aAAa;IACT,SAAS;IACT,QAAQ;EAAA;AAEhB;;;ACjEO,SAAS,oBACZ,IACA,GACA,GACA,OACA,QACA,OAEJ;AACU,QAAA,OAAO,QAAQ,IAAI;AAEzB,KAAG,SAAS;AAET,KAAA,IAAK,IAAI,QAAQ;AACjB,KAAA,IAAI,QAAQ,IAAI,SAAS;AAEzB,KAAA,KAAK,KAAM,IAAI,GAAG;AACrB,KAAG,KAAK,CAAC,OAAQ,IAAI,GAAG;AAEjB,SAAA;AACX;;;ACtBO,SAAS,oBAAoB,cACpC;AACU,QAAA,WAAW,aAAa,aAAa,OAAO;AAElD,SAAS,WAAW,qBAAqB,oBAAoB,qBAAsB,SAAS,KAAK,SAAS,QAAQ;AACtH;;;AC0IO,IAAM,qBAAN,MACP;EAyCI,YAAY,UACZ;AAtCO,SAAA,eAAe,IAAI,UAAU;AAQpB,SAAA,WAAW,IAAI,UAAU;AAKzB,SAAA,uBAAuB,IAAI,aAAa,sBAAsB;AAE9D,SAAA,mBAAmB,IAAI,OAAO;AAE9C,SAAgB,oBAA+B,CAAC,GAAG,GAAG,GAAG,CAAC;AAOzC,SAAA,mCAAA,oBACP,IAAI;AAEN,SAAA,uBAA6D,uBAAA,OAAO,IAAI;AAMhF,SAAiB,qBAA6C,CAAA;AAM1D,SAAK,YAAY;AACR,aAAA,aAAa,eAAe,MAAM,sBAAsB;EAAA;;EAI9D,mBACP;AACS,SAAA,QAAQ,iBAAiB,KAAK,YAAY;EAAA;;;;;;;;;EAW5C,YAAY;IACf;IACA;IACA;IACA;EAAA,GAOJ;AAEI,SAAK,mBAAmB,SAAS;AAE5B,SAAA;MACD;MACA;MACA;MACA;IAAA;AAGC,SAAA,aAAa,SAAS,KAAK,QAAQ;AACxC,SAAK,mBAAmB,KAAK;AACxB,SAAA,oBAAoB,oBAAoB,KAAK,gBAAgB;AAE7D,SAAA,QAAQ,YAAY,KAAK,gBAAgB;EAAA;EAG3C,aACP;AACS,SAAA,QAAQ,aAAa,KAAK,gBAAgB;EAAA;;;;;;;;;;;;;EAe5C,KACH,eACA,QAAuB,MACvB,YACA,OAEJ;AACU,UAAA,eAAe,KAAK,gBAAgB,aAAa;AAEjD,UAAA,YAAY,KAAK,iBAAiB;AAExC,SAAK,eAAe;AACpB,SAAK,gBAAgB;AAEf,UAAA,kBAAkB,KAAK,mBAAmB,YAAY;AAE5D,QAAI,aAAa,eAAe,gBAAgB,SACzC,aAAa,gBAAgB,gBAAgB,QACpD;AACS,WAAA,QAAQ,sBAAsB,YAAY;AAE/C,sBAAgB,QAAQ,aAAa;AACrC,sBAAgB,SAAS,aAAa;IAAA;AAG1C,UAAMC,UAAS,aAAa;AAC5B,UAAM,WAAW,KAAK;AAEtB,UAAM,aAAaA,QAAO;AAC1B,UAAM,cAAcA,QAAO;AAEvB,QAAA,CAAC,SAAS,yBAAyB,SACvC;AACI,cAAQ,cAAc;IAAA;AAG1B,QAAI,OACJ;AACI,YAAM,aAAaA,QAAO;AAE1B,eAAS,IAAM,MAAM,IAAI,aAAc,MAAO;AAC9C,eAAS,IAAM,MAAM,IAAI,aAAc,MAAO;AAC9C,eAAS,QAAU,MAAM,QAAQ,aAAc,MAAO;AACtD,eAAS,SAAW,MAAM,SAAS,aAAc,MAAO;IAAA,OAG5D;AACI,eAAS,IAAI;AACb,eAAS,IAAI;AACb,eAAS,QAAQ;AACjB,eAAS,SAAS;IAAA;AAGtB;MACI,KAAK;MACL;MAAG;MACH,SAAS,QAAQA,QAAO;MACxB,SAAS,SAASA,QAAO;MACzB,CAAC,aAAa;IAAA;AAGlB,SAAK,QAAQ,gBAAgB,cAAc,OAAO,YAAY,QAAQ;AAEtE,QAAI,WACJ;AACS,WAAA,qBAAqB,KAAK,YAAY;IAAA;AAGxC,WAAA;EAAA;EAGJ,MACH,QACA,QAAuB,MAAM,KAC7B,YAEJ;AACI,QAAI,CAAC;AAAO;AAEZ,QAAI,QACJ;AACa,eAAA,KAAK,gBAAgB,MAAM;IAAA;AAGxC,SAAK,QAAQ;MACR,UAA2B,KAAK;MACjC;MACA;MACA,KAAK;IAAA;EACT;EAGM,gBACV;AACS,SAAA,uBAA8B,uBAAA,OAAO,IAAI;EAAA;;;;;;;;EAU3C,KACH,eACA,QAAyB,MAAM,KAC/B,YACA,OAEJ;AACI,UAAM,eAAe,KAAK,KAAK,eAAe,OAAO,YAAY,KAAK;AAEtE,SAAK,mBAAmB,KAAK;MACzB;MACA;IAAA,CACH;AAEM,WAAA;EAAA;;EAIJ,MACP;AACI,SAAK,mBAAmB,IAAI;AAE5B,UAAM,0BAA0B,KAAK,mBAAmB,KAAK,mBAAmB,SAAS,CAAC;AAE1F,SAAK,KAAK,wBAAwB,cAAc,OAAO,MAAM,wBAAwB,KAAK;EAAA;;;;;;;;EAUvF,gBAAgB,eACvB;AACI,QAAM,cAA0B,WAChC;AACI,sBAAiB,cAA0B;IAAA;AAG/C,WAAO,KAAK,iCAAiC,IAAI,aAAa,KAC3D,KAAK,kBAAkB,aAAa;EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA0CpC,cACH,4BACA,oBACA,WACA,MACA,YAEJ;AAGQ,QAAA,UAAU,IAAI,GAClB;AACI,WAAK,SAAS,UAAU;AACxB,iBAAW,KAAK,UAAU;AAC1B,gBAAU,IAAI;IAAA;AAGd,QAAA,UAAU,IAAI,GAClB;AACI,WAAK,UAAU,UAAU;AACzB,iBAAW,KAAK,UAAU;AAC1B,gBAAU,IAAI;IAAA;AAGZ,UAAA,EAAE,YAAY,YAAA,IAAgB;AAEpC,SAAK,QAAQ,KAAK,IAAI,KAAK,OAAO,aAAa,UAAU,CAAC;AAC1D,SAAK,SAAS,KAAK,IAAI,KAAK,QAAQ,cAAc,UAAU,CAAC;AAE7D,WAAO,KAAK,QAAQ;MAChB;MACA;MACA;MACA;MACA;IAAA;EACJ;;;;;EAOG,qBACP;AACQ,QAAA,CAAC,KAAK,aAAa,SACvB;AACI,WAAK,aAAa,UAAU;AAE5B,WAAK,QAAQ,gBAAgB,KAAK,cAAc,OAAO,MAAM,KAAK,QAAQ;IAAA;EAC9E;;EAIG,UACP;AACK,SAAK,YAAqB;AAE3B,SAAK,iCAAiC,QAAQ,CAAC,cAAc,QAC7D;AACI,UAAI,iBAAiB,KACrB;AACI,qBAAa,QAAQ;MAAA;IACzB,CACH;AAED,SAAK,iCAAiC,MAAM;AAEvC,SAAA,uBAA8B,uBAAA,OAAO,IAAI;EAAA;EAG1C,kBAAkB,eAC1B;AACI,QAAI,eAA6B;AAE7B,QAAA,aAAa,KAAK,aAAa,GACnC;AACoB,sBAAA,iBAAiB,aAAwB,EAAE;IAAA;AAG/D,QAAI,yBAAyB,cAC7B;AACmB,qBAAA;IAAA,WAEV,yBAAyB,eAClC;AACI,qBAAe,IAAI,aAAa;QAC5B,eAAe,CAAC,aAAa;MAAA,CAChC;AAEG,UAAA,cAAc,kBAAkB,cACpC;AACI,qBAAa,SAAS;MAAA;AAIZ,oBAAA,KAAK,WAAW,MAC9B;AACI,qBAAa,QAAQ;AAEhB,aAAA,iCAAiC,OAAO,aAAa;AAE1D,cAAM,kBAAkB,KAAK,qBAAqB,aAAa,GAAG;AAElE,YAAI,iBACJ;AACS,eAAA,qBAAqB,aAAa,GAAG,IAAI;AACzC,eAAA,QAAQ,uBAAuB,eAAe;QAAA;MACvD,CACH;IAAA;AAGA,SAAA,iCAAiC,IAAI,eAAe,YAAY;AAE9D,WAAA;EAAA;EAGJ,mBAAmB,cAC1B;AACI,WAAO,KAAK,qBAAqB,aAAa,GAAG,MAC7C,KAAK,qBAAqB,aAAa,GAAG,IAAI,KAAK,QAAQ,oBAAoB,YAAY;EAAA;EAG5F,aACP;AACI,SAAK,eAAe;AACpB,SAAK,gBAAgB;EAAA;AAE7B;", "names": ["_Filter", "CLEAR", "_Abstract<PERSON><PERSON><PERSON>", "tempMatrix", "_Batcher<PERSON>ipe", "_BackgroundSystem", "_ExtractSystem", "tempBounds", "uid", "cleanHash", "_RenderableGCSystem", "_TextureGCSystem", "_RenderTarget", "source", "_ViewSystem", "source"]}