{"version": 3, "sources": ["../../pixi.js/src/rendering/batcher/gpu/getTextureBatchBindGroup.ts", "../../pixi.js/src/rendering/renderers/shared/texture/CanvasPool.ts"], "sourcesContent": ["import { BindGroup } from '../../renderers/gpu/shader/BindGroup';\nimport { Texture } from '../../renderers/shared/texture/Texture';\n\nimport type { TextureSource } from '../../renderers/shared/texture/sources/TextureSource';\n\nconst cachedGroups: Record<number, BindGroup> = {};\n\n/**\n * @param textures\n * @param size\n * @param maxTextures\n * @internal\n */\nexport function getTextureBatchBindGroup(textures: TextureSource[], size: number, maxTextures: number)\n{\n    let uid = 2166136261; // FNV-1a 32-bit offset basis\n\n    for (let i = 0; i < size; i++)\n    {\n        uid ^= textures[i].uid;\n        uid = Math.imul(uid, 16777619);\n        uid >>>= 0;\n    }\n\n    return cachedGroups[uid] || generateTextureBatchBindGroup(textures, size, uid, maxTextures);\n}\n\nfunction generateTextureBatchBindGroup(textures: TextureSource[], size: number, key: number, maxTextures: number): BindGroup\n{\n    const bindGroupResources: Record<string, any> = {};\n\n    let bindIndex = 0;\n\n    for (let i = 0; i < maxTextures; i++)\n    {\n        const texture = i < size ? textures[i] : Texture.EMPTY.source;\n\n        bindGroupResources[bindIndex++] = texture.source;\n        bindGroupResources[bindIndex++] = texture.style;\n    }\n\n    // pad out with empty textures\n    const bindGroup = new BindGroup(bindGroupResources);\n\n    cachedGroups[key] = bindGroup;\n\n    return bindGroup;\n}\n\n", "import { DOMAdapter } from '../../../../environment/adapter';\nimport { nextPow2 } from '../../../../maths/misc/pow2';\n\nimport type { ICanvas, ICanvasRenderingContext2DSettings } from '../../../../environment/canvas/ICanvas';\nimport type { ICanvasRenderingContext2D } from '../../../../environment/canvas/ICanvasRenderingContext2D';\n\n/**\n * A utility type that represents a canvas and its rendering context.\n * @category rendering\n * @internal\n */\nexport interface CanvasAndContext\n{\n    /** The canvas element. */\n    canvas: ICanvas;\n    /** The rendering context of the canvas. */\n    context: ICanvasRenderingContext2D;\n}\n\n/**\n * CanvasPool is a utility class that manages a pool of reusable canvas elements\n * @category rendering\n * @internal\n */\nexport class CanvasPoolClass\n{\n    public canvasOptions: ICanvasRenderingContext2DSettings;\n\n    /**\n     * Allow renderTextures of the same size as screen, not just pow2\n     *\n     * Automatically sets to true after `setScreenSize`\n     * @default false\n     */\n    public enableFullScreen: boolean;\n    private _canvasPool: {[x in string | number]: CanvasAndContext[]};\n\n    constructor(canvasOptions?: ICanvasRenderingContext2DSettings)\n    {\n        this._canvasPool = Object.create(null);\n        this.canvasOptions = canvasOptions || {};\n        this.enableFullScreen = false;\n    }\n\n    /**\n     * Creates texture with params that were specified in pool constructor.\n     * @param pixelWidth - Width of texture in pixels.\n     * @param pixelHeight - Height of texture in pixels.\n     */\n    private _createCanvasAndContext(pixelWidth: number, pixelHeight: number): CanvasAndContext\n    {\n        const canvas = DOMAdapter.get().createCanvas();\n\n        canvas.width = pixelWidth;\n        canvas.height = pixelHeight;\n\n        const context = canvas.getContext('2d');\n\n        return { canvas, context };\n    }\n\n    /**\n     * Gets a Power-of-Two render texture or fullScreen texture\n     * @param minWidth - The minimum width of the render texture.\n     * @param minHeight - The minimum height of the render texture.\n     * @param resolution - The resolution of the render texture.\n     * @returns The new render texture.\n     */\n    public getOptimalCanvasAndContext(minWidth: number, minHeight: number, resolution = 1): CanvasAndContext\n    {\n        minWidth = Math.ceil((minWidth * resolution) - 1e-6);\n        minHeight = Math.ceil((minHeight * resolution) - 1e-6);\n        minWidth = nextPow2(minWidth);\n        minHeight = nextPow2(minHeight);\n\n        const key = (minWidth << 17) + (minHeight << 1);\n\n        if (!this._canvasPool[key])\n        {\n            this._canvasPool[key] = [];\n        }\n\n        let canvasAndContext = this._canvasPool[key].pop();\n\n        if (!canvasAndContext)\n        {\n            canvasAndContext = this._createCanvasAndContext(minWidth, minHeight);\n        }\n\n        return canvasAndContext;\n    }\n\n    /**\n     * Place a render texture back into the pool.\n     * @param canvasAndContext\n     */\n    public returnCanvasAndContext(canvasAndContext: CanvasAndContext): void\n    {\n        const canvas = canvasAndContext.canvas;\n        const { width, height } = canvas;\n\n        const key = (width << 17) + (height << 1);\n\n        canvasAndContext.context.resetTransform();\n        canvasAndContext.context.clearRect(0, 0, width, height);\n\n        this._canvasPool[key].push(canvasAndContext);\n    }\n\n    public clear(): void\n    {\n        this._canvasPool = {};\n    }\n}\n\n/**\n * CanvasPool is a utility class that manages a pool of reusable canvas elements\n * @category rendering\n * @internal\n */\nexport const CanvasPool = new CanvasPoolClass();\n"], "mappings": ";;;;;;;;AAKA,IAAM,eAA0C,CAAA;AAQhC,SAAA,yBAAyB,UAA2B,MAAc,aAClF;AACI,MAAI,MAAM;AAEV,WAAS,IAAI,GAAG,IAAI,MAAM,KAC1B;AACW,WAAA,SAAS,CAAC,EAAE;AACb,UAAA,KAAK,KAAK,KAAK,QAAQ;AACpB,aAAA;EAAA;AAGb,SAAO,aAAa,GAAG,KAAK,8BAA8B,UAAU,MAAM,KAAK,WAAW;AAC9F;AAEA,SAAS,8BAA8B,UAA2B,MAAc,KAAa,aAC7F;AACI,QAAM,qBAA0C,CAAA;AAEhD,MAAI,YAAY;AAEhB,WAAS,IAAI,GAAG,IAAI,aAAa,KACjC;AACI,UAAM,UAAU,IAAI,OAAO,SAAS,CAAC,IAAI,QAAQ,MAAM;AAEpC,uBAAA,WAAW,IAAI,QAAQ;AACvB,uBAAA,WAAW,IAAI,QAAQ;EAAA;AAIxC,QAAA,YAAY,IAAI,UAAU,kBAAkB;AAElD,eAAa,GAAG,IAAI;AAEb,SAAA;AACX;;;ACvBO,IAAM,kBAAN,MACP;EAYI,YAAY,eACZ;AACS,SAAA,cAAqB,uBAAA,OAAO,IAAI;AAChC,SAAA,gBAAgB,iBAAiB,CAAA;AACtC,SAAK,mBAAmB;EAAA;;;;;;EAQpB,wBAAwB,YAAoB,aACpD;AACI,UAAM,SAAS,WAAW,IAAI,EAAE,aAAa;AAE7C,WAAO,QAAQ;AACf,WAAO,SAAS;AAEV,UAAA,UAAU,OAAO,WAAW,IAAI;AAE/B,WAAA,EAAE,QAAQ,QAAQ;EAAA;;;;;;;;EAUtB,2BAA2B,UAAkB,WAAmB,aAAa,GACpF;AACI,eAAW,KAAK,KAAM,WAAW,aAAc,IAAI;AACnD,gBAAY,KAAK,KAAM,YAAY,aAAc,IAAI;AACrD,eAAW,SAAS,QAAQ;AAC5B,gBAAY,SAAS,SAAS;AAExB,UAAA,OAAO,YAAY,OAAO,aAAa;AAE7C,QAAI,CAAC,KAAK,YAAY,GAAG,GACzB;AACS,WAAA,YAAY,GAAG,IAAI,CAAA;IAAC;AAG7B,QAAI,mBAAmB,KAAK,YAAY,GAAG,EAAE,IAAI;AAEjD,QAAI,CAAC,kBACL;AACuB,yBAAA,KAAK,wBAAwB,UAAU,SAAS;IAAA;AAGhE,WAAA;EAAA;;;;;EAOJ,uBAAuB,kBAC9B;AACI,UAAM,SAAS,iBAAiB;AAC1B,UAAA,EAAE,OAAO,OAAA,IAAW;AAEpB,UAAA,OAAO,SAAS,OAAO,UAAU;AAEvC,qBAAiB,QAAQ,eAAe;AACxC,qBAAiB,QAAQ,UAAU,GAAG,GAAG,OAAO,MAAM;AAEtD,SAAK,YAAY,GAAG,EAAE,KAAK,gBAAgB;EAAA;EAGxC,QACP;AACI,SAAK,cAAc,CAAA;EAAC;AAE5B;AAOa,IAAA,aAAa,IAAI,gBAAgB;", "names": []}