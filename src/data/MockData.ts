export interface GameHistory {
  id: string;
  timestamp: Date;
  playerChoice: 'heads' | 'tails';
  result: 'heads' | 'tails';
  betAmount: number;
  won: boolean;
  balanceAfter: number;
}

export interface PlayerStats {
  totalGames: number;
  gamesWon: number;
  gamesLost: number;
  totalWinnings: number;
  totalLosses: number;
  winRate: number;
  biggestWin: number;
  biggestLoss: number;
  currentStreak: number;
  longestWinStreak: number;
  longestLoseStreak: number;
}

export class MockDataService {
  private gameHistory: GameHistory[] = [];
  private playerStats: PlayerStats = {
    totalGames: 0,
    gamesWon: 0,
    gamesLost: 0,
    totalWinnings: 0,
    totalLosses: 0,
    winRate: 0,
    biggestWin: 0,
    biggestLoss: 0,
    currentStreak: 0,
    longestWinStreak: 0,
    longestLoseStreak: 0,
  };

  constructor() {
    this.generateMockHistory();
  }

  private generateMockHistory(): void {
    // Generate some mock game history for demonstration
    const mockGames = [
      { choice: 'heads' as const, result: 'heads' as const, bet: 10, won: true },
      { choice: 'tails' as const, result: 'heads' as const, bet: 20, won: false },
      { choice: 'heads' as const, result: 'heads' as const, bet: 15, won: true },
      { choice: 'tails' as const, result: 'tails' as const, bet: 25, won: true },
      { choice: 'heads' as const, result: 'tails' as const, bet: 30, won: false },
    ];

    let balance = 1000;
    mockGames.forEach((game, index) => {
      const gameResult: GameHistory = {
        id: `mock-${index}`,
        timestamp: new Date(Date.now() - (mockGames.length - index) * 60000), // 1 minute apart
        playerChoice: game.choice,
        result: game.result,
        betAmount: game.bet,
        won: game.won,
        balanceAfter: game.won ? balance + game.bet : balance - game.bet,
      };
      
      balance = gameResult.balanceAfter;
      this.gameHistory.push(gameResult);
    });

    this.updatePlayerStats();
  }

  public addGameResult(
    playerChoice: 'heads' | 'tails',
    result: 'heads' | 'tails',
    betAmount: number,
    balanceAfter: number
  ): void {
    const won = playerChoice === result;
    const gameResult: GameHistory = {
      id: `game-${Date.now()}`,
      timestamp: new Date(),
      playerChoice,
      result,
      betAmount,
      won,
      balanceAfter,
    };

    this.gameHistory.push(gameResult);
    this.updatePlayerStats();

    // Keep only last 50 games to prevent memory issues
    if (this.gameHistory.length > 50) {
      this.gameHistory = this.gameHistory.slice(-50);
    }
  }

  private updatePlayerStats(): void {
    const stats = this.playerStats;
    stats.totalGames = this.gameHistory.length;
    stats.gamesWon = this.gameHistory.filter(game => game.won).length;
    stats.gamesLost = stats.totalGames - stats.gamesWon;
    stats.winRate = stats.totalGames > 0 ? (stats.gamesWon / stats.totalGames) * 100 : 0;

    stats.totalWinnings = this.gameHistory
      .filter(game => game.won)
      .reduce((sum, game) => sum + game.betAmount, 0);

    stats.totalLosses = this.gameHistory
      .filter(game => !game.won)
      .reduce((sum, game) => sum + game.betAmount, 0);

    stats.biggestWin = Math.max(0, ...this.gameHistory
      .filter(game => game.won)
      .map(game => game.betAmount));

    stats.biggestLoss = Math.max(0, ...this.gameHistory
      .filter(game => !game.won)
      .map(game => game.betAmount));

    // Calculate current streak
    let currentStreak = 0;
    for (let i = this.gameHistory.length - 1; i >= 0; i--) {
      const game = this.gameHistory[i];
      if (i === this.gameHistory.length - 1) {
        currentStreak = game.won ? 1 : -1;
      } else {
        const prevWon = currentStreak > 0;
        if (game.won === prevWon) {
          currentStreak += game.won ? 1 : -1;
        } else {
          break;
        }
      }
    }
    stats.currentStreak = currentStreak;

    // Calculate longest streaks
    let longestWin = 0;
    let longestLose = 0;
    let currentWinStreak = 0;
    let currentLoseStreak = 0;

    this.gameHistory.forEach(game => {
      if (game.won) {
        currentWinStreak++;
        currentLoseStreak = 0;
        longestWin = Math.max(longestWin, currentWinStreak);
      } else {
        currentLoseStreak++;
        currentWinStreak = 0;
        longestLose = Math.max(longestLose, currentLoseStreak);
      }
    });

    stats.longestWinStreak = longestWin;
    stats.longestLoseStreak = longestLose;
  }

  public getGameHistory(): GameHistory[] {
    return [...this.gameHistory];
  }

  public getPlayerStats(): PlayerStats {
    return { ...this.playerStats };
  }

  public getRecentGames(count: number = 10): GameHistory[] {
    return this.gameHistory.slice(-count);
  }

  public generateCoinFlipResult(): 'heads' | 'tails' {
    // Pure random for fair gameplay
    return Math.random() < 0.5 ? 'heads' : 'tails';
  }

  public generateWeightedResult(bias: 'heads' | 'tails' = 'heads', weight: number = 0.6): 'heads' | 'tails' {
    // For testing purposes - allows biased results
    const random = Math.random();
    if (bias === 'heads') {
      return random < weight ? 'heads' : 'tails';
    } else {
      return random < weight ? 'tails' : 'heads';
    }
  }

  public clearHistory(): void {
    this.gameHistory = [];
    this.playerStats = {
      totalGames: 0,
      gamesWon: 0,
      gamesLost: 0,
      totalWinnings: 0,
      totalLosses: 0,
      winRate: 0,
      biggestWin: 0,
      biggestLoss: 0,
      currentStreak: 0,
      longestWinStreak: 0,
      longestLoseStreak: 0,
    };
  }
}
