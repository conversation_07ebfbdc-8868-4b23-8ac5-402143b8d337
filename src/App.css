.app {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  font-family: 'Arial', sans-serif;
}

.app-header {
  text-align: center;
  margin-bottom: 30px;
  color: white;
}

.app-header h1 {
  font-size: 2.5rem;
  margin-bottom: 10px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.app-header p {
  font-size: 1.2rem;
  opacity: 0.9;
  margin: 0;
}

.app-main {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30px;
}

.coin-toss-container {
  background: rgba(255, 255, 255, 0.1);
  padding: 20px;
  border-radius: 15px;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.game-controls {
  color: white;
}

.game-info h3 {
  margin: 0 0 15px 0;
  font-size: 1.5rem;
}

.result {
  padding: 10px;
  border-radius: 8px;
  margin: 10px 0;
}

.result.win {
  background-color: rgba(46, 204, 113, 0.2);
  border: 2px solid #2ecc71;
  color: #2ecc71;
}

.result.lose {
  background-color: rgba(231, 76, 60, 0.2);
  border: 2px solid #e74c3c;
  color: #e74c3c;
}

.betting-controls {
  margin: 20px 0;
}

.betting-controls label {
  font-weight: bold;
  color: white;
}

.betting-controls input {
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  background: rgba(255, 255, 255, 0.9);
}

.choice-buttons button {
  transition: all 0.3s ease;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.choice-buttons button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.playing-indicator p {
  animation: pulse 1.5s ease-in-out infinite alternate;
}

@keyframes pulse {
  from {
    opacity: 0.7;
  }
  to {
    opacity: 1;
  }
}

.game-stats {
  background: rgba(255, 255, 255, 0.1);
  padding: 20px;
  border-radius: 15px;
  backdrop-filter: blur(10px);
  color: white;
  min-width: 300px;
}

.game-stats h3 {
  margin-top: 0;
  text-align: center;
  font-size: 1.3rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
}

.stat-label {
  font-size: 0.9rem;
  opacity: 0.8;
  margin-bottom: 5px;
}

.stat-value {
  font-size: 1.1rem;
  font-weight: bold;
  color: #f1c40f;
}

.app-footer {
  text-align: center;
  margin-top: 40px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
}

.app-footer p {
  margin: 5px 0;
}
