import React, { useEffect, useRef, useState } from 'react';
import { CoinTossGame } from '../game/CoinTossGame';
import type { GameState } from '../game/CoinTossGame';
import GameStats from './GameStats';
import './GameStats.css';

interface CoinTossCanvasProps {
  onGameStateChange?: (state: GameState) => void;
}

export const CoinTossCanvas: React.FC<CoinTossCanvasProps> = ({ onGameStateChange }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const gameRef = useRef<CoinTossGame | null>(null);
  const [gameState, setGameState] = useState<GameState>({
    balance: 1000,
    currentBet: 10,
    isPlaying: false,
    result: null,
    playerChoice: null,
  });
  const [showStats, setShowStats] = useState(false);

  useEffect(() => {
    if (canvasRef.current && !gameRef.current) {
      // Initialize the game
      gameRef.current = new CoinTossGame(canvasRef.current);
      
      // Set up state change listener
      gameRef.current.onStateChange((newState) => {
        setGameState(newState);
        if (onGameStateChange) {
          onGameStateChange(newState);
        }
      });

      // Get initial state
      setGameState(gameRef.current.getGameState());
    }

    // Cleanup on unmount
    return () => {
      if (gameRef.current) {
        gameRef.current.destroy();
        gameRef.current = null;
      }
    };
  }, [onGameStateChange]);

  const handleCoinToss = async (choice: 'heads' | 'tails', betAmount: number) => {
    if (gameRef.current && !gameState.isPlaying) {
      try {
        await gameRef.current.playRound(choice, betAmount);
      } catch (error) {
        console.error('Error playing round:', error);
      }
    }
  };

  return (
    <div className="coin-toss-container">
      <canvas
        ref={canvasRef}
        className="coin-toss-canvas"
        style={{
          border: '2px solid #34495e',
          borderRadius: '8px',
          boxShadow: '0 4px 8px rgba(0, 0, 0, 0.2)',
        }}
      />
      
      {/* Game Controls */}
      <div className="game-controls" style={{ marginTop: '20px', textAlign: 'center' }}>
        <div className="game-info" style={{ marginBottom: '20px' }}>
          <h3>Balance: ${gameState.balance}</h3>
          {gameState.result && (
            <div className={`result ${gameState.result === gameState.playerChoice ? 'win' : 'lose'}`}>
              <h4>Result: {gameState.result.toUpperCase()}</h4>
              <p>{gameState.result === gameState.playerChoice ? 'You Win!' : 'You Lose!'}</p>
            </div>
          )}
        </div>

        <div className="betting-controls" style={{ marginBottom: '20px' }}>
          <label htmlFor="bet-amount">Bet Amount: </label>
          <input
            id="bet-amount"
            type="number"
            min="1"
            max={gameState.balance}
            defaultValue="10"
            disabled={gameState.isPlaying}
            style={{
              margin: '0 10px',
              padding: '5px',
              borderRadius: '4px',
              border: '1px solid #ccc',
            }}
          />
        </div>

        <div className="choice-buttons">
          <button
            onClick={() => {
              const betInput = document.getElementById('bet-amount') as HTMLInputElement;
              const betAmount = parseInt(betInput.value) || 10;
              handleCoinToss('heads', betAmount);
            }}
            disabled={gameState.isPlaying}
            style={{
              margin: '0 10px',
              padding: '10px 20px',
              fontSize: '16px',
              backgroundColor: '#3498db',
              color: 'white',
              border: 'none',
              borderRadius: '5px',
              cursor: gameState.isPlaying ? 'not-allowed' : 'pointer',
              opacity: gameState.isPlaying ? 0.6 : 1,
            }}
          >
            {gameState.isPlaying ? 'Playing...' : 'Bet on Heads'}
          </button>
          
          <button
            onClick={() => {
              const betInput = document.getElementById('bet-amount') as HTMLInputElement;
              const betAmount = parseInt(betInput.value) || 10;
              handleCoinToss('tails', betAmount);
            }}
            disabled={gameState.isPlaying}
            style={{
              margin: '0 10px',
              padding: '10px 20px',
              fontSize: '16px',
              backgroundColor: '#e74c3c',
              color: 'white',
              border: 'none',
              borderRadius: '5px',
              cursor: gameState.isPlaying ? 'not-allowed' : 'pointer',
              opacity: gameState.isPlaying ? 0.6 : 1,
            }}
          >
            {gameState.isPlaying ? 'Playing...' : 'Bet on Tails'}
          </button>
        </div>

        {gameState.isPlaying && (
          <div className="playing-indicator" style={{ marginTop: '20px' }}>
            <p style={{ color: '#f39c12', fontWeight: 'bold' }}>
              🎲 Animation in progress... Watch the coin flip!
            </p>
          </div>
        )}

        <div className="additional-controls" style={{ marginTop: '20px' }}>
          <button
            onClick={() => setShowStats(true)}
            disabled={gameState.isPlaying}
            style={{
              padding: '10px 20px',
              fontSize: '14px',
              backgroundColor: '#9b59b6',
              color: 'white',
              border: 'none',
              borderRadius: '5px',
              cursor: gameState.isPlaying ? 'not-allowed' : 'pointer',
              opacity: gameState.isPlaying ? 0.6 : 1,
            }}
          >
            📊 View Statistics
          </button>
        </div>
      </div>

      {/* Statistics Modal */}
      {gameRef.current && (
        <GameStats
          stats={gameRef.current.getPlayerStats()}
          recentGames={gameRef.current.getRecentGames(10)}
          isVisible={showStats}
          onClose={() => setShowStats(false)}
        />
      )}
    </div>
  );
};

export default CoinTossCanvas;
