import React, { useEffect, useRef, useState } from 'react';
import { CoinTossGame } from '../game/CoinTossGame';
import type { GameState } from '../game/CoinTossGame';
import './ModernCoinToss.css';

interface CoinTossCanvasProps {
  onGameStateChange?: (state: GameState) => void;
}

export const CoinTossCanvas: React.FC<CoinTossCanvasProps> = ({ onGameStateChange }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const gameRef = useRef<CoinTossGame | null>(null);
  const [gameState, setGameState] = useState<GameState>({
    balance: 1000,
    currentBet: 10,
    isPlaying: false,
    result: null,
    playerChoice: null,
  });


  useEffect(() => {
    let mounted = true;

    const initializeGame = async () => {
      if (canvasRef.current && !gameRef.current && mounted) {
        try {
          // Initialize the game
          gameRef.current = new CoinTossGame(canvasRef.current);

          // Set up state change listener
          gameRef.current.onStateChange((newState) => {
            if (mounted) {
              setGameState(newState);
              if (onGameStateChange) {
                onGameStateChange(newState);
              }
            }
          });

          // Get initial state
          if (mounted) {
            setGameState(gameRef.current.getGameState());
          }
        } catch (error) {
          console.error('Failed to initialize game:', error);
        }
      }
    };

    initializeGame();

    // Cleanup on unmount
    return () => {
      mounted = false;
      if (gameRef.current) {
        try {
          gameRef.current.destroy();
        } catch (error) {
          console.warn('Error during game cleanup:', error);
        }
        gameRef.current = null;
      }
    };
  }, [onGameStateChange]);

  const handleCoinToss = async (choice: 'heads' | 'tails', betAmount: number) => {
    if (gameRef.current && !gameState.isPlaying) {
      try {
        await gameRef.current.playRound(choice, betAmount);
      } catch (error) {
        console.error('Error playing round:', error);
      }
    }
  };

  return (
    <div className="coin-toss-container">
      <canvas
        ref={canvasRef}
        className="coin-toss-canvas"
        style={{
          border: '2px solid #34495e',
          borderRadius: '8px',
          boxShadow: '0 4px 8px rgba(0, 0, 0, 0.2)',
        }}
      />
      
      {/* Modern Clean UI */}
      <div className="modern-controls">
        <div className="balance-display">
          <span className="balance-amount">${gameState.balance}</span>
        </div>

        {gameState.result && (
          <div className={`result-display ${gameState.result === gameState.playerChoice ? 'win' : 'lose'}`}>
            <div className="result-text">
              {gameState.result === gameState.playerChoice ? 'WIN' : 'LOSE'}
            </div>
            <div className="result-amount">
              {gameState.result === gameState.playerChoice ? '+' : '-'}${gameState.currentBet}
            </div>
          </div>
        )}

        <div className="bet-controls">
          <input
            id="bet-amount"
            type="range"
            min="10"
            max={Math.min(gameState.balance, 100)}
            defaultValue="25"
            disabled={gameState.isPlaying}
            className="bet-slider"
          />
          <div className="bet-amount-display">
            ${document.getElementById('bet-amount') ? (document.getElementById('bet-amount') as HTMLInputElement).value : '25'}
          </div>
        </div>

        <div className="flip-buttons">
          <button
            className="flip-btn heads-btn"
            onClick={() => {
              const betInput = document.getElementById('bet-amount') as HTMLInputElement;
              const betAmount = parseInt(betInput.value) || 25;
              handleCoinToss('heads', betAmount);
            }}
            disabled={gameState.isPlaying}
          >
            HEADS
          </button>

          <button
            className="flip-btn tails-btn"
            onClick={() => {
              const betInput = document.getElementById('bet-amount') as HTMLInputElement;
              const betAmount = parseInt(betInput.value) || 25;
              handleCoinToss('tails', betAmount);
            }}
            disabled={gameState.isPlaying}
          >
            TAILS
          </button>
        </div>
      </div>


    </div>
  );
};

export default CoinTossCanvas;
