import React, { useState } from 'react';
import type { PlayerStats, GameHistory } from '../data/MockData';

interface GameStatsProps {
  stats: PlayerStats;
  recentGames: GameHistory[];
  isVisible: boolean;
  onClose: () => void;
}

export const GameStats: React.FC<GameStatsProps> = ({ stats, recentGames, isVisible, onClose }) => {
  const [activeTab, setActiveTab] = useState<'stats' | 'history'>('stats');

  if (!isVisible) return null;

  const formatCurrency = (amount: number) => `$${amount.toFixed(2)}`;
  const formatPercentage = (value: number) => `${value.toFixed(1)}%`;
  const formatDate = (date: Date) => date.toLocaleTimeString();

  return (
    <div className="stats-overlay">
      <div className="stats-modal">
        <div className="stats-header">
          <h2>🎲 Game Statistics</h2>
          <button className="close-button" onClick={onClose}>×</button>
        </div>

        <div className="stats-tabs">
          <button 
            className={`tab-button ${activeTab === 'stats' ? 'active' : ''}`}
            onClick={() => setActiveTab('stats')}
          >
            Statistics
          </button>
          <button 
            className={`tab-button ${activeTab === 'history' ? 'active' : ''}`}
            onClick={() => setActiveTab('history')}
          >
            Recent Games
          </button>
        </div>

        <div className="stats-content">
          {activeTab === 'stats' && (
            <div className="stats-grid">
              <div className="stat-card">
                <div className="stat-icon">🎯</div>
                <div className="stat-info">
                  <div className="stat-label">Total Games</div>
                  <div className="stat-value">{stats.totalGames}</div>
                </div>
              </div>

              <div className="stat-card">
                <div className="stat-icon">🏆</div>
                <div className="stat-info">
                  <div className="stat-label">Win Rate</div>
                  <div className="stat-value">{formatPercentage(stats.winRate)}</div>
                </div>
              </div>

              <div className="stat-card">
                <div className="stat-icon">✅</div>
                <div className="stat-info">
                  <div className="stat-label">Games Won</div>
                  <div className="stat-value">{stats.gamesWon}</div>
                </div>
              </div>

              <div className="stat-card">
                <div className="stat-icon">❌</div>
                <div className="stat-info">
                  <div className="stat-label">Games Lost</div>
                  <div className="stat-value">{stats.gamesLost}</div>
                </div>
              </div>

              <div className="stat-card">
                <div className="stat-icon">💰</div>
                <div className="stat-info">
                  <div className="stat-label">Total Winnings</div>
                  <div className="stat-value">{formatCurrency(stats.totalWinnings)}</div>
                </div>
              </div>

              <div className="stat-card">
                <div className="stat-icon">💸</div>
                <div className="stat-info">
                  <div className="stat-label">Total Losses</div>
                  <div className="stat-value">{formatCurrency(stats.totalLosses)}</div>
                </div>
              </div>

              <div className="stat-card">
                <div className="stat-icon">🔥</div>
                <div className="stat-info">
                  <div className="stat-label">Current Streak</div>
                  <div className={`stat-value ${stats.currentStreak > 0 ? 'positive' : 'negative'}`}>
                    {stats.currentStreak > 0 ? `+${stats.currentStreak}` : stats.currentStreak}
                  </div>
                </div>
              </div>

              <div className="stat-card">
                <div className="stat-icon">⭐</div>
                <div className="stat-info">
                  <div className="stat-label">Biggest Win</div>
                  <div className="stat-value">{formatCurrency(stats.biggestWin)}</div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'history' && (
            <div className="history-list">
              {recentGames.length === 0 ? (
                <div className="no-games">No games played yet</div>
              ) : (
                recentGames.map((game) => (
                  <div key={game.id} className={`history-item ${game.won ? 'win' : 'loss'}`}>
                    <div className="game-info">
                      <div className="game-choice">
                        <span className="choice-label">Your choice:</span>
                        <span className="choice-value">{game.playerChoice.toUpperCase()}</span>
                      </div>
                      <div className="game-result">
                        <span className="result-label">Result:</span>
                        <span className="result-value">{game.result.toUpperCase()}</span>
                      </div>
                    </div>
                    <div className="game-details">
                      <div className="bet-amount">{formatCurrency(game.betAmount)}</div>
                      <div className={`outcome ${game.won ? 'win' : 'loss'}`}>
                        {game.won ? '+' : '-'}{formatCurrency(game.betAmount)}
                      </div>
                      <div className="game-time">{formatDate(game.timestamp)}</div>
                    </div>
                  </div>
                ))
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default GameStats;
