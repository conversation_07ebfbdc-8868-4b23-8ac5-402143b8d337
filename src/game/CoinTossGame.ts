import * as PIXI from 'pixi.js';
import { AnimationEngine } from '../animation/AnimationEngine';
import { MockDataService } from '../data/MockData';
import type { GameHistory, PlayerStats } from '../data/MockData';

export interface GameState {
  balance: number;
  currentBet: number;
  isPlaying: boolean;
  result: 'heads' | 'tails' | null;
  playerChoice: 'heads' | 'tails' | null;
}

export interface GameAssets {
  hand: PIXI.Sprite;
  coin: PIXI.Sprite;
  table: PIXI.Sprite;
  background: PIXI.Sprite;
}

export class CoinTossGame {
  private app: PIXI.Application;
  private animationEngine: AnimationEngine;
  private assets: Partial<GameAssets> = {};
  private gameState: GameState;
  private mockDataService: MockDataService;
  private onStateChangeCallback?: (state: GameState) => void;

  // Game dimensions and positions
  private readonly GAME_WIDTH = 800;
  private readonly GAME_HEIGHT = 600;
  private readonly TABLE_Y = 450;
  private readonly HAND_START_X = 200;
  private readonly HAND_START_Y = 300;
  private readonly COIN_START_X = 220;
  private readonly COIN_START_Y = 280;

  constructor(canvas: HTMLCanvasElement) {
    this.app = new PIXI.Application();
    this.animationEngine = new AnimationEngine();
    this.mockDataService = new MockDataService();
    this.gameState = {
      balance: 1000,
      currentBet: 10,
      isPlaying: false,
      result: null,
      playerChoice: null,
    };

    this.initializeApp(canvas);
  }

  private async initializeApp(canvas: HTMLCanvasElement): Promise<void> {
    await this.app.init({
      canvas,
      width: this.GAME_WIDTH,
      height: this.GAME_HEIGHT,
      backgroundColor: 0x2c3e50,
      antialias: true,
    });

    // Create placeholder assets (will be replaced with actual sprites)
    this.createPlaceholderAssets();
    this.setupScene();
  }

  private createPlaceholderAssets(): void {
    // Create detailed visual assets
    const handTexture = this.createHandTexture();
    const coinTexture = this.createCoinTexture();
    const tableTexture = this.createTableTexture();
    const backgroundTexture = this.createBackgroundTexture();

    this.assets.hand = new PIXI.Sprite(handTexture);
    this.assets.coin = new PIXI.Sprite(coinTexture);
    this.assets.table = new PIXI.Sprite(tableTexture);
    this.assets.background = new PIXI.Sprite(backgroundTexture);

    // Set anchors for proper rotation and positioning
    this.assets.coin.anchor.set(0.5);
    this.assets.hand.anchor.set(0.5, 1); // Anchor at bottom center for natural rotation
    this.assets.table.anchor.set(0.5, 0);
  }

  private createHandTexture(): PIXI.Texture {
    const graphics = new PIXI.Graphics();

    // Hand shape with fingers
    graphics.roundRect(10, 0, 40, 60, 15);
    graphics.fill(0xfdbcb4); // Skin color

    // Thumb
    graphics.roundRect(0, 20, 20, 30, 10);
    graphics.fill(0xfdbcb4);

    // Add some shading
    graphics.roundRect(15, 5, 30, 50, 12);
    graphics.fill(0xf4a688); // Slightly darker skin

    return this.app.renderer.generateTexture(graphics);
  }

  private createCoinTexture(): PIXI.Texture {
    const graphics = new PIXI.Graphics();
    const radius = 25;

    // Outer gold ring
    graphics.circle(radius, radius, radius);
    graphics.fill(0xffd700); // Gold

    // Inner circle with slight gradient effect
    graphics.circle(radius, radius, radius - 3);
    graphics.fill(0xffed4e); // Lighter gold

    // Add "H" for heads side (we'll handle tails in animation)
    graphics.rect(radius - 8, radius - 10, 3, 20);
    graphics.rect(radius + 5, radius - 10, 3, 20);
    graphics.rect(radius - 8, radius - 2, 16, 3);
    graphics.fill(0xb8860b); // Dark gold for text

    return this.app.renderer.generateTexture(graphics);
  }

  private createTableTexture(): PIXI.Texture {
    const graphics = new PIXI.Graphics();
    const width = 600;
    const height = 200;

    // Table surface base
    graphics.rect(0, 0, width, height);
    graphics.fill(0x8b4513); // Brown

    // Wood grain pattern
    for (let i = 0; i < 8; i++) {
      const y = i * 25;
      graphics.rect(0, y, width, 1);
      graphics.fill(0x654321); // Darker brown

      // Add some irregular grain lines
      for (let j = 0; j < 3; j++) {
        const x = Math.random() * width;
        const grainWidth = Math.random() * 100 + 50;
        graphics.rect(x, y + Math.random() * 20, grainWidth, 0.5);
        graphics.fill(0x5d4037); // Even darker
      }
    }

    // Table edge with 3D effect
    graphics.rect(0, 0, width, 8);
    graphics.fill(0xa0522d); // Lighter brown highlight

    // Shadow under the edge
    graphics.rect(0, 8, width, 4);
    graphics.fill(0x6d4c41); // Shadow

    // Worn spots and character marks
    for (let i = 0; i < 10; i++) {
      const x = Math.random() * width;
      const y = Math.random() * height;
      const size = Math.random() * 20 + 10;
      graphics.circle(x, y, size);
      graphics.fill(0x795548); // Worn wood color
    }

    // Table legs indication (just the tops visible)
    graphics.rect(50, height - 20, 15, 20);
    graphics.rect(width - 65, height - 20, 15, 20);
    graphics.fill(0x5d4037); // Darker wood for legs

    return this.app.renderer.generateTexture(graphics);
  }

  private createBackgroundTexture(): PIXI.Texture {
    const graphics = new PIXI.Graphics();

    // Create gradient background effect
    graphics.rect(0, 0, this.GAME_WIDTH, this.GAME_HEIGHT);
    graphics.fill(0x1a252f); // Very dark blue-gray

    // Add lighter area in center (like a spotlight)
    graphics.circle(this.GAME_WIDTH / 2, this.GAME_HEIGHT / 2, 300);
    graphics.fill(0x2c3e50); // Lighter blue-gray

    // Vignette effect (darker edges)
    const vignette = new PIXI.Graphics();
    vignette.rect(0, 0, this.GAME_WIDTH, this.GAME_HEIGHT);
    vignette.fill(0x000000); // Black

    // Cut out center circle for vignette
    vignette.circle(this.GAME_WIDTH / 2, this.GAME_HEIGHT / 2, 350);
    vignette.cut();

    // Add atmospheric particles/dust
    for (let i = 0; i < 30; i++) {
      const x = Math.random() * this.GAME_WIDTH;
      const y = Math.random() * this.GAME_HEIGHT;
      const size = Math.random() * 2 + 0.5;
      const alpha = Math.random() * 0.3 + 0.1;
      graphics.circle(x, y, size);
      graphics.fill({ color: 0x7f8c8d, alpha }); // Light gray with transparency
    }

    // Add some subtle texture lines
    for (let i = 0; i < 15; i++) {
      const x = Math.random() * this.GAME_WIDTH;
      const y1 = Math.random() * this.GAME_HEIGHT;
      const y2 = y1 + Math.random() * 100 + 50;
      graphics.moveTo(x, y1);
      graphics.lineTo(x, y2);
      graphics.stroke({ color: 0x34495e, width: 0.5, alpha: 0.3 });
    }

    return this.app.renderer.generateTexture(graphics);
  }

  private setupScene(): void {
    if (!this.assets.background || !this.assets.table || !this.assets.hand || !this.assets.coin) {
      throw new Error('Assets not created');
    }

    // Add background
    this.app.stage.addChild(this.assets.background);

    // Position and add table
    this.assets.table.x = this.GAME_WIDTH / 2;
    this.assets.table.y = this.TABLE_Y;
    this.app.stage.addChild(this.assets.table);

    // Position hand at starting position
    this.assets.hand.x = this.HAND_START_X;
    this.assets.hand.y = this.HAND_START_Y;
    this.app.stage.addChild(this.assets.hand);

    // Position coin at starting position (in hand)
    this.assets.coin.x = this.COIN_START_X;
    this.assets.coin.y = this.COIN_START_Y;
    this.app.stage.addChild(this.assets.coin);
  }

  public async playRound(playerChoice: 'heads' | 'tails', betAmount: number): Promise<void> {
    if (this.gameState.isPlaying) {
      throw new Error('Game is already playing');
    }

    if (betAmount > this.gameState.balance) {
      throw new Error('Insufficient balance');
    }

    // Update game state
    this.gameState.isPlaying = true;
    this.gameState.playerChoice = playerChoice;
    this.gameState.currentBet = betAmount;
    this.gameState.result = null;
    this.notifyStateChange();

    // Generate result using mock data service
    const result = this.mockDataService.generateCoinFlipResult();

    try {
      await this.playAnimation(result);
      
      // Update balance based on result
      if (result === playerChoice) {
        this.gameState.balance += betAmount;
      } else {
        this.gameState.balance -= betAmount;
      }

      this.gameState.result = result;

      // Record the game result in mock data service
      this.mockDataService.addGameResult(
        playerChoice,
        result,
        betAmount,
        this.gameState.balance
      );
    } finally {
      this.gameState.isPlaying = false;
      this.notifyStateChange();
    }
  }

  private async playAnimation(result: 'heads' | 'tails'): Promise<void> {
    if (!this.assets.hand || !this.assets.coin || !this.assets.table) {
      throw new Error('Assets not initialized');
    }

    // Reset positions
    this.resetPositions();

    // Create animation sequence
    this.animationEngine.createSequence();

    // Calculate positions
    const peakX = this.GAME_WIDTH / 2;
    const peakY = 150;
    const landX = this.GAME_WIDTH / 2;
    const landY = this.TABLE_Y - 20;

    // Hand positions
    const handThrowX = this.HAND_START_X + 50;
    const handThrowY = this.HAND_START_Y - 30;
    const handSlamX = landX;
    const handSlamY = this.TABLE_Y + 10;
    const handOpenX = landX + 80;
    const handOpenY = this.TABLE_Y + 10;

    // Add coin flip animation with result
    this.animationEngine.createCoinFlipAnimation(
      this.assets.coin,
      this.COIN_START_X,
      this.COIN_START_Y,
      peakX,
      peakY,
      landX,
      landY,
      2.0,
      result
    );

    // Add hand animation
    this.animationEngine.createHandAnimation(
      this.assets.hand,
      handThrowX,
      handThrowY,
      handSlamX,
      handSlamY,
      handOpenX,
      handOpenY
    );

    // Add table shake when coin lands
    this.animationEngine.createTableShakeAnimation(this.assets.table);

    // Play the complete animation sequence
    await this.animationEngine.playSequence();
  }

  private resetPositions(): void {
    if (!this.assets.hand || !this.assets.coin || !this.assets.table) return;

    this.assets.hand.x = this.HAND_START_X;
    this.assets.hand.y = this.HAND_START_Y;
    this.assets.hand.rotation = 0;

    this.assets.coin.x = this.COIN_START_X;
    this.assets.coin.y = this.COIN_START_Y;
    this.assets.coin.rotation = 0;

    this.assets.table.x = this.GAME_WIDTH / 2;
    this.assets.table.y = this.TABLE_Y;
  }

  public getGameState(): GameState {
    return { ...this.gameState };
  }

  public getGameHistory(): GameHistory[] {
    return this.mockDataService.getGameHistory();
  }

  public getPlayerStats(): PlayerStats {
    return this.mockDataService.getPlayerStats();
  }

  public getRecentGames(count: number = 5): GameHistory[] {
    return this.mockDataService.getRecentGames(count);
  }

  public onStateChange(callback: (state: GameState) => void): void {
    this.onStateChangeCallback = callback;
  }

  private notifyStateChange(): void {
    if (this.onStateChangeCallback) {
      this.onStateChangeCallback(this.getGameState());
    }
  }

  public destroy(): void {
    this.animationEngine.stopSequence();
    this.app.destroy(true);
  }
}
