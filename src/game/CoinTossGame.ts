import * as PIXI from 'pixi.js';
import { AnimationEngine } from '../animation/AnimationEngine';
import { MockDataService } from '../data/MockData';
import type { GameHistory, PlayerStats } from '../data/MockData';

export interface GameState {
  balance: number;
  currentBet: number;
  isPlaying: boolean;
  result: 'heads' | 'tails' | null;
  playerChoice: 'heads' | 'tails' | null;
}

export interface GameAssets {
  hand: PIXI.Sprite;
  coin: PIXI.Sprite;
  table: PIXI.Sprite;
  background: PIXI.Sprite;
}

export class CoinTossGame {
  private app!: PIXI.Application;
  private animationEngine: AnimationEngine;
  private assets: Partial<GameAssets> = {};
  private gameState: GameState;
  private mockDataService: MockDataService;
  private onStateChangeCallback?: (state: GameState) => void;

  // Game dimensions and positions
  private readonly GAME_WIDTH = 1000;
  private readonly GAME_HEIGHT = 700;
  private readonly COIN_SIZE = 80;
  private readonly HAND_SIZE = 120;

  constructor(canvas: HTMLCanvasElement) {
    this.animationEngine = new AnimationEngine();
    this.mockDataService = new MockDataService();
    this.gameState = {
      balance: 1000,
      currentBet: 10,
      isPlaying: false,
      result: null,
      playerChoice: null,
    };

    this.initializeApp(canvas);
  }

  private async initializeApp(canvas: HTMLCanvasElement): Promise<void> {
    try {
      this.app = new PIXI.Application();

      await this.app.init({
        canvas,
        width: this.GAME_WIDTH,
        height: this.GAME_HEIGHT,
        backgroundColor: 0x2c3e50,
        antialias: true,
      });

      // Wait a frame to ensure everything is properly initialized
      await new Promise(resolve => requestAnimationFrame(resolve));

      // Verify the app is properly initialized
      if (!this.app.stage || !this.app.renderer) {
        throw new Error('PIXI Application components not properly initialized');
      }

      // Create placeholder assets (will be replaced with actual sprites)
      this.createPlaceholderAssets();
      this.setupScene();
    } catch (error) {
      console.error('Failed to initialize PIXI application:', error);
      throw error;
    }
  }

  private createPlaceholderAssets(): void {
    // Create modern 2.5D visual assets
    const coinTexture = this.createModernCoin();
    const handTexture = this.createModernHand();
    const backgroundTexture = this.createModernBackground();

    this.assets.coin = new PIXI.Sprite(coinTexture);
    this.assets.hand = new PIXI.Sprite(handTexture);
    this.assets.background = new PIXI.Sprite(backgroundTexture);

    // Set anchors for proper rotation and positioning
    this.assets.coin.anchor.set(0.5);
    this.assets.coin.width = this.COIN_SIZE;
    this.assets.coin.height = this.COIN_SIZE;

    this.assets.hand.anchor.set(0.5, 0.8);
    this.assets.hand.width = this.HAND_SIZE;
    this.assets.hand.height = this.HAND_SIZE;
  }

  private createModernHand(): PIXI.Texture {
    const graphics = new PIXI.Graphics();
    const size = 120;

    // Modern hand silhouette inspired by the reference image
    // Palm base
    graphics.ellipse(size/2, size*0.7, size*0.35, size*0.25);
    graphics.fill(0x2c3e50); // Dark modern color

    // Thumb
    graphics.ellipse(size*0.2, size*0.5, size*0.12, size*0.2);
    graphics.fill(0x2c3e50);

    // Index finger pointing
    graphics.ellipse(size*0.8, size*0.3, size*0.08, size*0.25);
    graphics.fill(0x2c3e50);

    // Other fingers (simplified)
    graphics.ellipse(size*0.6, size*0.2, size*0.06, size*0.15);
    graphics.fill(0x2c3e50);
    graphics.ellipse(size*0.5, size*0.15, size*0.06, size*0.12);
    graphics.fill(0x2c3e50);
    graphics.ellipse(size*0.4, size*0.18, size*0.06, size*0.1);
    graphics.fill(0x2c3e50);

    // Add subtle highlight for 2.5D effect
    graphics.ellipse(size/2, size*0.7, size*0.3, size*0.2);
    graphics.fill(0x34495e);

    if (!this.app?.renderer) throw new Error('Renderer not available');
    return this.app.renderer.generateTexture(graphics);
  }

  private createModernCoin(): PIXI.Texture {
    const graphics = new PIXI.Graphics();
    const radius = 40;

    // Modern coin with 2.5D effect
    // Outer shadow for depth
    graphics.circle(radius + 2, radius + 2, radius);
    graphics.fill({ color: 0x000000, alpha: 0.2 });

    // Main coin body
    graphics.circle(radius, radius, radius);
    graphics.fill(0xf1c40f); // Modern gold

    // Inner highlight circle for 3D effect
    graphics.circle(radius - 5, radius - 5, radius - 15);
    graphics.fill(0xf39c12); // Darker gold

    // Center highlight
    graphics.circle(radius - 8, radius - 8, radius - 25);
    graphics.fill(0xffd700); // Bright gold

    // Simple modern "₿" symbol (Bitcoin-style)
    const symbolSize = radius * 0.6;
    graphics.rect(radius - symbolSize/4, radius - symbolSize/2, symbolSize/8, symbolSize);
    graphics.rect(radius - symbolSize/3, radius - symbolSize/3, symbolSize/2, symbolSize/8);
    graphics.rect(radius - symbolSize/3, radius, symbolSize/2, symbolSize/8);
    graphics.fill(0x2c3e50); // Dark contrast

    if (!this.app?.renderer) throw new Error('Renderer not available');
    return this.app.renderer.generateTexture(graphics);
  }

  private createModernBackground(): PIXI.Texture {
    const graphics = new PIXI.Graphics();

    // Modern gradient background
    graphics.rect(0, 0, this.GAME_WIDTH, this.GAME_HEIGHT);
    graphics.fill(0x0f1419); // Very dark modern background

    // Subtle radial gradient effect
    const centerX = this.GAME_WIDTH / 2;
    const centerY = this.GAME_HEIGHT / 2;

    // Create concentric circles for depth
    for (let i = 5; i > 0; i--) {
      const radius = (i * 150);
      const alpha = 0.1 - (i * 0.015);
      graphics.circle(centerX, centerY, radius);
      graphics.fill({ color: 0x2c3e50, alpha });
    }

    // Add minimal geometric elements for modern feel
    for (let i = 0; i < 8; i++) {
      const x = Math.random() * this.GAME_WIDTH;
      const y = Math.random() * this.GAME_HEIGHT;
      const size = Math.random() * 3 + 1;
      graphics.circle(x, y, size);
      graphics.fill({ color: 0x3498db, alpha: 0.1 });
    }

    if (!this.app?.renderer) throw new Error('Renderer not available');
    return this.app.renderer.generateTexture(graphics);
  }

  private setupScene(): void {
    // Comprehensive checks
    if (!this.app?.stage || !this.app?.renderer) {
      throw new Error('PIXI Application not properly initialized');
    }

    if (!this.assets.background || !this.assets.hand || !this.assets.coin) {
      throw new Error('Assets not created');
    }

    try {
      // Add background
      this.app.stage.addChild(this.assets.background);

      // Position hand at center-bottom (ready to flip)
      this.assets.hand.x = this.GAME_WIDTH / 2;
      this.assets.hand.y = this.GAME_HEIGHT * 0.8;
      this.app.stage.addChild(this.assets.hand);

      // Position coin in hand initially
      this.assets.coin.x = this.GAME_WIDTH / 2;
      this.assets.coin.y = this.GAME_HEIGHT * 0.7;
      this.app.stage.addChild(this.assets.coin);

      console.log('Modern scene setup completed');
    } catch (error) {
      console.error('Error setting up scene:', error);
      throw error;
    }
  }

  public async playRound(playerChoice: 'heads' | 'tails', betAmount: number): Promise<void> {
    if (this.gameState.isPlaying) {
      throw new Error('Game is already playing');
    }

    if (betAmount > this.gameState.balance) {
      throw new Error('Insufficient balance');
    }

    // Update game state
    this.gameState.isPlaying = true;
    this.gameState.playerChoice = playerChoice;
    this.gameState.currentBet = betAmount;
    this.gameState.result = null;
    this.notifyStateChange();

    // Generate result using mock data service
    const result = this.mockDataService.generateCoinFlipResult();

    try {
      await this.playAnimation(result);
      
      // Update balance based on result
      if (result === playerChoice) {
        this.gameState.balance += betAmount;
      } else {
        this.gameState.balance -= betAmount;
      }

      this.gameState.result = result;

      // Record the game result in mock data service
      this.mockDataService.addGameResult(
        playerChoice,
        result,
        betAmount,
        this.gameState.balance
      );
    } finally {
      this.gameState.isPlaying = false;
      this.notifyStateChange();
    }
  }

  private async playAnimation(result: 'heads' | 'tails'): Promise<void> {
    if (!this.assets.hand || !this.assets.coin || !this.assets.table) {
      throw new Error('Assets not initialized');
    }

    // Reset positions
    this.resetPositions();

    // Create animation sequence
    this.animationEngine.createSequence();

    // Modern physics-based positions
    const centerX = this.GAME_WIDTH / 2;
    const startY = this.GAME_HEIGHT * 0.7;
    const peakY = this.GAME_HEIGHT * 0.2;
    const landY = this.GAME_HEIGHT * 0.6;

    // Simple, clean animation - just coin flip
    this.animationEngine.createCoinFlipAnimation(
      this.assets.coin,
      centerX,
      startY,
      centerX,
      peakY,
      centerX,
      landY,
      1.5, // Faster, more responsive
      result
    );

    // Minimal hand animation - just a subtle gesture
    this.animationEngine.createHandAnimation(
      this.assets.hand,
      centerX,
      this.GAME_HEIGHT * 0.8,
      centerX - 50,
      this.GAME_HEIGHT * 0.75,
      centerX + 100,
      this.GAME_HEIGHT * 0.85
    );

    // Play the complete animation sequence
    await this.animationEngine.playSequence();
  }

  private resetPositions(): void {
    if (!this.assets.hand || !this.assets.coin) return;

    // Reset to modern starting positions
    this.assets.hand.x = this.GAME_WIDTH / 2;
    this.assets.hand.y = this.GAME_HEIGHT * 0.8;
    this.assets.hand.rotation = 0;

    this.assets.coin.x = this.GAME_WIDTH / 2;
    this.assets.coin.y = this.GAME_HEIGHT * 0.7;
    this.assets.coin.rotation = 0;
    this.assets.coin.scale.set(1);
  }

  public getGameState(): GameState {
    return { ...this.gameState };
  }

  public getGameHistory(): GameHistory[] {
    return this.mockDataService.getGameHistory();
  }

  public getPlayerStats(): PlayerStats {
    return this.mockDataService.getPlayerStats();
  }

  public getRecentGames(count: number = 5): GameHistory[] {
    return this.mockDataService.getRecentGames(count);
  }

  public onStateChange(callback: (state: GameState) => void): void {
    this.onStateChangeCallback = callback;
  }

  private notifyStateChange(): void {
    if (this.onStateChangeCallback) {
      this.onStateChangeCallback(this.getGameState());
    }
  }

  public destroy(): void {
    try {
      // Stop any running animations
      if (this.animationEngine) {
        this.animationEngine.stopSequence();
      }

      // Clean up PIXI application
      if (this.app) {
        // Stop the ticker first
        if (this.app.ticker) {
          this.app.ticker.stop();
        }

        // Destroy the application
        this.app.destroy(true, {
          children: true,
          texture: true
        });
      }
    } catch (error) {
      console.warn('Error during cleanup:', error);
    }
  }
}
