import * as PIXI from 'pixi.js';
import { gsap } from 'gsap';

export interface AnimationSequenceStep {
  target: PIXI.DisplayObject;
  duration: number;
  properties: Record<string, any>;
  ease?: string;
  delay?: number;
  onComplete?: () => void;
  onStart?: () => void;
}

export interface AnimationSequence {
  steps: AnimationSequenceStep[];
  onComplete?: () => void;
}

export class AnimationEngine {
  private timeline: gsap.core.Timeline | null = null;
  private isPlaying = false;

  constructor() {
    // Initialize GSAP with custom easing functions for realistic physics
    this.registerCustomEases();
  }

  private registerCustomEases() {
    // Custom easing for coin physics
    gsap.registerEase("coinFlip", (progress: number) => {
      // Simulate gravity with acceleration
      return progress < 0.5 
        ? 2 * progress * progress 
        : 1 - Math.pow(-2 * progress + 2, 3) / 2;
    });

    gsap.registerEase("handSlam", (progress: number) => {
      // Quick acceleration then sudden stop
      return progress < 0.8 
        ? Math.pow(progress / 0.8, 2) 
        : 1 - Math.pow((1 - progress) / 0.2, 3);
    });

    gsap.registerEase("coinBounce", (progress: number) => {
      // Bouncing effect for coin landing
      const bounces = 3;
      const decay = 0.7;
      return 1 - Math.abs(Math.sin(progress * Math.PI * bounces)) * Math.pow(decay, progress * bounces);
    });
  }

  public createSequence(): gsap.core.Timeline {
    this.timeline = gsap.timeline({
      paused: true,
      onComplete: () => {
        this.isPlaying = false;
      }
    });
    return this.timeline;
  }

  public addToSequence(
    target: PIXI.DisplayObject,
    properties: Record<string, any>,
    duration: number,
    options: {
      ease?: string;
      delay?: number;
      onComplete?: () => void;
      onStart?: () => void;
      position?: string | number;
    } = {}
  ): void {
    if (!this.timeline) {
      throw new Error('No timeline created. Call createSequence() first.');
    }

    const tween = gsap.to(target, {
      ...properties,
      duration,
      ease: options.ease || 'power2.out',
      delay: options.delay || 0,
      onComplete: options.onComplete,
      onStart: options.onStart,
    });

    if (options.position !== undefined) {
      this.timeline.add(tween, options.position);
    } else {
      this.timeline.add(tween);
    }
  }

  public playSequence(): Promise<void> {
    return new Promise((resolve) => {
      if (!this.timeline) {
        resolve();
        return;
      }

      this.isPlaying = true;
      this.timeline.eventCallback('onComplete', () => {
        this.isPlaying = false;
        resolve();
      });
      
      this.timeline.play();
    });
  }

  public pauseSequence(): void {
    if (this.timeline) {
      this.timeline.pause();
      this.isPlaying = false;
    }
  }

  public stopSequence(): void {
    if (this.timeline) {
      this.timeline.kill();
      this.timeline = null;
      this.isPlaying = false;
    }
  }

  public isAnimationPlaying(): boolean {
    return this.isPlaying;
  }

  // Utility methods for common animations
  public createCoinFlipAnimation(
    coin: PIXI.DisplayObject,
    startX: number,
    startY: number,
    peakX: number,
    peakY: number,
    endX: number,
    endY: number,
    duration: number = 2.0,
    result: 'heads' | 'tails' = 'heads'
  ): void {
    if (!this.timeline) return;

    // Set initial position and properties
    coin.x = startX;
    coin.y = startY;
    coin.rotation = 0;
    coin.scale.set(1);

    // Calculate rotation based on result (ensure it lands on the correct side)
    const baseRotations = 6; // Base number of rotations
    const finalRotation = result === 'heads' ? 0 : Math.PI; // 0 for heads, π for tails
    const totalRotation = Math.PI * 2 * baseRotations + finalRotation;

    // Create more realistic arc trajectory
    const upDuration = duration * 0.4; // 40% of time going up
    const downDuration = duration * 0.6; // 60% of time falling down

    // Add slight scale animation for depth effect
    this.addToSequence(coin, {
      scale: { x: 0.8, y: 1.2 }, // Squash effect at start
    }, 0.1, {
      ease: 'power2.out',
    });

    // Up phase - coin goes up with deceleration
    this.addToSequence(coin, {
      x: peakX,
      y: peakY,
      rotation: totalRotation * 0.3, // 30% of rotation going up
      scale: { x: 1, y: 1 }, // Return to normal scale
    }, upDuration, {
      ease: 'power2.out',
      position: 0.1,
    });

    // Peak moment - slight pause and scale effect
    this.addToSequence(coin, {
      scale: { x: 1.1, y: 0.9 }, // Slight stretch at peak
    }, 0.1, {
      ease: 'power1.inOut',
    });

    // Down phase - coin falls with gravity acceleration
    this.addToSequence(coin, {
      x: endX,
      y: endY,
      rotation: totalRotation, // Complete remaining rotation
      scale: { x: 1, y: 1 },
    }, downDuration, {
      ease: 'power2.in', // Gravity acceleration
      position: upDuration + 0.2,
    });

    // Landing bounce effect
    this.addToSequence(coin, {
      scale: { x: 1.2, y: 0.8 }, // Squash on impact
    }, 0.1, {
      ease: 'power2.out',
      position: upDuration + downDuration + 0.2,
    });

    // Settle back to normal
    this.addToSequence(coin, {
      scale: { x: 1, y: 1 },
    }, 0.2, {
      ease: 'elastic.out(1, 0.5)',
    });
  }

  public createHandAnimation(
    hand: PIXI.DisplayObject,
    throwX: number,
    throwY: number,
    slamX: number,
    slamY: number,
    openX: number,
    openY: number
  ): void {
    if (!this.timeline) return;

    // Set initial scale for hand
    hand.scale.set(1);

    // Initial throw motion with anticipation
    this.addToSequence(hand, {
      x: throwX - 10,
      y: throwY + 5,
      rotation: -0.2,
      scale: { x: 0.9, y: 1.1 }, // Slight squash for anticipation
    }, 0.1, {
      ease: 'power2.out',
    });

    // Actual throw motion
    this.addToSequence(hand, {
      x: throwX,
      y: throwY,
      rotation: -0.3,
      scale: { x: 1, y: 1 },
    }, 0.2, {
      ease: 'power2.out',
    });

    // Hand moves to prepare for slam (following coin's arc slightly)
    this.addToSequence(hand, {
      x: slamX - 20,
      y: slamY - 30,
      rotation: -0.1,
    }, 0.8, {
      ease: 'power1.inOut',
      position: 0.8, // Start moving to position
    });

    // Quick slam down with impact
    this.addToSequence(hand, {
      x: slamX,
      y: slamY,
      rotation: 0,
      scale: { x: 1.1, y: 0.9 }, // Impact squash
    }, 0.2, {
      ease: 'power3.in', // Fast slam
      position: 1.8, // When coin is about to land
    });

    // Settle after impact
    this.addToSequence(hand, {
      scale: { x: 1, y: 1 },
    }, 0.1, {
      ease: 'power2.out',
    });

    // Tense wait period - slight trembling effect
    for (let i = 0; i < 8; i++) {
      this.addToSequence(hand, {
        rotation: (i % 2 === 0) ? 0.02 : -0.02,
      }, 0.25, {
        ease: 'power1.inOut',
        position: 2.1 + (i * 0.25), // During the 2-second wait
      });
    }

    // Anticipation before reveal - slight lift
    this.addToSequence(hand, {
      y: slamY - 5,
      rotation: 0,
      scale: { x: 0.95, y: 1.05 },
    }, 0.2, {
      ease: 'power2.out',
      position: 4.0, // Just before reveal
    });

    // Dramatic reveal - hand opens and moves away
    this.addToSequence(hand, {
      x: openX,
      y: openY,
      rotation: 0.3,
      scale: { x: 1, y: 1 },
    }, 0.6, {
      ease: 'back.out(1.7)', // Overshoot for dramatic effect
      position: 4.2, // 2 seconds after slam
    });
  }

  public createTableShakeAnimation(table: PIXI.DisplayObject): void {
    if (!this.timeline) return;

    const originalX = table.x;
    const originalY = table.y;

    // Shake effect when coin lands
    this.addToSequence(table, {
      x: originalX + 3,
      y: originalY + 1,
    }, 0.05, {
      ease: 'power2.out',
      position: 2.2, // When coin lands
    });

    this.addToSequence(table, {
      x: originalX - 2,
      y: originalY - 1,
    }, 0.05, {
      ease: 'power2.out',
    });

    this.addToSequence(table, {
      x: originalX,
      y: originalY,
    }, 0.1, {
      ease: 'power2.out',
    });
  }
}
