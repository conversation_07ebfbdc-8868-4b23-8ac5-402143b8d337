import React, { useState } from 'react';
import CoinTossCanvas from './components/CoinTossCanvas';
import type { GameState } from './game/CoinTossGame';
import './App.css';

function App() {
  const [gameState, setGameState] = useState<GameState | null>(null);

  const handleGameStateChange = (newState: GameState) => {
    setGameState(newState);
  };

  return (
    <div className="app">
      <header className="app-header">
        <h1>🪙 Coin Toss Casino 🪙</h1>
        <p>Experience smooth 2D animations with realistic physics!</p>
      </header>

      <main className="app-main">
        <CoinTossCanvas onGameStateChange={handleGameStateChange} />

        {gameState && (
          <div className="game-stats">
            <h3>Game Statistics</h3>
            <div className="stats-grid">
              <div className="stat-item">
                <span className="stat-label">Current Balance:</span>
                <span className="stat-value">${gameState.balance}</span>
              </div>
              <div className="stat-item">
                <span className="stat-label">Last Bet:</span>
                <span className="stat-value">${gameState.currentBet}</span>
              </div>
              <div className="stat-item">
                <span className="stat-label">Your Choice:</span>
                <span className="stat-value">
                  {gameState.playerChoice ? gameState.playerChoice.toUpperCase() : 'None'}
                </span>
              </div>
              <div className="stat-item">
                <span className="stat-label">Last Result:</span>
                <span className="stat-value">
                  {gameState.result ? gameState.result.toUpperCase() : 'None'}
                </span>
              </div>
            </div>
          </div>
        )}
      </main>

      <footer className="app-footer">
        <p>Built with PixiJS, GSAP, React, and Vite</p>
        <p>Featuring smooth 2D animations and realistic physics</p>
      </footer>
    </div>
  );
}

export default App;
